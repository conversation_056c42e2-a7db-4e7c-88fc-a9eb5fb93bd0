package com.emm.controller;

import com.emm.dto.LoginRequest;
import com.emm.dto.LoginResponse;
import com.emm.service.AuthService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api")
public class AuthController {

    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);

    @Autowired
    private AuthService authService;

    @PostMapping("/login")
    public ResponseEntity<LoginResponse> login(@RequestBody LoginRequest loginRequest) {
        logger.info("Accessing POST /api/login");
        try {
            LoginResponse response = authService.login(loginRequest);
            if (response.isSuccess()) {
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            logger.error("Error in POST /api/login: {}", e.getMessage());
            LoginResponse errorResponse = new LoginResponse();
            errorResponse.setSuccess(false);
            errorResponse.setMessage("An error occurred during login: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    @PostMapping("/upload")
    public ResponseEntity<?> uploadFile() {
        logger.info("Accessing POST /api/upload");
        // TODO: Implement file upload logic
        return ResponseEntity.status(404).body("{'error': 'Image not found'}");
    }

    @GetMapping("/images")
    public ResponseEntity<?> getImages() {
        logger.info("Accessing GET /api/images");
        // TODO: Implement get images logic
        return ResponseEntity.status(404).body("{'error': 'Image not found'}");
    }

    @GetMapping("/image/{imageId}")
    public ResponseEntity<?> getImage(@PathVariable int imageId) {
        logger.info("Accessing GET /api/image/{}", imageId);
        // TODO: Implement get image logic
        return ResponseEntity.status(404).body("{'error': 'Image not found'}");
    }
}