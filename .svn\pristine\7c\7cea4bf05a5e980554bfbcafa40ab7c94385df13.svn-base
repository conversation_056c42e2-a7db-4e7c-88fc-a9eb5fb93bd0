<template>
	<view class="clock-in-container">
		<!-- 顶部状态栏 -->
		<view class="status-header">
			<view class="date-info">
				<text class="date">{{ currentDate }}</text>
				<text class="time">{{ currentTime }}</text>
			</view>
			<!-- 现在取消注释，显示位置信息 -->
		<!-- 	<view class="location-info">
				<text class="iconfont icon-location"></text>
				<text class="location-text">{{ locationName || '定位中...' }}</text>
			</view> -->
			<!-- 管理员设置按钮 -->
			<!-- 故障上报 -->
			<PermissionCheck permission="attendance:clock-rules">
				<view class="admin-settings" @click="goToAdminSettings">
					<text class="settings-icon">⚙️</text>
					<text class="settings-text">规则设置</text>
				</view>
			 </PermissionCheck>
		</view>
		
		<!-- 打卡状态展示 -->
		<view class="clock-status">
			<view class="status-card">
				<view class="work-time">
					<view class="time-item">
						<text class="time-label">上班时间</text>
						<text class="time-value">{{ attendanceRules.clockInTime || '08:30' }}</text>
					</view>
					<view class="time-divider"></view>
					<view class="time-item">
						<text class="time-label">下班时间</text>
						<text class="time-value">{{ attendanceRules.clockOutTime || '17:30' }}</text>
					</view>
				</view>
				
				<view class="clock-records">
					<view class="record-item">
						<text class="record-label">上班打卡</text>
						<text class="record-value" :class="{'not-clocked': !clockInTime}">
							{{ clockInTime || '未打卡' }}
						</text>
					</view>
					<view class="record-item">
						<text class="record-label">下班打卡</text>
						<text class="record-value" :class="{'not-clocked': !clockOutTime}">
							{{ clockOutTime || '未打卡' }}
						</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 打卡按钮区域 -->
		<view class="clock-action">
		<!-- 	<view class="action-tips" v-if="locationError">
				<text class="iconfont icon-warning"></text>
				<text>{{ locationError }}</text>
				<text class="retry-btn" @click="getLocation">重试</text>
			</view> -->
			
			<view class="action-button" :class="{'disabled': !canClockIn}" @click="handleClockIn">
				<view class="button-inner">
					<text class="button-text">{{ getClockButtonText() }}</text>
				</view>`
			</view>
			
			<view class="action-desc">
				<text>{{ getActionDescription() }}</text>
			</view>
		</view>
		
		<!-- 打卡记录 -->
		<view class="recent-records">
			<view class="section-title">最近打卡记录</view>
			<view class="record-list">
				<view class="record-empty" v-if="recentRecords.length === 0">
					<text>暂无打卡记录</text>
				</view>
				<view class="record-item" v-for="(record, index) in recentRecords" :key="index">
					<view class="record-date">
						<text class="date">{{ record.date }}</text>
						<text class="week">{{ record.week }}</text>
					</view>
					<view class="record-details">
						<view class="detail-item">
							<text class="detail-label">上班</text>
							<text class="detail-value" :class="{'abnormal': record.clockInStatus !== 'normal'}">
								{{ record.clockInTime }}
								<text class="status-tag" v-if="record.clockInStatus === 'late'">迟到</text>
							</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">下班</text>
							<text class="detail-value" :class="{'abnormal': record.clockOutStatus !== 'normal'}">
								{{ record.clockOutTime }}
								<text class="status-tag" v-if="record.clockOutStatus === 'early'">早退</text>
							</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 补卡申请弹窗 -->
		<!-- <uni-popup ref="supplementPopup" type="center">
			<view class="supplement-popup">
				<view class="popup-title">补卡申请</view>
				<view class="popup-content">
					<view class="form-item">
						<view class="form-label">补卡日期</view>
						<view class="form-input date-picker">
							<picker mode="date" :value="supplementDate" @change="supplementDate = $event.detail.value">
								<view>{{ supplementDate }}</view>
							</picker>
							<text class="icon-calendar">📅</text>
						</view>
					</view>
					
					<view class="form-item">
						<view class="form-label">补卡类型</view>
						<view class="form-input">
							<picker :range="supplementTypes" :value="supplementTypeIndex" @change="supplementTypeIndex = $event.detail.value">
								<view>{{ supplementTypes[supplementTypeIndex] }}</view>
							</picker>
						</view>
					</view>
					
					<view class="form-item">
						<view class="form-label">申请原因</view>
						<view class="form-input">
							<textarea v-model="supplementReason" placeholder="请输入补卡原因" maxlength="200" />
						</view>
					</view>
					
					<view class="form-item">
						<view class="form-label">证明材料</view>
						<view class="supplement-images">
							<view class="image-item" v-for="(image, index) in supplementImages" :key="index">
								<image class="supplement-image" :src="image" mode="aspectFill"></image>
								<view class="image-delete" @tap="deleteSupplementImage(index)">×</view>
							</view>
							<view class="image-add" @tap="chooseSupplementImage" v-if="supplementImages.length < 3">
								<view class="image-add-icon">+</view>
								<view class="image-add-text">上传</view>
							</view>
						</view>
					</view>
					
					<view class="popup-footer">
						<button class="btn-cancel" @click="$refs.supplementPopup.close()" :disabled="loading">取消</button>
						<button class="btn-confirm" @click="submitSupplementApplication" :disabled="loading">提交</button>
					</view>
				</view>
			</view>
		</uni-popup> -->
	</view>
</template>

<script>
	import { attendanceApi } from '@/utils/api.js';
	import PermissionCheck from "@/components/PermissionCheck.vue"; // 导入权限检查组件
	
	export default {
		components: {
		  PermissionCheck, // 本地注册组件
		},
		data() {
			return {
				// 当前时间相关
				timer: null,
				currentDate: '',
				currentTime: '',
				
				// 位置相关
				locationName: '定位中...',
				latitude: 0, // 默认纬度
				longitude: 0, // 默认经度
				clockStatus: 'normal', // 默认为normal，允许打卡
				locationError: '', // 存储定位错误信息
				useNativeGeolocation: true, // 默认使用原生定位
				locationPermissionDenied: false, // 位置权限是否被拒绝
				
				// 今日打卡记录
				clockInTime: '',
				clockOutTime: '',
				
				// 补卡相关
				supplementDate: this.formatDate(new Date()),
				supplementTypes: ['上班打卡', '下班打卡'],
				supplementTypeIndex: 0,
				supplementReason: '',
				supplementImages: [],
				
				// 最近打卡记录
				recentRecords: [],
				
				// 考勤规则
				attendanceRules: {
					clockInTime: '08:30',
					clockOutTime: '17:30',
					allowedDistance: 500, // 允许打卡的距离范围(米)
					lateThreshold: 15, // 迟到阈值(分钟)
					earlyLeaveThreshold: 15, // 早退阈值(分钟)
					locationUploadInterval: 1 // 位置上传时间间隔(分钟)
				},
				
				// 加载状态
				loading: false,
				
				// 是否是管理员
				isAdmin: false,
				
				// 定位轨迹上传定时器
				locationUploadTimer: null,
				isUploadingLocation: false // 是否正在上传位置
			}
		},
		computed: {
			canClockIn() {
				return this.clockStatus !== 'outside' && this.clockStatus !== 'loading';
			},
		},
		onLoad() {
			// 初始化时间显示
			this.updateDateTime();
			
			// 设置定时器更新时间
			this.timer = setInterval(() => {
				this.updateDateTime();
			}, 1000);
			
			// 获取考勤规则（先获取规则）
			this.getAttendanceRules();
			
			// 获取今日打卡记录
			this.getTodayClockRecord();
			
			// 获取最近打卡记录
			this.getRecentRecords();
			
			// 页面加载时获取位置
			this.checkLocationPermission().then(() => {
				// 权限通过，获取位置
				this.getLocation();
			}).catch(err => {
				console.error('位置权限检查失败:', err);
				this.locationError = err.errMsg || '获取位置权限失败';
				this.locationName = '无法获取位置';
			});
		},
		onUnload() {
			// 清除定时器
			if (this.timer) {
				clearInterval(this.timer);
				this.timer = null;
			}
			
			// 清除位置上传定时器
			this.stopLocationUpload();
		},
		onShow()
		{
			// 获取考勤规则（先获取规则）
			this.getAttendanceRules();
			
			// 获取今日打卡记录
			this.getTodayClockRecord();
			
			// 页面显示时更新位置
			if (!this.locationError) {
				this.getLocation();
			}
		},
		methods: {
			// 更新日期时间
			updateDateTime() {
				const now = new Date();
				
				// 格式化日期: 2023年01月01日 星期一
				const year = now.getFullYear();
				const month = String(now.getMonth() + 1).padStart(2, '0');
				const day = String(now.getDate()).padStart(2, '0');
				const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
				const weekDay = weekDays[now.getDay()];
				
				this.currentDate = `${year}年${month}月${day}日 ${weekDay}`;
				
				// 格式化时间: 08:00:00
				const hours = String(now.getHours()).padStart(2, '0');
				const minutes = String(now.getMinutes()).padStart(2, '0');
				const seconds = String(now.getSeconds()).padStart(2, '0');
				
				this.currentTime = `${hours}:${minutes}:${seconds}`;
			},
			
			// 获取位置信息
			getLocation() {
				// this.clockStatus = 'loading';
				// uni.showLoading({
				// 	title: '获取位置中...'
				// });
				
				// 设置超时，确保即使获取位置失败也能继续
				const locationTimeout = setTimeout(() => {
					//uni.hideLoading();
					console.log('位置获取超时，使用默认位置');
					this.locationError = '位置获取超时，请重试';
					this.clockStatus = 'normal';
				}, 15000); // 15秒超时
				
				// 优先使用原生定位
				if (this.useNativeGeolocation) {
					// 尝试使用原生定位
					this.getNativeLocation().then(() => {
						clearTimeout(locationTimeout);
						//uni.hideLoading();
					}).catch(err => {
						console.error('原生定位失败，尝试使用uni定位:', err);
						this.useNativeGeolocation = false;
						// 如果原生定位失败，回退到uni定位
						this.getUniLocation().then(() => {
							clearTimeout(locationTimeout);
							uni.hideLoading();
						}).catch(err => {
							clearTimeout(locationTimeout);
							this.handleLocationError(err);
							//uni.hideLoading();
						});
					});
				} else {
					// 直接使用uni定位
					this.getUniLocation().then(() => {
						clearTimeout(locationTimeout);
						//uni.hideLoading();
					}).catch(err => {
						clearTimeout(locationTimeout);
						this.handleLocationError(err);
						//uni.hideLoading();
					});
				}
			},
			
			// 使用原生定位API获取位置
			getNativeLocation() {
				return new Promise((resolve, reject) => {
					// #ifdef APP-PLUS
					try {
						plus.geolocation.getCurrentPosition(
							(position) => {
								console.log('原生定位成功:', position);
								
								// 提取位置信息
								const coords = position.coords;
								this.latitude = coords.latitude;
								this.longitude = coords.longitude;
								this.locationError = ''; // 清除错误信息
								this.locationPermissionDenied = false;
								this.clockStatus = 'normal';
								
								// 设置位置名称
								if (position.address) {
									this.locationName = position.address;
								} else {
									// 如果没有地址信息，使用逆地理编码获取
									this.getLocationName(coords.latitude, coords.longitude);
								}
								
								resolve(position);
							},
							(err) => {
								console.error('原生定位失败:', err);
								reject({
									errMsg: err.message || '获取位置失败',
									detail: err
								});
							},
							{
								enableHighAccuracy: true, // 高精度定位
								timeout: 15000, // 超时时间
								maximumAge: 0, // 不使用缓存
								provider: 'system', // 使用系统定位
								geocode: true // 获取地理编码信息
							}
						);
					} catch (e) {
						console.error('调用原生定位API异常:', e);
						reject({
							errMsg: '调用定位服务失败',
							detail: e.message
						});
					}
					// #endif
					
					// #ifndef APP-PLUS
					// 非APP环境下，使用uni定位API
					this.getUniLocation().then(resolve).catch(reject);
					// #endif
				});
			},
			
			// 使用uni.getLocation获取位置
			getUniLocation() {
				return new Promise((resolve, reject) => {
					uni.getLocation({
						type: 'gcj02',
						isHighAccuracy: true,
						highAccuracyExpireTime: 5000,
						success: (res) => {
							console.log('uni位置获取成功:', res);
							this.latitude = res.latitude;
							this.longitude = res.longitude;
							this.locationError = ''; // 清除错误信息
							this.locationPermissionDenied = false;
							this.clockStatus = 'normal';
							
							// 逆地理编码获取位置信息
							this.getLocationName(res.latitude, res.longitude);
							resolve(res);
						},
						fail: (err) => {
							console.error('uni获取位置失败:', err);
							//reject(err);
						},
						complete: () => {
							// 完成位置获取，无论成功失败
							console.log('位置获取完成');
						}
					});
				});
			},
			
			// 检查位置权限
			checkLocationPermission() {
				return new Promise((resolve, reject) => {
					// 如果已经被拒绝过，先提示用户
					if (this.locationPermissionDenied) {
						uni.showModal({
							title: '需要位置权限',
							content: '打卡功能需要获取您的位置，请授权位置权限',
							confirmText: '去设置',
							cancelText: '取消',
							success: (res) => {
								if (res.confirm) {
									this.openLocationSettings();
								} else {
									reject({
										errMsg: '位置权限被拒绝',
										detail: '用户取消授权'
									});
								}
							}
						});
						return;
					}
					
					// #ifdef APP-PLUS
					if (plus.os.name.toLowerCase() === 'android') {
						const mainActivity = plus.android.runtimeMainActivity();
						const PackageManager = plus.android.importClass("android.content.pm.PackageManager");
						const ContextCompat = plus.android.importClass("androidx.core.content.ContextCompat");
						const Manifest = plus.android.importClass("android.Manifest");
						
						// 检查是否有精确位置权限
						const hasFineLocationPermission = ContextCompat.checkSelfPermission(mainActivity, Manifest.permission.ACCESS_FINE_LOCATION) === PackageManager.PERMISSION_GRANTED;
						// 检查是否有粗略位置权限
						const hasCoarseLocationPermission = ContextCompat.checkSelfPermission(mainActivity, Manifest.permission.ACCESS_COARSE_LOCATION) === PackageManager.PERMISSION_GRANTED;
						
						if (hasFineLocationPermission || hasCoarseLocationPermission) {
							// 检查GPS是否开启
							const locationManager = plus.android.importClass("android.location.LocationManager");
							const locationService = mainActivity.getSystemService(locationManager);
							const isGPSEnabled = locationService.isProviderEnabled(locationManager.GPS_PROVIDER);
							const isNetworkEnabled = locationService.isProviderEnabled(locationManager.NETWORK_PROVIDER);
							
							if (isGPSEnabled || isNetworkEnabled) {
								resolve();
							} else {
								// GPS未开启
								uni.showModal({
									title: '定位服务未开启',
									content: '请开启GPS定位服务',
									confirmText: '去开启',
									success: (res) => {
										if (res.confirm) {
											// 跳转到位置设置页面
											const Intent = plus.android.importClass('android.content.Intent');
											const Settings = plus.android.importClass('android.provider.Settings');
											const intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
											mainActivity.startActivity(intent);
										}
										
										// 不管用户是否去开启，都拒绝当前Promise
										reject({
											errMsg: 'GPS定位服务未开启',
											detail: '请开启GPS定位服务'
										});
									}
								});
							}
						} else {
							// 没有权限，申请权限
							plus.android.requestPermissions(
								[Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION],
								(resultObj) => {
									if (resultObj.granted.indexOf(Manifest.permission.ACCESS_FINE_LOCATION) > -1 ||
										resultObj.granted.indexOf(Manifest.permission.ACCESS_COARSE_LOCATION) > -1) {
										resolve();
									} else {
										// 权限被拒绝
										this.locationPermissionDenied = true;
										reject({
											errMsg: '位置权限被拒绝',
											detail: '请在系统设置中开启位置权限'
										});
									}
								}
							);
						}
					} else if (plus.os.name.toLowerCase() === 'ios') {
						// iOS平台
						const cllocationMgr = plus.ios.import("CLLocationManager");
						const status = cllocationMgr.authorizationStatus();
						
						// kCLAuthorizationStatusAuthorizedAlways: 4
						// kCLAuthorizationStatusAuthorizedWhenInUse: 3
						if (status === 4 || status === 3) {
							resolve();
						} else {
							// 没有权限或权限不确定，尝试请求权限
							const locationMgr = new cllocationMgr();
							locationMgr.requestWhenInUseAuthorization(); // 请求使用期间访问位置
							
							// iOS权限请求是异步的，这里简单处理，假设用户会授权
							// 实际应用中应该有更完善的回调处理
							setTimeout(() => {
								const newStatus = cllocationMgr.authorizationStatus();
								if (newStatus === 4 || newStatus === 3) {
									resolve();
								} else {
									this.locationPermissionDenied = true;
									reject({
										errMsg: '位置权限被拒绝',
										detail: '请在系统设置中开启位置权限'
									});
								}
								plus.ios.deleteObject(locationMgr);
							}, 1500);
						}
						plus.ios.deleteObject(cllocationMgr);
					} else {
						// 其他平台，默认有权限
						resolve();
					}
					// #endif
					
					// #ifdef H5
					// H5环境
					if (navigator.geolocation) {
						// 检查浏览器是否支持Geolocation API
						navigator.permissions.query({name: 'geolocation'}).then(permissionStatus => {
							if (permissionStatus.state === 'granted' || permissionStatus.state === 'prompt') {
								resolve();
							} else {
								this.locationPermissionDenied = true;
								reject({
									errMsg: '位置权限被拒绝',
									detail: '请在浏览器设置中开启位置权限'
								});
							}
						}).catch(() => {
							// 如果无法查询权限，直接尝试获取位置
							resolve();
						});
					} else {
						reject({
							errMsg: '浏览器不支持获取位置',
							detail: '请使用支持地理位置的浏览器'
						});
					}
					// #endif
					
					// #ifdef MP-WEIXIN || MP-ALIPAY
					// 微信小程序或支付宝小程序
					uni.authorize({
						scope: 'scope.userLocation',
						success: function() {
							resolve();
						},
						fail: function(err) {
							// 权限被拒绝
							uni.getSetting({
								success: (res) => {
									if (res.authSetting['scope.userLocation'] === false) {
										// 已拒绝过权限
										uni.showModal({
											title: '需要位置权限',
											content: '打卡功能需要获取您的位置，请在设置中授权',
											confirmText: '去设置',
											success: (modalRes) => {
												if (modalRes.confirm) {
													uni.openSetting({
														success: (settingRes) => {
															if (settingRes.authSetting['scope.userLocation']) {
																resolve();
															} else {
																reject({
																	errMsg: '位置权限被拒绝',
																	detail: '用户在设置页面拒绝授权'
																});
															}
														}
													});
												} else {
													reject({
														errMsg: '位置权限被拒绝',
														detail: '用户取消前往设置页'
													});
												}
											}
										});
									} else {
										reject({
											errMsg: '位置权限被拒绝',
											detail: err.errMsg
										});
									}
								}
							});
						}
					});
					// 其他小程序平台，直接尝试获取权限
					resolve();
					// #endif
				});
			},
			
			// 处理位置错误
			handleLocationError(err) {
				this.clockStatus = 'outside';
				this.locationError = err.errMsg || '获取位置失败，请检查定位权限';
				this.getLocation();
			},
			
			// 获取位置名称
			getLocationName(latitude, longitude) {
				// 使用腾讯地图API获取位置名称
				uni.request({
					url: 'https://apis.map.qq.com/ws/geocoder/v1/',
					data: {
						location: `${latitude},${longitude}`,
						key: 'YOUR_MAP_KEY',  // 这里需要替换为实际的地图API密钥
						get_poi: 0
					},
					success: (res) => {
						if (res.statusCode === 200 && res.data.status === 0) {
							const address = res.data.result.address;
							this.locationName = address;
						} else {
							// 如果腾讯地图API失败，尝试使用备用方法
							this.getLocationNameFallback(latitude, longitude);
						}
					},
					fail: () => {
						// 如果请求失败，尝试使用备用方法
						this.getLocationNameFallback(latitude, longitude);
					}
				});
			},
			
			// 备用获取位置名称方法
			getLocationNameFallback(latitude, longitude) {
				// #ifdef APP-PLUS
				try {
					// 尝试使用原生定位获取地址
					plus.geolocation.getCurrentPosition(
						(position) => {
							if (position.addresses) {
								this.locationName = position.addresses;
							} else {
								this.locationName = `位置(${latitude.toFixed(6)},${longitude.toFixed(6)})`;
							}
						},
						(e) => {
							this.locationName = `位置(${latitude.toFixed(6)},${longitude.toFixed(6)})`;
							console.error('原生定位获取地址失败:', e.message);
						},
						{
							geocode: true,
							provider: 'baidu'
						}
					);
				} catch (e) {
					this.locationName = `位置(${latitude.toFixed(6)},${longitude.toFixed(6)})`;
				}
				// #endif
				
				// #ifndef APP-PLUS
				this.locationName = `位置(${latitude.toFixed(6)},${longitude.toFixed(6)})`;
				// #endif
			},
			
			// 检查是否在考勤范围内
			checkAttendanceArea(latitude, longitude) {
				// 如果后端API不可用，使用本地判断
				const checkLocal = () => {
					// 假设公司位置
					const companyLocation = {
						latitude: 34.341576, // 根据实际情况修改
						longitude: 108.940174 // 根据实际情况修改
					};
					
					// 计算距离
					const distance = this.calculateDistance(
						latitude, longitude,
						companyLocation.latitude, companyLocation.longitude
					);
					
					// 判断是否在范围内（假设500米内可打卡）
					const inArea = distance <= this.attendanceRules.allowedDistance;
					this.clockStatus = inArea ? 'normal' : 'outside';
					
					if (!inArea) {
						uni.showToast({
							title: `您距离考勤点${Math.round(distance)}米，不在打卡范围内`,
							icon: 'none',
							duration: 3000
						});
					}
				};
				
				// 调用后端API检查
				attendanceApi.checkAttendanceArea({
					latitude,
					longitude
				}).then(res => {
					if (res.code === 200) {
						this.clockStatus = res.data.inArea ? 'normal' : 'outside';
						
						// 如果不在范围内，显示距离信息
						if (!res.data.inArea && res.data.distance) {
							uni.showToast({
								title: `您距离考勤点${res.data.distance}米，不在打卡范围内`,
								icon: 'none',
								duration: 3000
							});
						}
					} else {
						// 如果API返回错误，使用本地判断
						checkLocal();
					}
				}).catch(err => {
					console.error('考勤范围校验异常:', err);
					// API调用失败，使用本地判断
					checkLocal();
				});
			},
			
			// 计算两点之间的距离（米）
			calculateDistance(lat1, lon1, lat2, lon2) {
				const R = 6371000; // 地球半径，单位米
				const dLat = this.deg2rad(lat2 - lat1);
				const dLon = this.deg2rad(lon2 - lon1);
				const a = 
					Math.sin(dLat/2) * Math.sin(dLat/2) +
					Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) * 
					Math.sin(dLon/2) * Math.sin(dLon/2);
				const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
				const distance = R * c;
				return distance;
			},
			
			// 角度转弧度
			deg2rad(deg) {
				return deg * (Math.PI/180);
			},
			
			// 获取考勤规则
			getAttendanceRules() {
				attendanceApi.getClockRules()
					.then(res => {
						if (res.code === 200 && res.data) {
							console.log('获取考勤规则成功:', res.data);
							this.attendanceRules = {
								...this.attendanceRules,
								...res.data
							};
						}
					})
					.catch(err => {
						console.error('获取考勤规则失败:', err);
					});
			},
			
			// 获取今日打卡记录
			getTodayClockRecord() {
				attendanceApi.getTodayRecord()
					.then(res => {
						console.log('获取今日打卡记录:', res);
						if (res.code === 200 && res.data) {
							const oldClockInTime = this.clockInTime;
							const oldClockOutTime = this.clockOutTime;
							
							this.clockInTime = res.data.clockInTime || '';
							this.clockOutTime = res.data.clockOutTime || '';
							
							// 如果已有上班打卡记录，显示打卡状态
							if (this.clockInTime && res.data.clockInStatus) {
								const statusText = res.data.clockInStatus === 'late' ? '（迟到）' : '';
								this.clockInTime = this.clockInTime + statusText;
							}
							
							// 如果已有下班打卡记录，显示打卡状态
							if (this.clockOutTime && res.data.clockOutStatus) {
								const statusText = res.data.clockOutStatus === 'early' ? '（早退）' : '';
								this.clockOutTime = this.clockOutTime + statusText;
							}
							
							// 检查是否需要开始或停止定位上传
							if (!oldClockInTime && this.clockInTime) {
								// 上班打卡成功，开始定位上传
								this.startLocationUpload();
							} else if (!oldClockOutTime && this.clockOutTime) {
								// 下班打卡成功，停止定位上传
								this.stopLocationUpload();
							} else if (this.clockInTime && !this.clockOutTime && !this.isUploadingLocation) {
								// 已上班打卡但未下班打卡，并且未启动定位上传，开始定位上传
								this.startLocationUpload();
							}
						}
					})
					.catch(err => {
						console.error('获取今日打卡记录失败:', err);
					});
			},
			
			// 获取最近打卡记录
			getRecentRecords() {
				attendanceApi.getRecentRecords(7)  // 获取最近7天的记录
					.then(res => {
						if (res.code === 200 && res.data && Array.isArray(res.data.records)) {
							this.recentRecords = res.data.records.map(item => {
								return {
									date: item.date,
									week: item.week,
									clockInTime: item.clockInTime || '未打卡',
									clockOutTime: item.clockOutTime || '未打卡',
									clockInStatus: item.clockInStatus || 'normal',
									clockOutStatus: item.clockOutStatus || 'normal'
								};
							});
						} else {
							// 处理返回数据格式不正确的情况
							console.warn('获取最近打卡记录返回数据格式异常:', res);
							this.recentRecords = [];
						}
					})
					.catch(err => {
						console.error('获取最近打卡记录失败:', err);
						this.recentRecords = []; // 确保在错误时也将数组清空
						uni.showToast({
							title: '获取打卡记录失败',
							icon: 'none'
						});
					});
			},
			
			// 处理打卡
			handleClockIn() {
				if (!this.canClockIn) {
					return;
				}
				
				// 判断是上班打卡还是下班打卡
				const isClockIn = !this.clockInTime;
				const isClockOut = !!this.clockInTime && !this.clockOutTime;
				
				if (!isClockIn && !isClockOut) {
					uni.showToast({
						title: '今日打卡已完成',
						icon: 'info'
					});
					return;
				}
				
				// 先获取最新位置，再提交打卡
				// uni.showLoading({
				// 	title: '获取位置中...'
				// });
				
				// 优先使用原生定位获取最新位置
				if (this.useNativeGeolocation) {
					this.getNativeLocation().then(() => {
						uni.hideLoading();
						this.submitAttendance();
					}).catch(err => {
						console.error('打卡时原生定位失败，尝试使用uni定位:', err);
						this.useNativeGeolocation = false;
						// 如果原生定位失败，回退到uni定位
						this.getUniLocation().then(() => {
							//uni.hideLoading();
							this.submitAttendance();
						}).catch(err => {
							//uni.hideLoading();
							// 如果定位失败，询问用户是否使用上次位置打卡
							// uni.showModal({
							// 	title: '定位失败',
							// 	content: '是否使用上次获取的位置打卡？',
							// 	confirmText: '确定',
							// 	cancelText: '取消',
							// 	success: (res) => {
							// 		if (res.confirm) {
							// 			this.submitAttendance();
							// 		}
							// 	}
							// });
						});
					});
				} else {
					// 直接使用uni定位
					this.getUniLocation().then(() => {
						uni.hideLoading();
						this.submitAttendance();
					}).catch(err => {
						uni.hideLoading();
						// 如果定位失败，询问用户是否使用上次位置打卡
						uni.showModal({
							title: '定位失败',
							content: '是否使用上次获取的位置打卡？',
							confirmText: '确定',
							cancelText: '取消',
							success: (res) => {
								if (res.confirm) {
									this.submitAttendance();
								}
							}
						});
					});
				}
			},
			
			// 显示补卡申请弹窗
			showSupplementModal() {
				// 重置表单数据
				this.resetSupplementForm();
				this.$refs.supplementPopup.open();
			},
			
			// 重置补卡表单
			resetSupplementForm() {
				this.supplementDate = this.formatDate(new Date());
				this.supplementTypeIndex = 0;
				this.supplementReason = '';
				this.supplementImages = [];
			},
			
			// 选择补卡证明图片
			chooseSupplementImage() {
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						// 限制最多3张图片
						if (this.supplementImages.length < 3) {
							this.supplementImages.push(res.tempFilePaths[0]);
						} else {
							uni.showToast({
								title: '最多上传3张图片',
								icon: 'none'
							});
						}
					}
				});
			},
			
			// 删除补卡证明图片
			deleteSupplementImage(index) {
				this.supplementImages.splice(index, 1);
			},
			
			// 提交补卡申请
			submitSupplementApplication() {
				// 表单验证
				if (!this.supplementDate) {
					uni.showToast({
						title: '请选择补卡日期',
						icon: 'none'
					});
					return;
				}
				
				if (!this.supplementReason || this.supplementReason.trim() === '') {
					uni.showToast({
						title: '请输入补卡原因',
						icon: 'none'
					});
					return;
				}
				
				// 显示加载状态
				this.loading = true;
				
				// 准备要提交的数据
				const formData = {
					userId: this.getUserId(),
					date: this.supplementDate,
					type: this.supplementTypeIndex + 1, // 1-上班打卡, 2-下班打卡
					reason: this.supplementReason,
					images: []
				};
				
				// 先上传图片，再提交表单
				if (this.supplementImages.length > 0) {
					this.uploadSupplementImages().then(imageUrls => {
						formData.images = imageUrls;
						this.sendSupplementRequest(formData);
					}).catch(err => {
						this.loading = false;
						uni.showToast({
							title: '图片上传失败',
							icon: 'none'
						});
					});
				} else {
					this.sendSupplementRequest(formData);
				}
			},
			
			// 上传补卡证明图片
			uploadSupplementImages() {
				return new Promise((resolve, reject) => {
					const uploadTasks = this.supplementImages.map(imagePath => {
						return new Promise((uploadResolve, uploadReject) => {
							uni.uploadFile({
								url: this.$api.baseUrl + '/attendance/upload',
								filePath: imagePath,
								name: 'file',
								header: {
									token: uni.getStorageSync('token')
								},
								success: (res) => {
									if (res.statusCode === 200) {
										const data = JSON.parse(res.data);
										if (data.code === 0 && data.data) {
											uploadResolve(data.data.url);
										} else {
											uploadReject(new Error(data.msg || '上传失败'));
										}
									} else {
										uploadReject(new Error('上传失败'));
									}
								},
								fail: (err) => {
									uploadReject(err);
								}
							});
						});
					});
					
					Promise.all(uploadTasks).then(resolve).catch(reject);
				});
			},
			
			// 发送补卡申请请求
			sendSupplementRequest(formData) {
				attendanceApi.submitSupplementApplication(formData).then(res => {
					this.loading = false;
					if (res.code === 0) {
						uni.showToast({
							title: res.msg || '补卡申请已提交',
							icon: 'success'
						});
						this.$refs.supplementPopup.close();
						this.resetSupplementForm();
						
						// 刷新打卡记录
						this.getTodayClockRecord();
						this.getRecentRecords();
					} else {
						uni.showToast({
							title: res.msg || '补卡申请失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					this.loading = false;
					console.error('补卡申请失败:', err);
					uni.showToast({
						title: '补卡申请失败',
						icon: 'none'
					});
				});
			},
			
			// 获取打卡按钮文本
			getClockButtonText() {
				if (!this.clockInTime) {
					return '上班打卡';
				}
				
				if (!this.clockOutTime) {
					return '下班打卡';
				}
				
				return '今日打卡已完成';
			},
			
			// 获取操作描述
			getActionDescription() {
				const now = new Date();
				const currentHour = now.getHours();
				const currentMinute = now.getMinutes();
				
				if (!this.clockInTime) {
					// 上班打卡判断
					const clockInTimeArr = (this.attendanceRules.clockInTime || '08:30').split(':');
					const clockInHour = parseInt(clockInTimeArr[0]);
					const clockInMinute = parseInt(clockInTimeArr[1]);
					
					if (currentHour > clockInHour || (currentHour === clockInHour && currentMinute > clockInMinute)) {
						return `已超过上班时间${this.attendanceRules.clockInTime}，打卡将记为迟到`;
					} else {
						return `上班时间${this.attendanceRules.clockInTime}，请按时打卡`;
					}
				}
				
				if (!this.clockOutTime) {
					// 下班打卡判断
					const clockOutTimeArr = (this.attendanceRules.clockOutTime || '17:30').split(':');
					const clockOutHour = parseInt(clockOutTimeArr[0]);
					const clockOutMinute = parseInt(clockOutTimeArr[1]);
					
					if (currentHour < clockOutHour || (currentHour === clockOutHour && currentMinute < clockOutMinute)) {
						return `下班时间${this.attendanceRules.clockOutTime}，提前打卡将记为早退`;
					} else {
						return `您已完成上班打卡，下班时间${this.attendanceRules.clockOutTime}`;
					}
				}
				return '您已完成今日打卡';
			},
			
			// 格式化日期为YYYY-MM-DD
			formatDate(date) {
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				return `${year}-${month}-${day}`;
			},
			
			// 获取用户ID
			getUserId() {
				const userId = uni.getStorageSync('userId');
				if (!userId) {
					console.warn('未找到用户ID，使用默认值1');
					return 1; // 如果没有找到用户ID，返回默认值1
				}
				return userId || 1; // 如果转换失败，返回默认值1
			},
			
			// 切换定位方式
			toggleLocationMethod() {
				this.useNativeGeolocation = !this.useNativeGeolocation;
				uni.showToast({
					title: `已切换为${this.useNativeGeolocation ? '原生' : '统一'}定位API`,
					icon: 'none',
					duration: 2000
				});
				
				// 重新获取位置
				setTimeout(() => {
					this.getLocation();
				}, 500);
			},
			
			// 提交考勤打卡
			submitAttendance() {
				// 不再检查位置状态
				// if (this.clockStatus !== 'normal') {
				// 	uni.showToast({
				// 		title: '不在考勤范围内，无法打卡',
				// 		icon: 'none'
				// 	});
				// 	return;
				// }
				
				const now = new Date();
				const clockType = !this.clockInTime ? 'checkin' : 'checkout';
				
				// 判断打卡状态
				let status = 'normal';
				
				if (clockType === 'checkin') {
					// 上班打卡：判断是否迟到
					const clockInTimeArr = this.attendanceRules.clockInTime.split(':');
					const clockInHour = parseInt(clockInTimeArr[0]);
					const clockInMinute = parseInt(clockInTimeArr[1]);
					const clockInTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), clockInHour, clockInMinute);
					
					if (now > clockInTime) {
						status = 'late';
					}
				} else if (clockType === 'checkout') {
					// 下班打卡：判断是否早退
					const clockOutTimeArr = this.attendanceRules.clockOutTime.split(':');
					const clockOutHour = parseInt(clockOutTimeArr[0]);
					const clockOutMinute = parseInt(clockOutTimeArr[1]);
					const clockOutTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), clockOutHour, clockOutMinute);
					
					if (now < clockOutTime) {
						status = 'early';
					}
				}
				
				this.loading = true;
				
				// 显示加载提示
				uni.showLoading({
					title: '打卡中...'
				});
				
				// 保存当前经纬度，用于打卡成功后上传
				const currentLocation = {
					latitude: this.latitude,
					longitude: this.longitude
				};
				
				attendanceApi.submitClock({
					clock_type: clockType,
					latitude: this.latitude,
					longitude: this.longitude,
					//face_photo: [], // 改为空数组而非空字符串
					//liveness_data: null, // 改为null而非空字符串
					leave_type: '', // 不是请假
					leave_reason: '', // 不是请假
					//leave_proof: [], // 改为空数组而非空字符串
					status: status, // 打卡状态
					user_id: this.getUserId() // 添加用户ID
				}).then(res => {
					uni.hideLoading();
					if (res.code === 200) {
						uni.showToast({
							title: clockType === 'checkin' ? '上班打卡成功' : '下班打卡成功',
							icon: 'success'
						});
						
						// 更新打卡记录
						this.getTodayClockRecord();
						this.getRecentRecords();
						
						// 根据打卡类型处理位置上传
						if (clockType === 'checkin') {
							// 上班打卡：立即上传一次位置记录，然后开始定时上传
							console.log('上班打卡成功，立即上传位置轨迹，然后开始定时上传');
							this.uploadTrajectoryRecord(currentLocation.latitude, currentLocation.longitude)
								.then(() => {
									// 上传成功后，开始定时上传
									this.startLocationUpload();
								})
								.catch(err => {
									console.error('上班打卡后上传位置失败:', err);
								});
						} else if (clockType === 'checkout') {
							// 下班打卡：立即上传最后一次位置记录，然后停止定时上传
							console.log('下班打卡成功，立即上传最后一次位置轨迹，然后停止定时上传');
							// 先停止定时上传
							this.stopLocationUpload();
							// 再上传最后一次位置
							this.uploadTrajectoryRecord(currentLocation.latitude, currentLocation.longitude)
								.catch(err => {
									console.error('下班打卡后上传位置失败:', err);
								});
						}
					} else {
						uni.showToast({
							title: res.message || '打卡失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					uni.hideLoading();
					console.error('打卡提交失败:', err);
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					});
				}).finally(() => {
					this.loading = false;
				});
			},
			
			// 切换管理员设置
			goToAdminSettings() {
				console.log('正在跳转到考勤规则设置页面');
				// 导航到考勤规则设置页面
				uni.navigateTo({
					url: '/pages/attendance/admin/rules',
					fail: (err) => {
						console.error('页面跳转失败:', err);
						uni.showToast({
							title: '页面跳转失败',
							icon: 'none'
						});
					}
				});
			},
			
			// 上传位置轨迹记录
			uploadTrajectoryRecord(latitude, longitude) {
				// 检查经纬度是否有效
				if (!longitude || !latitude || longitude === 0 || latitude === 0) {
					console.error('经纬度无效，无法上传位置轨迹:', {
						longitude: longitude,
						latitude: latitude
					});
					return Promise.reject({errMsg: '位置参数无效'});
				}
				
				// 获取用户ID
				const userId = this.getUserId();
				
				console.log('上传位置轨迹:', {
					userId: userId,
					longitude: longitude,
					latitude: latitude
				});
				
				// 调用API上传位置数据
				return attendanceApi.uploadPersonTrajectory({
					userId: userId, // 传递userId参数
					employeeId: userId, // 同时传递employeeId参数，值与userId相同
					longitude: longitude,
					latitude: latitude
				}).then(res => {
					if (res.code === 200) {
						console.log('位置轨迹上传成功:', res.data);
						return res.data;
					} else {
						console.error('位置轨迹上传失败:', res.message || '未知错误', res);
						return Promise.reject(res);
					}
				}).catch(err => {
					console.error('位置轨迹上传请求异常:', err);
					return Promise.reject(err);
				});
			},
			
			// 开始定时上传位置
			startLocationUpload() {
				if (this.locationUploadTimer) {
					// 如果已经有定时器在运行，先清除
					this.stopLocationUpload();
				}
				
				// 标记为正在上传位置
				this.isUploadingLocation = true;
				
				// 获取上传间隔时间（分钟）
				const interval = this.attendanceRules.locationUploadInterval || 1;
				const intervalMs = interval * 60 * 1000; // 转换为毫秒
				
				console.log(`开始定时上传位置，间隔时间: ${interval}分钟`);
				
				// 设置定时器，定时上传位置
				this.locationUploadTimer = setInterval(() => {
					this.uploadLocation();
				}, intervalMs);
			},
			
			// 停止定时上传位置
			stopLocationUpload() {
				if (this.locationUploadTimer) {
					console.log('停止定时上传位置');
					clearInterval(this.locationUploadTimer);
					this.locationUploadTimer = null;
				}
				
				// 标记为不在上传位置
				this.isUploadingLocation = false;
			},
			
			// 上传位置到服务器
			uploadLocation() {
				console.log('准备获取位置并上传轨迹...');
				
				// 获取最新位置
				if (this.useNativeGeolocation) {
					// 优先使用原生定位
					this.getNativeLocation()
						.then(position => {
							// 使用获取到的位置上传轨迹
							const coords = position.coords;
							this.uploadTrajectoryRecord(coords.latitude, coords.longitude);
						})
						.catch(err => {
							console.error('获取原生位置失败，尝试使用uni定位:', err);
							// 回退到uni定位
							this.getUniLocation()
								.then(res => {
									// 使用获取到的位置上传轨迹
									this.uploadTrajectoryRecord(res.latitude, res.longitude);
								})
								.catch(err => {
									console.error('位置上传失败，无法获取位置:', err);
								});
						});
				} else {
					// 直接使用uni定位
					this.getUniLocation()
						.then(res => {
							// 使用获取到的位置上传轨迹
							this.uploadTrajectoryRecord(res.latitude, res.longitude);
						})
						.catch(err => {
							console.error('位置上传失败，无法获取位置:', err);
						});
				}
			},
		}
	}
</script>

<style lang="scss">
	.clock-in-container {
		padding: 30rpx;
		background-color: #f5f7fa;
		min-height: 100vh;
	}
	
	.status-header {
		margin-bottom: 40rpx;
		
		.date-info {
			display: flex;
			flex-direction: column;
			align-items: center;
			margin-bottom: 20rpx;
			
			.date {
				font-size: 32rpx;
				color: #333;
				margin-bottom: 10rpx;
			}
			
			.time {
				font-size: 60rpx;
				font-weight: bold;
				color: #333;
			}
		}
		
		.location-info {
			display: flex;
			justify-content: center;
			align-items: center;
			
			.iconfont {
				color: $uni-color-primary;
				font-size: 32rpx;
				margin-right: 10rpx;
			}
			
			.location-text {
				font-size: 28rpx;
				color: #666;
			}
		}
		
		.admin-settings {
			position: absolute;
			top: 20rpx;
			right: 20rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			// background-color: rgba(255, 255, 255, 0.8);
			padding: 10rpx;
			border-radius: 10rpx;
			
			.settings-icon {
				font-size: 40rpx;
				margin-bottom: 5rpx;
			}
			
			.settings-text {
				font-size: 24rpx;
				color: #333;
			}
		}
	}
	
	.clock-status {
		margin-bottom: 40rpx;
		
		.status-card {
			background-color: #fff;
			border-radius: 16rpx;
			padding: 30rpx;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
			
			.work-time {
				display: flex;
				align-items: center;
				margin-bottom: 30rpx;
				
				.time-item {
					flex: 1;
					display: flex;
					flex-direction: column;
					align-items: center;
					
					.time-label {
						font-size: 28rpx;
						color: #666;
						margin-bottom: 10rpx;
					}
					
					.time-value {
						font-size: 36rpx;
						font-weight: bold;
						color: #333;
					}
				}
				
				.time-divider {
					width: 2rpx;
					height: 80rpx;
					background-color: #eee;
				}
			}
			
			.clock-records {
				display: flex;
				border-top: 2rpx solid #f5f5f5;
				padding-top: 30rpx;
				
				.record-item {
					flex: 1;
					display: flex;
					flex-direction: column;
					align-items: center;
					
					.record-label {
						font-size: 28rpx;
						color: #666;
						margin-bottom: 10rpx;
					}
					
					.record-value {
						font-size: 32rpx;
						color: $uni-color-success;
						font-weight: bold;
						
						&.not-clocked {
							color: #999;
						}
					}
				}
			}
		}
	}
	
	.clock-action {
		margin-bottom: 60rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		
		.action-tips {
			display: flex;
			align-items: center;
			background-color: rgba(250, 173, 20, 0.1);
			padding: 16rpx 30rpx;
			border-radius: 8rpx;
			margin-bottom: 30rpx;
			
			.iconfont {
				color: $uni-color-warning;
				margin-right: 10rpx;
				font-size: 32rpx;
			}
			
			text {
				font-size: 28rpx;
				color: $uni-color-warning;
			}
			
			.retry-btn {
				margin-left: 20rpx;
				color: #007AFF;
				text-decoration: underline;
			}
		}
		
		.action-button {
			width: 300rpx;
			height: 300rpx;
			border-radius: 50%;
			background: linear-gradient(135deg, #4483e5, #6a9eef);
			display: flex;
			justify-content: center;
			align-items: center;
			box-shadow: 0 10rpx 30rpx rgba(106, 158, 239, 0.3);
			margin-bottom: 30rpx;
			transition: all 0.3s ease;
			
			&.disabled {
				background: linear-gradient(135deg, #a0a0a0, #d0d0d0);
				box-shadow: 0 10rpx 30rpx rgba(160, 160, 160, 0.3);
			}
			
			.button-inner {
				width: 260rpx;
				height: 260rpx;
				border-radius: 50%;
				background-color: #fff;
				display: flex;
				justify-content: center;
				align-items: center;
			}
			
			.button-text {
				font-size: 36rpx;
				font-weight: bold;
				color: $uni-color-primary;
			}
			
			&.disabled .button-text {
				color: #999;
			}
			
			&:active {
				transform: scale(0.95);
			}
		}
		
		.action-desc {
			font-size: 28rpx;
			color: #666;
			text-align: center;
		}
	}
	
	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 20rpx;
		position: relative;
		padding-left: 20rpx;
		
		&::before {
			content: '';
			position: absolute;
			left: 0;
			top: 8rpx;
			width: 8rpx;
			height: 32rpx;
			background-color: $uni-color-primary;
			border-radius: 4rpx;
		}
	}
	
	.recent-records {
		.record-list {
			background-color: #fff;
			border-radius: 16rpx;
			padding: 20rpx;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
			
			.record-empty {
				padding: 40rpx 0;
				text-align: center;
				color: #999;
				font-size: 28rpx;
			}
			
			.record-item {
				display: flex;
				padding: 20rpx 0;
				border-bottom: 2rpx solid #f5f5f5;
				
				&:last-child {
					border-bottom: none;
				}
				
				.record-date {
					width: 180rpx;
					display: flex;
					flex-direction: column;
					
					.date {
						font-size: 28rpx;
						color: #333;
						margin-bottom: 6rpx;
					}
					
					.week {
						font-size: 24rpx;
						color: #999;
					}
				}
				
				.record-details {
					flex: 1;
					
					.detail-item {
						display: flex;
						margin-bottom: 10rpx;
						
						&:last-child {
							margin-bottom: 0;
						}
						
						.detail-label {
							width: 80rpx;
							font-size: 28rpx;
							color: #666;
						}
						
						.detail-value {
							flex: 1;
							font-size: 28rpx;
							color: $uni-color-success;
							
							&.abnormal {
								color: $uni-color-warning;
							}
							
							.status-tag {
								display: inline-block;
								font-size: 22rpx;
								padding: 2rpx 10rpx;
								border-radius: 4rpx;
								background-color: rgba(250, 173, 20, 0.1);
								color: $uni-color-warning;
								margin-left: 10rpx;
							}
						}
					}
				}
			}
		}
	}
	
	.face-verify-popup {
		width: 600rpx;
		background-color: #fff;
		border-radius: 20rpx;
		overflow: hidden;
		
		.popup-title {
			font-size: 32rpx;
			font-weight: bold;
			padding: 30rpx;
			text-align: center;
			border-bottom: 1rpx solid #eee;
		}
		
		.popup-content {
			padding: 30rpx;
		}
		
		.camera-container, .photo-preview {
			width: 400rpx;
			height: 400rpx;
			margin: 0 auto 30rpx;
			background-color: #f5f5f5;
			border-radius: 10rpx;
			overflow: hidden;
		}
		
		.popup-footer {
			display: flex;
			justify-content: space-between;
			padding-top: 20rpx;
			
			button {
				flex: 1;
				margin: 0 20rpx;
				font-size: 28rpx;
				border-radius: 40rpx;
				
				&.btn-cancel {
					background-color: #f5f5f5;
					color: #333;
				}
				
				&.btn-confirm {
					background-color: #007AFF;
					color: #fff;
				}
			}
		}
	}
	
	.supplement-popup {
		width: 600rpx;
		background-color: #fff;
		border-radius: 20rpx;
		overflow: hidden;
		
		.popup-title {
			font-size: 32rpx;
			font-weight: bold;
			padding: 30rpx;
			text-align: center;
			border-bottom: 1rpx solid #eee;
		}
		
		.popup-content {
			padding: 30rpx;
		}
		
		.form-item {
			margin-bottom: 20rpx;
			
			.form-label {
				font-size: 28rpx;
				color: #333;
				margin-bottom: 10rpx;
			}
			
			.form-input {
				background-color: #f5f5f5;
				border-radius: 10rpx;
				padding: 20rpx;
				font-size: 28rpx;
				
				textarea {
					width: 100%;
					height: 150rpx;
				}
			}
		}
		
		.popup-footer {
			display: flex;
			justify-content: space-between;
			padding-top: 20rpx;
			
			button {
				flex: 1;
				margin: 0 20rpx;
				font-size: 28rpx;
				border-radius: 40rpx;
				
				&.btn-cancel {
					background-color: #f5f5f5;
					color: #333;
				}
				
				&.btn-confirm {
					background-color: #007AFF;
					color: #fff;
				}
			}
		}
	}
	
	.supplement-images {
		display: flex;
		flex-wrap: wrap;
		margin-top: 10rpx;
		
		.image-item {
			position: relative;
			width: 150rpx;
			height: 150rpx;
			margin-right: 20rpx;
			margin-bottom: 20rpx;
			border-radius: 10rpx;
			overflow: hidden;
			
			.supplement-image {
				width: 100%;
				height: 100%;
			}
			
			.image-delete {
				position: absolute;
				top: 0;
				right: 0;
				width: 40rpx;
				height: 40rpx;
				background-color: rgba(0, 0, 0, 0.5);
				color: #fff;
				font-size: 32rpx;
				display: flex;
				justify-content: center;
				align-items: center;
			}
		}
		
		.image-add {
			width: 150rpx;
			height: 150rpx;
			background-color: #f5f5f5;
			border: 1rpx dashed #ccc;
			border-radius: 10rpx;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			
			.image-add-icon {
				font-size: 48rpx;
				color: #999;
				line-height: 1;
				margin-bottom: 10rpx;
			}
			
			.image-add-text {
				font-size: 24rpx;
				color: #999;
			}
		}
	}
	
	.clock-btn {
		margin-top: 40rpx;
		width: 90%;
		height: 90rpx;
		background: linear-gradient(135deg, #4B79A1, #283E51);
		color: white;
		font-size: 32rpx;
		border-radius: 45rpx;
		box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.1);
		display: flex;
		justify-content: center;
		align-items: center;
		
		&.disabled {
			background: linear-gradient(135deg, #ccc, #999);
			box-shadow: none;
		}
	}
	
	.date-picker {
		display: flex;
		align-items: center;
		justify-content: space-between;
		
		.icon-calendar {
			font-size: 32rpx;
			color: #999;
		}
	}
</style> 