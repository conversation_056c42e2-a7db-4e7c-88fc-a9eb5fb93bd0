package com.heating.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.heating.entity.patrol.TPatrolPlan;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface PatrolPlanRepository extends JpaRepository<TPatrolPlan, Long> {

    TPatrolPlan findTopByOrderByCreateTimeDesc();
    
    /**
     * 根据状态列表查询巡检计划
     * @param statuses 状态列表
     * @return 符合条件的巡检计划列表
     */
    List<TPatrolPlan> findByStatusIn(List<String> statuses);

//    @Query(value = "SELECT * FROM t_patrol_plan WHERE " +
//            "(:name IS NULL OR name LIKE CONCAT('%', :name, '%')) AND " +
//            "(:status IS NULL OR status = :status) AND " +
//            "(:patrolType IS NULL OR patrol_type = :patrolType) AND " +
//            "(:locations IS NULL OR locations LIKE CONCAT('%', :locations, '%')) " +
//            "ORDER BY create_time DESC " +
//            "LIMIT :start, :size",
//            nativeQuery = true)
//    List<TPatrolPlan> findPatrolPlans(
//            @Param("name") String name,
//            @Param("status") String status,
//            @Param("patrolType") String patrolType,
//            @Param("locations") String locations,
//            @Param("start") int start,
//            @Param("size") int size);

    @Query(value = "SELECT * FROM t_patrol_plan WHERE " +
            "(:name IS NULL OR name LIKE CONCAT('%', :name, '%')) AND " +
            "(:status IS NULL OR status = :status) AND " +
            "(:patrolType IS NULL OR patrol_type = :patrolType) AND " +
            "(:locations IS NULL OR locations LIKE CONCAT('%', :locations, '%')) AND " +
            "(:dateStart IS NULL OR (start_date <= :dateEnd AND (end_date IS NULL OR end_date >= :dateStart))) " +
            "ORDER BY create_time DESC " +
            "LIMIT :start, :size",
            nativeQuery = true)
    List<TPatrolPlan> findPatrolPlans(
            @Param("name") String name,
            @Param("status") String status,
            @Param("patrolType") String patrolType,
            @Param("locations") String locations,
            @Param("dateStart") LocalDate dateStart,
            @Param("dateEnd") LocalDate dateEnd,
            @Param("start") int start,
            @Param("size") int size);

    @Query(value = "SELECT COUNT(*) FROM t_patrol_plan WHERE " +
            "(:name IS NULL OR name LIKE CONCAT('%', :name, '%')) AND " +
            "(:status IS NULL OR status = :status) AND " +
            "(:patrolType IS NULL OR patrol_type = :patrolType) AND " +
            "(:locations IS NULL OR locations LIKE CONCAT('%', :locations, '%')) AND " +
            "(:dateStart IS NULL OR (start_date <= :dateEnd AND (end_date IS NULL OR end_date >= :dateStart)))",
            nativeQuery = true)
    long countPatrolPlans(
            @Param("name") String name,
            @Param("status") String status,
            @Param("patrolType") String patrolType,
            @Param("locations") String locations,
            @Param("dateStart") LocalDate dateStart,
            @Param("dateEnd") LocalDate dateEnd);

//    @Query(value = "SELECT COUNT(*) FROM t_patrol_plan WHERE " +
//            "(:name IS NULL OR name LIKE CONCAT('%', :name, '%')) AND " +
//            "(:status IS NULL OR status = :status) AND " +
//            "(:patrolType IS NULL OR patrol_type = :patrolType) AND " +
//            "(:locations IS NULL OR locations LIKE CONCAT('%', :locations, '%'))",
//            nativeQuery = true)
//    long countPatrolPlans(
//            @Param("name") String name,
//            @Param("status") String status,
//            @Param("patrolType") String patrolType,
//            @Param("locations") String locations);
} 