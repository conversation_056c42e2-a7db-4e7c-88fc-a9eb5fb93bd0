package com.heating.entity.permission;

import lombok.Data;

import jakarta.persistence.*;
import java.time.LocalDateTime;

/**
 * 角色权限关联实体类
 */
@Entity
@Table(name = "t_sys_role_permission")
@Data
public class TSysRolePermission {
    
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 角色编码
     */
    @Column(name = "role_code")
    private String roleCode;
    
    /**
     * 权限编码
     */
    @Column(name = "permission_code")
    private String permissionCode;
    
    /**
     * 状态:0-禁用,1-启用
     */
    @Column(name = "status")
    private Boolean status;
    
    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;
} 