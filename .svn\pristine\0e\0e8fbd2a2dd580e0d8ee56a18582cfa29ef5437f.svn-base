<<<<<<< .mine
package com.heating.service.impl;

import com.heating.service.AlarmService;
import com.heating.dto.alarm.AlarmMessageResponse;
import com.heating.dto.alarm.AlarmRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heating.entity.TManagerHeatUnit;
import com.heating.repository.ManagerHeatUnitRepository;
import com.heating.entity.TAlarmRecord;
import com.heating.repository.AlarmRecordRepository;
import com.heating.entity.THeatUnit;
import com.heating.repository.HeatUnitRepository;

import java.time.LocalDateTime;

/**
 * 告警服务实现类
 */
@Service
public class AlarmServiceImpl implements AlarmService {

    private static final Logger logger = LoggerFactory.getLogger(AlarmServiceImpl.class);
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    private ManagerHeatUnitRepository managerHeatUnitRepository;

    // 注入告警记录仓库
    @Autowired
    private AlarmRecordRepository alarmRecordRepository;

    // 注入热用户仓库
    @Autowired
    private HeatUnitRepository heatUnitRepository;
 

    /**
     * 获取API数据的通用方法
     * 
     * @param apiUrl API接口地址
     * // get post 请求类型
     * 
     * @param request 请求参数对象 (GET方法下未使用)
     * @return API返回的数据列表
     */
    public List<Map<String, Object>> getApi(String apiUrl, Object request, String method) {
        try {
            HttpClient client = HttpClient.newHttpClient(); 
            HttpRequest httpRequest = null;

            if (method.equals("GET")) {
                // 创建HTTP请求 - 使用GET方法
                httpRequest = HttpRequest.newBuilder()
                    .uri(URI.create(apiUrl))
                    .header("Content-Type", "application/json")
                    .GET()
                    .build();
            } else if (method.equals("POST")) {
                    // 创建HTTP请求 - 使用POST方法
                httpRequest = HttpRequest.newBuilder()
                    .uri(URI.create(apiUrl))
                    .header("Content-Type", "application/json")
                    .POST(HttpRequest.BodyPublishers.ofString(objectMapper.writeValueAsString(request)))
                    .build();
            }

            logger.debug("正在请求API: {}", apiUrl);
            // 发送请求并获取响应
            HttpResponse<String> response = client.send(httpRequest, 
                    HttpResponse.BodyHandlers.ofString());
            
            // 记录响应状态码和内容，便于调试
            logger.debug("API响应状态码: {}", response.statusCode());
            
            // 解析响应数据 
            try {
                // 解析整个响应为Map
                Map<String, Object> responseMap = objectMapper.readValue(response.body(), 
                        new TypeReference<Map<String, Object>>() {});
                
                // 返回结果列表
                List<Map<String, Object>> resultList = new ArrayList<>();
                resultList.add(responseMap);
                return resultList;
            } catch (Exception e) {
                logger.error("解析响应数据出错: {}", e.getMessage());
                logger.debug("响应内容: {}", response.body());
                throw new RuntimeException("解析响应数据出错: " + e.getMessage(), e);
            } 
        } catch (Exception e) {
            logger.error("调用API出错: {}", e.getMessage());
            throw new RuntimeException("调用API出错: " + e.getMessage(), e);
        }
    }


    private List<Long> getManagerIds(Object heatUnitId) { 
        // 从管理人员与热用户关联表中获取管理员ID列表  
        List<TManagerHeatUnit> managerHeatUnit = managerHeatUnitRepository.findByHeatUnitId(Long.parseLong(heatUnitId.toString()));
        List<Long> managerIds = new ArrayList<>();
        for (TManagerHeatUnit item : managerHeatUnit) {
            managerIds.add(item.getManagerId());
        } 
        // 返回管理员ID列表
        return managerIds;
    }

    /**
     * 获取告警消息
     * 从 '告警记录表' 中获取 is_alarm=1 status=0 的数据
     * 
     * @param userId 用户ID
     * @param role 用户角色
     * @param heatUnitId 用户项目权限，逗号分隔的项目ID字符串
     * @return 告警消息列表
     */
    @Override
    public List<AlarmMessageResponse> getAlarmMessages(Long userId, String role, String heatUnitId) {
        logger.info("开始获取告警消息列表: userId={}, role={}, heatUnitId={}", userId, role, heatUnitId);
        
        try {
            // 从数据库中获取需要提醒的告警信息 (is_alarm=1 且 status=0 的记录)
            List<TAlarmRecord> alarmRecords = alarmRecordRepository.findByIsAlarmAndStatus(1, 0);
            
            // 构建响应对象列表
            List<AlarmMessageResponse> responseList = new ArrayList<>();
            
            // 检查用户是否有全局权限
            boolean hasAllPermission = false;
            if (heatUnitId != null && !heatUnitId.isEmpty()) {
                hasAllPermission = heatUnitId.equals("0") || 
                                   heatUnitId.split(",").length > 0 && 
                                   java.util.Arrays.asList(heatUnitId.split(",")).contains("0");
            }
            
            // 将逗号分隔的热用户ID转换为列表
            List<Long> userHeatUnitIds = new ArrayList<>();
            if (heatUnitId != null && !heatUnitId.isEmpty() && !hasAllPermission) {
                for (String id : heatUnitId.split(",")) {
                    try {
                        userHeatUnitIds.add(Long.parseLong(id.trim()));
                    } catch (NumberFormatException e) {
                        logger.warn("无效的热用户ID格式: {}", id);
                    }
                }
            }
            
            // 遍历告警记录并转换为响应对象
            for (TAlarmRecord alarm : alarmRecords) {
                // 根据用户权限过滤告警
                if (!hasAllPermission && !userHeatUnitIds.isEmpty()) {
                    // 如果用户没有全局权限，且告警的热用户ID不在用户权限列表中，则跳过此告警
                    if (alarm.getHeatUnitId() == null || !userHeatUnitIds.contains(alarm.getHeatUnitId())) {
                        logger.debug("过滤掉无权限查看的告警: alarmId={}, heatUnitId={}", alarm.getId(), alarm.getHeatUnitId());
                        continue;
                    }
                }
                
                // 根据热用户ID获取热用户名称
                String heatUnitName = "";
                if (alarm.getHeatUnitId() != null) {
                    // 查询热用户信息
                    THeatUnit heatUnit = heatUnitRepository.findById(alarm.getHeatUnitId())
                            .orElse(null);
                    if (heatUnit != null) {
                        heatUnitName = heatUnit.getName();
                    }
                }
                
                // 获取项目管理员ID列表
                List<Long> managerIds = getManagerIds(alarm.getHeatUnitId());
                
                // 构建告警消息响应对象
                AlarmMessageResponse response = AlarmMessageResponse.builder()
                        .id(alarm.getId())
                        .heatUnitId(alarm.getHeatUnitId())
                        .heatUnitName(heatUnitName)
                        .managerIds(managerIds)
                        .alarmDesc(alarm.getAlarmDesc())
                        .alarmDt(alarm.getAlarmDt() != null ? alarm.getAlarmDt().toString() : "")
                        .build();
                
                responseList.add(response);
            }
            
            logger.info("获取告警消息列表成功，共{}条", responseList.size());
            return responseList;
            
        } catch (Exception e) {
            logger.error("获取告警消息列表失败: {}", e.getMessage(), e);
            // 发生异常时返回空列表
            return new ArrayList<>();
        }
    }

    /**
     * 更新告警状态
     * 根据告警ID更新告警记录的状态
     * 
     * @param alarmId 告警记录ID
     * @param status 新的告警状态 (1-已确认, 2-已完成, 3-已忽略)
     */
    @Override
    public void updateAlarmStatus(Long alarmId, Integer status) throws Exception {
        logger.info("开始更新告警状态: alarmId={}, status={}", alarmId, status);
        
        // 参数校验
        if (alarmId == null || status == null) {
            throw new IllegalArgumentException("告警ID和状态不能为空");
        }
        
        // 检查状态值是否合法
        if (status < 1 || status > 3) {
            throw new IllegalArgumentException("无效的状态值: " + status + "，有效值为: 1-已确认, 2-已完成, 3-已忽略");
        }
        
        try {
            // 查询告警记录
            TAlarmRecord alarmRecord = alarmRecordRepository.findById(alarmId)
                    .orElseThrow(() -> new Exception("告警记录不存在: " + alarmId));
            
            // 更新告警状态
            alarmRecord.setStatus(status);
            
            // 如果状态为已完成或已忽略，则同时更新告警清除时间
            if (status == 2 || status == 3) {
                alarmRecord.setAlarmClearDt(LocalDateTime.now());
                // 计算告警持续时间（分钟）
                if (alarmRecord.getAlarmDt() != null) {
                    long durationMinutes = java.time.Duration.between(
                            alarmRecord.getAlarmDt(), 
                            LocalDateTime.now()
                    ).toMinutes();
                    alarmRecord.setAlarmDuration((int) durationMinutes);
                }
            }
            
            // 保存更新后的告警记录
            alarmRecordRepository.save(alarmRecord);
            
            logger.info("更新告警状态成功: alarmId={}, status={}", alarmId, status);
            
        } catch (Exception e) {
            logger.error("更新告警状态失败: {}", e.getMessage(), e);
            throw new Exception("更新告警状态失败: " + e.getMessage(), e);
        }
    }   
} 

||||||| .r0
=======
package com.heating.service.impl;

import com.heating.service.AlarmService;
import com.heating.dto.alarm.AlarmMessageResponse;
import com.heating.dto.alarm.AlarmRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heating.entity.TManagerHeatUnit;
import com.heating.repository.ManagerHeatUnitRepository;
import com.heating.entity.TAlarmRecord;
import com.heating.repository.AlarmRecordRepository;
import com.heating.entity.THeatUnit;
import com.heating.repository.HeatUnitRepository;

import java.time.LocalDateTime;

/**
 * 告警服务实现类
 */
@Service
public class AlarmServiceImpl implements AlarmService {

    private static final Logger logger = LoggerFactory.getLogger(AlarmServiceImpl.class);
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    private ManagerHeatUnitRepository managerHeatUnitRepository;

    // 注入告警记录仓库
    @Autowired
    private AlarmRecordRepository alarmRecordRepository;

    // 注入热用户仓库
    @Autowired
    private HeatUnitRepository heatUnitRepository;
 

    /**
     * 获取API数据的通用方法
     * 
     * @param apiUrl API接口地址
     * // get post 请求类型
     * 
     * @param request 请求参数对象 (GET方法下未使用)
     * @return API返回的数据列表
     */
    public List<Map<String, Object>> getApi(String apiUrl, Object request, String method) {
        try {
            HttpClient client = HttpClient.newHttpClient(); 
            HttpRequest httpRequest = null;

            if (method.equals("GET")) {
                // 创建HTTP请求 - 使用GET方法
                httpRequest = HttpRequest.newBuilder()
                    .uri(URI.create(apiUrl))
                    .header("Content-Type", "application/json")
                    .GET()
                    .build();
            } else if (method.equals("POST")) {
                    // 创建HTTP请求 - 使用POST方法
                httpRequest = HttpRequest.newBuilder()
                    .uri(URI.create(apiUrl))
                    .header("Content-Type", "application/json")
                    .POST(HttpRequest.BodyPublishers.ofString(objectMapper.writeValueAsString(request)))
                    .build();
            }

            logger.debug("正在请求API: {}", apiUrl);
            // 发送请求并获取响应
            HttpResponse<String> response = client.send(httpRequest, 
                    HttpResponse.BodyHandlers.ofString());
            
            // 记录响应状态码和内容，便于调试
            logger.debug("API响应状态码: {}", response.statusCode());
            
            // 解析响应数据 
            try {
                // 解析整个响应为Map
                Map<String, Object> responseMap = objectMapper.readValue(response.body(), 
                        new TypeReference<Map<String, Object>>() {});
                
                // 返回结果列表
                List<Map<String, Object>> resultList = new ArrayList<>();
                resultList.add(responseMap);
                return resultList;
            } catch (Exception e) {
                logger.error("解析响应数据出错: {}", e.getMessage());
                logger.debug("响应内容: {}", response.body());
                throw new RuntimeException("解析响应数据出错: " + e.getMessage(), e);
            } 
        } catch (Exception e) {
            logger.error("调用API出错: {}", e.getMessage());
            throw new RuntimeException("调用API出错: " + e.getMessage(), e);
        }
    }


    private List<Long> getManagerIds(Object heatUnitId) { 
        // 从管理人员与热用户关联表中获取管理员ID列表  
        List<TManagerHeatUnit> managerHeatUnit = managerHeatUnitRepository.findByHeatUnitId(Long.parseLong(heatUnitId.toString()));
        List<Long> managerIds = new ArrayList<>();
        for (TManagerHeatUnit item : managerHeatUnit) {
            managerIds.add(item.getManagerId());
        } 
        // 返回管理员ID列表
        return managerIds;
    }

    /**
     * 获取告警消息
     * 从 '告警记录表' 中获取 is_alarm=1 status=0 的数据
     * 
     * @return 告警消息列表
     */
    @Override
    public List<AlarmMessageResponse> getAlarmMessages() {
        logger.info("开始获取告警消息列表");
        
        try {
            // 从数据库中获取需要提醒的告警信息 (is_alarm=1 且 status=0 的记录)
            List<TAlarmRecord> alarmRecords = alarmRecordRepository.findByIsAlarmAndStatus(1, 0);
            
            // 构建响应对象列表
            List<AlarmMessageResponse> responseList = new ArrayList<>();
            
            // 遍历告警记录并转换为响应对象
            for (TAlarmRecord alarm : alarmRecords) {
                // 根据热用户ID获取热用户名称
                String heatUnitName = "";
                if (alarm.getHeatUnitId() != null) {
                    // 查询热用户信息
                    THeatUnit heatUnit = heatUnitRepository.findById(alarm.getHeatUnitId())
                            .orElse(null);
                    if (heatUnit != null) {
                        heatUnitName = heatUnit.getName();
                    }
                }
                
                // 获取项目管理员ID列表
                List<Long> managerIds = getManagerIds(alarm.getHeatUnitId());
                
                // 构建告警消息响应对象
                AlarmMessageResponse response = AlarmMessageResponse.builder()
                        .id(alarm.getId())
                        .heatUnitId(alarm.getHeatUnitId())
                        .heatUnitName(heatUnitName)
                        .managerIds(managerIds)
                        .alarmDesc(alarm.getAlarmDesc())
                        .alarmDt(alarm.getAlarmDt() != null ? alarm.getAlarmDt().toString() : "")
                        .build();
                
                responseList.add(response);
            }
            
            logger.info("获取告警消息列表成功，共{}条", responseList.size());
            return responseList;
            
        } catch (Exception e) {
            logger.error("获取告警消息列表失败: {}", e.getMessage(), e);
            // 发生异常时返回空列表
            return new ArrayList<>();
        }
    }

    /**
     * 更新告警状态
     * 根据告警ID更新告警记录的状态
     * 
     * @param alarmId 告警记录ID
     * @param status 新的告警状态 (1-已确认, 2-已完成, 3-已忽略)
     */
    @Override
    public void updateAlarmStatus(Long alarmId, Integer status) throws Exception {
        logger.info("开始更新告警状态: alarmId={}, status={}", alarmId, status);
        
        // 参数校验
        if (alarmId == null || status == null) {
            throw new IllegalArgumentException("告警ID和状态不能为空");
        }
        
        // 检查状态值是否合法
        if (status < 1 || status > 3) {
            throw new IllegalArgumentException("无效的状态值: " + status + "，有效值为: 1-已确认, 2-已完成, 3-已忽略");
        }
        
        try {
            // 查询告警记录
            TAlarmRecord alarmRecord = alarmRecordRepository.findById(alarmId)
                    .orElseThrow(() -> new Exception("告警记录不存在: " + alarmId));
            
            // 更新告警状态
            alarmRecord.setStatus(status);
            
            // 如果状态为已完成或已忽略，则同时更新告警清除时间
            if (status == 2 || status == 3) {
                alarmRecord.setAlarmClearDt(LocalDateTime.now());
                // 计算告警持续时间（分钟）
                if (alarmRecord.getAlarmDt() != null) {
                    long durationMinutes = java.time.Duration.between(
                            alarmRecord.getAlarmDt(), 
                            LocalDateTime.now()
                    ).toMinutes();
                    alarmRecord.setAlarmDuration((int) durationMinutes);
                }
            }
            
            // 保存更新后的告警记录
            alarmRecordRepository.save(alarmRecord);
            
            logger.info("更新告警状态成功: alarmId={}, status={}", alarmId, status);
            
        } catch (Exception e) {
            logger.error("更新告警状态失败: {}", e.getMessage(), e);
            throw new Exception("更新告警状态失败: " + e.getMessage(), e);
        }
    }   
} 

>>>>>>> .r5108
