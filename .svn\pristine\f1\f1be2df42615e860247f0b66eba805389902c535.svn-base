<template>
	<view class="rules-container">
	<!-- 	<view class="page-header">
			<view class="page-title">考勤规则设置</view>
		</view> -->
		
		<view class="rules-form">
			<view class="form-item">
				<view class="form-label">上班时间</view>
				<view class="form-input">
					<picker mode="time" :value="clockInTime" @change="clockInTime = $event.detail.value">
						<view class="picker-value">{{ clockInTime }}</view>
					</picker>
				</view>
			</view>
			
			<view class="form-item">
				<view class="form-label">下班时间</view>
				<view class="form-input">
					<picker mode="time" :value="clockOutTime" @change="clockOutTime = $event.detail.value">
						<view class="picker-value">{{ clockOutTime }}</view>
					</picker>
				</view>
			</view>
			<view class="form-actions">
				<button class="btn-save" @click="saveRules" :disabled="loading">保存设置</button>
			</view>
		</view>
	</view>
</template>

<script>
	import { attendanceApi } from '@/utils/api.js';
	
	export default {
		data() {
			return {
				clockInTime: '09:00',
				clockOutTime: '18:00',
				allowedDistance: 500,
				lateThreshold: 15,
				earlyLeaveThreshold: 15,
				loading: false
			}
		},
		onLoad() {
			// 获取当前规则设置
			this.getRules();
		},
		methods: {
			// 获取规则
			getRules() {
				this.loading = true;
				attendanceApi.getClockRules()
					.then(res => {
						if (res.code === 200 && res.data) {
							this.clockInTime = res.data.clockInTime || '09:00';
							this.clockOutTime = res.data.clockOutTime || '18:00';
							this.allowedDistance = res.data.allowedDistance || 500;
							this.lateThreshold = res.data.lateThreshold || 15;
							this.earlyLeaveThreshold = res.data.earlyLeaveThreshold || 15;
						}
					})
					.catch(err => {
						console.error('获取考勤规则失败:', err);
						uni.showToast({
							title: '获取规则失败',
							icon: 'none'
						});
					})
					.finally(() => {
						this.loading = false;
					});
			},
			
			// 保存规则
			saveRules() {
				// 表单验证
				if (!this.clockInTime) {
					uni.showToast({
						title: '请设置上班时间',
						icon: 'none'
					});
					return;
				}
				
				if (!this.clockOutTime) {
					uni.showToast({
						title: '请设置下班时间',
						icon: 'none'
					});
					return;
				}
				
				this.loading = true;
				
				// 调用API保存规则
				attendanceApi.updateClockRules(this.clockInTime, this.clockOutTime)
					.then(res => {
						if (res.code === 200) {
							uni.showToast({
								title: '保存成功',
								icon: 'success'
							});
							setTimeout(() => {
								uni.navigateBack();
							}, 1500);
						} else {
							uni.showToast({
								title: res.message || '保存失败',
								icon: 'none'
							});
						}
					})
					.catch(err => {
						console.error('保存考勤规则失败:', err);
						uni.showToast({
							title: '保存失败',
							icon: 'none'
						});
					})
					.finally(() => {
						this.loading = false;
					});
			}
		}
	}
</script>

<style lang="scss">
	.rules-container {
		padding: 30rpx;
		background-color: #f5f7fa;
		min-height: 100vh;
	}
	
	.page-header {
		margin-bottom: 40rpx;
		
		.page-title {
			font-size: 36rpx;
			font-weight: bold;
			color: #333;
		}
	}
	
	.rules-form {
		background-color: #fff;
		border-radius: 16rpx;
		padding: 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
		
		.form-item {
			margin-bottom: 30rpx;
			
			.form-label {
				font-size: 30rpx;
				color: #333;
				margin-bottom: 16rpx;
			}
			
			.form-input {
				background-color: #f5f7fa;
				border-radius: 8rpx;
				padding: 20rpx;
				
				input {
					width: 100%;
					height: 60rpx;
					font-size: 28rpx;
				}
				
				.picker-value {
					height: 60rpx;
					line-height: 60rpx;
					font-size: 28rpx;
				}
			}
			
			.form-desc {
				font-size: 24rpx;
				color: #999;
				margin-top: 10rpx;
			}
		}
		
		.form-actions {
			margin-top: 50rpx;
			
			.btn-save {
				width: 100%;
				height: 90rpx;
				background: linear-gradient(135deg, #4483e5, #6a9eef);
				color: #fff;
				font-size: 32rpx;
				border-radius: 45rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				
				&:disabled {
					background: linear-gradient(135deg, #ccc, #999);
					opacity: 0.7;
				}
			}
		}
	}
</style> 