package com.emm.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.List;

public class FaultReportRequest {
    @JsonProperty("station_id")
    private String stationId;

    @JsonProperty("fault_type")
    private String faultType;

    @JsonProperty("fault_level")
    private String faultLevel;

    @JsonProperty("fault_desc")
    private String faultDesc;

    @JsonProperty("occur_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime occurTime;

    @JsonProperty("report_user_id")
    private Long reportUserId;

    @JsonProperty("images")
    private List<String> images;

    @JsonProperty("video_url")
    private String videoUrl;

    // Getters and Setters
    public String getStationId() {
        return stationId;
    }

    public void setStationId(String stationId) {
        this.stationId = stationId;
    }

    public String getFaultType() {
        return faultType;
    }

    public void setFaultType(String faultType) {
        this.faultType = faultType;
    }

    public String getFaultLevel() {
        return faultLevel;
    }

    public void setFaultLevel(String faultLevel) {
        this.faultLevel = faultLevel;
    }

    public String getFaultDesc() {
        return faultDesc;
    }

    public void setFaultDesc(String faultDesc) {
        this.faultDesc = faultDesc;
    }

    public LocalDateTime getOccurTime() {
        return occurTime;
    }

    public void setOccurTime(LocalDateTime occurTime) {
        this.occurTime = occurTime;
    }

    public Long getReportUserId() {
        return reportUserId;
    }

    public void setReportUserId(Long reportUserId) {
        this.reportUserId = reportUserId;
    }

    public List<String> getImages() {
        return images;
    }

    public void setImages(List<String> images) {
        this.images = images;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    // 添加数据验证方法
    public void validate() {
        if (stationId == null) {
            throw new IllegalArgumentException("站点ID不能为空");
        }
        if (faultType == null || faultType.trim().isEmpty()) {
            throw new IllegalArgumentException("故障类型不能为空");
        }
        if (faultDesc == null || faultDesc.trim().isEmpty()) {
            throw new IllegalArgumentException("故障描述不能为空");
        }
        if (occurTime == null) {
            throw new IllegalArgumentException("故障发生时间不能为空");
        }
    }
}