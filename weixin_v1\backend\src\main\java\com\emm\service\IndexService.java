package com.emm.service;


import com.emm.model.RoomTemperature;
import com.emm.repository.FaultRepository;
import com.emm.repository.RoomTemperatureRepository;
import com.emm.repository.UseHeatUnitRepository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.HashMap;
import java.util.ArrayList;

@Service
public class IndexService {
    
    @Autowired
    private RoomTemperatureRepository roomTemperatureRepository;

    @Autowired
    private UseHeatUnitRepository useHeatUnitRepository;

    @Autowired
    private FaultRepository faultRepository;

    public Map<String, Object> getStats() {
        Map<String, Object> stats = faultRepository.getFaultStatistics();
        Double avgTemp = roomTemperatureRepository.findAverageTemperatureToday();

        Map<String, Object> result = new HashMap<>();
        result.put("faultCount", stats.get("total"));
        result.put("processingCount", stats.get("processing"));
        result.put("completedCount", stats.get("returned"));
        result.put("avgTemp", avgTemp != null ? avgTemp : 0.0);
        result.put("currentDate", LocalDateTime.now().toLocalDate().toString());

        return result;
    }

    public List<Map<String, Object>> getRecentFaults() {
        return faultRepository.findRecentFaults();
    }

    public Map<String, Object> getStationTemps() {
        List<Map<String, Object>> stations = roomTemperatureRepository.findCommunityTemperatures()
                .stream()
                .limit(4)
                .collect(Collectors.toList());
        
        stations.forEach(station -> {
            double temp = (Double) station.get("temperature");
            station.put("status", temp < 18 ? "low" : temp > 24 ? "high" : "normal");
        });

        String updateTime = stations.stream()
                .map(station -> (LocalDateTime) station.get("latest_time"))
                .max(LocalDateTime::compareTo)
                .map(time -> time.toLocalTime().toString())
                .orElse(LocalDateTime.now().toLocalTime().toString());

        Map<String, Object> result = new HashMap<>();
        result.put("stations", stations);
        result.put("updateTime", updateTime);

        return result;
    }

    public Map<String, List<Map<String, Object>>> getCommunityTemps() {
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
        List<RoomTemperature> temps = roomTemperatureRepository.findRecentTemperatures(oneHourAgo);

        return temps.stream()
                .collect(Collectors.groupingBy(
                    RoomTemperature::getCommunityName,
                    Collectors.mapping(
                        temp -> {
                            Map<String, Object> tempData = new HashMap<>();
                            tempData.put("building", temp.getBuildingNo());
                            tempData.put("unit", temp.getUnitNo());
                            tempData.put("room", temp.getRoomNo());
                            tempData.put("temperature", temp.getIndoorTemp());
                            tempData.put("reportTime", temp.getReportTime().toLocalTime().toString());
                            return tempData;
                        },
                        Collectors.toList()
                    )
                ));
    }

    public List<Map<String, Object>> getCommunities() {
        return useHeatUnitRepository.findAllCommunities();
    }

    public List<Map<String, Object>> getRecentTemperatures(LocalDateTime startTime) {
        List<RoomTemperature> temperatures = roomTemperatureRepository.findRecentTemperatures(startTime);
        List<Map<String, Object>> result = new ArrayList<>();
        
        for (RoomTemperature temp : temperatures) {
            Map<String, Object> tempMap = new HashMap<>();
            tempMap.put("communityName", temp.getCommunityName());
            tempMap.put("buildingNo", temp.getBuildingNo());
            tempMap.put("unitNo", temp.getUnitNo());
            tempMap.put("roomNo", temp.getRoomNo());
            tempMap.put("indoorTemp", temp.getIndoorTemp());
            tempMap.put("outdoorTemp", temp.getOutdoorTemp());
            tempMap.put("latitude", temp.getLatitude());
            tempMap.put("longitude", temp.getLongitude());
            tempMap.put("reportTime", temp.getReportTime());
            tempMap.put("imageUrl", temp.getImageUrl());
            tempMap.put("videoUrl", temp.getVideoUrl());
            
            result.add(tempMap);
        }
        
        return result;
    }

    public List<RoomTemperature> getTopNRecentTemperatures(LocalDateTime startTime, int limit) {
        Pageable pageRequest = PageRequest.of(0, limit);
        return roomTemperatureRepository.findTopNRecentTemperatures(startTime, pageRequest);
    }
}