# Server configuration
server.port=5000

# Database configuration
spring.datasource.url=**************************************
spring.datasource.username=xeh
spring.datasource.password=111510
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA configuration
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=true

# JWT configuration
jwt.secret=your_jwt_secret
jwt.expiration=3600000

# Logging configuration
logging.level.root=INFO
logging.level.com.emm=DEBUG
#logging.level.org.hibernate.SQL=DEBUG
#logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE