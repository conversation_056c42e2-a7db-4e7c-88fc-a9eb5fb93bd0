package com.heating.repository;

import com.heating.entity.THeatUnit;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.heating.dto.heatUnit.HeatUnitDTO;

import java.util.List;

/**
 * 热用户仓库接口
 * 用于操作 t_heat_unit 表
 */
@Repository
public interface HeatUnitRepository extends JpaRepository<THeatUnit, Long> {
    
    /**
     * 根据热用户名称查询热用户
     * 
     * @param name 热用户名称
     * @return 热用户实体
     */
    THeatUnit findByName(String name);
    
    /**
     * 查询所有热用户信息，转换为DTO
     * 
     * @return 热用户DTO列表
     */
    @Query("SELECT new com.heating.dto.heatUnit.HeatUnitDTO(u.id, u.name, CAST(u.longitude as double), CAST(u.latitude as double)) FROM THeatUnit u ORDER BY u.id")
    List<HeatUnitDTO> findHeatUnits();
}