<template>
	<view class="change-password-container">
	<!-- 	<view class="page-header">
			<text class="page-title">密码修改</text>
		</view> -->
		
		<view class="form-card">
			<view class="form-item">
				<text class="form-label">当前密码</text>
				<view class="form-input">
					<input type="password" v-model="password.current" placeholder="请输入当前密码" password />
				</view>
			</view>
			
			<view class="form-item">
				<text class="form-label">新密码</text>
				<view class="form-input">
					<input type="password" v-model="password.new" placeholder="请输入新密码" password />
				</view>
				<view class="password-rules">
					<text class="rule-item" :class="{ 'rule-passed': checkLength }">至少8个字符</text>
					<text class="rule-item" :class="{ 'rule-passed': checkNumber }">包含数字</text>
					<text class="rule-item" :class="{ 'rule-passed': checkLetter }">包含字母</text>
					<text class="rule-item" :class="{ 'rule-passed': checkSpecial }">包含特殊字符</text>
				</view>
			</view>
			
			<view class="form-item">
				<text class="form-label">确认新密码</text>
				<view class="form-input">
					<input type="password" v-model="password.confirm" placeholder="请再次输入新密码" password />
				</view>
				<view class="confirm-tip" v-if="password.new && password.confirm && password.new !== password.confirm">
					<text class="tip-error">两次输入的密码不一致</text>
				</view>
			</view>
		</view>
		
		<view class="submit-btn" :class="{ 'btn-disabled': !isFormValid }" @click="changePassword">修改密码</view>
		
		<view class="help-tip">
			<text class="tip-title">注意事项：</text>
			<text class="tip-content">1. 为保证账号安全，建议使用强密码</text>
			<text class="tip-content">2. 密码修改成功后，需要重新登录</text>
			<text class="tip-content">3. 如忘记当前密码，请联系管理员重置</text>
		</view>
	</view>
</template>

<script>
import { userApi } from '../../utils/api';

export default {
	data() {
		return {
			password: {
				current: '',
				new: '',
				confirm: ''
			},
			isSubmitting: false
		}
	},
	computed: {
		// 检查密码长度
		checkLength() {
			return this.password.new.length >= 8;
		},
		
		// 检查是否包含数字
		checkNumber() {
			return /\d/.test(this.password.new);
		},
		
		// 检查是否包含字母
		checkLetter() {
			return /[a-zA-Z]/.test(this.password.new);
		},
		
		// 检查是否包含特殊字符
		checkSpecial() {
			return /[^a-zA-Z0-9]/.test(this.password.new);
		},
		
		// 表单是否有效
		isFormValid() {
			return this.password.current && 
				this.password.new && 
				this.password.confirm && 
				this.password.new === this.password.confirm &&
				this.checkLength &&
				this.checkNumber &&
				this.checkLetter;
		}
	},
	methods: {
		// 修改密码
		changePassword() {
			if (!this.isFormValid) {
				return;
			}
			
			// 防止重复提交
			if (this.isSubmitting) {
				return;
			}
			
			this.isSubmitting = true;
			uni.showLoading({
				title: '处理中...'
			});
			
			// 准备请求数据
			const passwordData = {
				oldPassword: this.password.current,
				newPassword: this.password.new
			};
			
			// 调用API进行密码修改
			userApi.changePassword(passwordData)
				.then(res => {
					uni.hideLoading();
					this.isSubmitting = false;
					
					if (res.code === 200) {
						// 密码修改成功
						uni.showModal({
							title: '修改成功',
							content: '密码修改成功，请使用新密码重新登录',
							showCancel: false,
							success: () => {
								// 清除登录状态，跳转到登录页
								uni.removeStorageSync('token');
								uni.removeStorageSync('userInfo');
								uni.removeStorageSync('userId');
								uni.removeStorageSync('userRole');
								uni.removeStorageSync('userPermissions');
								uni.reLaunch({
									url: '/pages/user/login'
								});
							}
						});
					} else {
						// 密码修改失败
						this.showError(res.message || '密码修改失败');
					}
				})
				.catch(err => {
					uni.hideLoading();
					this.isSubmitting = false;
					console.error('密码修改请求失败:', err);
					this.showError('网络错误，请稍后重试');
				});
		},
		
		// 显示错误提示
		showError(message) {
			uni.showToast({
				title: message,
				icon: 'none'
			});
		}
	}
}
</script>

<style lang="scss">
.change-password-container {
	background-color: #f5f7fa;
	min-height: 100vh;
	padding-bottom: 40rpx;
}

.page-header {
	background-color: #fff;
	padding: 30rpx;
	border-bottom: 1rpx solid #eee;
	
	.page-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}
}

.form-card {
	margin: 30rpx;
	background-color: #fff;
	border-radius: 12rpx;
	padding: 20rpx;
	box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.05);
	
	.form-item {
		padding: 24rpx 20rpx;
		border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
		
		&:last-child {
			border-bottom: none;
		}
		
		.form-label {
			font-size: 28rpx;
			color: #666;
			margin-bottom: 16rpx;
			display: block;
		}
		
		.form-input {
			input {
				height: 80rpx;
				font-size: 32rpx;
				color: #333;
				width: 100%;
			}
		}
		
		.password-rules {
			display: flex;
			flex-wrap: wrap;
			margin-top: 16rpx;
			
			.rule-item {
				font-size: 24rpx;
				color: #999;
				margin-right: 30rpx;
				margin-bottom: 10rpx;
				display: flex;
				align-items: center;
				
				&::before {
					content: '';
					display: inline-block;
					width: 16rpx;
					height: 16rpx;
					border-radius: 50%;
					background-color: #e8e8e8;
					margin-right: 8rpx;
				}
				
				&.rule-passed {
					color: #52c41a;
					
					&::before {
						background-color: #52c41a;
					}
				}
			}
		}
		
		.confirm-tip {
			margin-top: 16rpx;
			
			.tip-error {
				font-size: 24rpx;
				color: #f5222d;
			}
		}
	}
}

.submit-btn {
	margin: 60rpx 30rpx;
	height: 90rpx;
	line-height: 90rpx;
	background-color: $uni-color-primary;
	color: #fff;
	text-align: center;
	border-radius: 12rpx;
	font-size: 32rpx;
	box-shadow: 0 6rpx 20rpx rgba(24, 144, 255, 0.3);
	font-weight: bold;
	
	&:active {
		opacity: 0.9;
		transform: translateY(2rpx);
	}
	
	&.btn-disabled {
		background-color: #ccc;
		box-shadow: none;
	}
}

.help-tip {
	padding: 0 40rpx;
	margin-top: 40rpx;
	
	.tip-title {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 16rpx;
		display: block;
	}
	
	.tip-content {
		font-size: 26rpx;
		color: #999;
		margin-bottom: 10rpx;
		display: block;
		line-height: 1.6;
	}
}
</style> 