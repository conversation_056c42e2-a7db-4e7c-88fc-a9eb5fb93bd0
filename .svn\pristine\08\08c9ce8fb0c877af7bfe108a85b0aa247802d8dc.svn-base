package com.heating.service;

import com.heating.dto.ManagerHeatUnitDto;
import java.util.List;

/**
 * 管理人员与热用户关联服务接口
 */
public interface ManagerHeatUnitService {
    
    /**
     * 保存管理人员与热用户关联
     * @param dto 关联DTO
     * @return 保存后的记录ID
     */
    Long save(ManagerHeatUnitDto dto);
    
    /**
     * 更新管理人员与热用户关联
     * @param id 记录ID
     * @param dto 关联DTO
     * @return 更新后的DTO
     */
    ManagerHeatUnitDto update(Long id, ManagerHeatUnitDto dto);
    
    /**
     * 删除管理人员与热用户关联
     * @param id 记录ID
     */
    void delete(Long id);
    
    /**
     * 根据ID获取关联信息
     * @param id 记录ID
     * @return 关联DTO
     */
    ManagerHeatUnitDto getById(Long id);
    
    /**
     * 获取指定管理人员的热用户列表
     * @param managerId 管理人员ID
     * @return 热用户列表
     */
    List<ManagerHeatUnitDto> getByManagerId(Long managerId);
    
    /**
     * 获取管理指定热用户的人员列表
     * @param heatUnitId 热用户ID
     * @return 管理人员列表
     */
    List<ManagerHeatUnitDto> getByHeatUnitId(Long heatUnitId);
} 