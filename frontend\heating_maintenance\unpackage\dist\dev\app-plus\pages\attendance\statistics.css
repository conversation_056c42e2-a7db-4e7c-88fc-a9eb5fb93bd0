/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uniui-cart-filled[data-v-946bce22]:before {
  content: "\e6d0";
}
.uniui-gift-filled[data-v-946bce22]:before {
  content: "\e6c4";
}
.uniui-color[data-v-946bce22]:before {
  content: "\e6cf";
}
.uniui-wallet[data-v-946bce22]:before {
  content: "\e6b1";
}
.uniui-settings-filled[data-v-946bce22]:before {
  content: "\e6ce";
}
.uniui-auth-filled[data-v-946bce22]:before {
  content: "\e6cc";
}
.uniui-shop-filled[data-v-946bce22]:before {
  content: "\e6cd";
}
.uniui-staff-filled[data-v-946bce22]:before {
  content: "\e6cb";
}
.uniui-vip-filled[data-v-946bce22]:before {
  content: "\e6c6";
}
.uniui-plus-filled[data-v-946bce22]:before {
  content: "\e6c7";
}
.uniui-folder-add-filled[data-v-946bce22]:before {
  content: "\e6c8";
}
.uniui-color-filled[data-v-946bce22]:before {
  content: "\e6c9";
}
.uniui-tune-filled[data-v-946bce22]:before {
  content: "\e6ca";
}
.uniui-calendar-filled[data-v-946bce22]:before {
  content: "\e6c0";
}
.uniui-notification-filled[data-v-946bce22]:before {
  content: "\e6c1";
}
.uniui-wallet-filled[data-v-946bce22]:before {
  content: "\e6c2";
}
.uniui-medal-filled[data-v-946bce22]:before {
  content: "\e6c3";
}
.uniui-fire-filled[data-v-946bce22]:before {
  content: "\e6c5";
}
.uniui-refreshempty[data-v-946bce22]:before {
  content: "\e6bf";
}
.uniui-location-filled[data-v-946bce22]:before {
  content: "\e6af";
}
.uniui-person-filled[data-v-946bce22]:before {
  content: "\e69d";
}
.uniui-personadd-filled[data-v-946bce22]:before {
  content: "\e698";
}
.uniui-arrowthinleft[data-v-946bce22]:before {
  content: "\e6d2";
}
.uniui-arrowthinup[data-v-946bce22]:before {
  content: "\e6d3";
}
.uniui-arrowthindown[data-v-946bce22]:before {
  content: "\e6d4";
}
.uniui-back[data-v-946bce22]:before {
  content: "\e6b9";
}
.uniui-forward[data-v-946bce22]:before {
  content: "\e6ba";
}
.uniui-arrow-right[data-v-946bce22]:before {
  content: "\e6bb";
}
.uniui-arrow-left[data-v-946bce22]:before {
  content: "\e6bc";
}
.uniui-arrow-up[data-v-946bce22]:before {
  content: "\e6bd";
}
.uniui-arrow-down[data-v-946bce22]:before {
  content: "\e6be";
}
.uniui-arrowthinright[data-v-946bce22]:before {
  content: "\e6d1";
}
.uniui-down[data-v-946bce22]:before {
  content: "\e6b8";
}
.uniui-bottom[data-v-946bce22]:before {
  content: "\e6b8";
}
.uniui-arrowright[data-v-946bce22]:before {
  content: "\e6d5";
}
.uniui-right[data-v-946bce22]:before {
  content: "\e6b5";
}
.uniui-up[data-v-946bce22]:before {
  content: "\e6b6";
}
.uniui-top[data-v-946bce22]:before {
  content: "\e6b6";
}
.uniui-left[data-v-946bce22]:before {
  content: "\e6b7";
}
.uniui-arrowup[data-v-946bce22]:before {
  content: "\e6d6";
}
.uniui-eye[data-v-946bce22]:before {
  content: "\e651";
}
.uniui-eye-filled[data-v-946bce22]:before {
  content: "\e66a";
}
.uniui-eye-slash[data-v-946bce22]:before {
  content: "\e6b3";
}
.uniui-eye-slash-filled[data-v-946bce22]:before {
  content: "\e6b4";
}
.uniui-info-filled[data-v-946bce22]:before {
  content: "\e649";
}
.uniui-reload[data-v-946bce22]:before {
  content: "\e6b2";
}
.uniui-micoff-filled[data-v-946bce22]:before {
  content: "\e6b0";
}
.uniui-map-pin-ellipse[data-v-946bce22]:before {
  content: "\e6ac";
}
.uniui-map-pin[data-v-946bce22]:before {
  content: "\e6ad";
}
.uniui-location[data-v-946bce22]:before {
  content: "\e6ae";
}
.uniui-starhalf[data-v-946bce22]:before {
  content: "\e683";
}
.uniui-star[data-v-946bce22]:before {
  content: "\e688";
}
.uniui-star-filled[data-v-946bce22]:before {
  content: "\e68f";
}
.uniui-calendar[data-v-946bce22]:before {
  content: "\e6a0";
}
.uniui-fire[data-v-946bce22]:before {
  content: "\e6a1";
}
.uniui-medal[data-v-946bce22]:before {
  content: "\e6a2";
}
.uniui-font[data-v-946bce22]:before {
  content: "\e6a3";
}
.uniui-gift[data-v-946bce22]:before {
  content: "\e6a4";
}
.uniui-link[data-v-946bce22]:before {
  content: "\e6a5";
}
.uniui-notification[data-v-946bce22]:before {
  content: "\e6a6";
}
.uniui-staff[data-v-946bce22]:before {
  content: "\e6a7";
}
.uniui-vip[data-v-946bce22]:before {
  content: "\e6a8";
}
.uniui-folder-add[data-v-946bce22]:before {
  content: "\e6a9";
}
.uniui-tune[data-v-946bce22]:before {
  content: "\e6aa";
}
.uniui-auth[data-v-946bce22]:before {
  content: "\e6ab";
}
.uniui-person[data-v-946bce22]:before {
  content: "\e699";
}
.uniui-email-filled[data-v-946bce22]:before {
  content: "\e69a";
}
.uniui-phone-filled[data-v-946bce22]:before {
  content: "\e69b";
}
.uniui-phone[data-v-946bce22]:before {
  content: "\e69c";
}
.uniui-email[data-v-946bce22]:before {
  content: "\e69e";
}
.uniui-personadd[data-v-946bce22]:before {
  content: "\e69f";
}
.uniui-chatboxes-filled[data-v-946bce22]:before {
  content: "\e692";
}
.uniui-contact[data-v-946bce22]:before {
  content: "\e693";
}
.uniui-chatbubble-filled[data-v-946bce22]:before {
  content: "\e694";
}
.uniui-contact-filled[data-v-946bce22]:before {
  content: "\e695";
}
.uniui-chatboxes[data-v-946bce22]:before {
  content: "\e696";
}
.uniui-chatbubble[data-v-946bce22]:before {
  content: "\e697";
}
.uniui-upload-filled[data-v-946bce22]:before {
  content: "\e68e";
}
.uniui-upload[data-v-946bce22]:before {
  content: "\e690";
}
.uniui-weixin[data-v-946bce22]:before {
  content: "\e691";
}
.uniui-compose[data-v-946bce22]:before {
  content: "\e67f";
}
.uniui-qq[data-v-946bce22]:before {
  content: "\e680";
}
.uniui-download-filled[data-v-946bce22]:before {
  content: "\e681";
}
.uniui-pyq[data-v-946bce22]:before {
  content: "\e682";
}
.uniui-sound[data-v-946bce22]:before {
  content: "\e684";
}
.uniui-trash-filled[data-v-946bce22]:before {
  content: "\e685";
}
.uniui-sound-filled[data-v-946bce22]:before {
  content: "\e686";
}
.uniui-trash[data-v-946bce22]:before {
  content: "\e687";
}
.uniui-videocam-filled[data-v-946bce22]:before {
  content: "\e689";
}
.uniui-spinner-cycle[data-v-946bce22]:before {
  content: "\e68a";
}
.uniui-weibo[data-v-946bce22]:before {
  content: "\e68b";
}
.uniui-videocam[data-v-946bce22]:before {
  content: "\e68c";
}
.uniui-download[data-v-946bce22]:before {
  content: "\e68d";
}
.uniui-help[data-v-946bce22]:before {
  content: "\e679";
}
.uniui-navigate-filled[data-v-946bce22]:before {
  content: "\e67a";
}
.uniui-plusempty[data-v-946bce22]:before {
  content: "\e67b";
}
.uniui-smallcircle[data-v-946bce22]:before {
  content: "\e67c";
}
.uniui-minus-filled[data-v-946bce22]:before {
  content: "\e67d";
}
.uniui-micoff[data-v-946bce22]:before {
  content: "\e67e";
}
.uniui-closeempty[data-v-946bce22]:before {
  content: "\e66c";
}
.uniui-clear[data-v-946bce22]:before {
  content: "\e66d";
}
.uniui-navigate[data-v-946bce22]:before {
  content: "\e66e";
}
.uniui-minus[data-v-946bce22]:before {
  content: "\e66f";
}
.uniui-image[data-v-946bce22]:before {
  content: "\e670";
}
.uniui-mic[data-v-946bce22]:before {
  content: "\e671";
}
.uniui-paperplane[data-v-946bce22]:before {
  content: "\e672";
}
.uniui-close[data-v-946bce22]:before {
  content: "\e673";
}
.uniui-help-filled[data-v-946bce22]:before {
  content: "\e674";
}
.uniui-paperplane-filled[data-v-946bce22]:before {
  content: "\e675";
}
.uniui-plus[data-v-946bce22]:before {
  content: "\e676";
}
.uniui-mic-filled[data-v-946bce22]:before {
  content: "\e677";
}
.uniui-image-filled[data-v-946bce22]:before {
  content: "\e678";
}
.uniui-locked-filled[data-v-946bce22]:before {
  content: "\e668";
}
.uniui-info[data-v-946bce22]:before {
  content: "\e669";
}
.uniui-locked[data-v-946bce22]:before {
  content: "\e66b";
}
.uniui-camera-filled[data-v-946bce22]:before {
  content: "\e658";
}
.uniui-chat-filled[data-v-946bce22]:before {
  content: "\e659";
}
.uniui-camera[data-v-946bce22]:before {
  content: "\e65a";
}
.uniui-circle[data-v-946bce22]:before {
  content: "\e65b";
}
.uniui-checkmarkempty[data-v-946bce22]:before {
  content: "\e65c";
}
.uniui-chat[data-v-946bce22]:before {
  content: "\e65d";
}
.uniui-circle-filled[data-v-946bce22]:before {
  content: "\e65e";
}
.uniui-flag[data-v-946bce22]:before {
  content: "\e65f";
}
.uniui-flag-filled[data-v-946bce22]:before {
  content: "\e660";
}
.uniui-gear-filled[data-v-946bce22]:before {
  content: "\e661";
}
.uniui-home[data-v-946bce22]:before {
  content: "\e662";
}
.uniui-home-filled[data-v-946bce22]:before {
  content: "\e663";
}
.uniui-gear[data-v-946bce22]:before {
  content: "\e664";
}
.uniui-smallcircle-filled[data-v-946bce22]:before {
  content: "\e665";
}
.uniui-map-filled[data-v-946bce22]:before {
  content: "\e666";
}
.uniui-map[data-v-946bce22]:before {
  content: "\e667";
}
.uniui-refresh-filled[data-v-946bce22]:before {
  content: "\e656";
}
.uniui-refresh[data-v-946bce22]:before {
  content: "\e657";
}
.uniui-cloud-upload[data-v-946bce22]:before {
  content: "\e645";
}
.uniui-cloud-download-filled[data-v-946bce22]:before {
  content: "\e646";
}
.uniui-cloud-download[data-v-946bce22]:before {
  content: "\e647";
}
.uniui-cloud-upload-filled[data-v-946bce22]:before {
  content: "\e648";
}
.uniui-redo[data-v-946bce22]:before {
  content: "\e64a";
}
.uniui-images-filled[data-v-946bce22]:before {
  content: "\e64b";
}
.uniui-undo-filled[data-v-946bce22]:before {
  content: "\e64c";
}
.uniui-more[data-v-946bce22]:before {
  content: "\e64d";
}
.uniui-more-filled[data-v-946bce22]:before {
  content: "\e64e";
}
.uniui-undo[data-v-946bce22]:before {
  content: "\e64f";
}
.uniui-images[data-v-946bce22]:before {
  content: "\e650";
}
.uniui-paperclip[data-v-946bce22]:before {
  content: "\e652";
}
.uniui-settings[data-v-946bce22]:before {
  content: "\e653";
}
.uniui-search[data-v-946bce22]:before {
  content: "\e654";
}
.uniui-redo-filled[data-v-946bce22]:before {
  content: "\e655";
}
.uniui-list[data-v-946bce22]:before {
  content: "\e644";
}
.uniui-mail-open-filled[data-v-946bce22]:before {
  content: "\e63a";
}
.uniui-hand-down-filled[data-v-946bce22]:before {
  content: "\e63c";
}
.uniui-hand-down[data-v-946bce22]:before {
  content: "\e63d";
}
.uniui-hand-up-filled[data-v-946bce22]:before {
  content: "\e63e";
}
.uniui-hand-up[data-v-946bce22]:before {
  content: "\e63f";
}
.uniui-heart-filled[data-v-946bce22]:before {
  content: "\e641";
}
.uniui-mail-open[data-v-946bce22]:before {
  content: "\e643";
}
.uniui-heart[data-v-946bce22]:before {
  content: "\e639";
}
.uniui-loop[data-v-946bce22]:before {
  content: "\e633";
}
.uniui-pulldown[data-v-946bce22]:before {
  content: "\e632";
}
.uniui-scan[data-v-946bce22]:before {
  content: "\e62a";
}
.uniui-bars[data-v-946bce22]:before {
  content: "\e627";
}
.uniui-checkbox[data-v-946bce22]:before {
  content: "\e62b";
}
.uniui-checkbox-filled[data-v-946bce22]:before {
  content: "\e62c";
}
.uniui-shop[data-v-946bce22]:before {
  content: "\e62f";
}
.uniui-headphones[data-v-946bce22]:before {
  content: "\e630";
}
.uniui-cart[data-v-946bce22]:before {
  content: "\e631";
}
@font-face {
  font-family: uniicons;
  src: url("../../assets/uniicons.32e978a5.ttf");
}
.uni-icons[data-v-946bce22] {
  font-family: uniicons;
  text-decoration: none;
  text-align: center;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-popup[data-v-7db519c7] {
  position: fixed;
  z-index: 99;
}
.uni-popup.top[data-v-7db519c7], .uni-popup.left[data-v-7db519c7], .uni-popup.right[data-v-7db519c7] {
  top: 0;
}
.uni-popup .uni-popup__wrapper[data-v-7db519c7] {
  display: block;
  position: relative;
  /* iphonex 等安全区设置，底部安全区适配 */
}
.uni-popup .uni-popup__wrapper.left[data-v-7db519c7], .uni-popup .uni-popup__wrapper.right[data-v-7db519c7] {
  padding-top: 0;
  flex: 1;
}
.fixforpc-z-index[data-v-7db519c7] {
  z-index: 999;
}
.fixforpc-top[data-v-7db519c7] {
  top: 0;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-calendar-item__weeks-box[data-v-f9a24ebd] {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.uni-calendar-item__weeks-box-text[data-v-f9a24ebd] {
  font-size: 14px;
  color: #333;
}
.uni-calendar-item__weeks-lunar-text[data-v-f9a24ebd] {
  font-size: 12px;
  color: #333;
}
.uni-calendar-item__weeks-box-item[data-v-f9a24ebd] {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 3.125rem;
  height: 3.125rem;
}
.uni-calendar-item__weeks-box-circle[data-v-f9a24ebd] {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 8px;
  height: 8px;
  border-radius: 8px;
  background-color: #e43d33;
}
.uni-calendar-item--disable[data-v-f9a24ebd] {
  background-color: rgba(249, 249, 249, 0.3);
  color: #c0c0c0;
}
.uni-calendar-item--isDay-text[data-v-f9a24ebd] {
  color: #2979ff;
}
.uni-calendar-item--isDay[data-v-f9a24ebd] {
  background-color: #2979ff;
  opacity: 0.8;
  color: #fff;
}
.uni-calendar-item--extra[data-v-f9a24ebd] {
  color: #e43d33;
  opacity: 0.8;
}
.uni-calendar-item--checked[data-v-f9a24ebd] {
  background-color: #2979ff;
  color: #fff;
  opacity: 0.8;
}
.uni-calendar-item--multiple[data-v-f9a24ebd] {
  background-color: #2979ff;
  color: #fff;
  opacity: 0.8;
}
.uni-calendar-item--before-checked[data-v-f9a24ebd] {
  background-color: #ff5a5f;
  color: #fff;
}
.uni-calendar-item--after-checked[data-v-f9a24ebd] {
  background-color: #ff5a5f;
  color: #fff;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-calendar[data-v-3099330a] {
  display: flex;
  flex-direction: column;
}
.uni-calendar__mask[data-v-3099330a] {
  position: fixed;
  bottom: 0;
  top: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.4);
  transition-property: opacity;
  transition-duration: 0.3s;
  opacity: 0;
  z-index: 99;
}
.uni-calendar--mask-show[data-v-3099330a] {
  opacity: 1;
}
.uni-calendar--fixed[data-v-3099330a] {
  position: fixed;
  left: 0;
  right: 0;
  transition-property: transform;
  transition-duration: 0.3s;
  transform: translateY(460px);
  bottom: calc(var(--window-bottom));
  z-index: 99;
}
.uni-calendar--ani-show[data-v-3099330a] {
  transform: translateY(0);
}
.uni-calendar__content[data-v-3099330a] {
  background-color: #fff;
}
.uni-calendar__header[data-v-3099330a] {
  position: relative;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 50px;
  border-bottom-color: #EDEDED;
  border-bottom-style: solid;
  border-bottom-width: 1px;
}
.uni-calendar--fixed-top[data-v-3099330a] {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  border-top-color: #EDEDED;
  border-top-style: solid;
  border-top-width: 1px;
}
.uni-calendar--fixed-width[data-v-3099330a] {
  width: 50px;
}
.uni-calendar__backtoday[data-v-3099330a] {
  position: absolute;
  right: 0;
  top: 0.78125rem;
  padding: 0 5px;
  padding-left: 10px;
  height: 25px;
  line-height: 25px;
  font-size: 12px;
  border-top-left-radius: 25px;
  border-bottom-left-radius: 25px;
  color: #333;
  background-color: #f1f1f1;
}
.uni-calendar__header-text[data-v-3099330a] {
  text-align: center;
  width: 100px;
  font-size: 14px;
  color: #333;
}
.uni-calendar__header-btn-box[data-v-3099330a] {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
}
.uni-calendar__header-btn[data-v-3099330a] {
  width: 10px;
  height: 10px;
  border-left-color: #808080;
  border-left-style: solid;
  border-left-width: 2px;
  border-top-color: #555555;
  border-top-style: solid;
  border-top-width: 2px;
}
.uni-calendar--left[data-v-3099330a] {
  transform: rotate(-45deg);
}
.uni-calendar--right[data-v-3099330a] {
  transform: rotate(135deg);
}
.uni-calendar__weeks[data-v-3099330a] {
  position: relative;
  display: flex;
  flex-direction: row;
}
.uni-calendar__weeks-item[data-v-3099330a] {
  flex: 1;
}
.uni-calendar__weeks-day[data-v-3099330a] {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 45px;
  border-bottom-color: #F5F5F5;
  border-bottom-style: solid;
  border-bottom-width: 1px;
}
.uni-calendar__weeks-day-text[data-v-3099330a] {
  font-size: 14px;
}
.uni-calendar__box[data-v-3099330a] {
  position: relative;
}
.uni-calendar__box-bg[data-v-3099330a] {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.uni-calendar__box-bg-text[data-v-3099330a] {
  font-size: 200px;
  font-weight: bold;
  color: #999;
  opacity: 0.1;
  text-align: center;
  line-height: 1;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.statistics-container {
  padding: 0.625rem;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.625rem 0;
}
.filter-section .month-selector {
  display: flex;
  align-items: center;
}
.filter-section .month-selector .current-month {
  font-size: 1rem;
  font-weight: bold;
  margin-right: 0.3125rem;
}
.filter-section .month-selector .month-arrows {
  display: flex;
  align-items: center;
}
.filter-section .staff-selector {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 0.3125rem 0.625rem;
  border-radius: 0.9375rem;
  font-size: 0.875rem;
}
.filter-section .staff-selector uni-text {
  margin-right: 0.3125rem;
}
.statistics-cards {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.625rem;
}
.statistics-cards .stat-card {
  flex: 1;
  margin: 0 0.3125rem;
  background-color: #fff;
  border-radius: 0.3125rem;
  padding: 0.625rem;
  text-align: center;
  box-shadow: 0 0.0625rem 0.15625rem rgba(0, 0, 0, 0.05);
}
.statistics-cards .stat-card:first-child {
  margin-left: 0;
}
.statistics-cards .stat-card:last-child {
  margin-right: 0;
}
.statistics-cards .stat-card .stat-value {
  font-size: 1.125rem;
  font-weight: bold;
  color: #333;
}
.statistics-cards .stat-card .stat-label {
  font-size: 0.75rem;
  color: #999;
  margin-top: 0.3125rem;
}
.statistics-cards .stat-card:nth-child(1) .stat-value {
  color: #4cd964;
}
.statistics-cards .stat-card:nth-child(2) .stat-value {
  color: #f0ad4e;
}
.statistics-cards .stat-card:nth-child(3) .stat-value {
  color: #5bc0de;
}
.statistics-cards .stat-card:nth-child(4) .stat-value {
  color: #dd524d;
}
.main-calendar {
  background-color: #fff;
  border-radius: 0.3125rem;
  padding: 0.625rem;
  margin-bottom: 0.625rem;
  box-shadow: 0 0.0625rem 0.15625rem rgba(0, 0, 0, 0.05);
}
.main-calendar .calendar-header {
  display: flex;
  margin-bottom: 0.625rem;
}
.main-calendar .calendar-header .header-item {
  flex: 1;
  text-align: center;
  font-size: 0.875rem;
  color: #666;
}
.main-calendar .calendar-days {
  display: flex;
  flex-wrap: wrap;
}
.main-calendar .calendar-days .calendar-day {
  width: 14.2857142857%;
  height: 2.5rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  margin-bottom: 0.3125rem;
}
.main-calendar .calendar-days .calendar-day .day-number {
  font-size: 0.875rem;
  color: #333;
}
.main-calendar .calendar-days .calendar-day.current-month .day-number {
  color: #007AFF;
}
.main-calendar .calendar-days .calendar-day.today .day-number {
  width: 1.875rem;
  height: 1.875rem;
  background-color: #007AFF;
  color: #fff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.main-calendar .calendar-days .calendar-day.normal .day-number {
  color: #4cd964;
}
.main-calendar .calendar-days .calendar-day.late .day-number {
  color: #f0ad4e;
}
.main-calendar .calendar-days .calendar-day.early .day-number {
  color: #5bc0de;
}
.main-calendar .calendar-days .calendar-day.absent .day-number {
  color: #dd524d;
}
.charts-section {
  background-color: #fff;
  border-radius: 0.3125rem;
  padding: 0.625rem;
  margin-bottom: 0.625rem;
  box-shadow: 0 0.0625rem 0.15625rem rgba(0, 0, 0, 0.05);
}
.charts-section .section-title {
  font-size: 0.9375rem;
  font-weight: bold;
  margin-bottom: 0.625rem;
}
.charts-section .charts-container {
  display: flex;
  flex-direction: column;
}
.charts-section .charts-container .chart-card {
  width: 100%;
  margin-bottom: 0.625rem;
  background-color: #fff;
  border-radius: 0.3125rem;
  text-align: center;
}
.charts-section .charts-container .chart-card .chart-title {
  font-size: 0.875rem;
  font-weight: bold;
  margin-bottom: 0.3125rem;
}
.charts-section .charts-container .chart-card .chart-container {
  height: 12.5rem;
  width: 100%;
  position: relative;
  padding: 0.3125rem 0 0.9375rem 0;
}
.charts-section .charts-container .chart-card .chart-container uni-canvas {
  width: 100%;
  height: 100%;
}
.attendance-records {
  background-color: #fff;
  border-radius: 0.3125rem;
  padding: 0.625rem;
  box-shadow: 0 0.0625rem 0.15625rem rgba(0, 0, 0, 0.05);
}
.attendance-records .record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.625rem;
}
.attendance-records .record-header uni-text {
  font-size: 0.9375rem;
  font-weight: bold;
}
.attendance-records .record-header .view-all {
  font-size: 0.8125rem;
  color: #007AFF;
  font-weight: normal;
}
.attendance-records .record-list .record-item {
  display: flex;
  padding: 0.625rem 0;
  border-bottom: 0.03125rem solid #f5f5f5;
}
.attendance-records .record-list .record-item:last-child {
  border-bottom: none;
}
.attendance-records .record-list .record-item .record-date {
  width: 3.125rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.attendance-records .record-list .record-item .record-date .day {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
}
.attendance-records .record-list .record-item .record-date .week {
  font-size: 0.75rem;
  color: #999;
  margin-top: 0.15625rem;
}
.attendance-records .record-list .record-item .record-time {
  flex: 1;
  margin-left: 0.625rem;
}
.attendance-records .record-list .record-item .record-time .time-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.3125rem;
}
.attendance-records .record-list .record-item .record-time .time-item .time-label {
  width: 3.75rem;
  font-size: 0.8125rem;
  color: #999;
}
.attendance-records .record-list .record-item .record-time .time-item .time-value {
  font-size: 0.875rem;
  color: #333;
  margin-right: 0.3125rem;
}
.attendance-records .record-list .record-item .record-time .time-item .time-value.abnormal {
  color: #f0ad4e;
}
.attendance-records .record-list .record-item .record-time .time-item .status-tag {
  font-size: 0.6875rem;
  padding: 0.0625rem 0.3125rem;
  border-radius: 0.625rem;
}
.attendance-records .record-list .record-item .record-time .time-item .status-tag.late {
  background-color: #fef0e5;
  color: #f0ad4e;
}
.attendance-records .record-list .record-item .record-time .time-item .status-tag.early {
  background-color: #e5f5fa;
  color: #5bc0de;
}
.attendance-records .record-list .record-item .record-location {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-top: 0.3125rem;
}
.attendance-records .record-list .record-item .record-location .location-label {
  font-size: 0.8125rem;
  color: #999;
  margin-bottom: 0.15625rem;
}
.attendance-records .record-list .record-item .record-location .location-value {
  font-size: 0.8125rem;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.attendance-records .empty-records {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.875rem 0;
}
.attendance-records .empty-records uni-image {
  width: 6.25rem;
  height: 6.25rem;
  margin-bottom: 0.625rem;
}
.attendance-records .empty-records uni-text {
  font-size: 0.875rem;
  color: #999;
}
.staff-popup {
  background-color: #fff;
  border-top-left-radius: 0.625rem;
  border-top-right-radius: 0.625rem;
  padding-bottom: 0.9375rem;
}
.staff-popup .popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.9375rem;
  border-bottom: 0.03125rem solid #f5f5f5;
}
.staff-popup .popup-header uni-text {
  font-size: 1rem;
  font-weight: bold;
}
.staff-popup .popup-header .close-btn {
  font-size: 0.875rem;
  color: #007AFF;
}
.staff-popup .search-box {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  margin: 0.625rem 0.9375rem;
  padding: 0.3125rem 0.625rem;
  border-radius: 0.3125rem;
}
.staff-popup .search-box uni-input {
  flex: 1;
  height: 1.875rem;
  margin-left: 0.3125rem;
  font-size: 0.875rem;
}
.staff-popup .staff-list {
  max-height: 18.75rem;
}
.staff-popup .staff-list .staff-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.625rem 0.9375rem;
  border-bottom: 0.03125rem solid #f5f5f5;
}
.staff-popup .staff-list .staff-item uni-text {
  font-size: 0.9375rem;
  color: #333;
}

/* 日考勤详情弹窗样式 */
.day-detail-popup {
  background-color: #fff;
  border-radius: 0.75rem 0.75rem 0 0;
  padding: 0.9375rem;
  max-height: 80vh;
  overflow-y: auto;
}
.popup-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.9375rem;
  font-size: 1rem;
  font-weight: bold;
}
.close-btn {
  color: #999;
  font-size: 0.875rem;
}
.day-stats-chart {
  width: 100%;
  height: 10.9375rem;
  margin-bottom: 0.625rem;
}
.day-stats-summary {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.9375rem;
  border-radius: 0.3125rem;
  background-color: #f8f8f8;
  padding: 0.625rem 0.3125rem;
}
.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 25%;
}
.stat-value {
  font-size: 1.0625rem;
  font-weight: bold;
  margin-bottom: 0.1875rem;
}
.stat-item.normal .stat-value {
  color: #4cd964;
}
.stat-item.late .stat-value {
  color: #f0ad4e;
}
.stat-item.early .stat-value {
  color: #f56c6c;
}
.stat-item.absent .stat-value {
  color: #dd524d;
}
.stat-label {
  font-size: 0.75rem;
  color: #666;
}
.section-title {
  font-size: 0.875rem;
  font-weight: bold;
  margin: 0.625rem 0;
  color: #333;
  border-left: 0.25rem solid #007aff;
  padding-left: 0.625rem;
}
.staff-detail-section {
  margin-bottom: 0.9375rem;
  background-color: #f8f8f8;
  border-radius: 0.3125rem;
  padding: 0.625rem;
}
.staff-list-section {
  margin-bottom: 0.9375rem;
}
.staff-category {
  margin-bottom: 0.625rem;
  border-radius: 0.3125rem;
  overflow: hidden;
  background-color: #f8f8f8;
}
.category-header {
  display: flex;
  justify-content: space-between;
  padding: 0.46875rem 0.625rem;
  font-size: 0.8125rem;
  color: #fff;
}
.category-header.normal {
  background-color: #4cd964;
}
.category-header.late {
  background-color: #f0ad4e;
}
.category-header.early {
  background-color: #f56c6c;
}
.category-header.absent {
  background-color: #dd524d;
}
.staff-items {
  padding: 0.3125rem 0.625rem;
}
.staff-item {
  display: flex;
  justify-content: space-between;
  padding: 0.46875rem 0;
  border-bottom: 1px solid #eee;
}
.staff-item:last-child {
  border-bottom: none;
}
.staff-name {
  font-size: 0.8125rem;
  color: #333;
}
.staff-time {
  font-size: 0.75rem;
  color: #666;
}
.empty-day-detail {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.5625rem 0;
}
.empty-day-detail uni-image {
  width: 6.25rem;
  height: 6.25rem;
  margin-bottom: 0.625rem;
}
.empty-day-detail uni-text {
  font-size: 0.875rem;
  color: #999;
}

/* 现有的日详情内容样式 */
.day-detail-content {
  background-color: #fff;
  border-radius: 0.3125rem;
}
.detail-item {
  display: flex;
  justify-content: space-between;
  padding: 0.46875rem 0;
  border-bottom: 1px solid #eee;
}
.detail-label {
  color: #666;
  font-size: 0.8125rem;
}
.detail-value {
  color: #333;
  font-size: 0.8125rem;
}
.detail-value-box {
  display: flex;
  align-items: center;
}
.status-tag {
  display: inline-block;
  padding: 0.0625rem 0.3125rem;
  font-size: 0.6875rem;
  border-radius: 0.1875rem;
  margin-left: 0.3125rem;
  color: #fff;
}
.status-tag.late {
  background-color: #f0ad4e;
}
.status-tag.early {
  background-color: #f56c6c;
}
.detail-photos {
  margin-top: 0.46875rem;
}
.photo-list {
  display: flex;
  flex-wrap: wrap;
  margin-top: 0.3125rem;
}
.photo-list uni-image {
  width: 5rem;
  height: 5rem;
  margin: 0.3125rem;
  border-radius: 0.25rem;
}