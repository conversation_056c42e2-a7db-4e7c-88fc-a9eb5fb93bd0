package com.heating.entity.user;
import com.heating.dto.user.Certification;
import com.heating.dto.user.WorkStats;

import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import com.heating.converter.JsonConverter;

@Entity
@Table(name = "t_user_app")
@Data
public class TUser {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotEmpty
    @Size(min = 4, max = 50)
    @Column(unique = true)
    private String username;
    
    @NotEmpty
    @Size(min = 6)
    private String password;
    
    @NotEmpty
    @Size(max = 50)
    private String name;
    
    @Email
    @Column(unique = true)
    private String email;
    
    private String phone;
    
    private String role;
    
    private Boolean enabled = true;
    
    private LocalDateTime createTime;
    
    private LocalDateTime updateTime;
    
    @Column(name = "nick_name", length = 50)
    private String nickName;
    
    @Column(length = 255)
    private String avatar;
    
    private Integer gender;
    
    @Column(name = "openid", unique = true, length = 100)
    private String openid;
    
    @Column(name = "unionid", length = 100)
    private String unionid;

    @Convert(converter = JsonConverter.class)
    @Column(columnDefinition = "json")
    private List<Certification> certifications;

    @Convert(converter = JsonConverter.class)
    @Column(name = "workStats", columnDefinition = "json")
    private WorkStats workStats;

    @Column(name = "is_audit")
    private Integer isAudit;

    @Column(length = 50)
    private String department;

    @Convert(converter = JsonConverter.class)
    @Column(columnDefinition = "json")
    private List<String> skills;
    
    @Column(name = "last_login_time")
    private LocalDateTime lastLoginTime;
    
    @Column(name = "certification_status", nullable = false)
    private Integer certificationStatus = 0;
    
    @Transient
    private List<String> permissions;
    
} 