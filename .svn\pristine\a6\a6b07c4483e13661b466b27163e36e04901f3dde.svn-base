package com.heating.dto.attendance;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AttendanceSupplementApproveResponse {
    private Long id; // 补卡申请ID
    private String status; // 状态：approved/rejected
    private String approvedTime; // 审批时间
    private String approvedBy; // 审批人
} 