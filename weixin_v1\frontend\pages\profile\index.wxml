<view class="container">
  <view class="user-card">
    <view class="avatar-box">
      <image class="avatar" src="{{userInfo.headpic || '/images/default-avatar.png'}}" bindtap="chooseImage"></image>
    </view>
    <view class="user-info">
      <text class="name">{{userInfo.name || '未登录'}}</text>
      <text class="role">{{userInfo.role || '暂无角色'}}</text>
    </view>
  </view>

  <view class="menu-list">
    <view class="menu-group">
      <view class="menu-item" bindtap="goToMyFaults">
        <view class="menu-content">
          <text class="wx-icon">⚠️</text>
          <text class="menu-text">我的故障</text>
        </view>
        <text class="wx-icon">›</text>
      </view>
      <view class="menu-item" bindtap="goToMyWorkorders">
        <view class="menu-content">
          <text class="wx-icon">📝</text>
          <text class="menu-text">我的工单</text>
        </view>
        <text class="wx-icon">›</text>
      </view>
      <view class="menu-item" bindtap="goToMyTemps">
        <view class="menu-content">
          <text class="wx-icon">🌡️</text>
          <text class="menu-text">我的室温</text>
        </view>
        <text class="wx-icon">›</text>
      </view>
    </view>

    <view class="menu-group">
      <view class="menu-item" bindtap="goToSettings">
        <view class="menu-content">
          <text class="wx-icon">⚙️</text>
          <text class="menu-text">系统设置</text>
        </view>
        <text class="wx-icon">›</text>
      </view>
      <view class="menu-item" bindtap="goToAbout">
        <view class="menu-content">
          <text class="wx-icon">ℹ️</text>
          <text class="menu-text">关于我们</text>
        </view>
        <text class="wx-icon">›</text>
      </view>
    </view>
  </view>

  <view class="logout-btn" wx:if="{{userInfo.name}}" bindtap="handleLogout">
    <button type="warn">退出登录</button>
  </view>
</view> 