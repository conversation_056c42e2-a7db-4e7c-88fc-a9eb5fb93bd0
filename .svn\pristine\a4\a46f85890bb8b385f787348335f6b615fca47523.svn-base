package com.heating.dto.permission;

/**
 * 角色权限响应数据传输对象
 */
public record RolePermissionResponse(
    Long id,
    String permissionCode
) {
    /**
     * 紧凑型规范构造函数
     * 用于验证输入参数
     */
    public RolePermissionResponse {
        // 验证参数，如果不符合要求则抛出异常
        if (id == null) {
            throw new IllegalArgumentException("权限ID不能为空");
        }
        if (permissionCode == null || permissionCode.isBlank()) {
            throw new IllegalArgumentException("权限编码不能为空");
        }
    }
} 