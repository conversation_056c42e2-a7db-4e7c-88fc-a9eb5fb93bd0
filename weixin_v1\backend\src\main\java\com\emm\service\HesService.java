package com.emm.service;

import com.emm.repository.HesRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class HesService {

    @Autowired
    private HesRepository hesRepository;

    public List<Map<String, Object>> getAllStations() {
        return hesRepository.findAllStations();
    }

    public List<Map<String, Object>> getStationsByCommunityId(Long communityId) {
        return hesRepository.findStationsByCommunityId(communityId);
    }
}