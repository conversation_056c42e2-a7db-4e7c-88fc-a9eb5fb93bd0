<template>
	<view class="patrol-detail-container">
		<!-- 计划基本信息 -->
		<view class="detail-card">
			<view class="card-header">
				<text class="card-title">{{ isRecord ? '巡检记录详情' : '巡检计划详情' }}</text>
				<view class="plan-status" :class="planInfo.status">{{ getStatusText(planInfo.status) }}</view>
			</view>
			
			<view class="info-group">
				<view class="info-item">
					<text class="info-label">计划名称</text>
					<text class="info-value">{{ planInfo.title }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">计划编号</text>
					<text class="info-value">{{ planInfo.code }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">巡检类型</text>
					<text class="info-value">{{ planInfo.type }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">开始时间</text>
					<text class="info-value">{{ formatDate(planInfo.startTime) }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">结束时间</text>
					<text class="info-value">{{ formatDate(planInfo.endTime) }}</text>
				</view>
				<view class="info-item" v-if="planInfo.scheduleType">
					<text class="info-label">巡检周期</text>
					<text class="info-value">{{ getScheduleText(planInfo.scheduleType, planInfo.scheduleWeekDays, planInfo.scheduleMonthDays) }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">巡检地点</text>
					<text class="info-value">{{ planInfo.location }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">负责人</text>
					<text class="info-value">{{ planInfo.manager }}</text>
				</view> 
			</view>
			
			<view class="plan-desc" v-if="planInfo.description">
				<text class="desc-title">计划描述：</text>
				<text class="desc-content">{{ planInfo.description }}</text>
			</view>
		</view>
		
		<!-- 巡检任务列表 -->
		<view class="detail-card">
			<view class="card-header">
				<text class="card-title">巡检任务</text>
			</view>
			
			<view class="task-list">
				<view class="task-item" v-for="(task, index) in planTasks" :key="index" @click="toggleTaskDetails(index)">
					<view class="task-header">
						<view class="task-left">
							<view class="task-status" :class="task.status"></view>
							<text class="task-title">{{ task.title }}</text>
						</view>
						<text class="iconfont" :class="task.showDetails ? 'icon-arrow-up' : 'icon-arrow-down'"></text>
					</view>
					
					<view class="task-details" v-if="task.showDetails">
						<view class="detail-row">
							<text class="detail-label">巡检对象:</text>
							<text class="detail-value">{{ task.target }}</text>
						</view>
						<view class="detail-row">
							<text class="detail-label">巡检点:</text>
							<text class="detail-value">{{ task.point }}</text>
						</view>
						<view class="detail-row">
							<text class="detail-label">巡检内容:</text>
							<text class="detail-value">{{ task.content }}</text>
						</view>
						<view class="detail-row">
							<text class="detail-label">标  准  值:</text>
							<text class="detail-value">{{ task.standard }} {{ task.unit }}  </text>
						</view>
						<view class="detail-row" v-if="task.status === 'completed'">
							<text class="detail-label">实际值</text>
							<text class="detail-value" :class="{ 'value-abnormal': task.isAbnormal }">{{ task.actualValue }}</text>
						</view>
						<view class="detail-row" v-if="task.status === 'completed' && task.completedTime">
							<text class="detail-label">完成时间</text>
							<text class="detail-value">{{ task.completedTime }}</text>
						</view>
						<view class="detail-row" v-if="task.status === 'completed' && task.remark">
							<text class="detail-label">备注</text>
							<text class="detail-value">{{ task.remark }}</text>
						</view>
						
						<view class="task-images" v-if="task.status === 'completed' && task.images && task.images.length > 0">
							<text class="images-title">巡检照片</text>
							<view class="image-list">
								<image v-for="(img, imgIndex) in task.images" :key="imgIndex" :src="img" @click="previewImage(task.images, imgIndex)" mode="aspectFill"></image>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 底部操作按钮 -->
	<!-- 	<view class="action-buttons" v-if="!isRecord">
			<view class="action-btn start" v-if="planInfo.status === 'pending'" @click="startPatrol">
				<text>开始巡检</text>
			</view>
			<view class="action-btn continue" v-if="planInfo.status === 'processing'" @click="continuePatrol">
				<text>继续巡检</text>
			</view>
			<view class="action-btn complete" v-if="planInfo.status === 'processing'" @click="completePatrol">
				<text>完成巡检</text>
			</view>
			<view class="action-btn export" v-if="planInfo.status === 'completed'" @click="exportReport">
				<text>导出报告</text>
			</view>
		</view> -->
		
		<!-- 记录操作按钮 -->
		<!-- <view class="action-buttons" v-if="isRecord">
			<view class="action-btn export" @click="exportReport">
				<text>导出记录</text>
			</view>
		</view> -->
	</view>
</template>

<script>
import { patrolApi } from '@/utils/api.js';

export default {
	data() {
		return {
			planId: '',
			planInfo: {
				id: '',
				title: '',
				code: '',
				type: '',
				startTime: '',
				endTime: '',
				location: '',
				manager: '',
				progress: '',
				description: '',
				status: 'pending'
			},
			planTasks: [],
			// stats: {
			// 	totalTasks: 0,
			// 	completedTasks: 0,
			// 	abnormalCount: 0,
			// 	completionRate: 0
			// },
			isLoading: false,
			// 是否为巡检记录详情
			isRecord: false
		}
	},
	computed: {
		progressPercent() {
			if (!this.planInfo.progress) return 0;
			
			const progressParts = this.planInfo.progress.split('/');
			if (progressParts.length !== 2) return 0;
			
			const completed = parseInt(progressParts[0]);
			const total = parseInt(progressParts[1]);
			
			if (isNaN(completed) || isNaN(total) || total === 0) return 0;
			
			return Math.floor((completed / total) * 100);
		}
	},
	onLoad(options) {
		this.planId = options.id;
		// 判断是否为巡检记录
		this.isRecord = options.type === 'record';
		
		if (this.isRecord) {
			this.loadPatrolResultDetail();
		} else {
			this.loadPlanDetail();
		}
	},
	methods: {
		// 加载巡检结果详情
		loadPatrolResultDetail() {
			this.isLoading = true;
			// 显示加载中
			uni.showLoading({
			   title: "加载中...",
			});
			patrolApi.getPatrolResultDetail(this.planId)
				.then(res => {
					if (res.code === 200 && res.data) {
						const data = res.data;
						const recordInfo = data.recordInfo;
						
						// 设置巡检记录基本信息
						this.planInfo = {
							id: recordInfo.id,
							title: recordInfo.planName,
							code: `REC-${recordInfo.id}`,
							type: '巡检记录',
							startTime: recordInfo.startTime,
							endTime: recordInfo.endTime,
							location: recordInfo.locations,
							manager: recordInfo.executorName || '未分配',
							description: recordInfo.remark || '',
							status: recordInfo.status,
							scheduleType: recordInfo.scheduleType,
							scheduleWeekDays: recordInfo.scheduleWeekDays,
							scheduleMonthDays: recordInfo.scheduleMonthDays
						};
						
						// 转换巡检结果为任务列表
						if (data.resultList && data.resultList.length > 0) {
							this.planTasks = data.resultList.map(result => ({
								id: result.id,
								title: result.itemName,
								target: result.deviceName || '',
								point: result.categoryName || '',
								content: result.description || '',
								standard: result.normalRange || '',
								unit: result.unit || '',
								actualValue: result.paramValue || '',
								isAbnormal: result.checkResult === 'abnormal',
								completedTime: this.formatDateTime(result.createTime),
								remark: result.description || '',
								status: 'completed', // 巡检记录中的项目都是已完成的
								images: result.images || [],
								showDetails: false,
								latitude: result.latitude,
								longitude: result.longitude
							}));
						}
						
						// 更新统计信息
						// if (data.summary) {
						// 	this.stats = {
						// 		totalItems: data.summary.totalItems || 0,
						// 		completedTasks: data.summary.totalItems || 0, // 记录中的项目都是已完成的
						// 		abnormalCount: data.summary.abnormalCount || 0,
						// 		completionRate: data.summary.completionRate?.replace('%', '') || 100
						// 	};
						// } else {
						// 	this.calculateStats();
						// }
					} else {
						uni.showToast({
							title: res.message || '获取巡检结果详情失败',
							icon: 'none'
						});
					}
				})
				.catch(err => {
					uni.hideLoading();
					console.error('获取巡检结果详情失败:', err);
					uni.showToast({
						title: '获取巡检结果详情失败',
						icon: 'none'
					});
				})
				.finally(() => {
					this.isLoading = false;
					uni.hideLoading();
				});
		},
		
		// 加载巡检计划详情
		loadPlanDetail() {
			this.isLoading = true;
			// 显示加载中
			uni.showLoading({
			   title: "加载中...",
			});
			const apiCall = this.isRecord 
				? patrolApi.getPatrolRecordDetail(this.planId)
				: patrolApi.getPlanDetail(this.planId);
				
			apiCall.then(res => {
				if (res.code === 200 && res.data) {
					// 转换API返回的数据到页面格式
					const data = res.data;
					this.planInfo = {
						id: this.planId,
						title: data.name,
						code: data.planNo,
						type: data.patrolType,
						startTime: data.startDate,
						endTime: data.endDate,
						location: data.locations,
						manager: data.executorNames?.join('、') || '未分配',
						progress: `0/${data.patrolItems?.length || 0}`, // 进度信息需要计算
						description: data.description || '',
						status: data.status,
						scheduleType: data.scheduleType,
						scheduleWeekDays: data.scheduleWeekDays,
						scheduleMonthDays: data.scheduleMonthDays
					};
					
					// 转换任务列表数据
					if (data.patrolItems && data.patrolItems.length > 0) {
						this.planTasks = data.patrolItems.map(item => ({
							id: item.id,
							title: item.itemName,
							target: item.deviceName || '',
							point: item.checkMethod || '',
							content: item.description,
							standard: item.normalRange || '',
							unit: item.unit,
							actualValue: '',
							isAbnormal: false,
							completedTime: '',
							remark: '',
							status: 'pending', // 默认为待执行
							images: [],
							showDetails: false
						}));
						
						// 更新进度信息
						const completedTasks = this.isRecord ? this.planTasks.length : 0;
						this.planInfo.progress = `${completedTasks}/${this.planTasks.length}`;
					}
					
					//this.calculateStats();
					uni.hideLoading();
				} else {
					uni.hideLoading();
					uni.showToast({
						title: res.message || '获取巡检计划详情失败',
						icon: 'none'
					});
				}
			})
			.catch(err => {
				uni.hideLoading();
				console.error('获取巡检计划详情失败:', err);
				uni.showToast({
					title: '获取巡检计划详情失败',
					icon: 'none'
				});
			})
			.finally(() => {
				this.isLoading = false;
				uni.hideLoading();
			});
		},
		
		// 计算统计信息
		calculateStats() {
			if (!this.planTasks || this.planTasks.length === 0) {
				this.stats = {
					totalTasks: 0,
					completedTasks: 0,
					abnormalCount: 0,
					completionRate: 0
				};
				return;
			}
			
			const totalTasks = this.planTasks.length;
			const completedTasks = this.planTasks.filter(task => task.status === 'completed').length;
			const abnormalCount = this.planTasks.filter(task => task.isAbnormal).length;
			const completionRate = totalTasks > 0 ? Math.floor((completedTasks / totalTasks) * 100) : 0;
			
			this.stats = {
				totalTasks,
				completedTasks,
				abnormalCount,
				completionRate
			};
		},
		
		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				'pending': '待执行',
				'processing': '进行中',
				'completed': '已完成',
				'overdue': '已超时',
				'canceled': '已取消'
			};
			return statusMap[status] || '未知';
		},
		
		// 切换任务详情显示/隐藏
		toggleTaskDetails(index) {
			this.$set(this.planTasks[index], 'showDetails', !this.planTasks[index].showDetails);
		},
		
		// 预览图片
		previewImage(images, current) {
			uni.previewImage({
				urls: images,
				current: images[current]
			});
		},
		
		// 开始巡检
		startPatrol() {
			uni.navigateTo({
				url: `/pages/patrol/execute?id=${this.planId}`
			});
		},
		
		// 继续巡检
		continuePatrol() {
			uni.navigateTo({
				url: `/pages/patrol/execute?id=${this.planId}`
			});
		},
		
		// 完成巡检
		completePatrol() {
			uni.showModal({
				title: '确认完成',
				content: '是否确认完成本次巡检计划？未完成的巡检项将被标记为已跳过。',
				confirmText: '确认',
				cancelText: '取消',
				success: (res) => {
					if (res.confirm) {
						// 实际应用中这里会调用API完成巡检计划
						// uni.request({
						//   url: `/api/patrol/plans/${this.planId}/complete`,
						//   method: 'POST',
						//   success: (res) => {
						//     if (res.data.success) {
						//       uni.showToast({
						//         title: '巡检计划已完成',
						//         icon: 'success'
						//       });
						//       this.loadPlanDetail();
						//     }
						//   }
						// });
						
						// 模拟完成
						uni.showToast({
							title: '巡检计划已完成',
							icon: 'success'
						});
						
						setTimeout(() => {
							this.planInfo.status = 'completed';
							this.planInfo.progress = '12/12';
							this.planTasks.forEach(task => {
								if (task.status === 'pending') {
									task.status = 'skipped';
								}
							});
							this.calculateStats();
						}, 500);
					}
				}
			});
		},
		
		// 导出报告
		exportReport() {
			uni.showToast({
				title: '报告已导出',
				icon: 'success'
			});
		},
		
		// 格式化日期为yyyy-MM-dd
		formatDate(dateStr) {
			if (!dateStr) return '';
			const date = new Date(dateStr);
			if (isNaN(date.getTime())) return dateStr;
			
			const year = date.getFullYear();
			const month = (date.getMonth() + 1).toString().padStart(2, '0');
			const day = date.getDate().toString().padStart(2, '0');
			
			return `${year}-${month}-${day}`;
		},
		
		// 格式化日期时间为yyyy-MM-dd HH:mm:ss
		formatDateTime(dateStr) {
			if (!dateStr) return '';
			const date = new Date(dateStr);
			if (isNaN(date.getTime())) return dateStr;
			
			const year = date.getFullYear();
			const month = (date.getMonth() + 1).toString().padStart(2, '0');
			const day = date.getDate().toString().padStart(2, '0');
			const hour = date.getHours().toString().padStart(2, '0');
			const minute = date.getMinutes().toString().padStart(2, '0');
			const second = date.getSeconds().toString().padStart(2, '0');
			
			return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
		},
		
		// 获取巡检周期文本
		getScheduleText(scheduleType, scheduleWeekDays, scheduleMonthDays) {
			const scheduleMap = {
				'daily': '每日',
				'weekly': '每周',
				'monthly': '每月'
			};
			const scheduleText = scheduleMap[scheduleType] || '未知';
			
			if (scheduleType === 'daily') {
				return scheduleText;
			} else if (scheduleType === 'weekly' && scheduleWeekDays && scheduleWeekDays.length > 0) {
				const weekDayMap = {
					1: '周一',
					2: '周二',
					3: '周三',
					4: '周四',
					5: '周五',
					6: '周六',
					7: '周日',
				};
				
				const weekDays = scheduleWeekDays.map(day => weekDayMap[day] || `周${day}`).join('、');
				return `${scheduleText}（${weekDays}）`;
			} else if (scheduleType === 'monthly' && scheduleMonthDays && scheduleMonthDays.length > 0) {
				const monthDays = scheduleMonthDays.map(day => `${day}日`).join('、');
				return `${scheduleText}（${monthDays}）`;
			}
			
			return scheduleText;
		}
	}
}
</script>

<style lang="scss">
	.patrol-detail-container {
		padding: 20rpx;
		background-color: #f5f5f5;
		// min-height: 100vh;
		// padding-bottom: 120rpx;
	}
	
	.detail-card {
		background-color: #fff;
		border-radius: 12rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
		
		.card-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 20rpx;
			
			.card-title {
				font-size: 32rpx;
				font-weight: bold;
				position: relative;
				padding-left: 20rpx;
				
				&::before {
					content: '';
					position: absolute;
					left: 0;
					top: 8rpx;
					width: 6rpx;
					height: 28rpx;
					background-color: $uni-color-primary;
					border-radius: 3rpx;
				}
			}
			
			.plan-status {
				padding: 6rpx 16rpx;
				border-radius: 4rpx;
				font-size: 24rpx;
				
				&.pending {
					background-color: rgba(24, 144, 255, 0.1);
					color: $uni-color-primary;
				}
				
				&.processing {
					background-color: rgba(82, 196, 26, 0.1);
					color: $uni-color-success;
				}
				
				&.completed {
					background-color: rgba(82, 196, 26, 0.1);
					color: $uni-color-success;
				}
				
				&.overdue {
					background-color: rgba(245, 34, 45, 0.1);
					color: $uni-color-error;
				}
				
				&.canceled {
					background-color: rgba(102, 102, 102, 0.1);
					color: $uni-text-color-grey;
				}
			}
		}
	}
	
	.info-group {
		margin-bottom: 20rpx;
		
		.info-item {
			display: flex;
			margin-bottom: 16rpx;
			
			&:last-child {
				margin-bottom: 0;
			}
			
			.info-label {
				width: 160rpx;
				font-size: 28rpx;
				color: $uni-text-color-grey;
			}
			
			.info-value {
				flex: 1;
				font-size: 28rpx;
				color: $uni-text-color;
				
				&.progress {
					margin-bottom: 8rpx;
				}
			}
			
			.progress-bar {
				flex: 1;
				height: 10rpx;
				background-color: #eee;
				border-radius: 5rpx;
				overflow: hidden;
				margin-top: 10rpx;
				
				.progress-inner {
					height: 100%;
					background-color: $uni-color-primary;
				}
			}
		}
	}
	
	.plan-desc {
		border-top: 1rpx solid #eee;
		padding-top: 20rpx;
		
		.desc-title {
			font-size: 28rpx;
			color: $uni-text-color-grey;
			margin-bottom: 8rpx;
			display: block;
		}
		
		.desc-content {
			font-size: 28rpx;
			color: $uni-text-color;
			line-height: 1.6;
		}
	}
	
	.task-list {
		.task-item {
			border-bottom: 1rpx solid #eee;
			padding: 20rpx 0;
			
			&:last-child {
				border-bottom: none;
			}
			
			.task-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				
				.task-left {
					display: flex;
					align-items: center;
					
					.task-status {
						width: 12rpx;
						height: 12rpx;
						border-radius: 50%;
						margin-right: 16rpx;
						
						&.pending {
							background-color: $uni-text-color-grey;
						}
						
						&.completed {
							background-color: $uni-color-success;
						}
						
						&.skipped {
							background-color: $uni-color-warning;
						}
					}
					
					.task-title {
						font-size: 28rpx;
						font-weight: bold;
						color: $uni-text-color;
					}
				}
				
				.iconfont {
					font-size: 28rpx;
					color: $uni-text-color-grey;
				}
			}
			
			.task-details {
				margin-top: 16rpx;
				padding: 16rpx;
				background-color: #f8f8f8;
				border-radius: 8rpx;
				
				.detail-row {
					display: flex;
					margin-bottom: 12rpx;
					
					&:last-child {
						margin-bottom: 0;
					}
					
					.detail-label {
						width: 120rpx;
						font-size: 26rpx;
						color: $uni-text-color-grey;
					}
					
					.detail-value {
						flex: 1;
						font-size: 26rpx;
						color: $uni-text-color;
						
						&.value-abnormal {
							color: $uni-color-error;
						}
					}
				}
				
				.task-images {
					margin-top: 16rpx;
					
					.images-title {
						font-size: 26rpx;
						color: $uni-text-color-grey;
						margin-bottom: 8rpx;
						display: block;
					}
					
					.image-list {
						display: flex;
						flex-wrap: wrap;
						
						image {
							width: 140rpx;
							height: 140rpx;
							margin-right: 10rpx;
							margin-bottom: 10rpx;
							border-radius: 8rpx;
						}
					}
				}
			}
		}
	}
	
	.stats-container {
		display: flex;
		justify-content: space-between;
		
		.stats-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			
			.stats-value {
				font-size: 36rpx;
				font-weight: bold;
				color: $uni-color-primary;
				margin-bottom: 8rpx;
			}
			
			.stats-label {
				font-size: 24rpx;
				color: $uni-text-color-grey;
			}
		}
	}
	
	.action-buttons {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		padding: 20rpx;
		background-color: #fff;
		display: flex;
		justify-content: center;
		box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);
		
		.action-btn {
			flex: 1;
			height: 80rpx;
			line-height: 80rpx;
			text-align: center;
			border-radius: 8rpx;
			font-size: 30rpx;
			margin: 0 10rpx;
			
			&.start {
				background-color: $uni-color-primary;
				color: #fff;
			}
			
			&.continue {
				background-color: $uni-color-success;
				color: #fff;
			}
			
			&.complete {
				background-color: $uni-color-warning;
				color: #fff;
			}
			
			&.export {
				background-color: $uni-color-primary;
				color: #fff;
			}
		}
	}
</style> 