Page({
  data: {
    orders: [],
    page: 1,
    pageSize: 20,
    total: 0,
    loading: false,
    hasMore: true,
    selectedDate: '',
    currentStatus: ''
  }, 
  onLoad() {
    this.loadData(true);
  },

  // 日期选择处理
  onDateChange(e) {
    const selectedDate = e.detail.value;
    this.setData({ selectedDate });
    this.loadData(true);
  },

  // 状态筛选处理
  filterByStatus(e) {
    const status = e.currentTarget.dataset.status;
    this.setData({ currentStatus: status });
    this.loadData(true);
  },

  // 跳转到工单详情页
  goToDetail(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/workorder/detail/index?id=${orderId}`
    });
  },

  // 处理接单按钮点击
  handleAccept(e) {
    const orderId = e.currentTarget.dataset.id;
    const userInfo = wx.getStorageSync('userInfo');

    console.log(userInfo); 

    if (!userInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '接单确认',
      content: '确认接收该维修工单？',
      success: (res) => {
        if (res.confirm) {
          wx.request({
            url: 'http://localhost:5000/api/workorder/accept',
            method: 'POST',
            data: {
              orderId: orderId,
              action: 'accept',
              repairUserId: userInfo.id,
              operatorId: userInfo.id
            },
            success: (res) => {
              if (res.data.success) {
                wx.showToast({
                  title: '接单成功',
                  icon: 'success'
                });
                this.loadData(true);
              } else {
                wx.showToast({
                  title: res.data.message || '接单失败',
                  icon: 'none'
                });
              }
            }
          });
        }
      }
    });
  },

  loadData(reset = false) {
    if (this.data.loading) return;
    
    if (reset) {
      this.setData({
        page: 1,
        orders: [],
        hasMore: true
      });
    }

    this.setData({ loading: true }); 
    wx.request({
      url: 'http://localhost:5000/api/workorder/list',
      method: 'GET',
      data: {
        uid: wx.getStorageSync("userId"),
        date: this.data.selectedDate,
        status: this.data.currentStatus, 
      },
      success: (res) => {
        if (res.data.success) {
          const { list, total } = res.data.data;
          this.setData({
            orders: [...this.data.orders, ...list],
            total,
            hasMore: this.data.orders.length + list.length < total
          });
        } else {
          wx.showToast({
            title: res.data.message || '加载失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({ loading: false });
        wx.stopPullDownRefresh();
      }
    });
  },

  loadMore() {
    if (this.data.hasMore && !this.data.loading) {
      this.setData({
        page: this.data.page + 1
      }, () => {
        this.loadData();
      });
    }
  },

  onPullDownRefresh() {
    this.loadData(true);
    wx.stopPullDownRefresh();
  },

  onReachBottom() {
    this.loadMore();
  }
}); 