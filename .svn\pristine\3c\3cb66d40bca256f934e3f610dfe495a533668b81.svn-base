<template>
  <view class="fault-list-container">
    <!-- Tab 切换 -->
    <view class="tab-container">
      <view 
        class="tab-item" 
        :class="{ active: activeTab === 'all' }" 
        @click="switchTab('all')"
      >
        全部
      </view>
      <view 
        class="tab-item" 
        :class="{ active: activeTab === 'pending' }" 
        @click="switchTab('pending')"
      >
        待确认
      </view>
      <view 
        class="tab-item" 
        :class="{ active: activeTab === 'confirmed' }" 
        @click="switchTab('confirmed')"
      >
        已确认
      </view>
      <view 
        class="tab-item" 
        :class="{ active: activeTab === 'returned' }" 
        @click="switchTab('returned')"
      >
        已退回
      </view>
    </view>
    
    <!-- 顶部筛选 -->
    <view class="filter-section">
      <view class="date-filter">
        <picker mode="date" :value="filterDate" @change="onDateChange">
          <view class="date-picker">
            <text class="date-text">{{ filterDate || "选择日期" }}</text>
          </view>
        </picker>
      </view>

      <button class="refresh-button" @click="refreshData">重置</button>
    </view>

    <!-- 故障列表 -->
    <view class="fault-list" v-if="faultList.length > 0">
      <view
        class="fault-item"
        v-for="fault in faultList"
        :key="fault.fault_id"
        @click="viewFaultDetail(fault.fault_id)"
      >
        <view class="fault-header">
          <text class="heat-unit-name">{{ fault.heat_unit_name }}</text>
          <view class="fault-status" :class="getFaultStatusClass(fault.fault_status)">
            {{ fault.fault_status }}
          </view>
        </view>

        <view class="fault-content">
          <view class="fault-desc">{{ fault.fault_desc }}</view>

          <view class="fault-info">
            <view class="info-item">
              <text class="info-label">发生时间：</text>
              <text class="info-value">{{ formatTimestamp(fault.occur_time) }}</text>
            </view>
           
			<view class="info-item">
			  <text class="info-label">故障等级：</text>
			  <text
			    class="info-value level-tag"
			    :class="getFaultLevelClass(fault.fault_level)"
			  >
			    {{ fault.fault_level }}
			  </text>
			</view>
          </view>

          <view class="fault-footer">
            <view class="report-info">
              <text class="reporter">{{ fault.report_user_name }}</text>
              <text class="report-time">{{ formatTimestamp(fault.report_time) }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" v-if="!isLoading && faultList.length === 0">
      <image
        class="empty-image"
        src="/static/images/empty-state.png"
        mode="aspectFit"
      ></image>
      <text class="empty-text">暂无故障记录</text>
	  <button class="refresh-button" @click="refreshData">刷新</button>
    </view>

    <!-- 加载中 -->
    <view class="loading-container" v-if="isLoading">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 加载失败 -->
    <view class="error-container" v-if="loadError && !isLoading">
      <text class="error-text">加载失败，请重试</text>
      <button class="retry-btn" @click="loadFaultList">重新加载</button>
    </view>

    <!-- 底部添加按钮 -->
    <PermissionCheck permission="fault:report:create">
      <view class="floating-button" @click="navigateToReport">
        <text class="plus-icon">+</text>
      </view>
    </PermissionCheck>
  </view>
</template>

<script>
import { faultApi, heatUnitApi } from "@/utils/api.js";
import PermissionCheck from "@/components/PermissionCheck.vue";

export default {
  components: {
    PermissionCheck, // 本地注册组件
  },
  data() {
    // 获取当前日期，格式为 YYYY-MM-DD
    const now = new Date();
    const year = now.getFullYear();
    const month = (now.getMonth() + 1).toString().padStart(2, "0");
    const day = now.getDate().toString().padStart(2, "0");
    const today = `${year}-${month}-${day}`;

    return {
      faultList: [],
      isLoading: false,
      loadError: false,

      // 当前激活的标签页
      activeTab: 'all', // 'all', 'pending', 'confirmed', 'returned'

      // 筛选条件
      filterDate: "",
      currentStatus: "",

      // 分页相关
      page: 1,
      pageSize: 5,
      hasMore: true,
    };
  },
  onLoad() {
   this.page = 1;
   this.faultList = [];
   this.hasMore = true;
   // 加载故障列表
   this.loadFaultList().then(() => {
     uni.stopPullDownRefresh();
   });
  },
  onShow()
  {
     // 检查是否需要刷新列表数据
     const app = getApp();
     const needRefresh = app.globalData && app.globalData.refreshFaultList;
     
     if (needRefresh) {
        // 重置页码和列表
        this.page = 1;
        this.faultList = [];
        this.hasMore = true;
        
        // 清除刷新标记
        app.globalData.refreshFaultList = false;
        
        // 加载数据
        this.loadFaultList();
     } else {
        // 如果列表为空，则初始化加载
        if (this.faultList.length === 0) {
           this.loadFaultList();
        }
     }
  },
  // 下拉刷新
  onPullDownRefresh() {
    this.page = 1;
    this.faultList = [];
    this.hasMore = true;
    this.loadFaultList().then(() => {
      uni.stopPullDownRefresh();
    });
  },
  // 上拉加载更多
  onReachBottom() {
    if (this.hasMore && !this.isLoading) {
      this.page++;
      this.loadFaultList(true);
    }
  },
  methods: {
    // 切换标签页
    switchTab(tab) {
      if (this.activeTab !== tab) {
        this.activeTab = tab;
        this.page = 1;
        this.faultList = [];
        this.hasMore = true;
        
        // 根据选中的标签页设置状态过滤
        switch(tab) {
          case 'all':
            this.currentStatus = "";
            break;
          case 'pending':
            this.currentStatus = "待确认";
            break;
          case 'confirmed':
            this.currentStatus = "已确认";
            break;
          case 'returned':
            this.currentStatus = "已退回";
            break;
        }
        
        this.loadFaultList();
      }
    },
    
    // 格式化时间戳为 yyyy-MM-dd HH:mm:ss 格式
    formatTimestamp(timestamp) {
      if (!timestamp) return '';
      
      // 如果时间戳是字符串，转换为数字
      const ts = typeof timestamp === 'string' ? Number(timestamp) : timestamp;
      
      // 检查时间戳长度，如果是13位则直接使用，如果是10位则乘以1000
      const date = new Date(ts.toString().length === 13 ? ts : ts * 1000);
      
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const seconds = date.getSeconds().toString().padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
	
    // 加载故障列表
    async loadFaultList(isLoadMore = false) {
      if (this.isLoading) return;

      this.isLoading = true;
      if (!isLoadMore) {
        this.loadError = false;
      }

      try {
        // 构建查询参数
        const params = {
          page: this.page,
          pageSize: this.pageSize,
        };

        // 添加日期筛选
        if (this.filterDate) {
          params.date = this.filterDate;
        }

        // 添加状态筛选
        if (this.currentStatus) {
          params.status = this.currentStatus;
        }
        
        // 添加用户的项目权限IDs (必须参数)
        const heatUnitId = uni.getStorageSync('heatUnitId');
		console.log("heatUnitId==",heatUnitId)
        if (!heatUnitId) {
          // 如果没有热用户ID权限，显示错误并返回
          uni.showToast({
            title: "无项目访问权限",
            icon: "none",
          });
          this.loadError = true;
          this.isLoading = false;
          return;
        }
        
        // 设置热用户ID参数
        params.heatUnitId = heatUnitId;

        // 调用API获取故障列表
        const res = await faultApi.getFaultList(params);

        if (res.code === 200) {
          const { list, total, totalPages } = res.data;
          
          if (isLoadMore) {
            // 加载更多模式：追加数据
            this.faultList = [...this.faultList, ...list];
          } else {
            // 初始加载模式：替换数据
            this.faultList = list;
          }

          // 判断是否还有更多数据
          this.hasMore = this.page < totalPages;
        } else {
          this.loadError = true;
          uni.showToast({
            title: res.message || "获取故障列表失败",
            icon: "none",
          });
        }
      } catch (err) {
        console.error("获取故障列表异常:", err);
        this.loadError = true;
        uni.showToast({
          title: "网络异常，请稍后重试",
          icon: "none",
        });
      } finally {
        this.isLoading = false;
      }
    },

    // 刷新
    async refreshData() {
      this.page = 1;
      this.hasMore = true;
      this.filterDate = "";
      this.faultList = [];
      this.activeTab = 'all';
      this.currentStatus = "";
      
      // 重新加载故障列表
      this.loadFaultList();
    },
    // 查看故障详情
    viewFaultDetail(faultId) {
      uni.navigateTo({
        url: `/pages/fault/detail?id=${faultId}`,
      });
    },

    // 导航到故障上报页面
    navigateToReport() {
      uni.navigateTo({
        url: "/pages/fault/createfault",
      });
    },

    // 日期选择变更
    onDateChange(e) {
      this.filterDate = e.detail.value;
      this.page = 1; // 重置页码
      this.faultList = []; // 清空列表
      this.hasMore = true; // 重置加载更多状态
      this.loadFaultList();
    },

    // 获取故障状态对应的样式类
    getFaultStatusClass(status) {
      const statusClassMap = {
        待确认: "pending",
        已确认: "confirmed",
        已完成: "completed",
        已退回: "returned",
      };
      return statusClassMap[status] || "default";
    },

    // 获取故障等级对应的样式类
    getFaultLevelClass(level) {
      const levelClassMap = {
        提示: "notice",
        一般: "normal",
        重要: "important",
        严重: "serious ",
		紧急: "critical",
      };
      return levelClassMap[level] || "default";
    },
  },
};
</script>

<style lang="scss">
.fault-list-container {
  background-color: #f5f7fa;
  min-height: 100vh;
  padding-bottom: 120rpx; // 为底部按钮留出空间
}

/* Tab切换 */
.tab-container {
  display: flex;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
  position: sticky;
  top: 0;
  z-index: 999;
  
  .tab-item {
    flex: 1;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    font-size: 30rpx;
    color: #666;
    position: relative;
    
    &.active {
      color: #007aff;
      font-weight: 500;
      
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 60rpx;
        height: 4rpx;
        background-color: #007aff;
        border-radius: 2rpx;
      }
    }
  }
}

// 筛选部分
.filter-section {
  background-color: #ffffff;
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 20rpx;
  position: sticky;
  top: 80rpx;
  z-index: 998;
  
  .date-filter {
    flex: 1;
    height: 70rpx;

    .date-picker {
      display: flex;
      align-items: center;
      height: 70rpx;
      border: 1px solid #e5e5e5;
      border-radius: 6rpx;
      padding: 0 20rpx;
      font-size: 28rpx;
      color: #333;

      .date-text {
        flex: 1;
      }

      .iconfont {
        margin-left: 10rpx;
        color: #999;
      }
    }
  }

  .date-filter {
    margin-right: 20rpx;
  }
}

// 故障列表
.fault-list {
  padding: 0 30rpx;

  .fault-item {
    background-color: #ffffff;
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    overflow: hidden;

    .fault-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx 30rpx;
      border-bottom: 1px solid #f0f0f0;

      .heat-unit-name {
        font-size: 32rpx;
        font-weight: 500;
        color: #333;
      }

      .fault-status {
        padding: 6rpx 16rpx;
        border-radius: 6rpx;
        font-size: 24rpx;

        &.pending {
          background-color: #fff7e6;
          color: #fa8c16;
        }

        &.confirmed {
          background-color: #e6f7ff;
          color: #1890ff;
        }

        &.completed {
          background-color: #f6ffed;
          color: #52c41a;
        }
        
        &.returned {
          background-color: #fff1f0;
          color: #f5222d;
        }

        &.default {
          background-color: #f5f5f5;
          color: #999;
        }
      }
    }

    .fault-content {
      padding: 30rpx;

      .fault-desc {
        font-size: 28rpx;
        color: #333;
        line-height: 1.6;
        margin-bottom: 20rpx;
      }

      .fault-info {
        margin-bottom: 20rpx;

        .info-item {
          display: flex;
          align-items: center;
          margin-bottom: 10rpx;

          .info-label {
            font-size: 26rpx;
            color: #999;
            width: 150rpx;
          }

          .info-value {
            font-size: 26rpx;
            color: #333;
          }

          .level-tag {
            padding: 4rpx 12rpx;
            border-radius: 4rpx;
            font-size: 24rpx;

            &.notice {
              background-color: #e6f7ff;
              color: #1890ff;
            }

            &.normal {
              background-color: #e6f7ff;
              color: #1890ff;
            }

            &.important {
              background-color: #fff7e6;
              color: #fa8c16;
            }
             &.serious {
              background-color: #ffebee;
              color: #EF5350;
            }
            &.critical {
              background-color: #fff1f0;
              color: #f5222d;
            }

            &.default {
              background-color: #f5f5f5;
              color: #999;
            }
          }
        }
      }

      .fault-footer {
        display: flex;
        justify-content: flex-end;

        .report-info {
          font-size: 24rpx;
          color: #999;

          .reporter {
            margin-right: 10rpx;
          }
        }
      }
    }
  }
}
/* 重置按钮 */
.refresh-button {
    font-size: 28rpx;
    background-color: #fff;
    border-radius: 5rpx;
    align-items: center;
    height: 72rpx;
    border-radius: 6rpx;
    padding: 0 20rpx;
    font-size: 28rpx;
    position: relative;
    min-width: 80rpx;
  }
// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .empty-image {
    width: 240rpx;
    height: 240rpx;
    margin-bottom: 30rpx;
  }

  .empty-text {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 40rpx;
  }

  .refresh-button {
    font-size: 28rpx;
    color: #007aff;
    background-color: #fff;
    border: 2rpx solid #007aff;
    border-radius: 40rpx;
    padding: 10rpx 60rpx;
  }
}

// 加载状态
.loading-container {
  display: flex;
  justify-content: center;
  padding: 30rpx 0;

  .loading-text {
    font-size: 28rpx;
    color: #999;
  }
}

// 错误状态
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 50rpx 0;

  .error-text {
    font-size: 28rpx;
    color: #f5222d;
    margin-bottom: 20rpx;
  }

  .retry-btn {
    font-size: 28rpx;
    color: #fff;
    background-color: #1890ff;
    padding: 8rpx 30rpx;
    border-radius: 30rpx;
  }
}

.floating-button {
  position: fixed;
  right: 30rpx;
  bottom: 30rpx;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: #0088ff;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 136, 255, 0.4);
  z-index: 10;

  .plus-icon {
    font-size: 60rpx;
    color: #fff;
    font-weight: normal;
    line-height: 60rpx;
    margin-top: -3rpx;
  }
}

// 热用户选择器
.heat-unit-filter-section {
  background-color: #ffffff;
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 20rpx;
}

.heat-unit-filter {
  flex: 1;
  height: 70rpx;

  .heat-unit-picker {
    display: flex;
    align-items: center;
    height: 70rpx;
    border: 1px solid #e5e5e5;
    border-radius: 6rpx;
    padding: 0 20rpx;
    font-size: 28rpx;
    color: #333;

    .heat-unit-text {
      flex: 1;
    }

    .iconfont {
      margin-left: 10rpx;
      color: #999;
    }
  }
}
</style>
