/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-popup[data-v-7db519c7] {
  position: fixed;
  z-index: 99;
}
.uni-popup.top[data-v-7db519c7], .uni-popup.left[data-v-7db519c7], .uni-popup.right[data-v-7db519c7] {
  top: 0;
}
.uni-popup .uni-popup__wrapper[data-v-7db519c7] {
  display: block;
  position: relative;
  /* iphonex 等安全区设置，底部安全区适配 */
}
.uni-popup .uni-popup__wrapper.left[data-v-7db519c7], .uni-popup .uni-popup__wrapper.right[data-v-7db519c7] {
  padding-top: 0;
  flex: 1;
}
.fixforpc-z-index[data-v-7db519c7] {
  z-index: 999;
}
.fixforpc-top[data-v-7db519c7] {
  top: 0;
}

.station-control {
  min-height: 100vh;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: column;
}
.page-title {
  background-color: #0088ff;
  color: #fff;
  padding: 0.625rem 0.9375rem;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  font-weight: bold;
}
.back-button {
  position: absolute;
  left: 0.9375rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1.875rem;
  height: 1.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}
.tab-section {
  background-color: #0088ff;
  padding: 0 0.625rem 0.5rem;
}
.tab-bar {
  display: flex;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 0.25rem;
  padding: 0.1875rem;
}
.tab-item {
  flex: 1;
  text-align: center;
  padding: 0.5rem 0;
  font-size: 0.875rem;
  border-radius: 0.1875rem;
  color: #fff;
}
.tab-item.active {
  background-color: rgba(255, 255, 255, 0.9);
  color: #0088ff;
  font-weight: bold;
}
.station-selector {
  background-color: #fff;
  padding: 0.9375rem;
  display: flex;
  align-items: center;
  border-bottom: 0.03125rem solid #eee;
}
.station-label {
  font-size: 0.9375rem;
  color: #666;
  min-width: 4.6875rem;
}
.station-value-wrapper {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #e5e5e5;
  padding: 0.375rem 0.625rem;
  border-radius: 0.1875rem;
}
.station-value-text {
  font-size: 0.9375rem;
  color: #333;
  font-weight: bold;
}
.station-value-text.placeholder {
  color: #999;
  font-weight: normal;
}
.station-arrow {
  font-size: 0.9375rem;
  color: #999;
}
.station-content {
  flex: 1;
  padding: 0.625rem;
}
.card {
  background-color: #fff;
  border-radius: 0.375rem;
  padding: 0.9375rem;
  margin-bottom: 0.625rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.card-title {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.625rem;
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 0.5rem;
}
.info-row {
  display: flex;
  padding: 0.5rem 0;
}
.label {
  color: #666;
  width: 5.625rem;
}
.value {
  color: #333;
  flex: 1;
}
.value.online {
  color: #00c853;
}
.value.offline {
  color: #ef5350;
}
.value.fault {
  color: #ff9800;
}
.data-grid {
  display: flex;
  flex-wrap: wrap;
}
.data-item {
  width: 50%;
  padding: 0.625rem 0;
  display: flex;
  flex-direction: column;
}
.data-label {
  font-size: 0.8125rem;
  color: #666;
  margin-bottom: 0.3125rem;
}
.data-value {
  font-size: 1.125rem;
  color: #0088ff;
  font-weight: bold;
}
.control-grid {
  display: flex;
  flex-wrap: wrap;
}
.control-item {
  width: 50%;
  padding: 0.625rem 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.control-label {
  font-size: 0.875rem;
  color: #333;
}
.slider-control {
  margin: 0.9375rem 0;
}
.slider-label {
  font-size: 0.875rem;
  color: #333;
  margin-bottom: 0.625rem;
  display: block;
}
.slider-box {
  padding: 0 0.625rem;
}
.process-view {
  background-color: #fff;
  border-radius: 0.375rem;
  padding: 0.9375rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.process-image {
  width: 100%;
  text-align: center;
}
.process-image uni-image {
  width: 100%;
  max-width: 21.5625rem;
}
.process-legend {
  display: flex;
  justify-content: center;
  margin-top: 0.9375rem;
}
.legend-item {
  display: flex;
  align-items: center;
  margin: 0 0.625rem;
}
.legend-color {
  width: 0.9375rem;
  height: 0.5rem;
  margin-right: 0.3125rem;
}
.legend-text {
  font-size: 0.75rem;
  color: #666;
}
.no-station {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #999;
  font-size: 1rem;
}

/* 站点选择器样式 */
.station-picker {
  background-color: #f5f5f5;
  border-top-left-radius: 0.625rem;
  border-top-right-radius: 0.625rem;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}
.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.9375rem;
  background-color: #fff;
}
.picker-title {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
}
.picker-close {
  font-size: 0.875rem;
  color: #666;
}

/* 搜索栏样式 */
.search-bar {
  padding: 0.625rem 0.9375rem;
  background-color: #f5f5f5;
}
.search-input-wrap {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 0.25rem;
  padding: 0 0.625rem;
  height: 2.5rem;
}
.search-icon {
  margin-right: 0.3125rem;
  font-size: 0.875rem;
  color: #999;
}
.search-input {
  flex: 1;
  height: 2.5rem;
  font-size: 0.875rem;
}
.search-clear {
  width: 1.25rem;
  height: 1.25rem;
  line-height: 1.25rem;
  text-align: center;
  color: #999;
  font-size: 0.9375rem;
}

/* 状态筛选标签样式 */
.filter-tabs {
  display: flex;
  padding: 0.625rem 0.625rem;
  background-color: #f5f5f5;
}
.filter-tab {
  padding: 0.375rem 0.75rem;
  margin: 0 0.3125rem;
  font-size: 0.875rem;
  color: #666;
  background-color: #fff;
  border-radius: 1.5625rem;
}
.filter-tab.active {
  color: #fff;
  background-color: #0088ff;
}

/* 站点列表样式 */
.station-list {
  flex: 1;
  max-height: 60vh;
}
.station-item {
  margin: 0.625rem;
  background-color: #fff;
  border-radius: 0.25rem;
  overflow: hidden;
  box-shadow: 0 0.0625rem 0.25rem rgba(0, 0, 0, 0.05);
}
.station-item-content {
  position: relative;
  padding: 0.75rem;
}
.station-main-info {
  margin-bottom: 0.5rem;
}
.station-name {
  font-size: 0.9375rem;
  color: #333;
  font-weight: bold;
  margin-bottom: 0.25rem;
  display: block;
}
.station-address {
  font-size: 0.75rem;
  color: #999;
  display: block;
}
.station-temp-info {
  margin-top: 0.375rem;
  display: flex;
  justify-content: space-between;
}
.temp-item {
  font-size: 0.75rem;
  color: #666;
  flex: 1;
}
.station-status-tag {
  position: absolute;
  right: 0.75rem;
  bottom: 0.75rem;
  font-size: 0.75rem;
  padding: 0.125rem 0.375rem;
  border-radius: 0.125rem;
}
.station-status-tag.online {
  color: #00c853;
  background-color: rgba(0, 200, 83, 0.1);
}
.station-status-tag.offline {
  color: #ff5252;
  background-color: rgba(255, 82, 82, 0.1);
}
.station-status-tag.fault {
  color: #ff9800;
  background-color: rgba(255, 152, 0, 0.1);
}
.empty-tip {
  padding: 1.875rem 0;
  text-align: center;
  color: #999;
  font-size: 0.875rem;
  background-color: #f5f5f5;
}

/* 工作模式卡片样式 */
.mode-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}
.mode-title {
  font-size: 0.9375rem;
  color: #333;
  margin-right: 0.625rem;
}
.mode-buttons {
  display: flex;
  background-color: #f0f0f0;
  border-radius: 0.25rem;
  overflow: hidden;
}
.mode-button {
  padding: 0.375rem 1.25rem;
  font-size: 0.875rem;
  color: #666;
  text-align: center;
}
.mode-button.active {
  background-color: #0088ff;
  color: #fff;
}
.mode-desc {
  font-size: 0.75rem;
  color: #999;
  margin-top: 0.5rem;
}

/* 设备控制卡片样式 */
.device-section {
  margin-bottom: 0.625rem;
}
.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}
.blue-indicator {
  width: 0.25rem;
  height: 1rem;
  background-color: #0088ff;
  margin-right: 0.3125rem;
}
.section-title-text {
  font-size: 0.9375rem;
  color: #333;
  font-weight: bold;
}
.device-control-block {
  background-color: #fff;
  border: 0.03125rem solid #eeeeee;
  border-radius: 0.25rem;
  overflow: hidden;
  padding: 0.625rem;
}
.device-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.625rem;
}
.selector-wrap {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.dropdown-label {
  font-size: 0.75rem;
  color: #999;
  margin-bottom: 0.1875rem;
}
.dropdown-value {
  display: flex;
  align-items: center;
  border-bottom: 1px dashed #e0e0e0;
  padding-bottom: 0.25rem;
}
.device-name {
  font-size: 0.875rem;
  color: #333;
  font-weight: bold;
}
.dropdown-arrow {
  font-size: 0.625rem;
  color: #0088ff;
  margin-left: 0.3125rem;
}
.device-control-params {
  padding-top: 0.3125rem;
}
.param-label {
  font-size: 0.8125rem;
  color: #666;
  margin-bottom: 0.5rem;
  display: block;
}
.slider-row {
  display: flex;
  align-items: center;
}
.param-value {
  width: 1.5625rem;
  text-align: right;
  font-size: 0.875rem;
  color: #333;
  margin-left: 0.3125rem;
}

/* 控制算法卡片样式 */
.algorithm-selector {
  margin-bottom: 0.625rem;
}
.algorithm-item {
  display: flex;
  align-items: center;
  padding: 0.5rem 0;
}
.algorithm-radio {
  display: flex;
  align-items: center;
}
.algorithm-label {
  font-size: 0.875rem;
  color: #333;
  margin-left: 0.15625rem;
}
.algorithm-desc {
  font-size: 0.75rem;
  color: #666;
  padding: 0.3125rem;
  background-color: #f9f9f9;
  border-radius: 0.1875rem;
  margin-bottom: 0.625rem;
}
.algorithm-params {
  margin-top: 0.625rem;
}
.param-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.625rem;
}
.param-input {
  flex: 1;
  height: 2.1875rem;
  border: 0.03125rem solid #e5e5e5;
  border-radius: 0.1875rem;
  padding: 0 0.625rem;
  font-size: 0.875rem;
}
.param-unit {
  font-size: 0.875rem;
  color: #666;
  margin-left: 0.3125rem;
}
.apply-button {
  background-color: #0088ff;
  color: #fff;
  font-size: 0.875rem;
  padding: 0.5rem 0;
  border-radius: 0.1875rem;
  margin-top: 0.625rem;
}
.apply-button[disabled] {
  background-color: #cccccc;
  color: #999;
}

/* 新增样式 */
.confirm-button-row {
  display: flex;
  justify-content: flex-end;
  margin-top: 0.625rem;
}
.confirm-button {
  background-color: #0088ff;
  color: #fff;
  font-size: 0.875rem;
  padding: 0.375rem 0.75rem;
  border-radius: 0.1875rem;
}
.confirm-button[disabled] {
  background-color: #cccccc;
  color: #999;
}

/* 设备控制标签页样式 */
.device-tabs {
  display: flex;
  margin-bottom: 0.625rem;
  border-bottom: 0.03125rem solid #f0f0f0;
}
.device-tab {
  flex: 1;
  text-align: center;
  padding: 0.625rem 0;
  font-size: 0.875rem;
  color: #666;
  position: relative;
}
.device-tab.active {
  color: #0088ff;
  font-weight: bold;
}
.device-tab.active:after {
  content: "";
  position: absolute;
  bottom: -0.0625rem;
  left: 50%;
  transform: translateX(-50%);
  width: 40%;
  height: 0.125rem;
  background-color: #0088ff;
  border-radius: 0.0625rem;
}

/* 工业变频器风格的频率控制样式 */
.frequency-control {
  display: flex;
  margin: 0.625rem 0;
  border: 0.0625rem solid #e0e0e0;
  border-radius: 0.25rem;
  overflow: hidden;
}
.frequency-control.disabled {
  opacity: 0.6;
}
.frequency-display {
  flex: 1;
  background: #fff;
  padding: 0 0.625rem;
  display: flex;
  align-items: center;
  border: 0.03125rem solid #e0e0e0;
  height: 2.5rem;
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}
.frequency-input {
  flex: 1;
  height: 2.5rem;
  font-size: 1.25rem;
  color: #333;
  font-weight: bold;
  text-align: right;
  background-color: transparent;
}
.frequency-unit {
  font-size: 0.9375rem;
  color: #666;
  margin-left: 0.3125rem;
  width: 1.25rem;
}
.frequency-buttons {
  width: 2.5rem;
  display: flex;
  flex-direction: column;
}
.frequency-button {
  height: 1.25rem;
  line-height: 1.25rem;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #0088ff;
  color: white;
  font-size: 0.75rem;
}
.frequency-button.disabled {
  background-color: #cccccc;
}
.frequency-button.increase {
  border-bottom: 0.03125rem solid rgba(255, 255, 255, 0.3);
}

