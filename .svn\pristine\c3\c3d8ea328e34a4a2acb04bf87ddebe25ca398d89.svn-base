<<<<<<< .mine
package com.heating.service;
 

import com.heating.dto.fault.FaultReportRequest;    
import com.heating.dto.fault.FaultSetStatusRequest;
import com.heating.dto.fault.FaultWeeklyCountResponse;
import com.heating.dto.fault.FaultMessageResponse;
import org.springframework.data.repository.query.Param;

import java.sql.Date;
import java.util.List;
import java.util.Map;

public interface FaultService {
    void reportFault(FaultReportRequest request); 
    Map<String, Object> getFaultList(String status, Date date, String heatUnitId, Integer page, Integer pageSize);
    Map<String, Object> getFaultDetail(long faultId);
    Map<String, Object> getFaultStatistics();
    List<Map<String, Object>> getRecentFaults();     
    void setFaultStatus(FaultSetStatusRequest request);
    
    /**
     * 获取本周故障告警数量
     * @return 本周故障告警数量
     */
    FaultWeeklyCountResponse getWeeklyFaultCount();
    
    /**
     * 根据管理员ID查询故障列表
     * @param managerId 管理员ID
     * @return 故障列表
     */
    List<Map<String, Object>> getFaultsByManagerId(Long managerId);
    
    /**
     * 获取故障消息
     * 从故障信息表中获取状态为 '待确认' 的记录列表
     * 
     * @param userId 用户ID
     * @param role 用户角色
     * @param heatUnitId 用户项目权限，逗号分隔的项目ID字符串
     * @return 故障消息列表
     */
    List<FaultMessageResponse> getFaultMessages(Long userId, String role, String heatUnitId);
}||||||| .r0
=======
package com.heating.service;
 

import com.heating.dto.fault.FaultReportRequest;    
import com.heating.dto.fault.FaultSetStatusRequest;
import com.heating.dto.fault.FaultWeeklyCountResponse;
import com.heating.dto.fault.FaultMessageResponse;
import org.springframework.data.repository.query.Param;

import java.sql.Date;
import java.util.List;
import java.util.Map;

public interface FaultService {
    void reportFault(FaultReportRequest request); 
    Map<String, Object> getFaultList(String status, Date date, Long reportUserId, String role, Integer page, Integer pageSize);
    Map<String, Object> getFaultDetail(long faultId);
    Map<String, Object> getFaultStatistics();
    List<Map<String, Object>> getRecentFaults();     
    void setFaultStatus(FaultSetStatusRequest request);
    
    /**
     * 获取本周故障告警数量
     * @return 本周故障告警数量
     */
    FaultWeeklyCountResponse getWeeklyFaultCount();
    
    /**
     * 根据管理员ID查询故障列表
     * @param managerId 管理员ID
     * @return 故障列表
     */
    List<Map<String, Object>> getFaultsByManagerId(Long managerId);
    
    /**
     * 获取故障消息
     * 获取当前所有需要处理的故障消息列表
     * 
     * @return 故障消息列表
     */
    List<FaultMessageResponse> getFaultMessages();
}>>>>>>> .r5108
