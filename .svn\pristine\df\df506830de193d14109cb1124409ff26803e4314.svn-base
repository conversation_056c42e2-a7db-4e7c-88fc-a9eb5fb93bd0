<template>
  <view class="container"> 
    <!-- 添加页面标题 -->
    <view class="page-title">
      <text>创建巡检记录</text>
    </view>
    
    <!-- 表单内容区域 -->
    <scroll-view scroll-y class="form-container">
      <!-- 巡检计划选择 -->
      <view class="form-group">
        <text class="form-label required">巡检计划</text>
        <view class="form-input select-box" @click="showPlanSelector">
          <text v-if="formData.patrol_plan_id">{{ selectedPlanName }}</text>
          <text v-else class="placeholder">请选择巡检计划</text>
          <text class="select-arrow">▼</text>
        </view>
      </view>
      
      <!-- 执行人选择 -->
      <view class="form-group">
        <text class="form-label required">执行人</text>
        <view class="form-input select-box" @click="showExecutorSelector">
          <text v-if="formData.executor_ids && formData.executor_ids.length > 0">
            已选择 {{ formData.executor_ids.length }} 人{{ selectedExecutorNames ? '：' + selectedExecutorNames : '' }}
          </text>
          <text v-else class="placeholder">请选择执行人</text>
          <text class="select-arrow">▼</text>
        </view>
      </view>
      
      <!-- 开始时间 -->
      <view class="form-group">
        <text class="form-label required">开始时间</text>
        <view class="form-input select-box" @click="showStartTimePicker">
          <text v-if="formData.start_time">{{ formData.start_time }}</text>
          <text v-else class="placeholder">请选择开始时间</text>
          <text class="select-arrow">▼</text>
        </view>
      </view>
      
      <!-- 结束时间 -->
      <view class="form-group">
        <text class="form-label required">结束时间</text>
        <view class="form-input select-box" @click="showEndTimePicker">
          <text v-if="formData.end_time">{{ formData.end_time }}</text>
          <text v-else class="placeholder">请选择结束时间</text>
          <text class="select-arrow">▼</text>
        </view>
      </view>
      
      <!-- 记录状态 -->
      <view class="form-group">
        <text class="form-label required">记录状态</text>
        <view class="status-group">
          <view 
            v-for="status in statusOptions" 
            :key="status.value" 
            class="status-option" 
            :class="{ active: formData.status === status.value }"
            @click="formData.status = status.value"
          >
            <text class="status-dot"></text>
            <text class="status-text">{{ status.label }}</text>
          </view>
        </view>
      </view>
      
      <!-- 备注信息 -->
      <view class="form-group">
        <text class="form-label">备注信息</text>
        <textarea 
          class="form-textarea" 
          v-model="formData.remark" 
          placeholder="请输入巡检记录备注信息" 
          maxlength="200"
        ></textarea>
        <text class="char-count">{{ formData.remark.length }}/200</text>
      </view>
      
      <!-- 巡检结果 -->
      <view class="form-group" v-if="formData.patrol_results.length > 0">
        <text class="form-label">巡检结果</text>
        <view class="results-list">
          <view 
            class="result-item" 
            v-for="(result, index) in formData.patrol_results" 
            :key="index"
            @click="editPatrolResult(index)"
          >
            <view class="result-header">
              <text class="result-title">{{ result.item_info.itemName }}</text>
              <view class="result-status" :class="result.check_result">
                {{ result.check_result === 'normal' ? '正常' : '异常' }}
              </view>
            </view>
            <view class="result-body">
              <view class="result-row device-info">
                <text class="result-label">设备:</text>
                <text class="result-value">{{ result.item_info.deviceName || '未知设备' }}</text>
              </view>
              <view class="result-row" v-if="result.item_info.normalRange">
                <text class="result-label">正常范围:</text>
                <text class="result-value">{{ result.item_info.normalRange }}</text>
              </view>
              <view class="result-row" v-if="result.param_value">
                <text class="result-label">参数值:</text>
                <text class="result-value">{{ result.param_value }}{{ result.item_info.unit ? ' ' + result.item_info.unit : '' }}</text>
              </view>
              <view class="result-row" v-if="result.description">
                <text class="result-label">描述:</text>
                <text class="result-value">{{ result.description }}</text>
              </view>
              <view class="result-row" v-if="result.images && result.images.length > 0">
                <text class="result-label">照片:</text>
                <text class="result-value">已上传 {{ result.images.length }} 张</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 提交按钮 -->
      <view class="submit-section">
        <button 
          class="submit-btn" 
          :class="{ disabled: submitting }" 
          :disabled="submitting"
          @click="submitRecord"
        >
          {{ submitting ? '提交中...' : '提交巡检记录' }}
        </button>
      </view>
    </scroll-view>
    
    <!-- 巡检结果编辑弹窗 -->
    <uni-popup ref="resultPopup" type="bottom">
      <view class="result-popup">
        <view class="popup-header">
          <text class="popup-title">巡检结果 - {{ currentResult.item_info ? currentResult.item_info.itemName : '' }}</text>
          <text class="popup-close" @click="closeResultPopup">关闭</text>
        </view>
        <view class="popup-content">
          <!-- 设备信息 -->
          <view class="popup-form-group" v-if="currentResult.item_info">
            <text class="popup-form-label">设备信息</text>
            <view class="device-info-box">
              <text class="device-name">{{ currentResult.item_info.deviceName || '未知设备' }}</text>
              <text class="device-category" v-if="currentResult.item_info.categoryName">分类: {{ currentResult.item_info.categoryName }}</text>
            </view>
          </view>
          
          <!-- 检查标准 -->
          <view class="popup-form-group" v-if="currentResult.item_info && currentResult.item_info.normalRange">
            <text class="popup-form-label">检查标准</text>
            <view class="standard-info">
              <text class="normal-range">正常范围: {{ currentResult.item_info.normalRange }}</text>
              <text class="check-method" v-if="currentResult.item_info.checkMethod">检查方法: {{ currentResult.item_info.checkMethod }}</text>
            </view>
          </view>
          
          <!-- 参数值输入区域 -->
          <view class="popup-form-group" v-if="currentResult.item_info && currentResult.item_info.normalRange">
            <text class="popup-form-label required">当前值</text>
            <input 
              class="popup-input" 
              type="text" 
              v-model="currentResult.param_value" 
              placeholder="请输入当前测量值"
            />
            <text class="parameter-unit" v-if="currentResult.item_info.unit">{{ currentResult.item_info.unit }}</text>
          </view>
          
          <view class="popup-form-group">
            <text class="popup-form-label">检查结果</text>
            <view class="result-radio-group">
              <view 
                class="result-radio" 
                :class="{'result-radio-selected': currentResult.check_result === 'normal'}"
                @click="currentResult.check_result = 'normal'"
              >
                <text>正常</text>
              </view>
              <view 
                class="result-radio" 
                :class="{'result-radio-selected': currentResult.check_result === 'abnormal'}"
                @click="currentResult.check_result = 'abnormal'"
              >
                <text>异常</text>
              </view>
            </view>
          </view>
          
          <!-- 参数值输入区域 (用于没有正常范围的项目) -->
          <view class="popup-form-group" v-if="currentResult.item_info && !currentResult.item_info.normalRange && currentResult.item_info.hasValueInput">
            <text class="popup-form-label">参数值</text>
            <input 
              class="popup-input" 
              type="text" 
              v-model="currentResult.param_value" 
              placeholder="请输入参数值"
            />
            <text class="parameter-unit" v-if="currentResult.item_info.unit">{{ currentResult.item_info.unit }}</text>
          </view>
          
          <view class="popup-form-group">
            <text class="popup-form-label">描述</text>
            <textarea 
              class="popup-form-textarea" 
              v-model="currentResult.description" 
              placeholder="请输入描述信息"
            ></textarea>
          </view>
          
          <!-- 图片上传区域 -->
          <view class="popup-form-group">
            <text class="popup-form-label">照片</text>
            <view class="result-image-list" v-if="currentResult">
              <view class="result-image-item" v-for="(image, index) in currentResult.images" :key="index">
                <image class="result-image" :src="image" mode="aspectFill"></image>
                <view class="result-image-delete" @click="removeImage(index)">
                  <text class="icon-delete">×</text>
                </view>
              </view>
              <view class="result-image-add" v-if="currentResult.images.length < 4" @click="chooseImage">
                <text class="icon-add">+</text>
                <text class="result-image-tip">添加图片</text>
              </view>
            </view>
          </view>
          
          <view class="popup-form-group">
            <text class="popup-form-label">位置信息</text>
            <view class="location-btns">
              <button class="location-btn" @click="getLocation">获取当前位置</button>
              <text v-if="currentResult.latitude && currentResult.longitude">
                已获取位置 ({{ currentResult.latitude.toFixed(4) }}, {{ currentResult.longitude.toFixed(4) }})
              </text>
            </view>
          </view>
          
          <button class="popup-save-btn" @click="saveResult">保存</button>
        </view>
      </view>
    </uni-popup>
    
    <!-- 时间选择器弹窗 -->
    <uni-popup ref="datetimePopup" type="bottom">
      <view class="datetime-popup">
        <view class="popup-header">
          <text class="popup-title">{{ currentDatetimeType === 'start' ? '选择开始时间' : '选择结束时间' }}</text>
          <text class="popup-close" @click="closeDatetimePicker">关闭</text>
        </view>
        <view class="picker-body">
          <picker-view class="picker" :indicator-style="indicatorStyle" :value="pickerValue" @change="dateTimePickerChange">
            <picker-view-column>
              <view class="picker-item" v-for="(item, index) in years" :key="'year-'+index">{{ item }}年</view>
            </picker-view-column>
            <picker-view-column>
              <view class="picker-item" v-for="(item, index) in months" :key="'month-'+index">{{ item }}月</view>
            </picker-view-column>
            <picker-view-column>
              <view class="picker-item" v-for="(item, index) in days" :key="'day-'+index">{{ item }}日</view>
            </picker-view-column>
            <picker-view-column>
              <view class="picker-item" v-for="(item, index) in hours" :key="'hour-'+index">{{ item }}时</view>
            </picker-view-column>
            <picker-view-column>
              <view class="picker-item" v-for="(item, index) in minutes" :key="'minute-'+index">{{ item }}分</view>
            </picker-view-column>
            <picker-view-column>
              <view class="picker-item" v-for="(item, index) in seconds" :key="'second-'+index">{{ item }}秒</view>
            </picker-view-column>
          </picker-view>
        </view>
        <view class="btn-area">
          <button class="confirm-btn" @click="confirmDateTime">确定</button>
        </view>
      </view>
    </uni-popup>
    
    <!-- 执行人选择器弹窗 -->
    <uni-popup ref="executorPopup" type="bottom">
      <view class="executor-popup">
        <view class="popup-header">
          <text class="popup-title">选择执行人</text>
          <text class="popup-close" @click="closeExecutorSelector">关闭</text>
        </view>
        <view class="executor-body">
          <view class="executor-search">
            <input type="text" v-model="executorSearchKey" placeholder="搜索执行人" class="search-input" />
          </view>
          <scroll-view scroll-y class="executor-list">
            <checkbox-group @change="handleExecutorChange">
              <label class="executor-item" v-for="(executor, index) in filteredExecutors" :key="index">
                <view class="executor-info">
                  <checkbox :value="executor.id.toString()" :checked="isExecutorSelected(executor.id)" />
                  <text class="executor-name">{{ executor.name }}</text>
                </view>
                <text class="executor-dept" v-if="executor.departmentName">{{ executor.departmentName }}</text>
              </label>
            </checkbox-group>
          </scroll-view>
        </view>
        <view class="btn-area">
          <button class="confirm-btn" @click="confirmExecutorSelection">确定 (已选 {{ formData.executor_ids.length }} 人)</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { patrolApi } from '@/utils/api.js';

export default {
  data() {
    return {
      formData: {
        patrol_plan_id: '', // 巡检计划ID
        executor_ids: [], // 执行人ID数组
        start_time: '', // 开始时间
        end_time: '', // 结束时间
        status: 'completed', // 状态：pending/processing/completed
        remark: '', // 备注
        patrol_results: [] // 巡检结果
      },
      statusOptions: [
        { label: '待执行', value: 'pending' },
        { label: '执行中', value: 'processing' },
        { label: '已完成', value: 'completed' }
      ],
      selectedPlanName: '',
      selectedExecutorNames: '',
      plans: [],
      executors: [],
      loading: false,
      submitting: false,
      currentResultIndex: -1,
      currentResult: {
        patrol_item_id: '',
        check_result: 'normal',
        param_value: '',
        description: '',
        images: [],
        latitude: 0,
        longitude: 0
      },
      
      // 日期时间选择器相关数据
      currentDatetimeType: 'start', // 当前正在选择的时间类型：start/end
      indicatorStyle: 'height: 50px;',
      pickerValue: [0, 0, 0, 0, 0, 0],
      years: [],
      months: [],
      days: [],
      hours: [],
      minutes: [],
      seconds: [],
      year: 2020,
      month: 1,
      day: 1,
      hour: 0,
      minute: 0,
      second: 0,
      
      // 执行人选择器相关数据
      executorSearchKey: '',
      selectedExecutorIds: []
    };
  },
  computed: {
    // 获取经过搜索过滤的执行人列表
    filteredExecutors() {
      if (!this.executorSearchKey) {
        return this.executors;
      }
      
      const keyword = this.executorSearchKey.toLowerCase();
      return this.executors.filter(exec => 
        exec.name.toLowerCase().includes(keyword) || 
        (exec.departmentName && exec.departmentName.toLowerCase().includes(keyword))
      );
    }
  },
  onLoad(options) {
    // 从路由参数中获取数据
    if (options.plan_id) {
      this.formData.patrol_plan_id = options.plan_id;
      this.loadPlanDetails(options.plan_id);
    }
    
    // 加载巡检计划列表
    this.loadPatrolPlans();
    
    // 加载执行人列表
    this.loadExecutors();
    
    // 设置默认开始和结束时间为当前时间
    const now = new Date();
    this.formData.start_time = this.formatDateTime(now);
    this.formData.end_time = this.formatDateTime(now);
  },
  methods: {
    // 格式化日期时间
    formatDateTime(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    
    // 加载巡检计划列表
    loadPatrolPlans() {
      this.loading = true;
      patrolApi.getPatrolPlans({})
        .then(res => {
          if (res.code === 200 && res.data) {
            this.plans = res.data || [];
            
            // 如果已有选择的计划ID，设置对应的计划名称
            if (this.formData.patrol_plan_id) {
              const selectedPlan = this.plans.find(plan => plan.id == this.formData.patrol_plan_id);
              if (selectedPlan) {
                this.selectedPlanName = selectedPlan.name;
              }
            }
          } else {
            uni.showToast({
              title: '获取巡检计划列表失败',
              icon: 'none'
            });
          }
        })
        .catch(err => {
          console.error('获取巡检计划列表错误:', err);
          uni.showToast({
            title: '获取巡检计划列表失败',
            icon: 'none'
          });
        })
        .finally(() => {
          this.loading = false;
        });
    },
    
    // 加载巡检计划详情
    loadPlanDetails(planId) {
      patrolApi.getPlanDetail(planId)
        .then(res => {
          if (res.code === 200 && res.data) {
            this.selectedPlanName = res.data.name;
            
            // 获取计划中的执行人
            if (res.data.executorIds && res.data.executorIds.length > 0) {
              // 加载执行人列表
              this.loadPlanExecutors(planId);
            }
            
            // 加载巡检项目，从API获取巡检项目列表
            this.loadPatrolItems(planId);
          }
        })
        .catch(err => {
          console.error('获取巡检计划详情错误:', err);
        });
    },
    
    // 加载巡检项目列表
    loadPatrolItems(planId) {
      patrolApi.getItemList({ planId: planId })
        .then(res => {
          if (res.code === 200 && res.data) {
            // 根据API返回的巡检项列表初始化巡检结果
            if (res.data && res.data.length > 0) {
              this.formData.patrol_results = res.data.map(item => ({
                patrol_item_id: item.id,
                check_result: 'normal',
                param_value: '',
                description: '',
                images: [],
                latitude: 0,
                longitude: 0,
                // 存储巡检项的详细信息，用于展示
                item_info: {
                  deviceId: item.deviceId,
                  deviceName: item.deviceName,
                  itemName: item.itemName || `巡检项 ${item.id}`,
                  categoryName: item.categoryName || '',
                  paramType: item.paramType || '',
                  unit: item.unit || '',
                  normalRange: item.normalRange || '',
                  checkMethod: item.checkMethod || '',
                  importance: item.importance || '',
                  description: item.description || ''
                }
              }));
            } else {
              // 如果没有巡检项，显示提示
              uni.showToast({
                title: '该巡检计划没有巡检项',
                icon: 'none'
              });
              this.formData.patrol_results = [];
            }
          } else {
            uni.showToast({
              title: '获取巡检项目失败',
              icon: 'none'
            });
          }
        })
        .catch(err => {
          console.error('获取巡检项目列表错误:', err);
          uni.showToast({
            title: '获取巡检项目失败',
            icon: 'none'
          });
        });
    },
    
    // 加载计划指定的执行人
    loadPlanExecutors(planId) {
      patrolApi.getPlanExecutors(planId)
        .then(res => {
          if (res.code === 200 && res.data) {
            this.executors = res.data || [];
            
            // 更新已选择执行人的显示
            this.updateSelectedExecutorNames();
          }
        })
        .catch(err => {
          console.error('获取计划执行人列表错误:', err);
        });
    },
    
    // 更新已选择执行人的显示
    updateSelectedExecutorNames() {
      if (this.formData.executor_ids && this.formData.executor_ids.length > 0 && this.executors.length > 0) {
        const selectedUsers = this.executors.filter(exec => 
          this.formData.executor_ids.includes(exec.id)
        );
        
        if (selectedUsers.length > 0) {
          this.selectedExecutorNames = selectedUsers.map(user => user.name).join('，');
          
          // 如果名称太长，只显示人数
          if (this.selectedExecutorNames.length > 15) {
            this.selectedExecutorNames = `共${selectedUsers.length}人`;
          }
        } else {
          this.selectedExecutorNames = '';
        }
      } else {
        this.selectedExecutorNames = '';
      }
    },
    
    // 加载执行人列表
    loadExecutors() {
      patrolApi.getPlanExecutors()
        .then(res => {
          if (res.code === 200 && res.data) {
            this.executors = res.data || [];
            
            // 默认选择当前用户作为执行人
            const userInfo = uni.getStorageSync('userInfo');
            if (userInfo && userInfo.userId) {
              this.formData.executor_ids = [userInfo.userId];
              this.updateSelectedExecutorNames();
            }
          }
        })
        .catch(err => {
          console.error('获取执行人列表错误:', err);
        });
    },
    
    // 显示巡检计划选择器
    showPlanSelector() {
      if (this.plans.length === 0) {
        uni.showToast({
          title: '暂无可选巡检计划',
          icon: 'none'
        });
        return;
      }
      
      const planItems = this.plans.map(plan => plan.name);
      uni.showActionSheet({
        itemList: planItems,
        success: res => {
          const index = res.tapIndex;
          this.formData.patrol_plan_id = this.plans[index].id;
          this.selectedPlanName = this.plans[index].name;
          
          // 加载计划详情
          this.loadPlanDetails(this.formData.patrol_plan_id);
        }
      });
    },
    
    // 显示执行人选择器
    showExecutorSelector() {
      if (this.executors.length === 0) {
        uni.showToast({
          title: '暂无可选执行人',
          icon: 'none'
        });
        return;
      }
      
      // 初始化已选择的执行人ID
      this.selectedExecutorIds = [...this.formData.executor_ids];
      
      // 显示选择器
      this.$refs.executorPopup.open();
    },
    
    // 显示开始时间选择器
    showStartTimePicker() {
      this.currentDatetimeType = 'start';
      this.initDateTimePicker(this.formData.start_time);
      this.$refs.datetimePopup.open();
    },
    
    // 显示结束时间选择器
    showEndTimePicker() {
      this.currentDatetimeType = 'end';
      this.initDateTimePicker(this.formData.end_time);
      this.$refs.datetimePopup.open();
    },
    
    // 关闭时间选择器
    closeDatetimePicker() {
      this.$refs.datetimePopup.close();
    },
    
    // 初始化时间选择器
    initDateTimePicker(dateTimeStr) {
      let date;
      
      if (dateTimeStr) {
        date = new Date(dateTimeStr);
        if (isNaN(date.getTime())) {
          date = new Date();
        }
      } else {
        date = new Date();
      }
      
      this.year = date.getFullYear();
      this.month = date.getMonth() + 1;
      this.day = date.getDate();
      this.hour = date.getHours();
      this.minute = date.getMinutes();
      this.second = date.getSeconds();
      
      // 生成年份数据，当前年份前后10年
      this.years = [];
      for (let i = this.year - 10; i <= this.year + 10; i++) {
        this.years.push(i);
      }
      
      // 生成月份数据
      this.months = [];
      for (let i = 1; i <= 12; i++) {
        this.months.push(i);
      }
      
      this.updateDays();
      
      // 生成小时数据
      this.hours = [];
      for (let i = 0; i <= 23; i++) {
        this.hours.push(i);
      }
      
      // 生成分钟和秒数据
      this.minutes = [];
      this.seconds = [];
      for (let i = 0; i <= 59; i++) {
        this.minutes.push(i);
        this.seconds.push(i);
      }
      
      // 更新pickerValue
      this.updatePickerValue();
    },
    
    // 更新天数，根据年月确定
    updateDays() {
      let daysInMonth = 31;
      
      // 计算当月天数
      if (this.month === 2) {
        // 闰年2月29天，平年28天
        daysInMonth = (this.year % 4 === 0 && this.year % 100 !== 0) || this.year % 400 === 0 ? 29 : 28;
      } else if ([4, 6, 9, 11].includes(this.month)) {
        daysInMonth = 30;
      }
      
      this.days = [];
      for (let i = 1; i <= daysInMonth; i++) {
        this.days.push(i);
      }
      
      // 如果当前选择的日期超出了当月的最大天数，则调整为当月最后一天
      if (this.day > daysInMonth) {
        this.day = daysInMonth;
      }
    },
    
    // 更新pickerValue
    updatePickerValue() {
      const yearIndex = this.years.indexOf(this.year);
      const monthIndex = this.months.indexOf(this.month);
      const dayIndex = this.days.indexOf(this.day);
      const hourIndex = this.hours.indexOf(this.hour);
      const minuteIndex = this.minutes.indexOf(this.minute);
      const secondIndex = this.seconds.indexOf(this.second);
      
      this.pickerValue = [
        yearIndex !== -1 ? yearIndex : 0,
        monthIndex !== -1 ? monthIndex : 0,
        dayIndex !== -1 ? dayIndex : 0,
        hourIndex !== -1 ? hourIndex : 0,
        minuteIndex !== -1 ? minuteIndex : 0,
        secondIndex !== -1 ? secondIndex : 0
      ];
    },
    
    // 日期时间选择器变化处理
    dateTimePickerChange(e) {
      const val = e.detail.value;
      
      // 更新选中的值
      this.year = this.years[val[0]];
      this.month = this.months[val[1]];
      
      // 更新天数列表
      this.updateDays();
      
      // 如果当前选中的日期超出了更新后的天数范围，则重置为1号
      if (val[2] >= this.days.length) {
        val[2] = 0;
      }
      
      this.day = this.days[val[2]];
      this.hour = this.hours[val[3]];
      this.minute = this.minutes[val[4]];
      this.second = this.seconds[val[5]];
    },
    
    // 确认日期时间选择
    confirmDateTime() {
      // 格式化日期时间为字符串
      const datetimeStr = this.formatPickerDateTime();
      
      // 更新表单数据
      if (this.currentDatetimeType === 'start') {
        this.formData.start_time = datetimeStr;
        
        // 如果开始时间晚于结束时间，则更新结束时间
        if (this.formData.end_time && new Date(this.formData.start_time) > new Date(this.formData.end_time)) {
          this.formData.end_time = datetimeStr;
        }
      } else {
        this.formData.end_time = datetimeStr;
        
        // 如果结束时间早于开始时间，显示提示
        if (this.formData.start_time && new Date(this.formData.end_time) < new Date(this.formData.start_time)) {
          uni.showToast({
            title: '结束时间不能早于开始时间',
            icon: 'none'
          });
          this.formData.end_time = this.formData.start_time;
        }
      }
      
      // 关闭选择器
      this.closeDatetimePicker();
    },
    
    // 格式化选择器日期时间
    formatPickerDateTime() {
      const year = this.year;
      const month = String(this.month).padStart(2, '0');
      const day = String(this.day).padStart(2, '0');
      const hour = String(this.hour).padStart(2, '0');
      const minute = String(this.minute).padStart(2, '0');
      const second = String(this.second).padStart(2, '0');
      
      return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
    },
    
    // 编辑巡检结果
    editPatrolResult(index) {
      this.currentResultIndex = index;
      
      // 深拷贝以避免直接修改原对象
      const resultCopy = JSON.parse(JSON.stringify(this.formData.patrol_results[index]));
      
      // 确保images字段存在
      if (!resultCopy.images) {
        resultCopy.images = [];
      }
      
      this.currentResult = resultCopy;
      this.$refs.resultPopup.open();
    },
    
    // 关闭结果弹窗
    closeResultPopup() {
      this.$refs.resultPopup.close();
    },
    
    // 保存结果
    saveResult() {
      // 对于有正常范围的项目，检查参数值是否已填写
      if (this.currentResult.item_info && this.currentResult.item_info.normalRange && !this.currentResult.param_value) {
        uni.showToast({
          title: '请输入当前测量值',
          icon: 'none'
        });
        return;
      }
      
      // 保存当前编辑的结果到表单数据
      if (this.currentResultIndex >= 0) {
        // 确保使用images字段
        if (!this.currentResult.images) {
          this.currentResult.images = [];
        }
        
        this.formData.patrol_results[this.currentResultIndex] = JSON.parse(JSON.stringify(this.currentResult));
      }
      
      this.closeResultPopup();
    },
    
    // 选择图片
    chooseImage() {
      uni.chooseImage({
        count: 4 - (this.currentResult.images?.length || 0),
        success: (res) => {
          const tempFilePaths = res.tempFilePaths;
          const newImages = tempFilePaths.map(path => ({
            url: path,
            file: path
          }));
          
          if (!this.currentResult.images) {
            this.currentResult.images = [];
          }
          
          this.currentResult.images = [...this.currentResult.images, ...newImages];
        }
      });
    },
    
    // 删除图片
    removeImage(index) {
      this.currentResult.images.splice(index, 1);
    },
    
    // 获取位置信息
    getLocation() {
      uni.getLocation({
        type: 'gcj02',
        success: (res) => {
          this.currentResult.latitude = res.latitude;
          this.currentResult.longitude = res.longitude;
        },
        fail: (err) => {
          console.error('获取位置失败:', err);
          uni.showToast({
            title: '获取位置失败，请检查位置权限',
            icon: 'none'
          });
        }
      });
    },
    
    // 表单验证
    validateForm() {
      if (!this.formData.patrol_plan_id) {
        uni.showToast({
          title: '请选择巡检计划',
          icon: 'none'
        });
        return false;
      }
      
      if (this.formData.executor_ids.length === 0) {
        uni.showToast({
          title: '请选择执行人',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.formData.start_time) {
        uni.showToast({
          title: '请选择开始时间',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.formData.end_time) {
        uni.showToast({
          title: '请选择结束时间',
          icon: 'none'
        });
        return false;
      }
      
      if (new Date(this.formData.end_time) < new Date(this.formData.start_time)) {
        uni.showToast({
          title: '结束时间不能早于开始时间',
          icon: 'none'
        });
        return false;
      }
      
      return true;
    },
    
    // 提交巡检记录
    submitRecord() {
      if (!this.validateForm()) {
        return;
      }
      
      // 确认提交弹窗
      uni.showModal({
        title: '提交确认',
        content: '确定要提交巡检记录吗？',
        success: (res) => {
          if (res.confirm) {
            this.doSubmit();
          }
        }
      });
    },
    
    // 执行提交操作
    doSubmit() {
      this.submitting = true;
      
      // 根据最新接口文档严格构造请求数据
      const submitData = {
        patrol_plan_id: this.formData.patrol_plan_id, // 巡检计划ID
        executor_id: this.formData.executor_ids[0] || '', // 执行人ID
        start_time: this.formData.start_time, // 开始时间
        end_time: this.formData.end_time, // 结束时间
        status: this.formData.status, // 状态:pending,processing,completed
        remark: this.formData.remark || '', // 备注
        patrol_results: [] // 巡检结果数组
      };
      
      // 处理巡检项目数组，严格按照最新接口文档的字段要求
      if (this.formData.patrol_results && this.formData.patrol_results.length > 0) {
        submitData.patrol_results = this.formData.patrol_results.map(result => {
          // 从结果中提取接口需要的确切字段
          const cleanResult = {
            patrol_item_id: result.patrol_item_id, // 巡检项ID
            check_result: result.check_result, // 检查结果:normal-正常,abnormal-异常
            param_value: result.param_value || result.parameter_value || '', // 参数值
            description: result.description || '', // 描述
            latitude: result.latitude ? Number(result.latitude) : 0, // 经度，确保为数字类型
            longitude: result.longitude ? Number(result.longitude) : 0 // 纬度，确保为数字类型
          };
          
          // 处理图片字段，按最新文档使用images
          try {
            // 仅使用images字段
            if (result.images && result.images.length) {
              const imagesArray = Array.from(result.images);
              cleanResult.images = imagesArray.filter(Boolean).map(img => 
                typeof img === 'string' ? img : 
                (img && img.url ? img.url : '')
              ).filter(url => url);
            } else {
              cleanResult.images = []; // 确保有空数组
            }
          } catch (e) {
            console.error('处理图片字段出错:', e);
            cleanResult.images = []; // 出错时提供默认空数组
          }
          
          return cleanResult;
        });
      }
      
      console.log('提交数据:', JSON.stringify(submitData));
      
      patrolApi.submitPatrolRecord(submitData)
        .then(res => {
          if (res.code === 200) {
            uni.showToast({
              title: '巡检记录提交成功',
              icon: 'success'
            });
            
            // 延迟跳转
            setTimeout(() => {
              uni.navigateBack();
            }, 1500);
          } else {
            uni.showToast({
              title: res.message || '提交失败',
              icon: 'none'
            });
          }
        })
        .catch(err => {
          console.error('提交巡检记录错误:', err);
          uni.showToast({
            title: '提交失败，请重试',
            icon: 'none'
          });
        })
        .finally(() => {
          this.submitting = false;
        });
    },
    
    // 关闭执行人选择器
    closeExecutorSelector() {
      this.$refs.executorPopup.close();
      this.executorSearchKey = ''; // 清空搜索关键字
    },
    
    // 处理执行人选择变化
    handleExecutorChange(e) {
      // 更新选中的执行人ID
      this.selectedExecutorIds = e.detail.value.map(id => parseInt(id));
    },
    
    // 确认执行人选择
    confirmExecutorSelection() {
      // 更新表单数据中的执行人ID
      this.formData.executor_ids = [...this.selectedExecutorIds];
      
      // 更新显示的执行人名称
      this.updateSelectedExecutorNames();
      
      // 关闭选择器
      this.closeExecutorSelector();
    },
    
    // 检查执行人是否被选中
    isExecutorSelected(executorId) {
      return this.selectedExecutorIds.includes(executorId);
    }
  }
};
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  width: 100%;
}

.page-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  padding: 40rpx 30rpx 20rpx;
  text-align: center;
  background-color: #fff;
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    bottom: 5rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 60rpx;
    height: 6rpx;
    background-color: #1890ff;
    border-radius: 3rpx;
  }
}

.form-container {
  flex: 1;
  padding: 30rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}

.form-group {
  margin-bottom: 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  width: 90%;
  max-width: 680rpx;
  margin-left: auto;
  margin-right: auto;
  box-sizing: border-box;
  border-left: 4rpx solid #1890ff;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.required:after {
  content: '*';
  color: #f56c6c;
  margin-left: 6rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 1px solid #dcdfe6;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.select-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.placeholder {
  color: #999;
}

.select-arrow {
  font-size: 24rpx;
  color: #999;
}

.form-textarea {
  width: 100%;
  height: 200rpx;
  border: 1px solid #dcdfe6;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

.status-group {
  display: flex;
  flex-direction: row;
  gap: 30rpx;
}

.status-option {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 10rpx;
  padding: 20rpx;
  border: 1px solid #dcdfe6;
  border-radius: 8rpx;
  transition: all 0.3s;
  cursor: pointer;
}

.status-option.active {
  border-color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
}

.status-dot {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background-color: #dcdfe6;
}

.status-option.active .status-dot {
  background-color: #1890ff;
}

.status-text {
  font-size: 28rpx;
  color: #333;
}

.status-option.active .status-text {
  color: #1890ff;
  font-weight: 500;
}

.results-list {
  margin-top: 20rpx;
  width: 100%;
}

.result-item {
  background-color: #f9f9f9;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 16rpx;
  border-left: 4rpx solid #1890ff;
  transition: all 0.3s ease;
  cursor: pointer;
  
  &:nth-child(3n+1) {
    border-left-color: #1890ff;
  }
  
  &:nth-child(3n+2) {
    border-left-color: #52c41a;
  }
  
  &:nth-child(3n+3) {
    border-left-color: #faad14;
  }
  
  &:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
  }
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.result-title {
  font-size: 28rpx;
  font-weight: 500;
}

.result-status {
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
}

.result-status.normal {
  background-color: #f0f9eb;
  color: #67c23a;
}

.result-status.abnormal {
  background-color: #fef0f0;
  color: #f56c6c;
}

.result-body {
  font-size: 26rpx;
  color: #666;
}

.result-row {
  margin-bottom: 8rpx;
}

.result-label {
  color: #999;
  margin-right: 8rpx;
}

.submit-section {
  margin: 40rpx 0;
  width: 90%;
  max-width: 680rpx;
  display: flex;
  justify-content: center;
  margin-left: auto;
  margin-right: auto;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  background-color: #1890ff;
  color: #fff;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  box-shadow: 0 6rpx 16rpx rgba(24, 144, 255, 0.3);
}

.submit-btn.disabled {
  background-color: #a0cfff;
}

/* 弹窗样式 */
.result-popup {
  background-color: #fff;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  padding-bottom: env(safe-area-inset-bottom);
  max-height: 80vh;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #eee;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 500;
}

.popup-close {
  font-size: 28rpx;
  color: #999;
}

.popup-content {
  padding: 30rpx;
  max-height: calc(80vh - 100rpx);
  overflow-y: auto;
}

.popup-form-group {
  margin-bottom: 24rpx;
  position: relative;
}

.popup-form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
}

.popup-input {
  width: 100%;
  height: 80rpx;
  border: 1px solid #dcdfe6;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.popup-input:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.popup-form-textarea {
  width: 100%;
  height: 160rpx;
  border: 1px solid #dcdfe6;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.result-radio-group {
  display: flex;
  gap: 20rpx;
}

.result-radio {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx;
  border: 1px solid #dcdfe6;
  border-radius: 8rpx;
  transition: all 0.3s;
}

.result-radio.result-radio-selected {
  border-color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
}

.result-image-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.result-image-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  margin: 10rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.result-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.result-image-delete {
  position: absolute;
  top: 6rpx;
  right: 6rpx;
  width: 36rpx;
  height: 36rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.icon-delete {
  font-size: 24rpx;
  color: #ffffff;
  line-height: 24rpx;
}

.result-image-add {
  width: 160rpx;
  height: 160rpx;
  margin: 10rpx;
  background-color: #f5f5f5;
  border: 1px dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #999;
}

.icon-add {
  font-size: 48rpx;
  line-height: 48rpx;
  color: #999;
}

.result-image-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

.location-btns {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.location-btn {
  background-color: #1890ff;
  color: #fff;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 28rpx;
  border-radius: 8rpx;
}

.popup-save-btn {
  margin-top: 40rpx;
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  background-color: #1890ff;
  color: #fff;
  border-radius: 8rpx;
  font-size: 32rpx;
}

.device-info-box {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.device-name {
  font-size: 28rpx;
  font-weight: 500;
}

.device-category {
  font-size: 24rpx;
  color: #999;
}

.standard-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.normal-range {
  font-size: 24rpx;
  color: #999;
}

.check-method {
  font-size: 24rpx;
  color: #999;
}

.parameter-unit {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24rpx;
  color: #666;
}

/* 时间选择器样式 */
.datetime-popup {
  background-color: #fff;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  padding-bottom: env(safe-area-inset-bottom);
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  border-bottom: 1px solid #eee;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.popup-close {
  font-size: 28rpx;
  color: #999;
  padding: 10rpx;
}

.picker-body {
  padding: 30rpx 20rpx;
}

.picker {
  width: 100%;
  height: 400rpx;
}

.picker-item {
  height: 50px;
  line-height: 50px;
  text-align: center;
  font-size: 28rpx;
  color: #333;
}

.btn-area {
  padding: 20rpx 30rpx 40rpx;
}

.confirm-btn {
  width: 100%;
  background-color: #1890ff;
  color: #fff;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
  border-radius: 44rpx;
  font-weight: 500;
}

/* 执行人选择器样式 */
.executor-popup {
  background-color: #fff;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  padding-bottom: env(safe-area-inset-bottom);
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.executor-body {
  padding: 20rpx 30rpx;
  flex: 1;
}

.executor-search {
  margin-bottom: 20rpx;
}

.search-input {
  width: 100%;
  height: 80rpx;
  border: 1px solid #dcdfe6;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  background-color: #f5f5f5;
}

.executor-list {
  height: 600rpx;
  margin-bottom: 20rpx;
}

.executor-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 10rpx;
  border-bottom: 1px solid #eaeaea;
  transition: background-color 0.3s ease;
}

.executor-item:active {
  background-color: rgba(24, 144, 255, 0.05);
}

.executor-info {
  display: flex;
  align-items: center;
}

.executor-name {
  font-size: 28rpx;
  margin-left: 16rpx;
  color: #333;
}

.executor-dept {
  font-size: 24rpx;
  color: #999;
}

.btn-area {
  padding: 20rpx 30rpx 40rpx;
}

.confirm-btn {
  width: 100%;
  background: linear-gradient(135deg, #1890ff, #096dd9);
  color: #fff;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
  border-radius: 44rpx;
  font-weight: 500;
  box-shadow: 0 6rpx 16rpx rgba(24, 144, 255, 0.3);
}
</style>
