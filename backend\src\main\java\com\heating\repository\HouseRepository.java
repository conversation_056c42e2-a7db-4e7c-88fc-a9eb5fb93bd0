package com.heating.repository;

import com.heating.entity.house.THouse;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface HouseRepository extends JpaRepository<THouse, Long> {

    /**
     * 根据小区ID和房间信息查找住户
     * 
     * @param heatUnitId 热力单位ID（小区ID）
     * @param roomNo 房号
     * @return 匹配的住户信息，如果未找到则返回null
     */
    @Query("SELECT h FROM THouse h WHERE h.heatUnitId = :heatUnitId AND " +
           "h.roomNo = :roomNo")
    THouse findByHeatUnitIdAndRoomInfo(
        @Param("heatUnitId") Long heatUnitId,
        @Param("roomNo") String roomNo
    );
} 