Page({
  data: {
    settings: {
      notification: true,
      sound: true,
      vibration: true
    },
    cacheSize: '2.5MB',
    version: '1.0.0'
  },

  onLoad() {
    this.loadSettings();
  },

  loadSettings() {
    // TODO: 从本地存储或服务器获取设置
  },

  toggleNotification(e) {
    this.setData({
      'settings.notification': e.detail.value
    });
    this.saveSettings();
  },

  toggleSound(e) {
    this.setData({
      'settings.sound': e.detail.value
    });
    this.saveSettings();
  },

  toggleVibration(e) {
    this.setData({
      'settings.vibration': e.detail.value
    });
    this.saveSettings();
  },

  saveSettings() {
    // TODO: 保存设置到本地存储或服务器
    wx.showToast({
      title: '设置已保存',
      icon: 'success'
    });
  }
}); 