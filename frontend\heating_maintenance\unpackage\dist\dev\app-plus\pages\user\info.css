/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.custom-tabbar[data-v-6def6a3b] {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3.4375rem;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding-bottom: env(safe-area-inset-bottom);
  box-shadow: 0 -0.125rem 0.625rem rgba(0, 0, 0, 0.06);
  z-index: 999;
}
.custom-tabbar .tab-item[data-v-6def6a3b] {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.375rem 0;
  position: relative;
  transition: all 0.3s;
}
.custom-tabbar .tab-item.active[data-v-6def6a3b] {
  transform: translateY(-0.1875rem);
}
.custom-tabbar .tab-item.active .tab-text[data-v-6def6a3b] {
  color: #1890ff;
  font-weight: 500;
}
.custom-tabbar .tab-item .icon-container[data-v-6def6a3b] {
  width: 1.875rem;
  height: 1.875rem;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 0.1875rem;
}
.custom-tabbar .tab-item .icon-container .tab-icon[data-v-6def6a3b] {
  width: 1.5rem;
  height: 1.5rem;
  transition: all 0.3s;
}
.custom-tabbar .tab-item .tab-text[data-v-6def6a3b] {
  font-size: 0.75rem;
  color: #999;
  line-height: 1;
  transition: all 0.3s;
}
.custom-tabbar .tab-item .active-indicator[data-v-6def6a3b] {
  position: absolute;
  bottom: -0.09375rem;
  left: 50%;
  transform: translateX(-50%);
  width: 0.5rem;
  height: 0.1875rem;
  background: #1890ff;
  border-radius: 0.1875rem;
}
body[data-v-6def6a3b] {
  padding-bottom: calc(3.4375rem + env(safe-area-inset-bottom));
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.user-info-container {
  padding-bottom: 1.5625rem;
  background-color: #f5f7fa;
}
.user-header {
  background-image: linear-gradient(135deg, #1890ff, #0076e4);
  padding: 3.75rem 0.9375rem 2.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  overflow: hidden;
}
.user-header .user-header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><circle cx="20" cy="20" r="15" fill="rgba(255,255,255,0.05)"/><circle cx="70" cy="70" r="25" fill="rgba(255,255,255,0.05)"/><circle cx="100" cy="30" r="20" fill="rgba(255,255,255,0.05)"/></svg>');
  background-size: 6.25rem;
  opacity: 0.8;
}
.user-header .avatar-container {
  position: relative;
  margin-bottom: 0.625rem;
  z-index: 1;
}
.user-header .avatar-container .avatar {
  width: 5rem;
  height: 5rem;
  border-radius: 50%;
  border: 0.1875rem solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 0.25rem 0.625rem rgba(0, 0, 0, 0.15);
}
.user-header .avatar-container .edit-avatar {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 1.75rem;
  height: 1.75rem;
  background-color: #fff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 0.125rem 0.3125rem rgba(0, 0, 0, 0.2);
}
.user-header .avatar-container .edit-avatar .iconfont {
  font-size: 0.9375rem;
  color: #1890ff;
}
.user-header .user-details {
  color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  z-index: 1;
}
.user-header .user-details .user-name {
  font-size: 1.375rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  text-shadow: 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.2);
}
.user-header .user-details .user-role {
  margin-bottom: 0.375rem;
}
.user-header .user-details .user-role .role-badge {
  background-color: rgba(255, 255, 255, 0.25);
  padding: 0.25rem 0.75rem;
  border-radius: 0.9375rem;
  font-size: 0.8125rem;
  box-shadow: 0 0.0625rem 0.1875rem rgba(0, 0, 0, 0.15);
}
.user-header .user-details .user-id {
  font-size: 0.8125rem;
  opacity: 0.9;
  letter-spacing: 0.03125rem;
}
.work-stats {
  margin-top: -1.5625rem;
  margin-left: 0.9375rem;
  margin-right: 0.9375rem;
  margin-bottom: 0.9375rem;
  background-color: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 0.3125rem 0.9375rem rgba(24, 144, 255, 0.1);
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 0.9375rem 0;
  position: relative;
  z-index: 10;
}
.work-stats .stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.3125rem 0;
}
.work-stats .stat-item .stat-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 0.375rem;
  line-height: 1;
}
.work-stats .stat-item .stat-label {
  font-size: 0.8125rem;
  color: #666;
  font-weight: 500;
}
.work-stats .stat-divider {
  width: 0.0625rem;
  height: 2.1875rem;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.02), rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.02));
}
.quick-actions {
  margin: 0 0.9375rem 0.9375rem;
  background-color: #fff;
  border-radius: 0.5rem;
  padding: 0.9375rem 0.625rem;
  box-shadow: 0 0.3125rem 0.9375rem rgba(0, 0, 0, 0.05);
}
.quick-actions .quick-grid {
  display: flex;
  justify-content: space-around;
}
.quick-actions .quick-grid .quick-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.46875rem 0;
  transition: transform 0.2s;
}
.quick-actions .quick-grid .quick-item:active {
  transform: scale(0.95);
}
.quick-actions .quick-grid .quick-item .quick-icon-bg {
  width: 3.4375rem;
  height: 3.4375rem;
  border-radius: 0.9375rem;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 0.46875rem;
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.1), rgba(24, 144, 255, 0.2));
  box-shadow: 0 0.1875rem 0.46875rem rgba(24, 144, 255, 0.1);
}
.quick-actions .quick-grid .quick-item .quick-icon-bg .iconfont {
  font-size: 1.625rem;
  color: #1890ff;
}
.quick-actions .quick-grid .quick-item .quick-icon-bg:nth-child(1) {
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.1), rgba(24, 144, 255, 0.2));
}
.quick-actions .quick-grid .quick-item:nth-child(1) .quick-icon-bg {
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.1), rgba(24, 144, 255, 0.2));
}
.quick-actions .quick-grid .quick-item:nth-child(2) .quick-icon-bg {
  background: linear-gradient(135deg, rgba(82, 196, 26, 0.1), rgba(82, 196, 26, 0.2));
}
.quick-actions .quick-grid .quick-item:nth-child(2) .quick-icon-bg .iconfont {
  color: #52c41a;
}
.quick-actions .quick-grid .quick-item:nth-child(3) .quick-icon-bg {
  background: linear-gradient(135deg, rgba(250, 140, 22, 0.1), rgba(250, 140, 22, 0.2));
}
.quick-actions .quick-grid .quick-item:nth-child(3) .quick-icon-bg .iconfont {
  color: #fa8c16;
}
.quick-actions .quick-grid .quick-item:nth-child(4) .quick-icon-bg {
  background: linear-gradient(135deg, rgba(245, 34, 45, 0.1), rgba(245, 34, 45, 0.2));
}
.quick-actions .quick-grid .quick-item:nth-child(4) .quick-icon-bg .iconfont {
  color: #f5222d;
}
.quick-actions .quick-grid .quick-item .item-text {
  font-size: 0.875rem;
  color: #333;
  font-weight: 500;
}
.feature-list {
  margin-top: 0.625rem;
}
.feature-list .feature-section {
  margin: 0 0.9375rem 0.9375rem;
  background-color: #fff;
  border-radius: 0.375rem;
  padding: 0.625rem;
  box-shadow: 0 0.1875rem 0.625rem rgba(0, 0, 0, 0.05);
}
.feature-list .feature-section .section-title {
  font-size: 0.9375rem;
  font-weight: bold;
  margin-bottom: 0.625rem;
  padding: 0 0.625rem;
  display: flex;
  align-items: center;
}
.feature-list .feature-section .section-title .section-icon {
  font-size: 1rem;
  color: #1890ff;
  margin-right: 0.3125rem;
}
.feature-list .feature-section .menu-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 0.625rem;
  border-bottom: 0.03125rem solid rgba(0, 0, 0, 0.05);
  transition: background-color 0.2s;
}
.feature-list .feature-section .menu-item:active {
  background-color: rgba(0, 0, 0, 0.02);
}
.feature-list .feature-section .menu-item:last-child {
  border-bottom: none;
}
.feature-list .feature-section .menu-item .menu-icon-container {
  width: 2.1875rem;
  height: 2.1875rem;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 0.625rem;
  box-shadow: 0 0.125rem 0.3125rem rgba(0, 0, 0, 0.08);
}
.feature-list .feature-section .menu-item .menu-icon-container .iconfont {
  font-size: 1.125rem;
  color: #fff;
}
.feature-list .feature-section .menu-item .menu-icon-container.profile-icon {
  background: linear-gradient(135deg, #1890ff, #36b3ff);
}
.feature-list .feature-section .menu-item .menu-icon-container.message-icon {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}
.feature-list .feature-section .menu-item .menu-icon-container.security-icon {
  background: linear-gradient(135deg, #fa8c16, #ffa940);
}
.feature-list .feature-section .menu-item .menu-icon-container.binding-icon {
  background: linear-gradient(135deg, #722ed1, #9254de);
}
.feature-list .feature-section .menu-item .menu-icon-container.help-icon {
  background: linear-gradient(135deg, #13c2c2, #36cfc9);
}
.feature-list .feature-section .menu-item .menu-icon-container.about-icon {
  background: linear-gradient(135deg, #eb2f96, #f759ab);
}
.feature-list .feature-section .menu-item .menu-icon-container.message-test-icon {
  background: linear-gradient(135deg, #13c2c2, #36cfc9);
}
.feature-list .feature-section .menu-item .menu-content {
  flex: 1;
  font-size: 0.9375rem;
}
.feature-list .feature-section .menu-item .iconfont.icon-arrow-right {
  font-size: 0.875rem;
  color: #ccc;
}
.logout-btn {
  margin: 1.5625rem 0.9375rem;
  height: 2.8125rem;
  line-height: 2.8125rem;
  background-color: #fff;
  color: #f5222d;
  text-align: center;
  border-radius: 0.375rem;
  font-size: 1rem;
  box-shadow: 0 0.1875rem 0.625rem rgba(0, 0, 0, 0.05);
  font-weight: bold;
}
.logout-btn:active {
  background-color: rgba(255, 255, 255, 0.9);
  transform: translateY(0.0625rem);
}