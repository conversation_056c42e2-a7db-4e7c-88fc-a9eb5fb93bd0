package com.emm.repository;

import com.emm.model.OperationLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface OperationLogRepository extends JpaRepository<OperationLog, Long> { 
  
    @Query("SELECT o FROM OperationLog o WHERE o.orderId = :orderId")
    List<OperationLog> findByOrderId(@Param("orderId") String orderId);

    @Query("SELECT o FROM OperationLog o WHERE o.faultId = :faultId")
    List<OperationLog> findByFaultId(@Param("faultId") String faultId);

    @Query("SELECT o FROM OperationLog o WHERE o.operatorId = :operatorId")
    List<OperationLog> findByOperatorId(@Param("operatorId") Long operatorId);
}