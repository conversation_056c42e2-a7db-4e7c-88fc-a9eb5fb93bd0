/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-popup[data-v-7db519c7] {
  position: fixed;
  z-index: 99;
}
.uni-popup.top[data-v-7db519c7], .uni-popup.left[data-v-7db519c7], .uni-popup.right[data-v-7db519c7] {
  top: 0;
}
.uni-popup .uni-popup__wrapper[data-v-7db519c7] {
  display: block;
  position: relative;
  /* iphonex 等安全区设置，底部安全区适配 */
}
.uni-popup .uni-popup__wrapper.left[data-v-7db519c7], .uni-popup .uni-popup__wrapper.right[data-v-7db519c7] {
  padding-top: 0;
  flex: 1;
}
.fixforpc-z-index[data-v-7db519c7] {
  z-index: 999;
}
.fixforpc-top[data-v-7db519c7] {
  top: 0;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  width: 100%;
}
.page-title {
  font-size: 1.25rem;
  font-weight: bold;
  color: #333;
  padding: 1.25rem 0.9375rem 0.625rem;
  text-align: center;
  background-color: #fff;
  position: relative;
}
.page-title::after {
  content: "";
  position: absolute;
  bottom: 0.15625rem;
  left: 50%;
  transform: translateX(-50%);
  width: 1.875rem;
  height: 0.1875rem;
  background-color: #1890ff;
  border-radius: 0.09375rem;
}
.form-container {
  flex: 1;
  padding: 0.9375rem 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}
.form-group {
  margin-bottom: 0.9375rem;
  background-color: #fff;
  border-radius: 0.375rem;
  padding: 0.75rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
  width: 90%;
  max-width: 21.25rem;
  margin-left: auto;
  margin-right: auto;
  box-sizing: border-box;
  border-left: 0.125rem solid #1890ff;
}
.form-label {
  display: block;
  font-size: 0.875rem;
  color: #333;
  margin-bottom: 0.5rem;
  font-weight: 500;
}
.required:after {
  content: "*";
  color: #f56c6c;
  margin-left: 0.1875rem;
}
.form-input {
  width: 100%;
  height: 2.5rem;
  border: 1px solid #dcdfe6;
  border-radius: 0.25rem;
  padding: 0 0.625rem;
  font-size: 0.875rem;
  box-sizing: border-box;
}
.select-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.placeholder {
  color: #999;
}
.select-arrow {
  font-size: 0.75rem;
  color: #999;
}
.form-textarea {
  width: 100%;
  height: 6.25rem;
  border: 1px solid #dcdfe6;
  border-radius: 0.25rem;
  padding: 0.625rem;
  font-size: 0.875rem;
  box-sizing: border-box;
}
.char-count {
  text-align: right;
  font-size: 0.75rem;
  color: #999;
  margin-top: 0.25rem;
}
.status-group {
  display: flex;
  flex-direction: row;
  gap: 0.9375rem;
}
.status-option {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 0.3125rem;
  padding: 0.625rem;
  border: 1px solid #dcdfe6;
  border-radius: 0.25rem;
  transition: all 0.3s;
  cursor: pointer;
}
.status-option.active {
  border-color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
}
.status-dot {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  background-color: #dcdfe6;
}
.status-option.active .status-dot {
  background-color: #1890ff;
}
.status-text {
  font-size: 0.875rem;
  color: #333;
}
.status-option.active .status-text {
  color: #1890ff;
  font-weight: 500;
}
.results-list {
  margin-top: 0.625rem;
  width: 100%;
}
.result-item {
  background-color: #f9f9f9;
  border-radius: 0.375rem;
  padding: 0.625rem;
  margin-bottom: 0.5rem;
  border-left: 0.125rem solid #1890ff;
  transition: all 0.3s ease;
  cursor: pointer;
}
.result-item:nth-child(3n+1) {
  border-left-color: #1890ff;
}
.result-item:nth-child(3n+2) {
  border-left-color: #52c41a;
}
.result-item:nth-child(3n+3) {
  border-left-color: #faad14;
}
.result-item:active {
  transform: scale(0.98);
  box-shadow: 0 0.0625rem 0.25rem rgba(0, 0, 0, 0.03);
}
.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}
.result-title {
  font-size: 0.875rem;
  font-weight: 500;
}
.result-status {
  padding: 0.1875rem 0.5rem;
  border-radius: 0.9375rem;
  font-size: 0.75rem;
}
.result-status.normal {
  background-color: #f0f9eb;
  color: #67c23a;
}
.result-status.abnormal {
  background-color: #fef0f0;
  color: #f56c6c;
}
.result-body {
  font-size: 0.8125rem;
  color: #666;
}
.result-row {
  margin-bottom: 0.25rem;
}
.result-label {
  color: #999;
  margin-right: 0.25rem;
}
.submit-section {
  margin: 1.25rem 0;
  width: 90%;
  max-width: 21.25rem;
  display: flex;
  justify-content: center;
  margin-left: auto;
  margin-right: auto;
}
.submit-btn {
  width: 100%;
  height: 2.75rem;
  line-height: 2.75rem;
  text-align: center;
  background-color: #1890ff;
  color: #fff;
  border-radius: 1.375rem;
  font-size: 1rem;
  font-weight: 500;
  box-shadow: 0 0.1875rem 0.5rem rgba(24, 144, 255, 0.3);
}
.submit-btn.disabled {
  background-color: #a0cfff;
}

/* 弹窗样式 */
.result-popup {
  background-color: #fff;
  border-top-left-radius: 0.625rem;
  border-top-right-radius: 0.625rem;
  padding-bottom: env(safe-area-inset-bottom);
  max-height: 80vh;
}
.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.9375rem;
  border-bottom: 1px solid #eee;
}
.popup-title {
  font-size: 1rem;
  font-weight: 500;
}
.popup-close {
  font-size: 0.875rem;
  color: #999;
}
.popup-content {
  padding: 0.9375rem;
  max-height: calc(80vh - 3.125rem);
  overflow-y: auto;
}
.popup-form-group {
  margin-bottom: 0.75rem;
  position: relative;
}
.popup-form-label {
  font-size: 0.875rem;
  color: #333;
  margin-bottom: 0.375rem;
  display: block;
}
.popup-input {
  width: 100%;
  height: 2.5rem;
  border: 1px solid #dcdfe6;
  border-radius: 0.25rem;
  padding: 0 0.625rem;
  font-size: 0.875rem;
  box-sizing: border-box;
}
.popup-input:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
.popup-form-textarea {
  width: 100%;
  height: 5rem;
  border: 1px solid #dcdfe6;
  border-radius: 0.25rem;
  padding: 0.625rem;
  font-size: 0.875rem;
  box-sizing: border-box;
}
.result-radio-group {
  display: flex;
  gap: 0.625rem;
}
.result-radio {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  border: 1px solid #dcdfe6;
  border-radius: 0.25rem;
  transition: all 0.3s;
}
.result-radio.result-radio-selected {
  border-color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
}
.result-image-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -0.3125rem;
}
.result-image-item {
  position: relative;
  width: 5rem;
  height: 5rem;
  margin: 0.3125rem;
  border-radius: 0.25rem;
  overflow: hidden;
}
.result-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.result-image-delete {
  position: absolute;
  top: 0.1875rem;
  right: 0.1875rem;
  width: 1.125rem;
  height: 1.125rem;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.icon-delete {
  font-size: 0.75rem;
  color: #ffffff;
  line-height: 0.75rem;
}
.result-image-add {
  width: 5rem;
  height: 5rem;
  margin: 0.3125rem;
  background-color: #f5f5f5;
  border: 1px dashed #ddd;
  border-radius: 0.25rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #999;
}
.icon-add {
  font-size: 1.5rem;
  line-height: 1.5rem;
  color: #999;
}
.result-image-tip {
  font-size: 0.75rem;
  color: #999;
  margin-top: 0.25rem;
}
.location-btns {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}
.location-btn {
  background-color: #1890ff;
  color: #fff;
  height: 2.1875rem;
  line-height: 2.1875rem;
  font-size: 0.875rem;
  border-radius: 0.25rem;
}
.popup-save-btn {
  margin-top: 1.25rem;
  width: 100%;
  height: 2.75rem;
  line-height: 2.75rem;
  text-align: center;
  background-color: #1890ff;
  color: #fff;
  border-radius: 0.25rem;
  font-size: 1rem;
}
.device-info-box {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}
.device-name {
  font-size: 0.875rem;
  font-weight: 500;
}
.device-category {
  font-size: 0.75rem;
  color: #999;
}
.standard-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}
.normal-range {
  font-size: 0.75rem;
  color: #999;
}
.check-method {
  font-size: 0.75rem;
  color: #999;
}
.parameter-unit {
  position: absolute;
  right: 0.625rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.75rem;
  color: #666;
}

/* 时间选择器样式 */
.datetime-popup {
  background-color: #fff;
  border-top-left-radius: 0.625rem;
  border-top-right-radius: 0.625rem;
  padding-bottom: env(safe-area-inset-bottom);
}
.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0.9375rem;
  border-bottom: 1px solid #eee;
}
.popup-title {
  font-size: 1rem;
  font-weight: 500;
  color: #333;
}
.popup-close {
  font-size: 0.875rem;
  color: #999;
  padding: 0.3125rem;
}
.picker-body {
  padding: 0.9375rem 0.625rem;
}
.picker {
  width: 100%;
  height: 12.5rem;
}
.picker-item {
  height: 50px;
  line-height: 50px;
  text-align: center;
  font-size: 0.875rem;
  color: #333;
}
.btn-area {
  padding: 0.625rem 0.9375rem 1.25rem;
}
.confirm-btn {
  width: 100%;
  background-color: #1890ff;
  color: #fff;
  height: 2.75rem;
  line-height: 2.75rem;
  font-size: 1rem;
  border-radius: 1.375rem;
  font-weight: 500;
}

/* 执行人选择器样式 */
.executor-popup {
  background-color: #fff;
  border-top-left-radius: 0.625rem;
  border-top-right-radius: 0.625rem;
  padding-bottom: env(safe-area-inset-bottom);
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}
.executor-body {
  padding: 0.625rem 0.9375rem;
  flex: 1;
}
.executor-search {
  margin-bottom: 0.625rem;
}
.search-input {
  width: 100%;
  height: 2.5rem;
  border: 1px solid #dcdfe6;
  border-radius: 0.25rem;
  padding: 0 0.625rem;
  font-size: 0.875rem;
  box-sizing: border-box;
  background-color: #f5f5f5;
}
.executor-list {
  height: 18.75rem;
  margin-bottom: 0.625rem;
}
.executor-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0.3125rem;
  border-bottom: 1px solid #eaeaea;
  transition: background-color 0.3s ease;
}
.executor-item:active {
  background-color: rgba(24, 144, 255, 0.05);
}
.executor-info {
  display: flex;
  align-items: center;
}
.executor-name {
  font-size: 0.875rem;
  margin-left: 0.5rem;
  color: #333;
}
.executor-dept {
  font-size: 0.75rem;
  color: #999;
}
.btn-area {
  padding: 0.625rem 0.9375rem 1.25rem;
}
.confirm-btn {
  width: 100%;
  background: linear-gradient(135deg, #1890ff, #096dd9);
  color: #fff;
  height: 2.75rem;
  line-height: 2.75rem;
  font-size: 1rem;
  border-radius: 1.375rem;
  font-weight: 500;
  box-shadow: 0 0.1875rem 0.5rem rgba(24, 144, 255, 0.3);
}