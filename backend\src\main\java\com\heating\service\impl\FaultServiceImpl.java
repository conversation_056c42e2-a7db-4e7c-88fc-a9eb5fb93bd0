
package com.heating.service.impl;

import com.heating.dto.fault.FaultSetStatusRequest;
import com.heating.entity.fault.TFault;
import com.heating.entity.fault.TFaultAttachment;
import com.heating.entity.order.TOperationLog;
import com.heating.entity.order.TWorkOrder;
import com.heating.entity.house.THouse;
import com.heating.entity.user.TUser;
import com.heating.repository.*;
import com.heating.entity.TManagerHeatUnit;
import com.heating.service.FaultService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.heating.dto.fault.FaultReportRequest;
import com.heating.dto.fault.FaultAttachmentRequest;
import com.heating.dto.fault.FaultWeeklyCountResponse;
import com.heating.dto.fault.FaultMessageResponse;
import com.heating.dto.fault.FaultRequest;

import java.util.Optional;


import java.sql.Date;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;

@Service
public class FaultServiceImpl implements FaultService {

    private static final Logger logger = LoggerFactory.getLogger(FaultServiceImpl.class);

    @Autowired
    private FaultRepository faultRepository;

    @Autowired
    private FaultAttachmentRepository faultAttachmentRepository;

    @Autowired
    private WorkOrderRepository workOrderRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private OperationLogRepository operationLogRepository;
    
    @Autowired
    private ManagerHeatUnitRepository managerHeatUnitRepository; 
    
    @Autowired
    private HouseRepository houseRepository;

    @Override
    @Transactional
    public void reportFault(FaultReportRequest request) {
        try { 
              // 先检查房间号对应的住户是否存在
              if (request.getAddress() != null && !request.getAddress().trim().isEmpty()) {
                  try {
                      // 解析房间号格式 xx-xx-xxxx
                      String address = request.getAddress().trim();
                      // 查找对应的住户ID
                      THouse house = houseRepository.findByHeatUnitIdAndRoomInfo(
                          request.getHeatUnitId(),
                              address
                      );

                      if (house == null) {
                          logger.warn("未找到匹配的住户: 小区ID={}, 地址={}", request.getHeatUnitId(), address);
                          // 抛出自定义异常，包含详细错误信息
                          throw new RuntimeException("未找到住户信息，请检查楼号、单元号和房号是否正确");
                      }

                      // 如果找到了住户，记录住户ID
                      logger.info("找到匹配的住户ID: {}", house.getId());

                  } catch (RuntimeException e) {
                      // 重新抛出RuntimeException类型的异常
                      throw e;
                  } catch (Exception e) {
                      logger.error("解析房间号时出错: {}", e.getMessage(), e);
                      throw new RuntimeException("解析房间号时出错: " + e.getMessage());
                  }
              }
              
              // 创建故障对象
              TFault fault = new TFault();
              fault.setFaultNo(generateFaultNo());  
              fault.setHeatUnitId(request.getHeatUnitId());
              fault.setAlarmId(request.getAlarmId());
              fault.setFaultType(request.getFaultType());
              fault.setFaultSource(request.getFaultSource());
              fault.setFaultLevel(request.getFaultLevel() != null ? request.getFaultLevel() : "1");
              fault.setFaultDesc(request.getFaultDesc());
              fault.setOccurTime(request.getOccurTime());
              fault.setReportUserId(request.getReportUserId());
              fault.setAddress(request.getAddress());
              fault.setReportTime(LocalDateTime.now());
              
              // 判断故障状态，如果没有设置或为空，则设置为"待确认"
              String faultStatus = request.getFaultStatus();
              if (faultStatus == null || faultStatus.trim().isEmpty()) {
                  faultStatus = "待确认";
              }
              fault.setFaultStatus(faultStatus);
              
              fault.setManagerId(request.getManagerId());
              
              // 处理房间号，查找对应的住户ID
              if (request.getAddress() != null && !request.getAddress().trim().isEmpty()) {
                  try {
                      // 解析房间号格式 xx-xx-xxxx
                      String address = request.getAddress().trim();
                      // 查找对应的住户ID
                      THouse house = houseRepository.findByHeatUnitIdAndRoomInfo(
                          request.getHeatUnitId(),
                              address
                      );

                      // 此时我们已经在上面验证过house不为null
                      fault.setHouseId(house.getId());
                  } catch (Exception e) {
                      logger.error("设置住户ID时出错: {}", e.getMessage(), e);
                      // 继续处理，不影响故障上报
                  }
              }
              
              fault.setCreatedAt(LocalDateTime.now());  
              fault.setUpdatedAt(LocalDateTime.now());  
              faultRepository.save(fault);

              // 保存故障附件
              for (FaultAttachmentRequest attachment : request.getAttachment()) {
                  TFaultAttachment faultAttachment = new TFaultAttachment();
                faultAttachment.setFaultId(fault.getId());
                faultAttachment.setFileType(attachment.getFileType());
                faultAttachment.setFilePath(attachment.getFilePath());
                faultAttachment.setCreatedAt(LocalDateTime.now());
                faultAttachmentRepository.save(faultAttachment);
              }
              
              // 如果故障状态为"已确认"，自动创建工单
              if ("已确认".equals(faultStatus)) {
                  logger.info("故障状态为已确认，自动创建工单");
                  
                  // 创建工单
                  TWorkOrder workOrder = new TWorkOrder();
                  workOrder.setOrderNo(generateOrderNo());
                  workOrder.setFaultId(fault.getId());
                  workOrder.setRepairContent(fault.getFaultDesc()); 
                  workOrder.setOrderStatus("待接单");
                  workOrder.setHeatUnitId(fault.getHeatUnitId());
                  workOrder.setCreatedAt(LocalDateTime.now());
                  workOrder.setUpdatedAt(LocalDateTime.now());
                  workOrderRepository.save(workOrder);
                  
                  // 创建操作日志
                  TOperationLog log = new TOperationLog();
                  log.setWorkOrderId(workOrder.getId());
                  log.setOperationType("故障确认");
                  log.setOperationDesc("系统自动确认故障并生成工单");
                  log.setOperatorId(request.getReportUserId()); // 使用上报人作为操作人
                  log.setCreatedAt(LocalDateTime.now());
                  operationLogRepository.save(log);
                  
                  logger.info("已为故障 {} 创建工单 {}", fault.getId(), workOrder.getId());
              }
                 
        } catch (RuntimeException e) {
            logger.error("故障上报失败: {}", e.getMessage());
            throw e; // 直接重新抛出运行时异常，保留原始错误信息
        } catch (Exception e) {
            logger.error("故障上报失败: {}", e.getMessage(), e);
            throw new RuntimeException("故障上报失败: " + e.getMessage());
        }
    }
 

    @Override
    public Map<String, Object> getFaultList(String status, Date date, String heatUnitId, Integer page, Integer pageSize) {
        try {
            // 默认值处理
            page = (page == null || page < 1) ? 1 : page;
            pageSize = (pageSize == null || pageSize < 1) ? 10 : pageSize;
            
            // 计算偏移量
            int offset = (page - 1) * pageSize;

            // 声明变量
            List<Map<String, Object>> faults;
            long total;
            
            // 处理热用户ID
            if (heatUnitId != null && !heatUnitId.isEmpty()) {
                logger.info("根据用户项目权限筛选故障列表: heatUnitId={}", heatUnitId);
                
                // 检查是否有全局权限（包含ID=0）
                boolean hasAllPermission = heatUnitId.equals("0") || 
                                          heatUnitId.split(",").length > 0 && 
                                          java.util.Arrays.asList(heatUnitId.split(",")).contains("0");
                
                if (hasAllPermission) {
                    // 有全局权限，获取所有故障记录
                    logger.info("用户拥有全局权限，显示所有故障记录");
                    faults = faultRepository.findFaultListWithPaging(status, date, offset, pageSize);
                    total = faultRepository.countFaultList(status, date);
                } else {
                    // 将逗号分隔的热用户ID转换为列表
                    List<Long> heatUnitIdList = new ArrayList<>();
                    for (String id : heatUnitId.split(",")) {
                        try {
                            heatUnitIdList.add(Long.parseLong(id.trim()));
                        } catch (NumberFormatException e) {
                            logger.warn("无效的热用户ID格式: {}", id);
                        }
                    }
                    
                    if (!heatUnitIdList.isEmpty()) {
                        if (heatUnitIdList.size() == 1) {
                            // 只有一个热用户ID时，使用单热用户查询
                            Long singleHeatUnitId = heatUnitIdList.get(0);
                            faults = faultRepository.findFaultListByHeatUnitWithPaging(status, date, singleHeatUnitId, offset, pageSize);
                            total = faultRepository.countFaultListByHeatUnit(status, date, singleHeatUnitId);
                        } else {
                            // 多个热用户ID时，使用多热用户查询方法
                            logger.info("使用多热用户查询方法，热用户IDs: {}", heatUnitIdList);
                            faults = findFaultListByMultipleHeatUnits(status, date, heatUnitIdList, offset, pageSize);
                            total = countFaultListByMultipleHeatUnits(status, date, heatUnitIdList);
                        }
                    } else {
                        // 无有效的热用户ID，返回空列表
                        logger.warn("用户没有有效的热用户权限，返回空列表");
                        faults = new ArrayList<>();
                        total = 0;
                    }
                }
            } else {
                // 没有指定热用户ID，获取所有故障记录
                logger.info("未指定热用户ID，显示所有故障记录");
                faults = faultRepository.findFaultListWithPaging(status, date, offset, pageSize);
                total = faultRepository.countFaultList(status, date);
            }
            
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("list", faults);
            result.put("total", total);
            result.put("page", page);
            result.put("pageSize", pageSize);
            result.put("totalPages", (total + pageSize - 1) / pageSize);
            
            return result;
        } catch (Exception e) {
            logger.error("获取故障列表失败", e);
            throw new RuntimeException("获取故障列表失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getFaultDetail(long faultId) {
        try {
            logger.info("=== Fault Detail Debug ===");
            logger.info("Requested fault_id: {}", faultId);
            
            // 获取故障基本信息
            Map<String, Object> faultDetail = faultRepository.findFaultDetail(faultId);
            if (faultDetail == null) {
                throw new RuntimeException("故障信息不存在");
            }
            
            // 获取附件信息
            List<TFaultAttachment> attachments = faultAttachmentRepository.findByFaultId(faultId);
            logger.info("Found {} attachments for fault ID: {}", attachments.size(), faultId);
            
            // 分离图片和视频附件
            List<String> imageUrls = new ArrayList<>();
            String videoUrl = null;
            
            for (TFaultAttachment attachment : attachments) {
                logger.info("Processing attachment: type={}, path={}", attachment.getFileType(), attachment.getFilePath());
                
                if ("image".equals(attachment.getFileType())) {
                    String imagePath = attachment.getFilePath();
                    // 确保路径格式正确
                    if (imagePath != null && !imagePath.isEmpty()) {
                        imageUrls.add(imagePath);
                        logger.info("Added image URL: {}", imagePath);
                    }
                } else if ("video".equals(attachment.getFileType())) {
                    videoUrl = attachment.getFilePath();
                    logger.info("Found video URL: {}", videoUrl);
                }
            }
            
            // 构建返回数据
            Map<String, Object> data = new HashMap<>();
            data.put("fault_info", faultDetail);
            data.put("images", imageUrls);
            if (videoUrl != null) {
                data.put("video", videoUrl);
            }
            
            // 检查故障状态是否为"已确认"，如果是则获取关联的工单信息
            String faultStatus = (String) faultDetail.get("fault_status");
            if ("已确认".equals(faultStatus)) {
                logger.info("故障状态为已确认，获取关联工单信息");
                
                // 根据故障ID查询关联的工单
                Optional<TWorkOrder> workOrderOpt = workOrderRepository.findByFaultId(faultId);
                if (workOrderOpt.isPresent()) {
                    TWorkOrder workOrder = workOrderOpt.get();
                    
                    // 构建工单信息
                    Map<String, Object> workOrderInfo = new HashMap<>();
                    workOrderInfo.put("id", workOrder.getId());
                    workOrderInfo.put("code", workOrder.getOrderNo());
                    workOrderInfo.put("status", workOrder.getOrderStatus());
                    workOrderInfo.put("create_time", workOrder.getCreatedAt() != null ? 
                        workOrder.getCreatedAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")) : "");
                    
                    // 获取维修人员信息
                    if (workOrder.getRepairUserId() > 0) {
                        // 从用户表中获取维修人员信息
                        Optional<TUser> userOpt = userRepository.findById(workOrder.getRepairUserId());
                        if (userOpt.isPresent()) {
                            TUser repairUser = userOpt.get();
                            workOrderInfo.put("assignee", repairUser.getName() != null ? repairUser.getName() : repairUser.getUsername());
                            workOrderInfo.put("phone", repairUser.getPhone() != null ? repairUser.getPhone() : "暂无联系方式");
                        } else {
                            workOrderInfo.put("assignee", "维修人员" + workOrder.getRepairUserId());
                            workOrderInfo.put("phone", "暂无联系方式");
                        }
                    }
                    
                    // 添加维修内容和结果
                    if (workOrder.getRepairContent() != null && !workOrder.getRepairContent().isEmpty()) {
                        workOrderInfo.put("repair_content", workOrder.getRepairContent());
                    }
                    
                    if (workOrder.getRepairResult() != null && !workOrder.getRepairResult().isEmpty()) {
                        workOrderInfo.put("repair_result", workOrder.getRepairResult());
                    }
                    
                    // 添加维修耗材信息
//                    if (workOrder.getRepairMaterials() != null && !workOrder.getRepairMaterials().isEmpty()) {
//                        workOrderInfo.put("repair_materials", workOrder.getRepairMaterials());
//                    }
                    
                    // 如果工单已完成，添加完成时间
                    if ("已完成".equals(workOrder.getOrderStatus()) && workOrder.getRepairTime() != null) {
                        workOrderInfo.put("complete_time", workOrder.getRepairTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")));
                    }
                    
                    // 获取工单的操作日志
                    List<Map<String, Object>> operationLogs = workOrderRepository.findOperationLogs(workOrder.getId());
                    
                    // 处理操作日志中的操作人信息
                    if (operationLogs != null && !operationLogs.isEmpty()) {
                        for (Map<String, Object> log : operationLogs) {
                            Object operatorIdObj = log.get("operatorId");
                            if (operatorIdObj != null) {
                                try {
                                    Long operatorId = Long.valueOf(operatorIdObj.toString());
                                    Optional<TUser> operatorOpt = userRepository.findById(operatorId);
                                    if (operatorOpt.isPresent()) {
                                        TUser operator = operatorOpt.get();
                                        log.put("operatorName", operator.getName() != null ? operator.getName() : operator.getUsername());
                                    }
                                } catch (Exception e) {
                                    logger.warn("获取操作人信息失败: {}", e.getMessage());
                                }
                            }
                        }
                    }
                    
                    // 将工单信息和操作日志添加到返回数据中
                    data.put("work_order", workOrderInfo);
                    data.put("operation_logs", operationLogs);
                    
                    logger.info("成功获取工单信息: id={}, status={}", workOrder.getId(), workOrder.getOrderStatus());
                } else {
                    logger.info("未找到故障ID={}关联的工单", faultId);
                }
            }
            
            logger.info("Returning data: images={}, video={}", imageUrls.size(), videoUrl != null);
            return data;
        } catch (Exception e) {
            logger.error("Error in get_fault_detail: {}", e.getMessage(), e);
            logger.error("Error type: {}", e.getClass().getName());
            logger.error("Stacktrace:", e);
            throw new RuntimeException("获取故障详情失败: " + e.getMessage());
        }
    }

    private String generateFaultNo() {
        return "FAULT-"+LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
    }

    private String generateOrderNo() {
        return "ORDER-"+LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
    } 

    @Override
    public Map<String, Object> getFaultStatistics() {
        Map<String, Object> result = new HashMap<>();
        result.put("total", faultRepository.getTotalCount());
        result.put("pending", faultRepository.countByStatus("待确认"));
        result.put("processing", faultRepository.countByStatus("已确认"));  
        result.put("returned", faultRepository.countByStatus("已退回"));
        return result;
    }

    @Override
    public List<Map<String, Object>> getRecentFaults() {
        return faultRepository.findRecentFaults();
    }   

    @Override
    public void setFaultStatus(FaultSetStatusRequest request) {
        logger.info("Confirming fault with ID: {}", request.getFaultId());
        TFault fault = faultRepository.findById(String.valueOf(request.getFaultId())).orElseThrow(() -> new RuntimeException("Fault not found"));
        if (fault == null) {
            throw new RuntimeException("故障信息不存在");
        }

        TOperationLog log = new TOperationLog(); 
        if ("已确认".equals(request.getFaultStatus())) {

            // 更新故障状态
            fault.setFaultStatus("已确认");
            if (request.getManagerId() != null) {
                fault.setManagerId(request.getManagerId());
            }
            fault.setUpdatedAt(LocalDateTime.now());
            faultRepository.save(fault);

            // 生成工单
            TWorkOrder workOrder = new TWorkOrder();
            workOrder.setOrderNo(generateOrderNo());
            workOrder.setFaultId(request.getFaultId());
            workOrder.setRepairContent(fault.getFaultDesc()); 
            workOrder.setOrderStatus("待接单");
            
            // 设置热用户ID，优先使用请求中的，如果没有则使用故障中的
            if (request.getHeatUnitId() != null) {
                workOrder.setHeatUnitId(request.getHeatUnitId());
                logger.info("从请求中获取热用户ID: {}", request.getHeatUnitId());
            } else {
                logger.warn("无法获取热用户ID，工单创建可能不完整");
            }
            
            workOrder.setCreatedAt(LocalDateTime.now());
            workOrder.setUpdatedAt(LocalDateTime.now());
            workOrderRepository.save(workOrder); 

            // 生成日志 
            log.setWorkOrderId(workOrder != null ? workOrder.getId() : null); 
            log.setOperationType("故障确认");
            log.setOperationDesc("确认故障并生成工单");
            log.setOperatorId(request.getOperatorId());
            log.setCreatedAt(LocalDateTime.now());
            operationLogRepository.save(log);  // 保存日志

        } else if ("已退回".equals(request.getFaultStatus())) {
            fault.setFaultStatus("已退回");
            if (request.getManagerId() != null) {
                fault.setManagerId(request.getManagerId());
            }
            fault.setUpdatedAt(LocalDateTime.now());
            faultRepository.save(fault);
        } else if ("已完成".equals(request.getFaultStatus())){
            fault.setFaultStatus("已完成");
            fault.setUpdatedAt(LocalDateTime.now());
            faultRepository.save(fault);

            // 改变工单状态
            Optional<TWorkOrder> workOrder = workOrderRepository.findByFaultId(request.getFaultId());
            if (workOrder.isPresent()) {
                workOrder.get().setOrderStatus("已完成");
                workOrder.get().setUpdatedAt(LocalDateTime.now());
                workOrderRepository.save(workOrder.get());
            }   

            // 生成日志 
            log.setWorkOrderId(workOrder.get().getId());
            log.setOperationType("故障完成");
            log.setOperationDesc("管理员确认故障已完成");
            log.setOperatorId(request.getOperatorId()); 
            operationLogRepository.save(log);  // 保存日志  
            
        } else {
            throw new RuntimeException("无效的故障状态");
        }  
    }

    /**
     * 获取本周故障告警数量
     * @return 本周故障告警数量
     */
    @Override
    public FaultWeeklyCountResponse getWeeklyFaultCount() {
        Long count = faultRepository.countCurrentWeekFaults();
        return new FaultWeeklyCountResponse(count.intValue());
    }

    @Override
    public List<Map<String, Object>> getFaultsByManagerId(Long managerId) {
        if (managerId == null) {
            throw new IllegalArgumentException("管理员ID不能为空");
        }
        return faultRepository.findByManagerId(managerId);
    }

 
    /**
     * 获取管理员ID列表
     * 根据热用户ID从管理人员与热用户关联表中获取管理员ID列表
     * 
     * @param heatUnitId 热用户ID
     * @return 管理员ID列表
     */
    private List<Long> getManagerIds(Object heatUnitId) { 
        if (heatUnitId == null) {
            logger.warn("热用户ID为空，无法获取管理员列表");
            return new ArrayList<>();
        }
        
        Long unitId;
        try {
            unitId = Long.parseLong(heatUnitId.toString());
        } catch (NumberFormatException e) {
            logger.error("热用户ID格式错误: {}", heatUnitId);
            return new ArrayList<>();
        }
        
        // 从管理人员与热用户关联表中获取管理员ID列表  
        List<TManagerHeatUnit> managerHeatUnit = managerHeatUnitRepository.findByHeatUnitId(unitId);
        
        List<Long> managerIds = new ArrayList<>();
        for (TManagerHeatUnit item : managerHeatUnit) {
            managerIds.add(item.getManagerId());
        } 
        
        // 返回管理员ID列表
        return managerIds;
    }

    /**
     * 获取故障消息
     * 从第三方接口获取当前所有需要处理的故障消息列表
     * 
     * @param userId 用户ID
     * @param role 用户角色
     * @param heatUnitId 用户项目权限，逗号分隔的项目ID字符串
     * @return 故障消息列表
     */
    @Override
    public List<FaultMessageResponse> getFaultMessages(Long userId, String role, String heatUnitId) {
        logger.info("开始获取故障消息列表: userId={}, role={}, heatUnitId={}", userId, role, heatUnitId); 
        List<FaultMessageResponse> faultMessages = new ArrayList<>();  
        try {  
            // 获取fault_status= '待确认' 的故障数据列表
            List<Map<String, Object>> apiResponseList = faultRepository.findFaultList("待确认", null);
            
            // 检查用户是否有全局权限
            boolean hasAllPermission = false;
            if (heatUnitId != null && !heatUnitId.isEmpty()) {
                hasAllPermission = heatUnitId.equals("0") || 
                                   heatUnitId.split(",").length > 0 && 
                                   java.util.Arrays.asList(heatUnitId.split(",")).contains("0");
            }
            
            // 将逗号分隔的热用户ID转换为列表
            List<Long> userHeatUnitIds = new ArrayList<>();
            if (heatUnitId != null && !heatUnitId.isEmpty() && !hasAllPermission) {
                for (String id : heatUnitId.split(",")) {
                    try {
                        userHeatUnitIds.add(Long.parseLong(id.trim()));
                    } catch (NumberFormatException e) {
                        logger.warn("无效的热用户ID格式: {}", id);
                    }
                }
            }

            // 遍历apiResponseList，将每个故障数据转换为FaultMessageResponse对象
            for (Map<String, Object> apiResponse : apiResponseList) {
                // 获取热用户ID
                Long faultHeatUnitId = null;
                try {
                    faultHeatUnitId = Long.parseLong(apiResponse.get("heat_unit_id").toString());
                } catch (Exception e) {
                    logger.warn("解析热用户ID失败: {}", apiResponse.get("heat_unit_id"));
                    continue;
                }
                
                // 根据用户权限过滤故障
                if (!hasAllPermission && !userHeatUnitIds.isEmpty()) {
                    // 如果用户没有全局权限，且故障的热用户ID不在用户权限列表中，则跳过此故障
                    if (faultHeatUnitId == null || !userHeatUnitIds.contains(faultHeatUnitId)) {
                        logger.debug("过滤掉无权限查看的故障: faultId={}, heatUnitId={}", 
                                    apiResponse.get("fault_id"), faultHeatUnitId);
                        continue;
                    }
                }
                
                FaultMessageResponse faultMessage = new FaultMessageResponse();
                faultMessage.setId(Long.parseLong(apiResponse.get("fault_id").toString()));
                faultMessage.setHeatUnitId(faultHeatUnitId);     
                faultMessage.setHeatUnitName(apiResponse.get("heat_unit_name").toString());
                faultMessage.setManagerIds(getManagerIds(apiResponse.get("heat_unit_id")));
                faultMessage.setFaultDesc(apiResponse.get("fault_desc").toString());
                faultMessage.setOccurTime(apiResponse.get("occur_time").toString());
                faultMessages.add(faultMessage);
            } 
            return faultMessages;
        } catch (Exception e) {
            logger.error("获取故障消息列表失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取故障消息列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户的热用户权限ID列表
     * @param userId 用户ID
     * @return 逗号分隔的热用户ID字符串
     */
    private String getUserHeatUnitIds(Long userId) {
        if (userId == null) {
            return null;
        }
        
        try {
            // 实际应该从数据库或缓存中获取用户的热用户权限
            // 这里简化处理，假设通过用户ID查询到的热用户权限存储在数据库中
            // 这个方法需要根据实际的数据库结构和业务逻辑进行实现
            
            // 示例：从 manager_heat_unit 表查询用户关联的热用户ID
            List<TManagerHeatUnit> managerHeatUnits = managerHeatUnitRepository.findByManagerId(userId);
            
            if (managerHeatUnits == null || managerHeatUnits.isEmpty()) {
                logger.info("用户(ID={})没有关联的热用户权限", userId);
                return null;
            }
            
            // 将查询结果转换为逗号分隔的字符串
            StringBuilder sb = new StringBuilder();
            for (TManagerHeatUnit unit : managerHeatUnits) {
                if (sb.length() > 0) {
                    sb.append(",");
                }
                sb.append(unit.getHeatUnitId());
            }
            
            String result = sb.toString();
            logger.info("用户(ID={})的热用户权限: {}", userId, result);
            return result;
        } catch (Exception e) {
            logger.error("获取用户热用户权限失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据多个热用户ID查询故障列表
     * 这个方法在FaultRepository中不存在时的临时替代方案
     * 实际项目中应该在Repository层实现此方法以提高性能
     * 
     * @param status 故障状态
     * @param date 日期
     * @param heatUnitIds 热用户ID列表
     * @param offset 偏移量
     * @param pageSize 每页数量
     * @return 故障列表
     */
    private List<Map<String, Object>> findFaultListByMultipleHeatUnits(String status, Date date, List<Long> heatUnitIds, int offset, int pageSize) {
        if (heatUnitIds == null || heatUnitIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 创建结果列表
        List<Map<String, Object>> result = new ArrayList<>();
        
        // 分别查询每个热用户的故障列表并合并结果
        for (Long heatUnitId : heatUnitIds) {
            List<Map<String, Object>> faults = faultRepository.findFaultListByHeatUnitWithPaging(status, date, heatUnitId, 0, Integer.MAX_VALUE);
            if (faults != null && !faults.isEmpty()) {
                result.addAll(faults);
            }
        }
        
        // 手动实现分页
        // 按照某个字段排序（通常是时间）
        // 这里以故障发生时间为例
        result.sort((a, b) -> {
            Object timeA = a.get("occur_time");
            Object timeB = b.get("occur_time");
            if (timeA == null && timeB == null) return 0;
            if (timeA == null) return 1;
            if (timeB == null) return -1;
            return timeB.toString().compareTo(timeA.toString()); // 降序排列
        });
        
        // 截取分页数据
        int endIndex = Math.min(offset + pageSize, result.size());
        if (offset < result.size()) {
            return result.subList(offset, endIndex);
        } else {
            return new ArrayList<>();
        }
    }
    
    /**
     * 计算多个热用户故障列表的总数
     * 
     * @param status 故障状态
     * @param date 日期
     * @param heatUnitIds 热用户ID列表
     * @return 故障总数
     */
    private long countFaultListByMultipleHeatUnits(String status, Date date, List<Long> heatUnitIds) {
        if (heatUnitIds == null || heatUnitIds.isEmpty()) {
            return 0;
        }
        
        long total = 0;
        for (Long heatUnitId : heatUnitIds) {
            total += faultRepository.countFaultListByHeatUnit(status, date, heatUnitId);
        }
        
        return total;
    }

} 