/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.faq-container {
  background-color: #f5f7fa;
  display: flex;
  flex-direction: column;
  height: 100vh;
}
.page-header {
  background-color: #fff;
  padding: 0.9375rem;
  border-bottom: 0.03125rem solid #eee;
}
.page-header .page-title {
  font-size: 1.125rem;
  font-weight: bold;
  color: #333;
}
.search-box {
  padding: 0.625rem 0.9375rem;
  background-color: #fff;
}
.search-box .search-input {
  position: relative;
  height: 2.5rem;
  background-color: #f5f7fa;
  border-radius: 1.25rem;
  display: flex;
  align-items: center;
  padding: 0 0.9375rem;
}
.search-box .search-input .search-icon {
  font-size: 1rem;
  color: #999;
  margin-right: 0.3125rem;
}
.search-box .search-input uni-input {
  flex: 1;
  height: 2.5rem;
  font-size: 0.875rem;
}
.search-box .search-input .clear-icon {
  font-size: 0.875rem;
  color: #999;
  padding: 0.3125rem;
}
.faq-tabs {
  display: flex;
  background-color: #fff;
  padding: 0 0.625rem;
  white-space: nowrap;
  overflow-x: auto;
  border-bottom: 0.03125rem solid #eee;
}
.faq-tabs .tab-item {
  padding: 0.625rem 0.9375rem;
  font-size: 0.875rem;
  color: #666;
  position: relative;
  flex-shrink: 0;
}
.faq-tabs .tab-item.active {
  color: #1890ff;
  font-weight: bold;
}
.faq-tabs .tab-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40%;
  height: 0.125rem;
  background-color: #1890ff;
  border-radius: 0.0625rem;
}
.faq-list {
  flex: 1;
  padding: 0.625rem 0;
}
.faq-list .faq-section {
  margin-bottom: 0.9375rem;
}
.faq-list .faq-section .section-title {
  padding: 0.625rem 0.9375rem;
  font-size: 0.9375rem;
  font-weight: bold;
  color: #333;
}
.faq-list .faq-section .faq-item {
  background-color: #fff;
  margin-bottom: 0.0625rem;
}
.faq-list .faq-section .faq-item .faq-question {
  padding: 0.9375rem;
  display: flex;
  align-items: flex-start;
  position: relative;
}
.faq-list .faq-section .faq-item .faq-question .question-marker {
  color: #1890ff;
  font-weight: bold;
  font-size: 0.9375rem;
  margin-right: 0.625rem;
  flex-shrink: 0;
}
.faq-list .faq-section .faq-item .faq-question .question-text {
  flex: 1;
  font-size: 0.875rem;
  color: #333;
  line-height: 1.6;
}
.faq-list .faq-section .faq-item .faq-question .question-arrow {
  font-size: 0.75rem;
  color: #999;
  margin-left: 0.625rem;
  transition: transform 0.3s;
  transform: rotate(90deg);
}
.faq-list .faq-section .faq-item .faq-question .question-arrow.arrow-up {
  transform: rotate(-90deg);
}
.faq-list .faq-section .faq-item .faq-answer {
  padding: 0 0.9375rem 0.9375rem;
  display: flex;
  align-items: flex-start;
  background-color: #f9f9f9;
}
.faq-list .faq-section .faq-item .faq-answer .answer-marker {
  color: #f5222d;
  font-weight: bold;
  font-size: 0.9375rem;
  margin-right: 0.625rem;
  flex-shrink: 0;
}
.faq-list .faq-section .faq-item .faq-answer .answer-text {
  flex: 1;
  font-size: 0.875rem;
  color: #666;
  line-height: 1.8;
}
.faq-list .no-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 3.125rem;
}
.faq-list .no-result uni-image {
  width: 6.25rem;
  height: 6.25rem;
  margin-bottom: 0.625rem;
}
.faq-list .no-result uni-text {
  font-size: 0.875rem;
  color: #999;
}
.feedback-section {
  padding: 0.9375rem;
  background-color: #fff;
  border-top: 0.03125rem solid #eee;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.feedback-section .feedback-text {
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 0.625rem;
}
.feedback-section .feedback-btn {
  padding: 0.46875rem 1.875rem;
  background-color: #1890ff;
  color: #fff;
  font-size: 0.875rem;
  border-radius: 1.25rem;
}
.feedback-section .feedback-btn:active {
  opacity: 0.8;
}