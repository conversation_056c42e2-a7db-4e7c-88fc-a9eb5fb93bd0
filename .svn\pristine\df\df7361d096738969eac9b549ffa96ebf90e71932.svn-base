package com.heating.service;

import com.heating.dto.permission.PermissionResponse;
import com.heating.dto.permission.RolePermissionResponse;
import java.util.List;

/**
 * 系统权限服务接口
 */
public interface SysPermissionService {

    /**
     * 获取系统权限列表
     * @return 权限列表
     */
    List<PermissionResponse> getSystemPermissions();
    
    /**
     * 获取角色权限编码列表
     * @param roleCode 角色编码
     * @return 角色权限列表
     */
    List<RolePermissionResponse> getRolePermissions(String roleCode);
} 