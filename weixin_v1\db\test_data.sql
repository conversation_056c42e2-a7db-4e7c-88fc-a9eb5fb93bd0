-- 插入故障数据
INSERT INTO fault (
    fault_id, station_id, fault_type, fault_level,
    fault_desc, occur_time, report_user_id, report_time,
    fault_status, created_at
) VALUES 
(CONCAT(DATE_FORMAT(NOW(), '%Y%m%d'), '000001'), 1, '设备故障', 1, 
 '1号机组异常震动', NOW(), 1, NOW(), '待确认', NOW()),
(CONCAT(DATE_FORMAT(NOW(), '%Y%m%d'), '000002'), 2, '管网故障', 2, 
 '管道泄漏', NOW(), 2, NOW(), '已确认', NOW()),
(CONCAT(DATE_FORMAT(NOW(), '%Y%m%d'), '000003'), 3, '温度异常', 3, 
 '供水温度过高', NOW(), 1, NOW(), '已退回', NOW());

-- 插入故障附件数据
INSERT INTO fault_attachment (
    fault_id, file_type, file_path, created_at
) VALUES 
('20240101000001', 'image', '/uploads/fault/20240101/img001.jpg', '2024-01-01 08:30:00'),
('20240101000001', 'video', '/uploads/fault/20240101/vid001.mp4', '2024-01-01 08:30:00'),
('20240101000002', 'image', '/uploads/fault/20240101/img002.jpg', '2024-01-01 09:30:00'),
('20240101000003', 'image', '/uploads/fault/20240101/img003.jpg', '2024-01-01 10:30:00'),
('20240101000004', 'image', '/uploads/fault/20240101/img004.jpg', '2024-01-01 11:15:00');

-- 插入工单数据
INSERT INTO work_order (
    order_id, fault_id, station_id, repair_user_id,
    repair_content, repair_result, repair_time,
    order_status, created_at
) VALUES 
(CONCAT(DATE_FORMAT(NOW(), '%Y%m%d'), '000001'), 
 CONCAT(DATE_FORMAT(NOW(), '%Y%m%d'), '000002'), 
 2, 3, '检查维修', NULL, NULL, '维修中', NOW());

-- 插入工单附件数据
INSERT INTO work_order_attachment (
    order_id, file_type, file_path, created_at
) VALUES 
('20240101000001', 'image', '/uploads/workorder/20240101/repair001.jpg', '2024-01-01 11:30:00'),
('20240101000001', 'image', '/uploads/workorder/20240101/repair002.jpg', '2024-01-01 11:30:00'),
('20240101000002', 'image', '/uploads/workorder/20240101/repair003.jpg', '2024-01-01 12:00:00');

-- 插入操作日志数据
INSERT INTO operation_log (
    fault_id, order_id, operation_type,
    operation_desc, operator_id, created_at
) VALUES 
('20240101000001', NULL, '故障上报',
 '用户提交故障上报', 1, '2024-01-01 08:30:00'),
('20240101000002', NULL, '故障上报',
 '用户提交故障上报', 2, '2024-01-01 09:30:00'),
('20240101000002', '20240101000001', '故障确认',
 '管理员确认故障并生成工单', 5, '2024-01-01 09:35:00'),
('20240101000002', '20240101000001', '工单接单',
 '维修人员接单', 3, '2024-01-01 10:00:00'),
('20240101000002', '20240101000001', '完成维修',
 '维修人员提交维修结果', 3, '2024-01-01 11:30:00'),
('20240101000003', NULL, '故障退回',
 '管理员退回故障', 5, '2024-01-01 10:35:00'),
('20240101000004', NULL, '故障确认',
 '管理员确认故障并生成工单', 5, '2024-01-01 11:20:00'),
('20240101000004', '20240101000002', '工单接单',
 '维修人员接单', 4, '2024-01-01 11:45:00');

-- 插入室外温度记录数据
INSERT INTO outdoor_temperature (
    temperature, record_time, created_at
) VALUES 
(-2.5, '2024-01-01 08:00:00', '2024-01-01 08:00:00'),
(-2.0, '2024-01-01 09:00:00', '2024-01-01 09:00:00'),
(-1.5, '2024-01-01 10:00:00', '2024-01-01 10:00:00'),
(-1.0, '2024-01-01 11:00:00', '2024-01-01 11:00:00'),
(0.0, '2024-01-01 12:00:00', '2024-01-01 12:00:00'),
(0.5, '2024-01-01 13:00:00', '2024-01-01 13:00:00');

-- 室温表
DROP TABLE IF EXISTS room_temperature;
CREATE TABLE room_temperature (
    id INT AUTO_INCREMENT PRIMARY KEY,
    community_name VARCHAR(50) NOT NULL COMMENT '小区名称',
    building_no VARCHAR(10) NOT NULL COMMENT '楼号',
    unit_no VARCHAR(10) NOT NULL COMMENT '单元号',
    room_no VARCHAR(10) NOT NULL COMMENT '房间号',
    temperature DECIMAL(4,1) NOT NULL COMMENT '室温',
    report_time DATETIME NOT NULL COMMENT '上报时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_community (community_name),
    INDEX idx_time (report_time)
) COMMENT '室温记录表';

-- 插入测试数据
INSERT INTO room_temperature 
(community_name, building_no, unit_no, room_no, temperature, report_time) VALUES 
('和平小区', 'A1', '1', '101', 22.5, '2024-01-01 08:00:00'),
('和平小区', 'A1', '1', '102', 21.8, '2024-01-01 08:05:00'),
('和平小区', 'A1', '2', '201', 23.0, '2024-01-01 08:10:00'),
('和平小区', 'A2', '1', '101', 20.5, '2024-01-01 08:15:00'),
('和平小区', 'A2', '1', '102', 21.0, '2024-01-01 08:20:00'),
('阳光小区', 'B1', '1', '101', 22.0, '2024-01-01 08:25:00'),
('阳光小区', 'B1', '1', '102', 21.5, '2024-01-01 08:30:00'),
('阳光小区', 'B1', '2', '201', 23.5, '2024-01-01 08:35:00'); 