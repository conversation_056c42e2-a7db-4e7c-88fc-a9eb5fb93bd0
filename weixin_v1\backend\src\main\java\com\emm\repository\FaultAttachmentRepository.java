package com.emm.repository;

import com.emm.model.FaultAttachment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface FaultAttachmentRepository extends JpaRepository<FaultAttachment, Long> {
     
    @Query(value = "SELECT " +
            "file_type, " +
            "file_path, " +
            "DATE_FORMAT(created_at, '%Y-%m-%d %H:%i') as uploadTime " +
            "FROM fault_attachment " +
            "WHERE fault_id = :faultId " +
            "ORDER BY created_at", nativeQuery = true)
    List<Map<String, Object>> findAttachmentsByFaultId(@Param("faultId") String faultId);

    // 添加基本的查询方法
    List<FaultAttachment> findByFaultId(String faultId);
} 