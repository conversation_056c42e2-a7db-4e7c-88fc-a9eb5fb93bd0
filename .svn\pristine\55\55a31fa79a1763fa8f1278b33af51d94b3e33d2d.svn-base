<template>
  <view>
    <slot></slot>
    <CustomTabBar v-if="showTabBar"></CustomTabBar>
  </view>
</template>

<script>
import CustomTabBar from "@/components/CustomTabBar.vue";

export default {
  name: "BaseTabBar",
  components: {
    CustomTabBar,
  },
  data() {
    return {
      showTabBar: true,
      tabBarPages: [
        "pages/home/<USER>",
        "pages/hes/list",
        "pages/message/center",
        "pages/user/info",
      ],
    };
  },
  onShow() {
    const pages = getCurrentPages();
    if (pages.length > 0) {
      const currentPage = pages[pages.length - 1];
      const currentRoute = currentPage.route;
      this.showTabBar = this.tabBarPages.includes(currentRoute);
    }
  },
};
</script>

<style></style>
