package com.heating.repository;

import com.heating.entity.TDevicePatrolItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 设备巡检项目Repository
 */
@Repository
public interface TDevicePatrolItemRepository extends JpaRepository<TDevicePatrolItem, Long> {
    
    /**
     * 根据设备ID和巡检项目字典ID查询设备巡检项
     * 
     * @param deviceId 设备ID
     * @param patrolItemDictId 巡检项目字典ID
     * @return 设备巡检项
     */
    Optional<TDevicePatrolItem> findByDeviceIdAndPatrolItemDictId(Long deviceId, Long patrolItemDictId);
    
    /**
     * 根据设备ID和巡检项目字典ID查询正常范围
     * 
     * @param deviceId 设备ID
     * @param patrolItemDictId 巡检项目字典ID
     * @return 正常范围
     */
    @Query("SELECT dpi.normalRange FROM TDevicePatrolItem dpi WHERE dpi.deviceId = :deviceId AND dpi.patrolItemDictId = :patrolItemDictId")
    String findNormalRangeByDeviceIdAndPatrolItemDictId(@Param("deviceId") Long deviceId, @Param("patrolItemDictId") Long patrolItemDictId);
    
    /**
     * 根据设备ID查询设备巡检项列表
     * 
     * @param deviceId 设备ID
     * @return 设备巡检项列表
     */
    List<TDevicePatrolItem> findByDeviceId(Long deviceId);
} 