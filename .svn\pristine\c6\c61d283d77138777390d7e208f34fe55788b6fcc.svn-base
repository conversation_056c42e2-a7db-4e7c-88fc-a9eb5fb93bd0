<<<<<<< .mine
package com.heating.controller;

import com.heating.dto.order.AcceptOrderRequest;
import com.heating.dto.order.CompleteOrderRequest;
import com.heating.dto.order.CreateOrderRequest;
import com.heating.dto.order.WorkOrderBaseInfoResponse;
import com.heating.dto.order.WorkOrderDetailResponse;
import com.heating.dto.order.WorkOrderMessageResponse;
import com.heating.dto.order.TransferOrderRequest;
import com.heating.service.WorkOrderService;
import com.heating.util.ApiResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat; 
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/WorkOrders")
public class WorkOrderController {

    private static final Logger logger = LoggerFactory.getLogger(WorkOrderController.class);

    @Autowired
    private WorkOrderService workOrderService;

    /**
     * 获取工单列表
     * @param date 查询日期
     * @param status 工单状态
     * @param uid 用户ID
     * @param limit 限制返回记录数
     * @param page 页码，默认1
     * @param pageSize 每页数量，默认10
     * @param type 工单类型，'my'表示我的工单，'pending'表示待接单工单
     * @param heatUnitId 用户项目权限，逗号分隔的项目ID字符串
     * @return 返回工单列表和总数
     */
    @GetMapping("/list")
    public ResponseEntity<?> getWorkOrderList(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) Long uid,
            @RequestParam(required = false) Integer limit,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String heatUnitId) {
        logger.info("获取工单列表: date={}, status={}, uid={}, limit={}, page={}, pageSize={}, type={}, heatUnitId={}", 
                   date, status, uid, limit, page, pageSize, type, heatUnitId);
        try {
            logger.debug("开始查询工单列表...");
            Map<String, Object> result = workOrderService.getWorkOrderList(date, status, uid, null, null, limit, page, pageSize, type, heatUnitId);
            return ResponseEntity.ok(ApiResponse.success("工单列表查询成功", result));
        } catch (Exception e) { 
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取工单详情
     * @param orderId 工单ID
     * @return 返回指定工单的详细信息
     */
    @GetMapping("/detail/{orderId}")
    public ResponseEntity<?> getWorkOrderDetail(@PathVariable int orderId) {
        logger.info("获取工单详情: orderId={}", orderId);
        try {
            WorkOrderDetailResponse workOrder = workOrderService.getWorkOrderDetail(orderId); 
            return ResponseEntity.ok(ApiResponse.success("工单详情查询成功", workOrder));
        } catch (Exception e) {
            logger.error("Error in GET /api/workOrder/detail/:{}", orderId, e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 完成工单
     * @param orderId 工单ID
     * @param request 完成工单请求体，包含:
     *                - 维修内容(repair_content)
     *                - 维修结果(repair_result)
     *                - 维修时间(repair_time)
     *                - 维修耗材及数量(repair_materials_quantity) 例如: {"管道": 2, "阀门": 1, "保温棉": 3}
     *                - 附件(attachment)
     * @return 返回完成工单的结果
     */
    @PostMapping("/complete")
    public ResponseEntity<?> completeWorkOrder(@RequestBody CompleteOrderRequest request) {
        logger.info("完成工单: orderId={}", request.getOrderId());
        logger.info("提交的数据: request={}", request);
        try {
            workOrderService.completeOrder(request); 
            return ResponseEntity.ok(ApiResponse.success("工单完成成功", null));
        } catch (Exception e) {
            logger.error("Error in POST /api/workOrder/complete/:{}", request.getOrderId(), e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 修改工单状态（接单、转移、完成）
     * @param request 修改工单状态请求体
     * @return 返回修改工单状态的结果
     */
    @PostMapping("/status")
    public ResponseEntity<?> updateWorkOrderStatus(@RequestBody AcceptOrderRequest request) {
        logger.info("修改工单状态: orderId={}, action={}", request.getOrderId(), request.getOrderStatus());
        try {
            workOrderService.updateWorkOrderStatus(request);  
            return ResponseEntity.ok(ApiResponse.success("工单状态修改成功", null)); 
        } catch (Exception e) {
            logger.error("Error in POST /api/workOrder/status: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 创建工单
     * @param request 创建工单请求体
     * @return 返回创建结果
     */
    @PostMapping("/create")
    public ResponseEntity<?> createWorkOrder(@RequestBody CreateOrderRequest request) {
        logger.info("创建工单请求: {}", request);
        try {
            workOrderService.createWorkOrder(request); 
            return ResponseEntity.ok(ApiResponse.success("工单创建成功", null));
        } catch (Exception e) {
            logger.error("创建工单失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage())); 
        }
    }
    
    /**
     * 获取工单消息
     * 从工单信息表中获取状态为 '待接单' 的记录列表
     * @param uid 用户ID
     * @param role 用户角色
     * @param heatUnitId 用户项目权限，逗号分隔的项目ID字符串
     * @return 返回待接单的工单消息列表
     */
    @GetMapping("/messages")
    public ResponseEntity<?> getWorkOrderMessages(
            @RequestParam(required = false) Long uid,
            @RequestParam(required = false) String role,
            @RequestParam(required = false) String heatUnitId) {
        logger.info("获取工单消息: uid={}, role={}, heatUnitId={}", uid, role, heatUnitId);
        try {
            List<WorkOrderMessageResponse> messages = workOrderService.getWorkOrderMessages(uid, role, heatUnitId);
            return ResponseEntity.ok(ApiResponse.success("工单消息获取成功", messages));
        } catch (Exception e) {
            logger.error("获取工单消息失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * 转派工单
     * @param request 转派工单请求体，包含:
     *                - 工单ID(order_id)
     *                - 转派人ID(transfer_user_id)
     *                - 转派目标人员ID(repair_user_id)
     *                - 转派原因(transfer_reason)
     *                - 转派时间(transfer_time)
     * @return 返回转派工单的结果
     */
    @PostMapping("/transfer")
    public ResponseEntity<?> transferWorkOrder(@RequestBody TransferOrderRequest request) {
        logger.info("转派工单: orderId={}", request.getOrderId());
        logger.info("提交的数据: request={}", request);
        try {
            workOrderService.transferOrder(request); 
            return ResponseEntity.ok(ApiResponse.success("工单转派成功", null));
        } catch (Exception e) {
            logger.error("Error in POST /api/WorkOrders/transfer: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取工单统计数据
     * @param uid 用户ID
     * @param heatUnitId 用户项目权限，逗号分隔的项目ID字符串
     * @return 返回工单统计数据，包括我的工单数、待处理工单数、待接单工单数
     */
    @GetMapping("/stats")
    public ResponseEntity<?> getWorkOrderStats(
            @RequestParam(required = false) Long uid,
            @RequestParam(required = false) String heatUnitId) {
        logger.info("获取工单统计数据: uid={}, heatUnitId={}", uid, heatUnitId);
        try {
            Map<String, Object> stats = workOrderService.getWorkOrderStats(uid, heatUnitId);
            return ResponseEntity.ok(ApiResponse.success("工单统计数据获取成功", stats));
        } catch (Exception e) {
            logger.error("获取工单统计数据失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
} ||||||| .r0
=======
package com.heating.controller;

import com.heating.dto.order.AcceptOrderRequest;
import com.heating.dto.order.CompleteOrderRequest;
import com.heating.dto.order.CreateOrderRequest;
import com.heating.dto.order.WorkOrderBaseInfoResponse;
import com.heating.dto.order.WorkOrderDetailResponse;
import com.heating.dto.order.WorkOrderMessageResponse;
import com.heating.dto.order.TransferOrderRequest;
import com.heating.service.WorkOrderService;
import com.heating.util.ApiResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat; 
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/WorkOrders")
public class WorkOrderController {

    private static final Logger logger = LoggerFactory.getLogger(WorkOrderController.class);

    @Autowired
    private WorkOrderService workOrderService;

    /**
     * 获取工单列表
     * @param date 查询日期
     * @param status 工单状态
     * @param role 用户角色，如果是'admin'，则获取所有记录，否则根据uid过滤
     * @param uid 用户ID
     * @param orderNo 工单号
     * @param limit 限制返回记录数
     * @param page 页码，默认1
     * @param pageSize 每页数量，默认10
     * @return 返回工单列表和总数
     */
    @GetMapping("/list")
    public ResponseEntity<?> getWorkOrderList(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String role,
            @RequestParam(required = false) Long uid,
            @RequestParam(required = false) String orderNo,
            @RequestParam(required = false) Integer limit,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        logger.info("获取工单列表: date={}, status={}, role={}, uid={}, orderNo={}, limit={}, page={}, pageSize={}", 
                   date, status, role, uid, orderNo, limit, page, pageSize);
        try {
            logger.debug("开始查询工单列表...");
            Map<String, Object> result = workOrderService.getWorkOrderList(date, status, uid, orderNo, role, limit, page, pageSize);
            return ResponseEntity.ok(ApiResponse.success("工单列表查询成功", result));
        } catch (Exception e) { 
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取工单详情
     * @param orderId 工单ID
     * @return 返回指定工单的详细信息
     */
    @GetMapping("/detail/{orderId}")
    public ResponseEntity<?> getWorkOrderDetail(@PathVariable int orderId) {
        logger.info("获取工单详情: orderId={}", orderId);
        try {
            WorkOrderDetailResponse workOrder = workOrderService.getWorkOrderDetail(orderId); 
            return ResponseEntity.ok(ApiResponse.success("工单详情查询成功", workOrder));
        } catch (Exception e) {
            logger.error("Error in GET /api/workOrder/detail/:{}", orderId, e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 完成工单
     * @param orderId 工单ID
     * @param request 完成工单请求体，包含:
     *                - 维修内容(repair_content)
     *                - 维修结果(repair_result)
     *                - 维修时间(repair_time)
     *                - 维修耗材及数量(repair_materials_quantity) 例如: {"管道": 2, "阀门": 1, "保温棉": 3}
     *                - 附件(attachment)
     * @return 返回完成工单的结果
     */
    @PostMapping("/complete")
    public ResponseEntity<?> completeWorkOrder(@RequestBody CompleteOrderRequest request) {
        logger.info("完成工单: orderId={}", request.getOrderId());
        logger.info("提交的数据: request={}", request);
        try {
            workOrderService.completeOrder(request); 
            return ResponseEntity.ok(ApiResponse.success("工单完成成功", null));
        } catch (Exception e) {
            logger.error("Error in POST /api/workOrder/complete/:{}", request.getOrderId(), e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 修改工单状态（接单、转移、完成）
     * @param request 修改工单状态请求体
     * @return 返回修改工单状态的结果
     */
    @PostMapping("/status")
    public ResponseEntity<?> updateWorkOrderStatus(@RequestBody AcceptOrderRequest request) {
        logger.info("修改工单状态: orderId={}, action={}", request.getOrderId(), request.getOrderStatus());
        try {
            workOrderService.updateWorkOrderStatus(request);  
            return ResponseEntity.ok(ApiResponse.success("工单状态修改成功", null)); 
        } catch (Exception e) {
            logger.error("Error in POST /api/workOrder/status: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 创建工单
     * @param request 创建工单请求体
     * @return 返回创建结果
     */
    @PostMapping("/create")
    public ResponseEntity<?> createWorkOrder(@RequestBody CreateOrderRequest request) {
        logger.info("创建工单请求: {}", request);
        try {
            workOrderService.createWorkOrder(request); 
            return ResponseEntity.ok(ApiResponse.success("工单创建成功", null));
        } catch (Exception e) {
            logger.error("创建工单失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage())); 
        }
    }
    
    /**
     * 获取工单消息
     * 从工单信息表中获取状态为 '待接单' 的记录列表
     * @param uid 用户ID
     * @param role 用户角色，如果是'admin'，则获取所有记录，否则根据uid过滤
     * @return 返回待接单的工单消息列表
     */
    @GetMapping("/messages")
    public ResponseEntity<?> getWorkOrderMessages(
            @RequestParam(required = false) Long uid,
            @RequestParam(required = false) String role) {
        logger.info("获取工单消息: uid={}, role={}", uid, role);
        try {
            List<WorkOrderMessageResponse> messages = workOrderService.getWorkOrderMessages(uid, role);
            return ResponseEntity.ok(ApiResponse.success("工单消息获取成功", messages));
        } catch (Exception e) {
            logger.error("获取工单消息失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * 转派工单
     * @param request 转派工单请求体，包含:
     *                - 工单ID(order_id)
     *                - 转派人ID(transfer_user_id)
     *                - 转派目标人员ID(repair_user_id)
     *                - 转派原因(transfer_reason)
     *                - 转派时间(transfer_time)
     * @return 返回转派工单的结果
     */
    @PostMapping("/transfer")
    public ResponseEntity<?> transferWorkOrder(@RequestBody TransferOrderRequest request) {
        logger.info("转派工单: orderId={}", request.getOrderId());
        logger.info("提交的数据: request={}", request);
        try {
            workOrderService.transferOrder(request); 
            return ResponseEntity.ok(ApiResponse.success("工单转派成功", null));
        } catch (Exception e) {
            logger.error("Error in POST /api/WorkOrders/transfer: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
} >>>>>>> .r5108
