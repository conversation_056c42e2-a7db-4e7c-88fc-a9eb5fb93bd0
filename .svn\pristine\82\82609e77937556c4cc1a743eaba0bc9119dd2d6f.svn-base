package com.heating.entity.user;

public enum SystemRole {
    ADMIN("admin", "系统管理员"),
    MAINTAINER("maintainer", "维修人员"),
    INSPECTOR("inspector", "巡检员"),
    USER("user", "普通用户");

    private final String code;
    private final String description;

    SystemRole(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static SystemRole fromCode(String code) {
        for (SystemRole role : values()) {
            if (role.code.equals(code)) {
                return role;
            }
        }
        throw new IllegalArgumentException("无效的角色编码: " + code);
    }
}