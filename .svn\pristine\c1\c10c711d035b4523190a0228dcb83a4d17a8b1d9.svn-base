package com.heating.controller;

import com.heating.dto.hes.HesDetailRequest;
import com.heating.dto.hes.HesRequest;
import com.heating.service.HesService;
import com.heating.util.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.heating.dto.hes.HesControlRequest;
import com.heating.dto.hes.HesHistoryRequest;
import com.heating.dto.hes.HesChartRequest;
import com.heating.dto.hes.HesAlarmsListRequest;
import com.heating.dto.hes.HesAlarmsDetailRequest;
import com.heating.dto.hes.HesAlarmsStatsRequest;
import com.heating.dto.hes.HesOnlineRateResponse;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/hes")
public class HesController {

    private static final Logger logger = LoggerFactory.getLogger(HesController.class);

    @Autowired
    private HesService hesService;

    @PostMapping("/list")
    public ResponseEntity<?> getHesList(@RequestBody HesRequest request) {
        logger.info("Accessing GET /api/hes/list");
        try {
            List<Map<String, Object>> hesList = hesService.getHesList(request); 
            return ResponseEntity.ok(ApiResponse.success("获取换热站列表成功",hesList.get(0).get("data")));
        } catch (Exception e) {
            logger.error("Error in GET /api/hes/list: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * 获取换热站详细数据
     * @param request 包含换热站ID的请求
     * @return 换热站详细数据
     */
    @PostMapping("/detail")
    public ResponseEntity<?> getHesDetail(@RequestBody HesDetailRequest request) {
        logger.info("Accessing POST /api/hes/detail with ID: {}", request.getId());
        try {
            Map<String, Object> hesDetail = hesService.getHesDetail(request);
            
            return ResponseEntity.ok(ApiResponse.success("换热站数据获取成功", hesDetail.get("data")));
        } catch (Exception e) {
            logger.error("Error in POST /api/hes/detail: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(ApiResponse.error("获取换热站数据失败: " + e.getMessage()));
        }
    }

    /**
     * 控制换热站
     * @param request 包含控制命令的请求
     * @return 控制结果
     */ 
    @PostMapping("/control")
    public ResponseEntity<?> controlHes(@RequestBody HesControlRequest request) {
        logger.info("Accessing POST /api/hes/control");
        try {
            Map<String, Object> controlResult = hesService.controlHes(request);
            return ResponseEntity.ok(ApiResponse.success("换热站控制成功", controlResult.get("data"))); 
        } catch (Exception e) {
            logger.error("Error in POST /api/hes/control: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(ApiResponse.error("换热站控制失败: " + e.getMessage()));
        }
    } 

    /**
     * 获取换热站历史数据
     * @param request 包含换热站ID的请求
     * @return 换热站历史数据
     */
    @PostMapping("/history")
    public ResponseEntity<?> getHesHistory(@RequestBody HesHistoryRequest request) {
        logger.info("Accessing POST /api/hes/history");
        try {
            List<Map<String, Object>> hesHistory = hesService.getHesHistory(request);
            return ResponseEntity.ok(ApiResponse.success("换热站历史数据获取成功", hesHistory.get(0).get("data")));
        } catch (Exception e) {
            logger.error("Error in POST /api/hes/history: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(ApiResponse.error("获取换热站历史数据失败: " + e.getMessage()));
        }
    }   

    /**
     * 获取换热站数据曲线
     * @param request 包含查询条件的请求
     * @return 换热站数据曲线
     */
    @PostMapping("/chart")
    public ResponseEntity<?> getHesChart(@RequestBody HesChartRequest request) {
        logger.info("Accessing POST /api/hes/chart");
        try {
            Map<String, Object> chartData = hesService.getHesChart(request);
            return ResponseEntity.ok(ApiResponse.success("数据曲线获取成功", chartData.get("data")));
        } catch (Exception e) {
            logger.error("Error in POST /api/hes/chart: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(ApiResponse.error("获取数据曲线失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取换热站告警列表
     * @param request 包含查询条件的请求
     * @return 换热站告警列表
     */
    @PostMapping("/alarms/list")
    public ResponseEntity<?> getHesAlarmsList(@RequestBody HesAlarmsListRequest request) {
        logger.info("Accessing POST /api/hes/alarms/list");
        try {
            Map<String, Object> alarmsList = hesService.getHesAlarmsList(request);
            return ResponseEntity.ok(ApiResponse.success("告警列表获取成功", alarmsList.get("data")));
        } catch (Exception e) {
            logger.error("Error in POST /api/hes/alarms/list: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(ApiResponse.error("获取告警列表失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取换热站告警详情
     * @param request 包含告警ID的请求
     * @return 换热站告警详情
     */
    @PostMapping("/alarms/detail")
    public ResponseEntity<?> getHesAlarmsDetail(@RequestBody HesAlarmsDetailRequest request) {
        logger.info("Accessing POST /api/hes/alarms/detail with ID: {}", request.getId());
        try {
            Map<String, Object> alarmDetail = hesService.getHesAlarmsDetail(request);
            return ResponseEntity.ok(ApiResponse.success("告警详情获取成功", alarmDetail.get("data")));
        } catch (Exception e) {
            logger.error("Error in POST /api/hes/alarms/detail: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(ApiResponse.error("获取告警详情失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取换热站告警统计
     * @param request 包含查询条件的请求
     * @return 换热站告警统计
     */
    @PostMapping("/alarms/stats")
    public ResponseEntity<?> getHesAlarmsStats(@RequestBody HesAlarmsStatsRequest request) {
        logger.info("Accessing POST /api/hes/alarms/stats");
        try {
            Map<String, Object> alarmsStats = hesService.getHesAlarmsStats(request);
            return ResponseEntity.ok(ApiResponse.success("告警统计获取成功", alarmsStats.get("data")));
        } catch (Exception e) {
            logger.error("Error in POST /api/hes/alarms/stats: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(ApiResponse.error("获取告警统计失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取换热站在线率
     * @return 换热站在线率信息
     */
    @GetMapping("/online-rate")
    public ResponseEntity<?> getOnlineRate() {
        logger.info("Accessing GET /api/hes/online-rate");
        try {
            HesOnlineRateResponse onlineRate = hesService.getOnlineRate();
            return ResponseEntity.ok(ApiResponse.success("换热站在线率获取成功", onlineRate));
        } catch (Exception e) {
            logger.error("Error in GET /api/hes/online-rate: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(ApiResponse.error("获取换热站在线率失败: " + e.getMessage()));
        }
    }
}