package com.heating.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.heating.entity.fault.TFaultAttachment;

import java.util.List;

@Repository
public interface FaultAttachmentRepository extends JpaRepository<TFaultAttachment, Long> {
    List<TFaultAttachment> findByFaultId(long faultId);
} 