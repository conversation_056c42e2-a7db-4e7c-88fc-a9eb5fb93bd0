/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-popup[data-v-7db519c7] {
  position: fixed;
  z-index: 99;
}
.uni-popup.top[data-v-7db519c7], .uni-popup.left[data-v-7db519c7], .uni-popup.right[data-v-7db519c7] {
  top: 0;
}
.uni-popup .uni-popup__wrapper[data-v-7db519c7] {
  display: block;
  position: relative;
  /* iphonex 等安全区设置，底部安全区适配 */
}
.uni-popup .uni-popup__wrapper.left[data-v-7db519c7], .uni-popup .uni-popup__wrapper.right[data-v-7db519c7] {
  padding-top: 0;
  flex: 1;
}
.fixforpc-z-index[data-v-7db519c7] {
  z-index: 999;
}
.fixforpc-top[data-v-7db519c7] {
  top: 0;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.account-binding-container {
  background-color: #f5f7fa;
  min-height: 100vh;
  padding-bottom: 1.25rem;
}
.page-header {
  background-color: #fff;
  padding: 0.9375rem;
  border-bottom: 0.03125rem solid #eee;
}
.page-header .page-title {
  font-size: 1.125rem;
  font-weight: bold;
  color: #333;
}
.binding-card {
  margin: 0.9375rem;
  background-color: #fff;
  border-radius: 0.375rem;
  padding: 0.3125rem 0;
  box-shadow: 0 0.1875rem 0.625rem rgba(0, 0, 0, 0.05);
}
.binding-card .binding-item {
  display: flex;
  align-items: center;
  padding: 0.9375rem;
  border-bottom: 0.03125rem solid rgba(0, 0, 0, 0.05);
}
.binding-card .binding-item:last-child {
  border-bottom: none;
}
.binding-card .binding-item .binding-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 0.625rem;
}
.binding-card .binding-item .binding-icon .iconfont {
  font-size: 1.25rem;
  color: #fff;
}
.binding-card .binding-item .binding-icon.phone-icon {
  background: linear-gradient(135deg, #1890ff, #36b3ff);
}
.binding-card .binding-item .binding-icon.email-icon {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}
.binding-card .binding-item .binding-icon.wechat-icon {
  background: linear-gradient(135deg, #07c160, #10d878);
}
.binding-card .binding-item .binding-icon.dingtalk-icon {
  background: linear-gradient(135deg, #1677ff, #4096ff);
}
.binding-card .binding-item .binding-info {
  flex: 1;
}
.binding-card .binding-item .binding-info .binding-name {
  font-size: 0.9375rem;
  color: #333;
  font-weight: bold;
  margin-bottom: 0.25rem;
  display: block;
}
.binding-card .binding-item .binding-info .binding-status {
  font-size: 0.8125rem;
  color: #52c41a;
}
.binding-card .binding-item .binding-info .binding-status.not-bound {
  color: #999;
}
.binding-card .binding-item .binding-action .action-btn {
  display: inline-block;
  padding: 0.375rem 0.9375rem;
  background-color: #f5f7fa;
  color: #1890ff;
  font-size: 0.8125rem;
  border-radius: 0.9375rem;
}
.binding-card .binding-item .binding-action .action-btn:active {
  opacity: 0.8;
}
.security-section {
  margin: 0.9375rem;
}
.security-section .security-title {
  margin-bottom: 0.625rem;
}
.security-section .security-title .title-text {
  font-size: 0.9375rem;
  color: #666;
  font-weight: bold;
}
.security-section .security-tips .tip-item {
  display: flex;
  margin-bottom: 0.46875rem;
}
.security-section .security-tips .tip-item .tip-marker {
  color: #999;
  margin-right: 0.3125rem;
}
.security-section .security-tips .tip-item .tip-text {
  font-size: 0.8125rem;
  color: #999;
  line-height: 1.6;
}
.popup-content {
  width: 18.75rem;
  background-color: #fff;
  border-radius: 0.375rem;
  overflow: hidden;
}
.popup-content .popup-title {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
  text-align: center;
  padding: 0.9375rem 0;
  border-bottom: 0.03125rem solid #eee;
}
.popup-content .popup-form {
  padding: 0.9375rem;
}
.popup-content .popup-form .form-item {
  margin-bottom: 0.9375rem;
}
.popup-content .popup-form .form-item uni-input {
  width: 100%;
  height: 2.5rem;
  border: 0.03125rem solid #ddd;
  border-radius: 0.25rem;
  padding: 0 0.625rem;
  font-size: 0.875rem;
}
.popup-content .popup-form .form-item.code-item {
  display: flex;
  align-items: center;
}
.popup-content .popup-form .form-item.code-item uni-input {
  flex: 1;
}
.popup-content .popup-form .form-item.code-item .send-code-btn {
  margin-left: 0.625rem;
  white-space: nowrap;
  padding: 0.46875rem 0.625rem;
  background-color: #1890ff;
  color: #fff;
  font-size: 0.8125rem;
  border-radius: 0.25rem;
}
.popup-content .popup-form .form-item.code-item .send-code-btn:active {
  opacity: 0.8;
}
.popup-content .popup-actions {
  display: flex;
  border-top: 0.03125rem solid #eee;
}
.popup-content .popup-actions .cancel-btn, .popup-content .popup-actions .confirm-btn {
  flex: 1;
  text-align: center;
  padding: 0.9375rem 0;
  font-size: 0.9375rem;
}
.popup-content .popup-actions .cancel-btn {
  color: #999;
  border-right: 0.03125rem solid #eee;
}
.popup-content .popup-actions .confirm-btn {
  color: #1890ff;
  font-weight: bold;
}