.container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.date-filter {
  background: #fff;
  padding: 24rpx;
  margin-bottom: 20rpx;
}

.selected {
  color: #333;
  font-size: 28rpx;
}

.workorder-list {
  margin-bottom: 20rpx;
}

.workorder-item {
  background: #fff;
  padding: 20rpx;
  margin-bottom: 2rpx;
  position: relative;
}

.workorder-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.workorder-no {
  font-size: 28rpx;
  color: #333;
}

.workorder-status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
}

.workorder-status.pending {
  color: #ff9f00;
  background: #fff6e5;
}

.workorder-status.processing {
  color: #409EFF;
  background: #ecf5ff;
}

.workorder-status.completed {
  color: #67C23A;
  background: #f0f9eb;
}

.workorder-info {
  font-size: 28rpx;
}

.info-item {
  display: flex;
  margin-bottom: 16rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  color: #999;
  width: 140rpx;
}

.value {
  color: #333;
  flex: 1;
}

/* 故障等级样式 */
.level-1 {
  color: #e6a23c; 
}

.level-2 {
  color: #f56c6c; 
}

.level-3 {
  color: #f56c6c;
  font-weight: bold;
}


/* 接单按钮样式 */
.accept-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  background: #67C23A;
  color: #fff;
  font-size: 32rpx;
  border-radius: 0;
  margin-top: 20rpx;
  border: none;
}

.accept-btn::after {
  border: none;
} 
.workorder-footer {
  padding-top: 20rpx;
  border-top: 1rpx solid #eee;
  display: flex;
  justify-content: flex-end;
}

/* 在现有样式基础上添加筛选栏样式 */
.filter-bar {
  background: #fff;
  padding: 24rpx;
  margin-bottom: 20rpx;
}

.status-filter {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.filter-btn {
  padding: 10rpx 20rpx;
  font-size: 28rpx;
  color: #666;
  background: #f5f5f5;
  border-radius: 8rpx;
}

.filter-btn.active {
  color: #fff;
  background: #409eff;
}

.date-picker {
  padding: 10rpx 0;
  font-size: 28rpx;
  color: #333;
}