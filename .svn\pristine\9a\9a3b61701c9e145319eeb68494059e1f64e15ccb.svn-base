# 智慧供暖运维系统-后端接口详细设计

## 1. 认证模块

### 1.1 用户登录
POST /api/auth/login
```json
// Request
{
  "username": "user_name",
  "password": "user_password"
}

// 用户存在返回
{
  "code": 200,
  "data": {
    "token": "eyJhbGciOiJ...",
    "userId": "u_123456",
    "role": "maintainer", // admin/maintainer/user
    "permissions": ["device:control", "patrol:create"]
  }
}

// 用户不存在返回
{
  "code": 400,
  "message": "用户不存在"
}
```

### 1.2 获取用户信息
GET /api/auth/user-info

Request Header  
Authorization: Bearer {token}


```json
// Response 200
{
  "code": 200,
  "data": {
    "userId": "u_123456",
    "name": "张三",
    "avatar": "https://...",
    "role": "maintainer",
    "phone": "13800138000",
    "department": "维修部",
    "skills": ["泵类维修", "压力容器"],
    "certifications": [
      {
        "name": "压力容器操作证",
        "number": "CERT2023001",
        "expireDate": "2024-12-31"
      }
    ],
    "workStats": {
      "monthlyOrders": 23,
      "averageRating": 4.8,
      "attendanceRate": 100
    }
  }
}
```

### 1.3 更新用户信息
POST /api/auth/user-modify
Request Header  
Authorization: Bearer {token} 

```json
// Request
{
  "name": "李四",
  "avatar": "https://...",
  "phone": "13900139000",
  "department": "维修部",
  "skills": ["泵类维修", "压力容器"],
  "certifications": [
    {
      "name": "压力容器操作证",
      "number": "CERT2023001",
      "expireDate": "2024-12-31"
    }
  ]
}

// Response 200
{
  "code": 200,
  "message": "用户信息更新成功"
} 
``` 
### 1.4 用户注册
POST /api/auth/register

```json
// Request
{
  "username": "user_name",
  "password": "user_password",
  "name": "张三",
  "email": "<EMAIL>",
  "phone": "13800138000"
} 

// Response 200
{
  "code": 200,
  "data": {
    "message": "用户注册成功"
  }
}  
``` 

### 1.5 获取用户列表
POST /api/auth/user/list

Request Header  
Authorization: Bearer {token}

```json
// Request
{
  "role": "inspector"
}

```json
// Response 200
{
  "code": 200,
   "message": "获取设备列表成功",
  "data": [
    {
      "id": 1,
      "name": "张三"
    },{
      "id": 2,
      "name": "李四"
    },{
      "id": 3,
      "name": "王五"
    }
  ]        
}
```

### 1.6 获取系统权限列表
GET /api/system/permission

Request Header  
Authorization: Bearer {token}
 

```json
// Response 200
{
    "message": "获取权限列表成功",
    "data": [
        {
            "id": 2,
            "permissionCode": "patrol:plans:add",
            "permissionName": "巡检计划",
            "menuName": "巡检计划",
            "path": "/page/patrol/plans"
        },
        {
            "id": 3,
            "permissionCode": "patrol:plans:delete",
            "permissionName": "删除巡检计划",
            "menuName": "巡检计划",
            "path": "/page/patrol/plans"
        },  
    ],
    "code": 200
}
```

### 1.7 获取角色权限编码列表
GET /api/role/permission/{role_code}
Request Header  
Authorization: Bearer {token}

```json
// Response 200
{
    "message": "获取权限列表成功",
    "data": [
        {
            "id": 2,
            "permissionCode": "patrol:plans:add"
        },
        {
            "id": 3,
            "permissionCode": "patrol:plans:delete"
        },  
    ],
    "code": 200
}
```

### 1.8 获取生成手机验证码
GET /api/phone/verification-code/{MobilePhone}
Request Header  
Authorization: Bearer {token}

```json
// Response 200
{
  "code": 200,
  "message": "获取手机验证码成功",
  "mobilePhone": 153199974425,
  "verificationCode": "056753"    
}
```


## 2. 设备管理

### 2.1 设备列表
POST /api/devices/list
Request Header  
Authorization: Bearer {token}

```json
// Request Query Parameters
{
  "page": 1,
  "pageSize": 10,
  "status": "online", // online/offline/fault
  "area": "east",
  "type": "pump",
  "keyword": "搜索关键词",
  "sortBy": "installTime", // installTime/maintainPeriod/alarmCount
  "sortOrder": "desc"
}

// Response 200
{
    "code": 200,
    "message": "获取设备列表成功",
    "data": {
        "content": [
            {
                "deviceId": "dev_test",
                "name": "1号循环泵",
                "type": "pump",
                "model": "XGZ-2023",
                "status": "online",
                "location": {
                    "building": "3号楼",
                    "floor": "1层",
                    "room": "泵房",
                    "coordinates": {
                        "lat": 39.904200,
                        "lng": 116.407400
                    }
                },
                "lastMaintenance": "2023-12-01 10:15:00",
                "nextMaintenance": "2023-12-16 10:15:00",
                "alarmCount": 0,
                "realTimeData": null,
                "manufacturer": null,
                "period": null
            },
            {
                "deviceId": "DEV-202312-003",
                "name": "2号循环泵",
                "type": "pump",
                "model": "XGZ-2023",
                "status": "online",
                "location": {
                    "building": "3号楼",
                    "floor": "1层",
                    "room": "泵房",
                    "coordinates": {
                        "lat": 39.904200,
                        "lng": 116.407400
                    }
                },
                "lastMaintenance": "2025-02-24 18:20:34",
                "nextMaintenance": "2025-03-11 18:20:34",
                "alarmCount": 0,
                "realTimeData": null,
                "manufacturer": null,
                "period": null
            }
        ],
        "pageable": {
            "pageNumber": 0,
            "pageSize": 10,
            "sort": {
                "empty": false,
                "sorted": true,
                "unsorted": false
            },
            "offset": 0,
            "paged": true,
            "unpaged": false
        },
        "last": true,
        "totalPages": 1,
        "totalElements": 7,
        "size": 10,
        "number": 0,
        "sort": {
            "empty": false,
            "sorted": true,
            "unsorted": false
        },
        "first": true,
        "numberOfElements": 7,
        "empty": false
    }
}
```

### 2.2 设备详情
GET /api/devices/{id}
Request Header  
Authorization: Bearer {token}

```json
// Response 200
{
  "code": 200,
  "data": {
    "basicInfo": {
      "deviceId": "dev_001",
      "name": "1号循环泵",
      "model": "XGZ-2023-Pro",
      "type": "pump",
      "status": "online",
      "installTime": "2023-01-15",
      "manufacturer": "格力",
      "photos": [
        "https://..."
      ]
    },
    "location": {
      "building": "3号楼",
      "floor": "1层",
      "room": "泵房",
      "coordinates": {
        "lat": 39.9042,
        "lng": 116.4074
      }
    },
    "maintenance": {
      "lastTime": "2023-12-01 10:15:00",
      "nextTime": "2023-12-15 10:15:00",
      "period": 15,
      "remainingDays": 3
    },
    "realTimeData": {
      "temperature": {
        "value": 65,
        "unit": "℃",
        "range": {
          "min": 50,
          "max": 80
        }
      },
      "pressure": {
        "value": 0.8,
        "unit": "MPa",
        "warning": true
      },
      "flow": {
        "value": 120,
        "unit": "m³/h"
      }
    },
    "operationLogs": [
      {
        "time": "2023-12-05 14:00:00",
        "operator": "张三",
        "action": "修改维护周期",
        "detail": "30天→15天"
      }
    ]
  }
}
```

### 2.3 新增设备
POST /api/devices/add
Request Header  
Authorization: Bearer {token}

```json
// Request
{
  "scanCode": "DEV-202312-001", // 可选，扫码录入时提供
  "name": "2号循环泵",
  "type": "pump",
  "model": "XGZ-2023",
  "manufacturer": "格力",
  "location": {
    "building": "3号楼",
    "floor": "1层",
    "room": "泵房",
    "coordinates": {
      "lat": 39.9042,
      "lng": 116.4074
    }
  },
  "specs": {
    "power": 5.5,
    "temperature": {
      "min": -20,
      "max": 150
    },
    "protocol": "Modbus"
  },
  "maintenance": {
    "period": 15,
    "staff": ["u_123456", "u_123457"]
  },
  "photos": ["base64..."] // 设备照片
}

// Response 200
{
  "code": 200,
  "data": {
    "deviceId": "dev_002",
    "message": "设备添加成功"
  }
}
```

### 2.4 更新设备
POST /api/devices/update/{id}
Request Header  
Authorization: Bearer {token}

```json
// Request
{
  "name": "2号循环泵",
  "type": "pump",
  "model": "XGZ-2023",
  "manufacturer": "格力",
  "location": {
    "building": "3号楼",
    "floor": "1层", 
    "room": "泵房",
    "coordinates": {
      "lat": 39.9042,
      "lng": 116.4074
    }
  },  
  "specs": {
    "power": 5.5,
    "temperature": {
      "min": -20,
      "max": 150
    },  
    "protocol": "Modbus"
  },
  "maintenance": {
    "last_maintenance": "2023-12-01 10:15:00",
    "next_maintenance": "2023-12-15 10:15:00",
    "period": 15,
    "staff": ["u_123456", "u_123457"]
  },  
  "photos": ["base64..."] // 设备照片
}

// Response 200
{
  "code": 200,
  "data": { 
    "message": "设备更新成功"
  }
}
```

### 2.5 设备维护记录
GET /api/devices/maintenance/{id}
Request Header  
Authorization: Bearer {token}

```json
// Response 200
{
  "code": 200,
  "data": {
    "total": 10,
    "list": [
      {
        "time": "2023-12-05 14:23:00",
        "action": "巡检",   
        "operator": "张三",
        "media": [
          {
            "type": "image",
            "url": "https://..."
          } 
        ]
      }
    ]
  }
}
```   

### 2.6 设备统计数据
GET /api/devices/stats
Request Header  
Authorization: Bearer {token}

```json
// Response 200
{
  "code": 200,
  "data": { 
    "total": 10,
    "online": 5,
    "offline": 3,
    "fault": 2
  }
}
```  

### 2.7 根据热用户ID获取设备列表
GET /api/devices/heat-unit/{heatUnitId} 
Request Header  
Authorization: Bearer {token}
```json
// Response 200
{
  "code": 200,
  "message": "获取设备列表成功",
  "data": [
      {
        "id": 1,
        "deviceParent": "换热站1",
        "type": "pump",
        "name": "循环水泵1号" 
      },
      {
        "id": 2,
        "deviceParent": "换热站2",
        "type": "exchanger",
        "name": "板式换热器2号"
      },
      {
        "id": 3,
        "deviceParent": "住户",
        "type": "valve",
        "name": "户内调节阀"  
      }
   ]  
}
```

### 2.8 根据设备ID获取设备巡检项
GET /api/devices/patrol-items/{deviceId}
Request Header  
Authorization: Bearer {token}
```json
// Response 200
{
  "code": 200,
  "message": "获取设备列表成功",
  "data": [
            { 
              id: 1
              deviceId: 1,
              patrolItemDictId: 1,
              itemName: "设备表面温度", 
              categoryName: "温度检测",
              paramType: "number",
              unit: "℃", 
              normalRange: "30-70", 
              checkMethod: "使用红外测温仪检测设备表面温度",
              description: "测量设备表面温度，正常范围30-70℃"
            },
            { 
              id: 2, 
              deviceId: 2,
              patrolItemDictId: 2,
              itemName: "管道泄漏检查", 
              categoryName: "泄漏检测",
              paramType: "boolean",
              unit: "无", 
              normalRange: "false", 
              checkMethod: "目视检查是否有水渍或滴漏现象",
              description: "检查管道接头、阀门等处是否有泄漏"
            },
            { 
              id: 2, 
              deviceId: 2,
              patrolItemDictId: 2,
              itemName: "设备噪音", 
              categoryName: "噪音检测",
              paramType: "selection",
              unit: "dB", 
              normalRange: "正常,轻微,严重", 
              checkMethod: "通过听觉判断设备运行噪音",
              description: "检查设备运行时的异常噪音"
            },
  ]
}
``` 

 
## 3. 巡检管理

### 3.1 创建巡检计划
POST /api/patrols/plans
Request Header  
Authorization: Bearer {token}

```json
// Request
{   
  "name": "2月日常巡检",
  "start_date": "2023-12-01",
  "end_date": "2023-12-31", 
  "executor_ids": [1,2,3],  
  "schedule_type": "daily",
  "schedule_interval": 3,
  "schedule_week_days": [1,3,5], 
  "schedule_month_days": [5,20],
  "device_ids":[1,2],
  "locations": "幸福家园换热站", 
  "patrol_item":[
          {
            "device_patrol_item_id": 1
          },
          {
            "device_patrol_item_id": 2
          } 
      ]
}

// Response 200
{
  "code": 200,
  "data": {
    "plan_no": "p_001",
    "message": "巡检计划创建成功"
  }
}
```

### 3.2 获取巡检计划列表
POST /api/patrols/plans/list
Request Header  
Authorization: Bearer {token}

```json
// Request
{
  "name": "",         // 巡检名称
  "stats": "pending", // 巡检状态
  "patrol_type":"",   // 巡检类型
  "search_date":"",   // 检索巡检时间 今天，本周，本月，上个月
  "locations": ""     // 巡检地点
}

 
// Response 200
{
  "code": 200,
  "message": "巡检列表查询成功",
  "data": [
    {
    "id": 1,
    "name": "",
    "plan_no": "" , // 计划编号
    "patrol_type": "",// 巡检类型:换热站巡检，日常巡检，设备巡检，管道巡检，阀门巡检
    "start_date": "",// 开始日期
    "end_date": "",  // 结束日期
    "locations": "", // 巡检地点 
    "executors":["","",""] ,// 巡检计划负责人
    "status": ""//  状态:pending-待执行,processing-执行中,completed-已完成
    },
    {
      "id": 2,
      "name": "",
      "plan_no": "" , // 计划编号
      "patrol_type": "",// 巡检类型:换热站巡检，日常巡检，设备巡检，管道巡检，阀门巡检
      "start_date": "",// 开始日期
      "end_date": "",  // 结束日期
      "locations": "", // 巡检地点 
      "executors":["","",""] ,// 巡检计划负责人
      "status": ""//  状态:pending-待执行,processing-执行中,completed-已完成
    }
  ]
}
 

### 3.3 获取巡检计划详情
GET /api/patrols/plans/{id}
Request Header  
Authorization: Bearer {token}

```json
// Response 200
{
  "code": 200,
  "message": "获取巡检计划详情成功",
  "data": {
    "planNo": "20231205142300",
    "name": "2月日常巡检",
    "patrolType": "日常巡检",
    "startDate": "2023-12-01",
    "endDate": "2023-12-31",
    "executorIds": [1, 2, 3],
    "executorNames": ["张三", "李四", "王五"],
    "scheduleType": "daily",
    "scheduleInterval": 3,
    "scheduleWeekDays": [1, 3, 5],
    "scheduleMonthDays": [5, 20], 
    "locations": "幸福家园换热站",
    "status": "pending",
    "patrolItems": [
      { 
        "id": 1, 
        "deviceName": "1号循环泵", // 根据表 't_device_patrol_item' 中 'device_id' 字段 从表 't_device' 中获取 
        "itemName": "", // 根据表 't_device_patrol_item' 中 'patrol_item_dict_id' 字段 从表 't_patrol_item_dictionary' 中获取
        "categoryName": "", // 根据表 't_device_patrol_item' 中 'patrol_item_dict_id' 字段 从表 't_patrol_item_dictionary' 中获取
        "paramType": "", // 根据表 't_device_patrol_item' 中 'patrol_item_dict_id' 字段 从表 't_patrol_item_dictionary' 中获取
        "unit": "",// 根据表 't_device_patrol_item' 中 'patrol_item_dict_id' 字段 从表 't_patrol_item_dictionary' 中获取
        "normalRange": "", // 从表 t_device_patrol_item 中获取
        "checkMethod": "",// 根据表 't_device_patrol_item' 中 'patrol_item_dict_id' 字段 从表 't_patrol_item_dictionary' 中获取
        "importance": "",// 根据表 't_device_patrol_item' 中 'patrol_item_dict_id' 字段 从表 't_patrol_item_dictionary' 中获取
        "description": ""// 根据表 't_device_patrol_item' 中 'patrol_item_dict_id' 字段 从表 't_patrol_item_dictionary' 中获取 
      },
      {
        "id": 1, 
        "deviceName": "1号循环泵", // 根据表 't_device_patrol_item' 中 'device_id' 字段 从表 't_device' 中获取 
        "itemName": "", // 根据表 't_device_patrol_item' 中 'patrol_item_dict_id' 字段 从表 't_patrol_item_dictionary' 中获取
        "categoryName": "", // 根据表 't_device_patrol_item' 中 'patrol_item_dict_id' 字段 从表 't_patrol_item_dictionary' 中获取
        "paramType": "", // 根据表 't_device_patrol_item' 中 'patrol_item_dict_id' 字段 从表 't_patrol_item_dictionary' 中获取
        "unit": "",// 根据表 't_device_patrol_item' 中 'patrol_item_dict_id' 字段 从表 't_patrol_item_dictionary' 中获取
        "normalRange": "", // 从表 t_device_patrol_item 中获取
        "checkMethod": "",// 根据表 't_device_patrol_item' 中 'patrol_item_dict_id' 字段 从表 't_patrol_item_dictionary' 中获取
        "importance": "",// 根据表 't_device_patrol_item' 中 'patrol_item_dict_id' 字段 从表 't_patrol_item_dictionary' 中获取
        "description": ""// 根据表 't_device_patrol_item' 中 'patrol_item_dict_id' 字段 从表 't_patrol_item_dictionary' 中获取
      }
    ]
  }
}

// Response 400
{
  "code": 400,
  "message": "获取巡检计划详情失败: 巡检计划不存在"
}

// Response 500
{
  "code": 500,
  "message": "获取巡检计划详情失败: 系统错误"
}
 

### 3.4 巡检记录提交
POST /api/patrols/records
Request Header  
Authorization: Bearer {token}

```json
// Request
{
  "patrol_plan_id": 22,// 巡检计划ID
  "executor_id": 1,           // 执行人ID 
  "start_time": "2023-12-01 10:15:00", // 开始时间  
  "end_time": "2023-12-01 10:15:00", // 结束时间
  "status": "completed", // 状态:pending,processing,completed
  "remark": "巡检完成", // 备注  
  "patrol_results": [
    {
      "patrol_item_id": 23,   
      "check_result": "normal", // 检查结果:normal-正常,abnormal-异常
      "param_value": "36℃",    // 参数值 
      "description": "",        // 描述
      "images": ["base64...","base64...","base64..."],    // 图片
      "latitude": 39.9042,      // 经度
      "longitude": 116.4074      // 纬度
    },
    {
      "patrol_item_id": 24,  
      "check_result": "abnormal",
      "param_value": "50dB",
      "description": "异常震动",
      "images": ["base64...","base64...","base64..."],    // 图片
      "latitude": 39.9042,
      "longitude": 116.4074
    }
  ] 
}

// Response 200
{
  "code": 200,
  "message": "巡检记录提交成功" 
}
```

### 3.5 更新巡检记录
POST /api/patrols/records/{id}
Request Header  
Authorization: Bearer {token}

```json
// Request  
{ 
  "end_time": "2023-12-01 10:15:00", // 结束时间 
  "status": "completed", // pending/processing/completed
  "remark": "巡检完成！" // 备注   
}

// Response 200
{
  "code": 200,
  "message": "巡检记录更新成功"
}

### 3.6 更新巡检结果
POST /api/patrols/result/{id}
Request Header  
Authorization: Bearer {token}

```json
// Request  
{ 
  "check_result": "normal", // 检查结果:normal-正常,abnormal-异常
  "param_value": "36℃", // 参数值 
  "description": "测试信息", // 描述
  "images": ["base64..."] // 图片 
}

// Response 200
{
  "code": 200,
  "message": "巡检记录更新成功"
}
 
### 3.7 巡检记录数据统计
GET /api/patrols/stats
Request Header  
Authorization: Bearer {token}

```json
// Response 200
{
  "code": 200,
  "data": {
    "total": 10,
    "completed": 5,
    "pending": 3
  }
}
``` 

### 3.8 根据巡检计划ID获取巡检项目
GET /api/patrols/plans/{id}/items
Request Header  
Authorization: Bearer {token}

// Response 200
{
  "code": 200,
  "message": "获取巡检项目列表成功",
  "data": [
    {
      "id": 1,
      "deviceId": "dev_001",
      "deviceName": "1号循环泵",
      "itemDictionaryId":1,
      "itemCode": "",
      "itemName": "",
      "categoryName": "",
      "paramType": "",
      "unit": "",
      "normalRange": "",
      "checkMethod": "",
      "importance": "",
      "description": ""
    },
    {
      "id": 2,
      "deviceId": "dev_002",
      "deviceName": "2号阀门",
      "itemDictionaryId":1,
      "itemCode": "",
      "itemName": "",
      "categoryName": "",
      "paramType": "",
      "unit": "",
      "normalRange": "",
      "checkMethod": "",
      "importance": "",
      "description": ""
    }
  ]
}

// Response 400
{
  "code": 400,
  "message": "获取巡检项目列表失败: 巡检计划不存在"
}

// Response 500
{
  "code": 500,
  "message": "获取巡检项目列表失败: 系统错误"
}

### 3.9 获取巡检项目字典列表
GET /api/patrols/dictionary/items
Request Header  
Authorization: Bearer {token}

```json
// Request Parameters
categoryId: 1 // 可选参数，类别ID，不传则查询所有类别

// Response 200
{
  "code": 200,
  "message": "获取巡检项目字典列表成功",
  "data": [
    {
      "id": 1,
      "itemCode": "BL001",
      "itemName": "锅炉水位",
      "categoryId": 1,
      "categoryName": "锅炉设备",
      "paramType": "number",
      "unit": "cm",
      "normalRange": "50-80",
      "checkMethod": "观察锅炉水位计读数",
      "importance": "critical",
      "description": "检查锅炉水位是否在正常范围内",
      "isActive": true
    },
    {
      "id": 2,
      "itemCode": "BL002",
      "itemName": "锅炉压力",
      "categoryId": 1,
      "categoryName": "锅炉设备",
      "paramType": "number",
      "unit": "MPa",
      "normalRange": "0.3-0.7",
      "checkMethod": "查看压力表读数",
      "importance": "critical",
      "description": "检查锅炉工作压力是否正常",
      "isActive": true
    }
  ]
}

// Response 400
{
  "code": 400,
  "message": "获取巡检项目字典列表失败: 参数错误"
}

// Response 500
{
  "code": 500,
  "message": "获取巡检项目字典列表失败: 系统错误"
}

### 3.10 获取巡检项目类别列表
GET /api/patrols/dictionary/categories
Request Header  
Authorization: Bearer {token}

```json
// Response 200
{
  "code": 200,
  "message": "获取巡检项目类别列表成功",
  "data": [
    {
      "id": 1,
      "categoryCode": "BL",
      "categoryName": "锅炉设备",
      "parentId": 0,
      "description": "锅炉及其附属设备的检查项目",
      "sortOrder": 10,
      "isActive": true
    },
    {
      "id": 2,
      "categoryCode": "PN",
      "categoryName": "管网系统",
      "parentId": 0,
      "description": "供热管网系统的检查项目",
      "sortOrder": 20,
      "isActive": true
    }
  ]
}

// Response 500
{
  "code": 500,
  "message": "获取巡检项目类别列表失败: 系统错误"
}


### 3.11 获取巡检记录列表 
POST /api/patrols/records
Request Header  
Authorization: Bearer {token}

```json 
// Request  
{ 
  "status": "completed",  
  "executorId": 1,
  "startDate": "",
  "endDate": "",
  "page": 1,
  "pageSize": 10 
}

```json
// Response 200
{
  "code": 200,
  "message": "获取巡检记录列表成功",
  "data": {
    "total": 12,
    "list": [
      {
        "id": 15,
        "patrolPlanId": 8,
        "planName": "金色家园换热站设备巡检",
        "executionDate": "2023-12-15",
        "startTime": "2023-12-15 08:30:00",
        "endTime": "2023-12-15 16:45:00",
        "status": "completed",
        "executorId": 102,
        "executorName": "张工",
        "abnormalCount": 2,
        "remark": "换热站1号阀门漏水，已临时处理",
        "locations": "金色家园换热站",
        "createTime": "2023-12-15 08:00:12",
        "updateTime": "2023-12-15 16:50:23"
      },
      {
        "id": 14,
        "patrolPlanId": 7,
        "planName": "西区管网巡检",
        "executionDate": "2023-12-14",
        "startTime": "2023-12-14 09:00:00",
        "endTime": "2023-12-14 17:30:00",
        "status": "completed",
        "executorId": 103,
        "executorName": "李工",
        "abnormalCount": 0,
        "remark": "",
        "locations": "西区供暖管网",
        "createTime": "2023-12-14 08:15:45",
        "updateTime": "2023-12-14 17:35:10"
      },
      // 所有符合条件的记录...
    ]
  }
}


失败响应：
{
  "code": 400,
  "message": "参数错误，时间范围格式不正确"
}

### 3.12 获取巡检结果详情
GET /api/patrols/records/15/results
Request Header  
Authorization: Bearer {token}
```json
// Response 200
{
  "code": 200,
  "message": "获取巡检结果详情成功",
  "data": {
    "recordInfo": {
      "id": 15,
      "patrolPlanId": 8,
      "planName": "金色家园换热站设备巡检",
      "executionDate": "2023-12-15",
      "startTime": "2023-12-15 08:30:00",
      "endTime": "2023-12-15 16:45:00",
      "status": "completed",
      "executorId": 102,
      "executorName": "张工",
      "executorPhone": "***********",
      "executorAvatar": "https://example.com/avatars/zhangong.jpg",
      "remark": "换热站1号阀门漏水，已临时处理",
      "locations": "金色家园换热站",
      "createTime": "2023-12-15 08:00:12",
      "updateTime": "2023-12-15 16:50:23"
    },
    "resultList": [
      {
        "id": 45,
        "patrolItemId": 12,
        "itemName": "一级循环泵检查",
        "deviceId": "dev_001",
        "deviceName": "1号循环泵",
        "deviceType": "pump",
        "categoryName": "设备检查",
        "checkResult": "normal",
        "paramValue": "正常",
        "paramType": "text",
        "normalRange": "正常",
        "unit": "",
        "description": "设备运行正常，无异常噪音",
        "images": [
          "https://example.com/patrol/images/20231215/img001.jpg",
          "https://example.com/patrol/images/20231215/img002.jpg"
        ],
        "latitude": 34.265321,
        "longitude": 108.953176,
        "createTime": "2023-12-15 09:15:22",
        "updateTime": "2023-12-15 09:15:22"
      },
      {
        "id": 46,
        "patrolItemId": 13,
        "itemName": "水泵压力参数检查",
        "deviceId": "dev_001",
        "deviceName": "1号循环泵",
        "deviceType": "pump",
        "categoryName": "参数检查",
        "checkResult": "normal",
        "paramValue": "0.65",
        "paramType": "number",
        "normalRange": "0.6-0.8",
        "unit": "MPa",
        "description": "压力值正常",
        "images": [
          "https://example.com/patrol/images/20231215/img003.jpg"
        ],
        "latitude": 34.265321,
        "longitude": 108.953176,
        "createTime": "2023-12-15 09:20:45",
        "updateTime": "2023-12-15 09:20:45"
      }
      // 更多巡检结果...
    ],
    "summary": {
      "totalItems": 12,
      "normalCount": 10,
      "abnormalCount": 2,
      "completionRate": "100%",
      "duration": "8小时15分钟"
    }
  }
}

失败响应：
{
  "code": 404,
  "message": "巡检记录不存在"
}

### 3.13 获取巡检计划执行人列表
GET /api/patrols/plans/15/executors
Request Header  
Authorization: Bearer {token}

```json
// Response 200
{
  code: 200,
  message: "获取计划执行人员列表成功(本地数据)",
  data: [
    {
      id: 1,
      name: "张工",
      role: "inspector", 
      skills: ["机械维护", "设备巡检", "阀门操作"]
    },
    {
      id: 2,
      name: "张工",
      role: "inspector", 
      skills: ["机械维护", "设备巡检", "阀门操作"]
    },
    {
      id: 3,
      name: "张工",
      role: "inspector", 
      skills: ["机械维护", "设备巡检", "阀门操作"]
    }
  ]
}

### 3.14 巡检消息
GET /api/patrols/plans/messages
Request Header  
Authorization: Bearer {token}

// 后端接口实现逻辑：
// 在巡检计划中，根据配置的巡检周期类型和对应的巡检时间，与当前时间进行（按当前时间早上8点）比对，如果到巡检时，自动提醒巡检人（对应配置）巡检。

```json
// Response 200
{
  code: 200,
  message: "获取巡检提醒消息成功",
  data: [
    {
      id: 1,// 巡检计划ID
      executor_ids:[1,2], // 巡检执行人
      name: "换热站一区设备巡检" // 巡检任务 
    },
    {
      id: 2,// 巡检计划ID
      executor_ids:[1,2], // 巡检执行人
      name: "换热站一区设备巡检" // 巡检任务 
    },
    {
      id: 3,// 巡检计划ID
      executor_ids:[1,2], // 巡检执行人
      name: "换热站一区设备巡检" // 巡检任务 
    },
  ]
}
 

## 4. 故障维修

### 4.1 上报故障接口
POST /api/faults/report
Request Header  
Authorization: Bearer {token}

```json
// Request  
{ 
  "heat_unit_id": 1,// 热力单元ID
  "alarm_id": -1; //告警ID
  "fault_type": "故障类型",// 故障类型
  "fault_level": "故障级别",// 故障级别
  "fault_desc": "故障描述",// 故障描述
  "occur_time": "2023-12-01 10:15:00",// 发生时间
  "report_user_id": 1,// 上报人ID
  "attachment": [
    {
      "file_type": "图片",
      "file_path": "https://..."
    },
    {
      "file_type": "视频",
      "file_path": "https://..."  
    }
  ]
} 

// Response 200
{
  "code": 200,
  "data": {
    "message": "故障上报成功"
  }
}
```

### 4.2 故障状态设定接口
POST /api/faults/status
Request Header  
Authorization: Bearer {token} 

```json
// Request
{
  "fault_id": 1,  
  "operator_id": 1,
  "fault_status": "已确认"
} 

// Response 200
{
  "code": 200,
  "message": "故障状态更新成功"
}
``` 

### 4.3 故障列表接口
GET /api/faults/list
Request Header  
Authorization: Bearer {token} 

```json
// Request Query Parameters
{
  "fault_status": "已确认",
  "date": "2025-03-01"
} 

// Response 200
{
    "code": 200,
    "message": "故障列表获取成功",
    "data": [
        {
          "heat_unit_name": "金色家园",
          "fault_desc": "故障描述",
          "occur_time": "2023-12-01 02:15",
          "fault_status": "待确认",
          "fault_level": "故障级别",
          "report_user_name": "admin",
          "report_time": "2025-03-01 11:23",
          "fault_id": 7
        },
        {
          "heat_unit_name": "金色家园",
          "fault_desc": "故障描述",
          "occur_time": "2023-12-01 02:15",
          "fault_status": "待确认",
          "heat_unit_name": "阳光小区",
          "fault_level": "故障级别",
          "report_user_name": "admin",
          "report_time": "2025-03-01 08:37",
          "fault_id": 6
        }
    ]
}
```

### 4.4 故障详情接口
GET /api/faults/detail/{id}
Request Header  
Authorization: Bearer {token} 

```json
// Request Query Parameters 

// Response 200
{
    "code": 200,
    "message": "故障详情获取成功",
    "data": {
        "fault_info": {
            "heat_unit_id": 1,
            "heat_unit_name": "金色家园",
            "fault_type": "故障类型",
            "occur_time": "2023-12-01 02:15",
            "fault_status": "待确认",
            "fault_level": "故障级别",
            "fault_source": "系统检测",
            "report_user_name": "admin",
            "report_time": "2025-03-01 11:23",
            "fault_desc": "故障描述",
            "id": 7,
            "created_time": "2025-03-01 11:23"
        },
        "fault_attachments": {
            "图片": "https://...",
            "视频": "https://..."
        }
    }
}
```
### 4.5 告警消息
GET /api/alarm/messages
Request Header  
Authorization: Bearer {token} 
 
整理数据输出格式： 
```json
// Response 200
{
    "code": 200,
    "message": "告警列表获取成功",
    "data": [
      {
        "id": 1, // 告警记录ID 
        "heatUnitId": 12, // 热用户ID
        "heatUnitName": "热用户名称",
        "faultLevels": "一般",
        "managerIds": [1,2,3], // 项目管理员ID 多人      
        "alarmDesc": "住户房号:10-2-1703 入户阀门上报异常",
        "alarmDt":“2025-04-29 16:58:25"
      },
      {
        "id": 2, // 告警记录ID
        "heatUnitId": 12, // 热用户ID
        "heatUnitName": "热用户名称",
        "faultLevels": "一般",
        "managerIds": [1,2,3], // 项目管理员ID 
        "alarmDesc": "住户房号:10-2-1703 入户阀门上报异常",
        "alarmDt":“2025-04-29 16:58:25"
      },
      {
        "id": 3, // 告警记录ID
        "heatUnitId": 12, // 热用户ID
        "heatUnitName": "热用户名称",
        "faultLevels": "一般",
        "managerIds": [1,2,3], // 项目管理员ID 
        "alarmDesc": "住户房号:10-2-1703 入户阀门上报异常",
        "alarmDt":“2025-04-29 16:58:25"
      }
    ]
}
```

### 4.6 故障消息
GET /api/faults/messages
Request Header  
Authorization: Bearer {token}  

```json
// Response 200
{
    "code": 200,
    "message": "故障消息获取成功",
    "data": [
      {
        "id": 1, // 故障id
        "managerIds": [1,2,3], // 项目管理员ID 多人 
        "faultDesc": "故障描述",
        "occurTime": "2025-04-29 16:58:25"
      },
      {
        "id": 2, // 故障id
        "managerIds": [1,2,3], // 项目管理员ID 多人 
        "faultDesc": "故障描述",
        "occurTime": "2025-04-29 16:58:25"
      },
      {
        "id": 2, // 故障id
        "managerIds": [1,2,3], // 项目管理员ID 多人 
        "faultDesc": "故障描述",
        "occurTime": "2025-04-29 16:58:25"
      }
    ]
}
``` 

### 4.7 更新告警状态
POST /api/alarm/updateStatus
Request Header  
Authorization: Bearer {token}  

```json

// Request  
{
  "alarm_id": 1,
  "alarm status": 1 // 1-已确认   2-已完成 3-已忽略
} 

// Response 200
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
``` 



 
## 5. 工单管理

### 5.1 创建工单 // 当故障状态为已确认时，自动创建工单
POST /api/WorkOrders/create 
Request Header  
Authorization: Bearer {token}  

```json
// Request
{
  "order_no": "20231201001",
  "fault_id": 1, 
  "repair_user_id": 1,
  "repair_content": "维修内容相关描述",
  "order_status": "待接单"
} 

// Response 200
{
  "code": 200,
  "message": "工单创建成功"
} 

### 5.2 工单列表
GET /api/WorkOrders/list   
// @Param("date") Date date, 
// @Param("status") String status,
// @Param("userId") Long userId
Request Header  
Authorization: Bearer {token}  

```json
// Request Query Parameters
 

// Response 200
{
    "code": 200,
    "message": "工单列表查询成功",
    "data": [
        {
            "orderId":1,
            "orderNo": "ORDER-20250301200122",
            "heatUnitName": "金色家园",
            "faultType": "故障类型",
            "faultLevel": "故障级别",
            "orderStatus": "待接单",
            "createdTime": "2025-03-01 12:01"
        },
        {
            "orderId":1,
            "orderNo": "ORDER-20250301193118",
            "heatUnitName": "金色家园",
            "faultType": "故障类型",
            "faultLevel": "故障级别",
            "orderStatus": "待接单",
            "createdTime": "2025-03-01 11:31"
        },
        {
            "orderId":3,
            "orderNo": "ORDER-20250301192747",
            "heatUnitName": "金色家园",
            "faultType": "故障类型",
            "faultLevel": "故障级别",
            "orderStatus": "待接单",
            "createdTime": "2025-03-01 11:27"
        }
    ]
} 
```

### 5.3 工单详情
GET /api/WorkOrders/detail/{id}
Request Header  
Authorization: Bearer {token} 

```json
// Request Query Parameters
{
  "page": 1,
  "pageSize": 10
}  

// Response 200
{
    "code": 200,
    "message": "工单详情查询成功",
    "data": {
        "orderNo": "ORDER-20250301200122",
        "faultId": 7,
        "heatUnitName": "金色家园",
        "repairUserId": 1,
        "repairUserName": "admin",
        "repairContent": "故障描述",
        "repairResult": null,
        "orderStatus": "待接单",
        "faultType": "故障类型",
        "faultLevel": "故障级别",
        "faultDesc": "故障描述",
        "repairTime": null,
        "createdTime": "2025-03-01 12:01",
        "updatedTime": "2025-03-01 12:01",
        "faultAttachments": [
            {
                "fileType": "图片",
                "filePath": "https://..."
            },
            {
                "fileType": "视频",
                "filePath": "https://..."
            }
        ],
        "workOrderAttachments": [],
        "operationLogs": [
            {
                "operationType": "故障确认",
                "operationDesc": "管理员确认故障并生成工单",
                "createdAt": "admin",
                "operatorName": "2025-03-01 20:01"
            }
        ]
    }
}
```

### 5.4 工单完成
POST /api/WorkOrders/complete
Request Header  
Authorization: Bearer {token}  

```json
// Request
{
  "order_id": 1,
  "repair_user_id": 1,
  "order_status": "已完成",
  "repair_content": "维修内容相关描述",
  "repair_result": "", 
  "attachment": [
    {
      "file_type": "图片",
      "file_path": "https://..."
    },
    {
      "file_type": "视频",
      "file_path": "https://..."  
    }
  ]
}  

// Response 200
{
  "code": 200,
  "message": "工单完成成功"
} 
```

### 5.5 修改工单状态（接单、转移、完成）  
POST /api/WorkOrders/status
Request Header  
Authorization: Bearer {token} 

```json
// Request
{
  "order_id": 1,
  "repair_user_id": 1, 
  "order_status": "已完成"  // accept/transfer
}     

// Response 200
{
  "code": 200,
  "message": "工单状态修改成功",
  "data": null
}
``` 

### 5.6 工单消息
GET /api/WorkOrders/messages
Request Header  
Authorization: Bearer {token}  

// 后端接口实现逻辑：
// 从工单信息表中，获取状态为 '待接单' 的记录列表 

```json
// Response 200
{
  "code": 200,
  "message": "工单消息获取成功",
  "data":[
    { 
      "id": 7,
      "fault_source": "故障来源",
      "fault_desc": "故障描述", 
      "createdTime": "2025-03-01 12:01",
      "updatedTime": "2025-03-01 12:01"
    },
    { 
      "id": 8,
      "fault_source": "故障来源",
      "fault_desc": "故障描述", 
      "createdTime": "2025-03-01 12:01",
      "updatedTime": "2025-03-01 12:01"
    } 
  ] 
}
```  


## 6. 室温管理

### 6.1 获取户外温度
GET /api/temperatures/outdoor
Request Header  
Authorization: Bearer {token}

```json
// Response 200
{
  "code": 200,
  "message": "户外温度获取成功",
  "data": {
    "temperature": 20,
    "record_time": "2025-03-01 12:00:00"
  }
}
``` 

### 6.2  获取室温列表
GET /api/temperatures/list
Request Header  
Authorization: Bearer {token}

```json
// Request Query Parameters
{
  "heat_unit_name": "金色家园",
  "date": "2025-03-01"
} 

// Response 200
{
    "code": 200,
    "message": "获取室温列表成功",
    "data": [ 
        {
            "heat_unit_name": "小区B",
            "building_no": "3号楼",
            "unit_no": "301",
            "room_no": "3-1",
            "indoor_temp": 17.5,
            "report_time": "2025-03-03 01:42",
            "status": "温度低",
            "room_info": "3号楼-301-3-1"
        },
        {
            "heat_unit_name": "小区B",
            "building_no": "3号楼",
            "unit_no": "302",
            "room_no": "3-2",
            "indoor_temp": 25.0,
            "report_time": "2025-03-03 01:42",
            "status": "温度高",
            "room_info": "3号楼-302-3-2"
        },
        {
            "heat_unit_name": "小区C",
            "building_no": "4号楼",
            "unit_no": "401",
            "room_no": "4-1",
            "indoor_temp": 20.0,
            "report_time": "2025-03-03 01:42",
            "status": "温度正常",
            "room_info": "4号楼-401-4-1"
        },
        {
            "heat_unit_name": "小区C",
            "building_no": "4号楼",
            "unit_no": "402",
            "room_no": "4-2",
            "indoor_temp": 22.0,
            "report_time": "2025-03-03 01:42",
            "status": "温度正常",
            "room_info": "4号楼-402-4-2"
        },
        {
            "heat_unit_name": "小区D",
            "building_no": "5号楼",
            "unit_no": "501",
            "room_no": "5-1",
            "indoor_temp": 18.0,
            "report_time": "2025-03-03 01:42",
            "status": "温度正常",
            "room_info": "5号楼-501-5-1"
        },
        {
            "heat_unit_name": "小区D",
            "building_no": "5号楼",
            "unit_no": "502",
            "room_no": "5-2",
            "indoor_temp": 23.0,
            "report_time": "2025-03-03 01:42",
            "status": "温度正常",
            "room_info": "5号楼-502-5-2"
        },
        {
            "heat_unit_name": "小区E",
            "building_no": "6号楼",
            "unit_no": "601",
            "room_no": "6-1",
            "indoor_temp": 21.0,
            "report_time": "2025-03-03 01:42",
            "status": "温度正常",
            "room_info": "6号楼-601-6-1"
        }
    ]
}
```
 

### 6.3 根据室温记录ID获取室温信息
GET /api/temperatures/{id}
Request Header    
Authorization: Bearer {token}

```json
// Response 200
{
    "code": 200,
    "message": "获取室温记录成功",
    "data": {
        "images": [
            "http://example.com/image1.jpg",
            "http://example.com/image2.jpg"
        ],
        "heat_unit_name": "小区A",
        "indoor_temp": 22.5,
        "latitude": "39.904200",
        "outdoor_temp": 15.0,
        "videos": [
            "http://example.com/video1.mp4"
        ],
        "remark": "正常",
        "building_no": "1号楼",
        "report_time": "2025-03-03 01:42",
        "unit_no": "101",
        "room_no": "1-1",
        "id": 29,
        "longitude": "116.407400",
        "status": "温度正常"
    }
}
``` 
 
### 6.4 上报室温记录
POST /api/temperatures/report
Request Header  
Authorization: Bearer {token}

```json
// Response 200
{

    "heat_unit_name": "金色家园",
    "building_no": "1号楼",
    "unit_no": "1单元",
    "room_no": "101",
    "indoor_temp": 22.5,
    "outdoor_temp": 15.0,
    "latitude": 39.9042,  
    "longitude": 116.4074,
    "images": ["https://...", "https://..."],
    "videos": ["https://...", "https://..."],
    "remark": "正常",
    "report_user_id": 1
} 

// Response 200
{
  "code": 200,
  "message": "室温记录上报成功"
}

## 7. 考勤管理
### 7.1 打卡（分上下班打卡，上班打卡开启定时推送位置功能，下班打卡关闭推送位置功能）
POST /api/attendance/clock
Request Header  
Authorization: Bearer {token}

```json
// Request
{
  "user_id": 1, 
  "clock_type": "checkin", 
  "latitude": 39.9042,
  "longitude": 116.4074,
  "face_photo": ["https://...", "https://..."],
  "liveness_data": ["https://...", "https://..."],
  "outdoor_flag": 0, 
  "leave_type": null,
  "leave_reason": null, 
  "leave_proof": null 
}

// Response 200
{
  "code": 200,
  "message": "打卡成功",
  "data": {
    "clock_time": "2023-12-05 08:59:00", 
    "status": "normal" // normal/late/early 
  }
}
```

### 7.2 考勤记录
(根据用户id查询考勤记录,如果用户id为空则查询所有用户考勤记录,其他参数也可以为空)
GET /api/attendance/records/user_id={user_id}&year={year}&month={month}&status={status}
Request Header  
Authorization: Bearer {token}

```json
// Response 200
{
    "code": 200,
    "message": "查询考勤记录成功",
    "data": {
        "summary": {
            "workdays": 21,
            "late": 2,
            "absent": 20,
            "attendance": 1
        },
        "records": [
            {
                "user": "张三",
                "clockType": "checkin",
                "clockTime": "2025-03-03 19:26:10",
                "latitude": 39.904200,
                "longitude": 116.407400,
                "facePhoto": [
                    "https://...",
                    "https://..."
                ],
                "livenessData": [
                    "https://...",
                    "https://..."
                ],
                "outdoorFlag": 0,
                "status": "late",
                "leaveType": null,
                "leaveReason": null,
                "leaveProof": null,
                "createTime": "2025-03-03 19:26:10"
            },
            {
                "user": "张三",
                "clockType": "checkin",
                "clockTime": "2025-03-03 19:15:24",
                "latitude": 39.904200,
                "longitude": 116.407400,
                "facePhoto": [
                    "https://...",
                    "https://..."
                ],
                "livenessData": [
                    "https://...",
                    "https://..."
                ],
                "outdoorFlag": 0,
                "status": "late",
                "leaveType": null,
                "leaveReason": null,
                "leaveProof": null,
                "createTime": "2025-03-03 19:15:24"
            }
        ]
    }
}
```

### 7.3 考勤统计  
(根据用户id查询考勤统计,如果用户id为空则查询所有用户考勤统计,其他参数也可以为空)
GET /api/attendance/stats/user_id={user_id}&year={year}&month={month} 
Request Header  
Authorization: Bearer {token}

```json 
// Response 200
{
    "code": 200,
    "message": "查询考勤统计成功",
    "data": {
        "summary": {
            "workdays": 21,
            "late": 2,
            "absent": 20,
            "attendance": 1
        },
        "chart": {
            "series": [
                {
                    "name": "出勤率",
                    "data": [
                        0.0,
                        0.0,
                        1.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0
                    ]
                },
                {
                    "name": "迟到率",
                    "data": [
                        0.0,
                        0.0,
                        1.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0,
                        0.0
                    ]
                },
                {
                    "name": "缺勤率",
                    "data": [
                        0.0,
                        0.0,
                        0.0,
                        1.0,
                        1.0,
                        1.0,
                        1.0,
                        0.0,
                        0.0,
                        1.0,
                        1.0,
                        1.0,
                        1.0,
                        1.0,
                        0.0,
                        0.0,
                        1.0,
                        1.0,
                        1.0,
                        1.0,
                        1.0,
                        0.0,
                        0.0,
                        1.0,
                        1.0,
                        1.0,
                        1.0,
                        1.0,
                        0.0,
                        0.0,
                        1.0
                    ]
                }
            ],
            "xaxis": [
                "01",
                "02",
                "03",
                "04",
                "05",
                "06",
                "07",
                "08",
                "09",
                "10",
                "11",
                "12",
                "13",
                "14",
                "15",
                "16",
                "17",
                "18",
                "19",
                "20",
                "21",
                "22",
                "23",
                "24",
                "25",
                "26",
                "27",
                "28",
                "29",
                "30",
                "31"
            ]
        }
    }
}
``` 

### 7.4 获取今日打卡记录
GET /api/attendance/today
Request Header
Authorization: Bearer {token}

```json
// Response 200
{
  "code": 200,
  "message": "获取今日打卡记录成功",
  "data": {
    "clockInTime": "2023-12-05 08:30:00",
    "clockInStatus": "normal",
    "clockOutTime": "2023-12-05 17:30:00",
    "clockOutStatus": "normal",
    "location": "陕西省西安市未央区未央路2号"
  }
}
```

### 7.5 获取最近打卡记录
GET /api/attendance/recent
Request Header
Authorization: Bearer {token}

```json
// Request Query Parameters
{
  "days": 7  // 获取最近几天的记录
}

// Response 200
{
  "code": 200,
  "message": "获取最近打卡记录成功",
  "data": [
    {
      "date": "2023-12-05",
      "week": "周二",
      "clockInTime": "08:30:00",
      "clockInStatus": "normal",
      "clockOutTime": "17:30:00",
      "clockOutStatus": "normal"
    },
    {
      "date": "2023-12-04",
      "week": "周一",
      "clockInTime": "08:45:00",
      "clockInStatus": "late",
      "clockOutTime": "17:30:00",
      "clockOutStatus": "normal"
    }
  ]
}
```

### 7.6 获取打卡规则
GET /api/attendance/rules
Request Header
Authorization: Bearer {token}

```json
// Response 200
{
  "code": 200,
  "message": "获取打卡规则成功",
  "data": {
    "clockInTime": "08:30",
    "clockOutTime": "17:30",
    "allowedDistance": 500,
    "lateThreshold": 15,
    "earlyLeaveThreshold": 15
  }
}
```

### 7.7 检查考勤范围
POST /api/attendance/check-area
Request Header
Authorization: Bearer {token}

```json
// Request
{
  "latitude": 39.9042,
  "longitude": 116.4074
}

// Response 200
{
  "code": 200,
  "message": "考勤范围检查成功",
  "data": {
    "inArea": true,
    "distance": 120  // 距离考勤点的距离(米)
  }
}
```

### 7.8 提交补卡申请
POST /api/attendance/supplement
Request Header
Authorization: Bearer {token}

```json
// Request
{
  "userId": 1,
  "date": "2023-12-03",
  "type": 1,  // 1-上班打卡, 2-下班打卡
  "reason": "加班忘记打卡",
  "images": ["https://...", "https://..."]  // 证明材料图片
}

// Response 200
{
  "code": 200,
  "message": "补卡申请提交成功",
  "data": {
    "id": 123,
    "status": "pending"  // pending/approved/rejected
  }
}
```

### 7.9 获取员工考勤列表(管理员用)
GET /api/attendance/staff
Request Header
Authorization: Bearer {token}

```json
// Request Query Parameters
{
  "departmentId": 1,  // 可选，部门ID
  "month": "2023-12",  // 可选，月份
  "status": "late"  // 可选，考勤状态 normal/late/early/absent
}

// Response 200
{
  "code": 200,
  "message": "获取员工考勤列表成功",
  "data": {
    "total": 50,
    "list": [
      {
        "userId": 1,
        "userName": "张三",
        "department": "运维部",
        "position": "运维工程师",
        "attendanceRate": 95.5,
        "lateCount": 2,
        "earlyLeaveCount": 0,
        "absentCount": 1
      },
      {
        "userId": 2,
        "userName": "李四",
        "department": "运维部",
        "position": "巡检员",
        "attendanceRate": 100,
        "lateCount": 0,
        "earlyLeaveCount": 0,
        "absentCount": 0
      }
    ]
  }
}
```

### 7.10 获取部门考勤统计(管理员用)
GET /api/attendance/department-stats
Request Header
Authorization: Bearer {token}

```json
// Request Query Parameters
{
  "year": 2023,  // 可选，年份
  "month": 12    // 可选，月份
}

// Response 200
{
  "code": 200,
  "message": "获取部门考勤统计成功",
  "data": [
    {
      "departmentId": 1,
      "departmentName": "运维部",
      "staffCount": 15,
      "attendanceRate": 97.8,
      "lateRate": 2.1,
      "earlyLeaveRate": 0.5,
      "absentRate": 0.7
    },
    {
      "departmentId": 2,
      "departmentName": "技术部",
      "staffCount": 10,
      "attendanceRate": 98.5,
      "lateRate": 1.5,
      "earlyLeaveRate": 0,
      "absentRate": 0
    }
  ]
}
```

### 7.11 审批补卡申请(管理员用)
POST /api/attendance/approve-supplement
Request Header
Authorization: Bearer {token}

```json
// Request
{
  "id": 123,  // 补卡申请ID
  "status": "approved",  // approved/rejected
  "remark": "审批通过"  // 审批备注
}

// Response 200
{
  "code": 200,
  "message": "审批成功",
  "data": {
    "id": 123,
    "status": "approved",
    "approvedTime": "2023-12-06 10:30:00",
    "approvedBy": "王主管"
  }
}
```

### 7.12 获取所有员工列表
GET /api/attendance/all-staff
Request Header
Authorization: Bearer {token}

```json
// Response 200
{
  "code": 200,
  "message": "获取员工列表成功",
  "data": [
    {
      "id": 1,
      "name": "张三",
      "department": "运维部",
      "position": "运维工程师"
    },
    {
      "id": 2,
      "name": "李四",
      "department": "运维部",
      "position": "巡检员"
    },
    {
      "id": 3,
      "name": "王五",
      "department": "技术部",
      "position": "技术主管"
    }
  ]
}
``` 
 
## 8. 换热站监控

### 8.1 换热站列表 // 通过第三方接口获取
POST /api/hes/list
Request Header  
Authorization: Bearer {token}

```json 
// Request Query Parameters
{
  "status": "",  // online/offline/fault，可选
  "keyword": ""  // 搜索关键词，可选 
}

// Response 200
{
  "code": 200,
  "message": "换热站列表获取成功",
  "data": {
    "total": 15,
    "list": [
      {
        "id": 1,
        "name": "金色家园换热站",
        "hes_code": "HES001",
        "heat_unit_name": "金色家园",
        "status": "online",
        "run_mode": "自动",
        "supply_temp": 85.5,
        "return_temp": 60.2,
        "supply_pressure": 0.6,
        "return_pressure": 0.3,
        "flow_rate": 25.6,
        "power": 1200.5,
        "alarm_count": 0,
        "last_update_time": "2025-03-01 12:30:45"
      },
      {
        "id": 2,
        "name": "翠湖花园换热站",
        "hes_code": "HES002",
        "heat_unit_name": "翠湖花园",
        "status": "online",
        "run_mode": "手动",
        "supply_temp": 83.2,
        "return_temp": 58.7,
        "supply_pressure": 0.58,
        "return_pressure": 0.28,
        "flow_rate": 22.3,
        "power": 1050.8,
        "alarm_count": 2,
        "last_update_time": "2025-03-01 12:28:15"
      },
      {
        "id": 3,
        "name": "阳光小区换热站",
        "hes_code": "HES003",
        "heat_unit_name": "阳光小区",
        "status": "fault",
        "run_mode": "停运",
        "supply_temp": 0,
        "return_temp": 0,
        "supply_pressure": 0,
        "return_pressure": 0,
        "flow_rate": 0,
        "power": 0,
        "alarm_count": 5,
        "last_update_time": "2025-03-01 10:15:30"
      }
    ]
  }
}
```

### 8.2 换热站数据查看 // 通过第三方接口获取
POST /api/hes/detail
Request Header  
Authorization: Bearer {token}

//Request Body
{
  "id": 1
} 

// Response 200
{
  "code": 200,
  "message": "换热站数据获取成功",
  "data": {
    "basic_info": {
      "id": 1,
      "name": "金色家园换热站",
      "hes_code": "HES001",
      "heat_unit_name": "金色家园",
      "status": "online",
      "run_mode": "自动",
      "calc_mode": "二次供水温度控制",
      "used_year": 5,
      "heating_area": 25000.5,
      "design_load": 1500.0,
      "design_flow": 30.0
    },
    "realtime_data": {
      "primary_system": {
        "supply_temp": 85.5,
        "return_temp": 60.2,
        "supply_pressure": 0.6,
        "return_pressure": 0.3,
        "flow_rate": 25.6,
        "power": 1200.5
      },
      "secondary_system": {
        "supply_temp": 65.3,
        "return_temp": 45.1,
        "supply_pressure": 0.4,
        "return_pressure": 0.2,
        "flow_rate": 28.3
      },
      "equipment_status": {
        "pumps": [
          {
            "id": 1,
            "name": "一次泵1#",
            "status": "running",
            "frequency": 42.5,
            "current": 18.6,
            "power": 7.5
          },
          {
            "id": 2,
            "name": "一次泵2#",
            "status": "standby",
            "frequency": 0,
            "current": 0,
            "power": 7.5
          },
          {
            "id": 3,
            "name": "二次泵1#",
            "status": "running",
            "frequency": 38.2,
            "current": 15.3,
            "power": 5.5
          }
        ],
        "valves": [
          {
            "id": 1,
            "name": "调节阀1#",
            "opening": 65,
            "status": "normal"
          },
          {
            "id": 2,
            "name": "调节阀2#",
            "opening": 40,
            "status": "normal"
          }
        ]
      },
      "last_update_time": "2025-03-01 12:30:45"
    }
  }
}
```

### 8.3 换热站控制 // 通过第三方接口发送控制命令
POST /api/hes/control  
Request Header  
Authorization: Bearer {token}

```json
// Request
{
  "hes_id": 1,
  "control_type": "pump",  // pump/valve/mode/algorithm
  "device_id": 1,  // 设备ID（泵或阀门ID）
  "action": "start",  // start/stop/adjust
  "value": 45.0,  // 频率值或阀门开度
  "operator_id": 1,
  "remark": "调整一次泵频率以提高供水温度"
}

// Response 200
{
  "code": 200,
  "message": "控制命令发送成功",
  "data": {
    "control_id": 123,
    "status": "success",
    "execution_time": "2025-03-01 13:05:22"
  }
}

// Response 600 (设备离线)
{
  "code": 600,
  "message": "设备离线",
  "details": "无法发送控制命令，请检查设备连接状态"
}
```

### 8.4 换热站远程协助 // 通过第三方接口获取
POST /api/hes/remote
Request Header  
Authorization: Bearer {token}


//Request Body
{
  "hes_id": 1
}   

// Response 200
{
  "code": 200,
  "message": "远程协助信息获取成功",
  "data": {
    "hes_id": 1,
    "name": "金色家园换热站",
    "cameras": [
      {
        "id": 1,
        "name": "主机房摄像头",
        "rtsp_url": "rtsp://*************:554/main",
        "status": "online"
      },
      {
        "id": 2,
        "name": "泵房摄像头",
        "rtsp_url": "rtsp://*************:554/main",
        "status": "online"
      }
    ],
    "audio": {
      "call_url": "sip:hes001@*************",
      "status": "available"
    },
    "remote_desktop": {
      "url": "https://remote.heating.com/hes/1",
      "status": "available"
    }
  }
}
```

### 8.5 换热站历史数据 // 通过第三方接口获取
POST /api/hes/history  
Request Header  
Authorization: Bearer {token}

//Request Body
{
  "start_time": "2025-03-01 00:00:00",
  "end_time": "2025-03-01 23:59:59",
  "data_type": ["supply_temp", "return_temp", "flow_rate"],  // 可选多个数据类型
  "interval": "hour"  // minute/hour/day
}

// Response 200
{
  "code": 200,
  "message": "历史数据获取成功",
  "data": {
    "hes_id": 1,
    "name": "金色家园换热站",
    "time_points": [
      "2025-03-01 00:00",
      "2025-03-01 01:00",
      "2025-03-01 02:00",
      "2025-03-01 03:00"
      // ...更多时间点
    ],
    "series": [
      {
        "name": "一次供水温度",
        "unit": "℃",
        "data": [85.2, 85.5, 85.3, 85.6]
      },
      {
        "name": "一次回水温度",
        "unit": "℃",
        "data": [60.1, 60.2, 60.0, 60.3]
      },
      {
        "name": "流量",
        "unit": "m³/h",
        "data": [25.3, 25.6, 25.4, 25.7]
      }
    ]
  }
}
```

### 8.6 换热站数据曲线 // 通过第三方接口获取
POST /api/hes/chart  
Request Header  
Authorization: Bearer {token}

```json
// Request Query Parameters
{
  "hes_ids": [1, 2, 3],  // 多个换热站ID
  "data_fields": ["supply_temp", "return_temp"],  // 要比较的字段
  "start_time": "2025-03-01 00:00:00",
  "end_time": "2025-03-01 23:59:59",
  "interval": "hour"  // minute/hour/day
}

// Response 200
{
  "code": 200,
  "message": "数据曲线获取成功",
  "data": {
    "time_points": [
      "2025-03-01 00:00",
      "2025-03-01 01:00",
      "2025-03-01 02:00",
      "2025-03-01 03:00"
      // ...更多时间点
    ],
    "series": [
      {
        "name": "金色家园-一次供水温度",
        "unit": "℃",
        "data": [85.2, 85.5, 85.3, 85.6]
      },
      {
        "name": "翠湖花园-一次供水温度",
        "unit": "℃",
        "data": [83.1, 83.2, 83.0, 83.3]
      },
      {
        "name": "金色家园-一次回水温度",
        "unit": "℃",
        "data": [60.1, 60.2, 60.0, 60.3]
      },
      {
        "name": "翠湖花园-一次回水温度",
        "unit": "℃",
        "data": [58.5, 58.7, 58.4, 58.8]
      }
    ]
  }
}
```

### 8.7 换热站告警列表 // 通过第三方接口获取
POST /api/hes/alarms/list  
Request Header  
Authorization: Bearer {token}

```json
// Request Query Parameters
{
  "hes_id": 1,  // 可选，不传则查询所有换热站(根据小区名称查询对应的换热站id)
  "status": "active",  // active/resolved/all
  "level": ["urgent", "warning"],  // 可选多个级别
  "start_time": "2025-03-01 00:00:00",  // 可选
  "end_time": "2025-03-01 23:59:59",  // 可选
  "page": 1,
  "pageSize": 10
}

// Response 200
{
  "code": 200,
  "message": "告警列表获取成功",
  "data": {
    "total": 25,
    "list": [
      {
        "id": 1,
        "hes_id": 1,
        "hes_name": "金色家园换热站", 
        "alarm_type": "热源",//热源、住户
        "alarm_name": "一次供水温度过高",
        "level": "urgent",//紧急、警告、通知
        "value": 95.5,
        "threshold": 90.0,
        "status": "active",//已确认、未确认
        "start_time": "2025-03-01 10:15:30",
        "end_time": null,
        "duration": "02:45:12"//持续时间
      },
      {
        "id": 2,
        "hes_id": 1,
        "hes_name": "金色家园换热站",
        "alarm_type": "压力",//热源、住户
        "alarm_name": "二次回水压力过低",
        "level": "warning",//紧急、警告、通知
        "value": 0.15,
        "threshold": 0.2,
        "status": "active",//已确认、未确认
        "start_time": "2025-03-01 11:05:22",
        "end_time": null,
        "duration": "01:55:20"
      }
    ]
  }
}
```

### 8.8 换热站告警详情 // 通过第三方接口获取
POST /api/hes/alarms/detail 
Request Header  
Authorization: Bearer {token}

//Request Body
{
  "id": 1
}

// Response 200
{
  "code": 200,
  "message": "告警详情获取成功",
  "data": {
    "id": 1,
    "hes_id": 1,
    "hes_name": "金色家园换热站",
    "alarm_type": "热源",//热源、住户
    "alarm_name": "一次供水温度过高",
    "level": "urgent",//紧急、警告、通知
    "value": 95.5,
    "threshold": 90.0,
    "status": "active",//已确认、未确认
    "start_time": "2025-03-01 10:15:30",
    "end_time": null,
    "duration": "02:45:12",
    "description": "一次供水温度超过设定阈值，可能导致系统过热",
    "possible_causes": [
      "调节阀故障",
      "温度传感器异常",
      "控制系统参数设置不当"
    ],
    "suggested_actions": [
      "检查调节阀工作状态",
      "校验温度传感器",
      "调整控制参数"
    ],
    "related_devices": [
      {
        "id": 5,
        "name": "一次供水温度传感器",
        "type": "sensor",
        "status": "normal"
      },
      {
        "id": 8,
        "name": "一次调节阀",
        "type": "valve",
        "status": "abnormal"
      }
    ],
    "history": [
      {
        "time": "2025-03-01 10:15:30",
        "event": "告警触发",
        "value": 95.5
      },
      {
        "time": "2025-03-01 10:20:15",
        "event": "通知发送",
        "recipients": ["张三", "李四"]
      },
      {
        "time": "2025-03-01 11:05:45",
        "event": "人工确认",
        "operator": "张三",
        "comment": "已远程调整阀门开度"
      }
    ]
  }
}
```

### 8.9 换热站告警统计
POST /api/hes/alarms/stats  
Request Header  
Authorization: Bearer {token}

```json
// Request Query Parameters
{
  "start_time": "2025-03-01 00:00:00",  // 可选
  "end_time": "2025-03-01 23:59:59",    // 可选
  "hes_ids": [1, 2, 3]                  // 可选，不传则统计所有换热站
}

// Response 200
{
  "code": 200,
  "message": "告警统计获取成功",
  "data": {
    "total": {
      "count": 45,
      "active": 12,
      "resolved": 33
    },
    "by_level": {
      "urgent": 8,
      "warning": 25,
      "notice": 12
    },
    "by_type": {
      "temperature_high": 15,
      "temperature_low": 5,
      "pressure_high": 7,
      "pressure_low": 10,
      "flow_abnormal": 8
    },
    "by_station": [
      {
        "hes_id": 1,
        "hes_name": "金色家园换热站",
        "count": 18,
        "active": 5
      },
      {
        "hes_id": 2,
        "hes_name": "翠湖花园换热站",
        "count": 12,
        "active": 3
      },
      {
        "hes_id": 3,
        "hes_name": "阳光小区换热站",
        "count": 15,
        "active": 4
      }
    ],
    "trend": {
      "dates": [
        "2025-02-24",
        "2025-02-25",
        "2025-02-26",
        "2025-02-27",
        "2025-02-28",
        "2025-03-01"
      ],
      "values": [5, 8, 6, 10, 9, 7]
    },
    "avg_response_time": "00:15:30",
    "avg_resolve_time": "01:25:45"
  }
}
```

## 9. 入户阀门控制

### 9.1 入户阀门列表 // 通过第三方接口获取
POST /api/valves/list
Request Header  
Authorization: Bearer {token} 

```json
// Request Query Parameters
{
  "heat_unit_id": 1,  // 可选，不传则查询所有 
  "building_id": 1,  // 可选
  "unit_id": 1,  // 可选
  "house_number": "101"  // 可选
} 

// Response 200
{
  "code": 200,
  "message": "入户阀门列表获取成功",
  "data": {
    "total": 100,
    "list": [
      {
        "id": 1,
        "name": "金色家园入户阀门",
        "status": "open"
      }   
    ]
  }
}
``` 

### 9.2 入户阀门控制（设置开度） // 通过第三方接口设置开度
POST /api/valves
Request Header  
Authorization: Bearer {token} 

//Request Body
{ 
  "heat_unit_id": 1,  // 可选，不传则查询所有 
  "building_id": 1,  // 可选
  "unit_id": 1,  // 可选
  "house_number": "101",  // 可选 
  "open_degree": 50 // 开度，0-100
}

// Response 200
{
  "code": 200,
  "message": "入户阀门开度设置成功"
}



## 10.缴费管理

### 10.1 缴费记录列表(根据缴费状态查询缴费记录、时间范围查询缴费记录) // 通过第三方接口获取
POST /api/payments/records
Request Header  
Authorization: Bearer {token}

```json
// Request Query Parameters
{ 
  "page": 1,
  "pageSize": 10,
  "status": "paid",  // paid/unpaid/all
  "start_time": "2025-03-01 00:00:00",  // 可选
  "end_time": "2025-03-01 23:59:59"    // 可选
}

// Response 200
{
  "code": 200,
  "message": "缴费记录列表获取成功",
  "data": { 
    "total": 100,
    "list": [
      {
        "id": 1,
        "user_id": 1,
        "hes_id": 1,  
        "hes_name": "金色家园换热站",
        "amount": 1000,
        "status": "paid",
        "payment_method": "wechat",
        "payment_time": "2025-03-01 10:15:30",
        "payment_channel": "微信支付"
      } 
    ]
  }
}
```

### 10.2 缴费自动开阀 
//下发小区名称，然后根据小区名称查询对应的楼栋id，然后根据楼栋id查询对应的单元id，
//然后根据单元id查询对应的户号，根据缴费情况，然后根据户号查询对应的入户阀门id，然后根据入户阀门id自动开阀


POST /api/payments/auto-open-valve
Request Header  
Authorization: Bearer {token}

//Request Body
{ 
  "heat_unit_name": "金色家园",  // 小区名称
  "building_id": 1,  // 楼栋id
  "unit_id": 1,  // 单元id  
}

// Response 200
{
  "code": 200,
  "message": "缴费用户阀门打开成功"
}

### 10.3 缴费统计（根据时间范围、缴费状态 查询缴费统计）
POST /api/payments/stats
Request Header  
Authorization: Bearer {token}

```json
// Request Query Parameters （根据时间范围、缴费状态 查询缴费统计）
{
  "heat_unit_name": "金色家园",  // 小区名称  
  "start_time": "2025-03-01 00:00:00",  // 可选
  "end_time": "2025-03-01 23:59:59",    // 可选
  "status": "paid"  // paid/unpaid/all // 可选
} 

// Response 200
{
  "code": 200,
  "message": "缴费统计获取成功",
  "data": {   
    "total": 1000,
    "paid": 1000,
    "unpaid": 0
  }
}


## 11. 消息通知

### 11.1 消息列表
GET /api/messages
Request Header  
Authorization: Bearer {token}

```json
// Request Query Parameters
{
  "page": 1,
  "pageSize": 20,
  "type": ["system", "work", "payment"],
  "status": "unread"
}

// Response 200
{
  "code": 200,
  "data": {
    "unreadCount": {
      "total": 12,
      "system": 3,
      "work": 8,
      "payment": 1
    },
    "list": [
      {
        "id": "msg_001",
        "type": "work",
        "level": "urgent",
        "title": "新工单分派通知",
        "content": "您有一个新的维修工单待处理",
        "time": "2023-12-05 14:23:00",
        "read": false,
        "data": {
          "orderId": "R-202312-001",
          "jumpUrl": "/pages/repair/detail?id=R-202312-001"
        }
      }
    ]
  }
}
```

### 11.2 推送设置
PUT /api/messages/settings
Request Header  
Authorization: Bearer {token}

```json
// Request
{
  "channels": {
    "miniprogram": true,
    "sms": false,
    "email": false
  },
  "quietTime": {
    "enabled": true,
    "start": "22:00",
    "end": "08:00"
  },
  "subscriptions": {
    "deviceAlarm": {
      "enabled": true,
      "level": ["urgent", "warning"]
    },
    "workOrder": {
      "enabled": true,
      "reminder": 30 // 提前30分钟
    },
    "payment": {
      "enabled": true,
      "frequency": "daily" // daily/weekly/monthly
    }
  }
}

// Response 200
{
  "code": 200,
  "data": {
    "message": "推送设置更新成功"
  }
}
```

### 11.3 未读数量
GET /api/messages/unread-count
Request Header  
Authorization: Bearer {token}

```json
// Response 200
{
  "code": 200,
  "data": {
    "unreadCount": {
      "total": 12,
      "system": 3,
      "work": 8,
      "payment": 1
    }
  }
}
``` 

## 12. WebSocket接口

### 12.1 实时数据推送
Request Header  
Authorization: Bearer {token}

WS /ws/realtime
```json
// 连接参数
{
  "token": "eyJhbGciOiJ...",
  "subscriptions": ["device", "alarm", "work"]
}

// 设备状态消息
{
  "type": "device_status",
  "time": "2023-12-05 14:23:00",
  "data": {
    "deviceId": "dev_001",
    "status": "fault",
    "parameters": {
      "temperature": 85,
      "pressure": 0.9
    }
  }
}

// 告警消息
{
  "type": "alarm",
  "time": "2023-12-05 14:23:00",
  "data": {
    "alarmId": "ALM-202312-001",
    "deviceId": "dev_001",
    "level": "urgent",
    "description": "温度超过阈值"
  }
}

// 工单更新消息
{
  "type": "work_order",
  "time": "2023-12-05 14:23:00",
  "data": {
    "orderId": "R-202312-001",
    "status": "processing",
    "handler": "张三"
  }
}
```

## 13. 通用接口

### 13.1 文件上传
POST /api/upload
Request Header  
Authorization: Bearer {token}

```json
// Request Multipart Form Data
{
  "file": "二进制文件",
  "type": "image", // image/video/audio
  "module": "repair", // repair/patrol/device
  "metadata": {
    "location": {
      "lat": 39.9042,
      "lng": 116.4074
    },
    "timestamp": "2023-12-05 14:23:00"
  }
}

// Response 200
{
  "code": 200,
  "data": {
    "url": "https://...",
    "filename": "IMG_001.jpg",
    "size": 1024000,
    "thumbnail": "https://..." // 如果是图片或视频
  }
}
```

### 13.2 首页统计数据
GET /api/stats/dashboard
Request Header  
Authorization: Bearer {token}

```json
// Response 200
{
  "code": 200,
  "data": {
    "devices": {
      "total": 356,
      "online": 349,
      "fault": 3,
      "onlineRate": "98%"
    },
    "work": {
      "today": {
        "total": 12,
        "pending": 3,
        "processing": 5,
        "completed": 4
      },
      "trend": {
        "dates": ["12-01", "12-02", "12-03", "12-04", "12-05"],
        "values": [10, 8, 15, 12, 12]
      }
    },
    "temperature": {
      "average": 22.5,
      "warning": 2, // 低温房间数
      "satisfaction": "95%"
    },
    "alarms": {
      "active": 3,
      "urgent": 1,
      "warning": 2
    }
  }
}
```

## 14. 错误码说明
```json
{
  "200": "操作成功",
  "400": {
    "code": 400,
    "message": "请求参数错误",
    "details": ["设备ID不能为空"]
  },
  "401": {
    "code": 401,
    "message": "未授权",
    "details": "请重新登录"
  },
  "403": {
    "code": 403,
    "message": "权限不足",
    "details": "需要管理员权限"
  },
  "404": {
    "code": 404,
    "message": "资源不存在",
    "details": "设备未找到"
  },
  "500": {
    "code": 500,
    "message": "服务器错误",
    "details": "数据库连接失败"
  },
  "600": {
    "code": 600,
    "message": "设备离线",
    "details": "无法获取实时数据"
  }
} 
```

## 15. 其他接口
### 15.1 获取热用户列表
GET /api/heatunits/list
Request Header  
Authorization: Bearer {token}

```json
// Response 200
{
  "code": 200,
  "message": "列表获取成功",
  "data": [
      {
        "id": 1,
        "name": "金色家园",
        "longitude": 108.923163, 
        "latitude": 34.278084 
      },
      {
        "id": 2,
        "name": "翠湖花园",
        "longitude": 108.923163, 
        "latitude": 34.278084 
      },
      {
        "id": 3,
        "name": "阳光小区",
        "longitude": 108.923163, 
        "latitude": 34.278084 
      },
    ] 
} 
```

### 15.2 获取热用户总数
GET /api/heatunits/count
Request Header  
Authorization: Bearer {token}
```json
// Response 200
{
  "code": 200,
  "message": "热用户总数获取成功",
  "data": {
    "count": 123
  }
}
```

### 15.3 获取换热站在线率
GET /api/hes/online-rate
Request Header  
Authorization: Bearer {token}
```json
// Response 200
{
  "code": 200,
  "message": "换热站在线率获取成功",
  "data": {
    "total": 15,
    "online": 14,
    "onlineRate": "93.3%"
  }
}
```


### 15.4 获取本周故障告警数量
GET /api/faults/weekly-count
Request Header  
Authorization: Bearer {token}
```json
// Response 200
{
  "code": 200,
  "message": "本周故障告警数量获取成功",
  "data": {
    "count": 12
  }
}
```

