package com.heating.service;

import java.util.List;
import java.util.Map;
import com.heating.dto.valve.ValveListRequest;
import com.heating.dto.valve.ValveControlRequest;

public interface ValveService {
    /**
     * 获取入户阀门列表
     * @param request 包含查询条件的请求
     * @return 入户阀门列表
     */
    List<Map<String, Object>> getValveList(ValveListRequest request);
    
    /**
     * 控制入户阀门（设置开度）
     * @param request 包含控制命令的请求
     * @return 控制结果
     */
    Map<String, Object> controlValve(ValveControlRequest request);
} 