package com.heating.dto.patrol;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 巡检消息响应DTO
 * 用于返回当天需要执行的巡检计划列表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PatrolMessageResponse {
    
    /**
     * 巡检计划ID
     */
    private Long id;
    
    /**
     * 巡检执行人ID列表
     */
    private List<Long> executorIds;
    
    /**
     * 巡检任务名称
     */
    private String name;
} 