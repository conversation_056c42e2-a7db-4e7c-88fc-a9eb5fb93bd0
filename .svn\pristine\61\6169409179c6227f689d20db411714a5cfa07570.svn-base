<template>
	<view class="account-binding-container">
		<view class="page-header">
			<text class="page-title">账号绑定</text>
		</view>
		
		<view class="binding-card">
			<view class="binding-item">
				<view class="binding-icon phone-icon">
					<text class="iconfont icon-phone"></text>
				</view>
				<view class="binding-info">
					<text class="binding-name">手机号</text>
					<text class="binding-status" v-if="bindings.phone">已绑定 ({{ maskPhone(bindings.phone) }})</text>
					<text class="binding-status not-bound" v-else>未绑定</text>
				</view>
				<view class="binding-action">
					<text class="action-btn" @click="handlePhone">{{ bindings.phone ? '更换' : '绑定' }}</text>
				</view>
			</view>
			
			<view class="binding-item">
				<view class="binding-icon email-icon">
					<text class="iconfont icon-email"></text>
				</view>
				<view class="binding-info">
					<text class="binding-name">邮箱</text>
					<text class="binding-status" v-if="bindings.email">已绑定 ({{ maskEmail(bindings.email) }})</text>
					<text class="binding-status not-bound" v-else>未绑定</text>
				</view>
				<view class="binding-action">
					<text class="action-btn" @click="handleEmail">{{ bindings.email ? '更换' : '绑定' }}</text>
				</view>
			</view>
			
			<view class="binding-item">
				<view class="binding-icon wechat-icon">
					<text class="iconfont icon-wechat"></text>
				</view>
				<view class="binding-info">
					<text class="binding-name">微信</text>
					<text class="binding-status" v-if="bindings.wechat">已绑定 ({{ bindings.wechatName }})</text>
					<text class="binding-status not-bound" v-else>未绑定</text>
				</view>
				<view class="binding-action">
					<text class="action-btn" @click="handleWechat">{{ bindings.wechat ? '解绑' : '绑定' }}</text>
				</view>
			</view>
			
			<view class="binding-item">
				<view class="binding-icon dingtalk-icon">
					<text class="iconfont icon-dingtalk"></text>
				</view>
				<view class="binding-info">
					<text class="binding-name">钉钉</text>
					<text class="binding-status" v-if="bindings.dingtalk">已绑定 ({{ bindings.dingtalkName }})</text>
					<text class="binding-status not-bound" v-else>未绑定</text>
				</view>
				<view class="binding-action">
					<text class="action-btn" @click="handleDingtalk">{{ bindings.dingtalk ? '解绑' : '绑定' }}</text>
				</view>
			</view>
		</view>
		
		<view class="security-section">
			<view class="security-title">
				<text class="title-text">安全提示</text>
			</view>
			<view class="security-tips">
				<view class="tip-item">
					<text class="tip-marker">•</text>
					<text class="tip-text">绑定手机号和邮箱可用于接收通知和找回密码</text>
				</view>
				<view class="tip-item">
					<text class="tip-marker">•</text>
					<text class="tip-text">绑定第三方账号可快速登录系统</text>
				</view>
				<view class="tip-item">
					<text class="tip-marker">•</text>
					<text class="tip-text">为保障账号安全，建议至少绑定手机号</text>
				</view>
			</view>
		</view>
		
		<!-- 弹出层：绑定手机号 -->
		<uni-popup ref="phonePopup" type="center">
			<view class="popup-content">
				<view class="popup-title">绑定手机号</view>
				<view class="popup-form">
					<view class="form-item">
						<input type="number" v-model="phoneForm.number" placeholder="请输入手机号码" maxlength="11" />
					</view>
					<view class="form-item code-item">
						<input type="number" v-model="phoneForm.code" placeholder="请输入验证码" maxlength="6" />
						<text class="send-code-btn" @click="sendCode('phone')">获取验证码</text>
					</view>
				</view>
				<view class="popup-actions">
					<text class="cancel-btn" @click="closePopup('phonePopup')">取消</text>
					<text class="confirm-btn" @click="confirmBinding('phone')">确定</text>
				</view>
			</view>
		</uni-popup>
		
		<!-- 弹出层：绑定邮箱 -->
		<uni-popup ref="emailPopup" type="center">
			<view class="popup-content">
				<view class="popup-title">绑定邮箱</view>
				<view class="popup-form">
					<view class="form-item">
						<input type="text" v-model="emailForm.address" placeholder="请输入邮箱地址" />
					</view>
					<view class="form-item code-item">
						<input type="number" v-model="emailForm.code" placeholder="请输入验证码" maxlength="6" />
						<text class="send-code-btn" @click="sendCode('email')">获取验证码</text>
					</view>
				</view>
				<view class="popup-actions">
					<text class="cancel-btn" @click="closePopup('emailPopup')">取消</text>
					<text class="confirm-btn" @click="confirmBinding('email')">确定</text>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
export default {
	data() {
		return {
			// 绑定状态
			bindings: {
				phone: '13812345678',
				email: '<EMAIL>',
				wechat: 'wx123456789',
				wechatName: '微信用户',
				dingtalk: '',
				dingtalkName: ''
			},
			// 手机绑定表单
			phoneForm: {
				number: '',
				code: ''
			},
			// 邮箱绑定表单
			emailForm: {
				address: '',
				code: ''
			}
		}
	},
	onLoad() {
		// 加载用户的绑定信息
		this.loadBindingInfo();
	},
	methods: {
		// 加载绑定信息
		loadBindingInfo() {
			// 从本地存储获取用户信息
			const userInfo = uni.getStorageSync('userInfo');
			if (userInfo) {
				// 如果有用户信息，更新绑定状态
				this.bindings.phone = userInfo.phone || '';
				this.bindings.email = userInfo.email || '';
				this.bindings.wechat = userInfo.wechatId || '';
				this.bindings.wechatName = userInfo.wechatName || '微信用户';
				this.bindings.dingtalk = userInfo.dingtalkId || '';
				this.bindings.dingtalkName = userInfo.dingtalkName || '钉钉用户';
			}
		},
		
		// 手机号脱敏
		maskPhone(phone) {
			if (!phone) return '';
			return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
		},
		
		// 邮箱脱敏
		maskEmail(email) {
			if (!email) return '';
			const parts = email.split('@');
			if (parts.length !== 2) return email;
			const name = parts[0];
			const domain = parts[1];
			return name.substring(0, 3) + '****@' + domain;
		},
		
		// 处理手机号绑定
		handlePhone() {
			this.$refs.phonePopup.open();
		},
		
		// 处理邮箱绑定
		handleEmail() {
			this.$refs.emailPopup.open();
		},
		
		// 处理微信绑定
		handleWechat() {
			if (this.bindings.wechat) {
				// 已绑定，询问是否解绑
				uni.showModal({
					title: '解绑确认',
					content: '确定要解绑微信账号吗？',
					success: (res) => {
						if (res.confirm) {
							// 模拟解绑成功
							this.bindings.wechat = '';
							this.bindings.wechatName = '';
							
							// 更新本地存储
							const userInfo = uni.getStorageSync('userInfo') || {};
							userInfo.wechatId = '';
							userInfo.wechatName = '';
							uni.setStorageSync('userInfo', userInfo);
							
							uni.showToast({
								title: '解绑成功',
								icon: 'success'
							});
						}
					}
				});
			} else {
				// 未绑定，调用绑定流程
				uni.showToast({
					title: '微信绑定功能开发中...',
					icon: 'none'
				});
			}
		},
		
		// 处理钉钉绑定
		handleDingtalk() {
			if (this.bindings.dingtalk) {
				// 已绑定，询问是否解绑
				uni.showModal({
					title: '解绑确认',
					content: '确定要解绑钉钉账号吗？',
					success: (res) => {
						if (res.confirm) {
							// 模拟解绑成功
							this.bindings.dingtalk = '';
							this.bindings.dingtalkName = '';
							
							// 更新本地存储
							const userInfo = uni.getStorageSync('userInfo') || {};
							userInfo.dingtalkId = '';
							userInfo.dingtalkName = '';
							uni.setStorageSync('userInfo', userInfo);
							
							uni.showToast({
								title: '解绑成功',
								icon: 'success'
							});
						}
					}
				});
			} else {
				// 未绑定，调用绑定流程
				uni.showToast({
					title: '钉钉绑定功能开发中...',
					icon: 'none'
				});
			}
		},
		
		// 发送验证码
		sendCode(type) {
			if (type === 'phone') {
				if (!this.phoneForm.number || this.phoneForm.number.length !== 11) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					});
					return;
				}
			} else if (type === 'email') {
				const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
				if (!emailRegex.test(this.emailForm.address)) {
					uni.showToast({
						title: '请输入正确的邮箱地址',
						icon: 'none'
					});
					return;
				}
			}
			
			// 模拟发送验证码
			uni.showToast({
				title: '验证码已发送',
				icon: 'success'
			});
		},
		
		// 确认绑定
		confirmBinding(type) {
			if (type === 'phone') {
				if (!this.phoneForm.number || this.phoneForm.number.length !== 11) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					});
					return;
				}
				
				if (!this.phoneForm.code || this.phoneForm.code.length !== 6) {
					uni.showToast({
						title: '请输入6位验证码',
						icon: 'none'
					});
					return;
				}
				
				// 模拟绑定成功
				this.bindings.phone = this.phoneForm.number;
				
				// 更新本地存储
				const userInfo = uni.getStorageSync('userInfo') || {};
				userInfo.phone = this.bindings.phone;
				uni.setStorageSync('userInfo', userInfo);
				
				uni.showToast({
					title: '手机绑定成功',
					icon: 'success'
				});
				
				this.closePopup('phonePopup');
			} else if (type === 'email') {
				const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
				if (!emailRegex.test(this.emailForm.address)) {
					uni.showToast({
						title: '请输入正确的邮箱地址',
						icon: 'none'
					});
					return;
				}
				
				if (!this.emailForm.code || this.emailForm.code.length !== 6) {
					uni.showToast({
						title: '请输入6位验证码',
						icon: 'none'
					});
					return;
				}
				
				// 模拟绑定成功
				this.bindings.email = this.emailForm.address;
				
				// 更新本地存储
				const userInfo = uni.getStorageSync('userInfo') || {};
				userInfo.email = this.bindings.email;
				uni.setStorageSync('userInfo', userInfo);
				
				uni.showToast({
					title: '邮箱绑定成功',
					icon: 'success'
				});
				
				this.closePopup('emailPopup');
			}
		},
		
		// 关闭弹出层
		closePopup(ref) {
			this.$refs[ref].close();
		}
	}
}
</script>

<style lang="scss">
.account-binding-container {
	background-color: #f5f7fa;
	min-height: 100vh;
	padding-bottom: 40rpx;
}

.page-header {
	background-color: #fff;
	padding: 30rpx;
	border-bottom: 1rpx solid #eee;
	
	.page-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}
}

.binding-card {
	margin: 30rpx;
	background-color: #fff;
	border-radius: 12rpx;
	padding: 10rpx 0;
	box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.05);
	
	.binding-item {
		display: flex;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
		
		&:last-child {
			border-bottom: none;
		}
		
		.binding-icon {
			width: 80rpx;
			height: 80rpx;
			border-radius: 50%;
			display: flex;
			justify-content: center;
			align-items: center;
			margin-right: 20rpx;
			
			.iconfont {
				font-size: 40rpx;
				color: #fff;
			}
			
			&.phone-icon {
				background: linear-gradient(135deg, #1890ff, #36b3ff);
			}
			
			&.email-icon {
				background: linear-gradient(135deg, #52c41a, #73d13d);
			}
			
			&.wechat-icon {
				background: linear-gradient(135deg, #07c160, #10d878);
			}
			
			&.dingtalk-icon {
				background: linear-gradient(135deg, #1677ff, #4096ff);
			}
		}
		
		.binding-info {
			flex: 1;
			
			.binding-name {
				font-size: 30rpx;
				color: #333;
				font-weight: bold;
				margin-bottom: 8rpx;
				display: block;
			}
			
			.binding-status {
				font-size: 26rpx;
				color: #52c41a;
				
				&.not-bound {
					color: #999;
				}
			}
		}
		
		.binding-action {
			.action-btn {
				display: inline-block;
				padding: 12rpx 30rpx;
				background-color: #f5f7fa;
				color: $uni-color-primary;
				font-size: 26rpx;
				border-radius: 30rpx;
				
				&:active {
					opacity: 0.8;
				}
			}
		}
	}
}

.security-section {
	margin: 30rpx;
	
	.security-title {
		margin-bottom: 20rpx;
		
		.title-text {
			font-size: 30rpx;
			color: #666;
			font-weight: bold;
		}
	}
	
	.security-tips {
		.tip-item {
			display: flex;
			margin-bottom: 15rpx;
			
			.tip-marker {
				color: #999;
				margin-right: 10rpx;
			}
			
			.tip-text {
				font-size: 26rpx;
				color: #999;
				line-height: 1.6;
			}
		}
	}
}

.popup-content {
	width: 600rpx;
	background-color: #fff;
	border-radius: 12rpx;
	overflow: hidden;
	
	.popup-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		text-align: center;
		padding: 30rpx 0;
		border-bottom: 1rpx solid #eee;
	}
	
	.popup-form {
		padding: 30rpx;
		
		.form-item {
			margin-bottom: 30rpx;
			
			input {
				width: 100%;
				height: 80rpx;
				border: 1rpx solid #ddd;
				border-radius: 8rpx;
				padding: 0 20rpx;
				font-size: 28rpx;
			}
			
			&.code-item {
				display: flex;
				align-items: center;
				
				input {
					flex: 1;
				}
				
				.send-code-btn {
					margin-left: 20rpx;
					white-space: nowrap;
					padding: 15rpx 20rpx;
					background-color: $uni-color-primary;
					color: #fff;
					font-size: 26rpx;
					border-radius: 8rpx;
					
					&:active {
						opacity: 0.8;
					}
				}
			}
		}
	}
	
	.popup-actions {
		display: flex;
		border-top: 1rpx solid #eee;
		
		.cancel-btn, .confirm-btn {
			flex: 1;
			text-align: center;
			padding: 30rpx 0;
			font-size: 30rpx;
		}
		
		.cancel-btn {
			color: #999;
			border-right: 1rpx solid #eee;
		}
		
		.confirm-btn {
			color: $uni-color-primary;
			font-weight: bold;
		}
	}
}
</style> 