/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.custom-tabbar[data-v-6def6a3b] {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3.4375rem;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding-bottom: env(safe-area-inset-bottom);
  box-shadow: 0 -0.125rem 0.625rem rgba(0, 0, 0, 0.06);
  z-index: 999;
}
.custom-tabbar .tab-item[data-v-6def6a3b] {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.375rem 0;
  position: relative;
  transition: all 0.3s;
}
.custom-tabbar .tab-item.active[data-v-6def6a3b] {
  transform: translateY(-0.1875rem);
}
.custom-tabbar .tab-item.active .tab-text[data-v-6def6a3b] {
  color: #1890ff;
  font-weight: 500;
}
.custom-tabbar .tab-item .icon-container[data-v-6def6a3b] {
  width: 1.875rem;
  height: 1.875rem;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 0.1875rem;
}
.custom-tabbar .tab-item .icon-container .tab-icon[data-v-6def6a3b] {
  width: 1.5rem;
  height: 1.5rem;
  transition: all 0.3s;
}
.custom-tabbar .tab-item .tab-text[data-v-6def6a3b] {
  font-size: 0.75rem;
  color: #999;
  line-height: 1;
  transition: all 0.3s;
}
.custom-tabbar .tab-item .active-indicator[data-v-6def6a3b] {
  position: absolute;
  bottom: -0.09375rem;
  left: 50%;
  transform: translateX(-50%);
  width: 0.5rem;
  height: 0.1875rem;
  background: #1890ff;
  border-radius: 0.1875rem;
}
body[data-v-6def6a3b] {
  padding-bottom: calc(3.4375rem + env(safe-area-inset-bottom));
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.hes-list-container {
  padding: 0.625rem;
  box-sizing: border-box;
  background-color: #f5f7fa;
  min-height: 100vh;
}
.search-bar {
  margin-bottom: 0.625rem;
}
.search-bar .search-input-wrapper {
  background-color: #fff;
  border-radius: 0.375rem;
  padding: 0.5rem 0.625rem;
  display: flex;
  align-items: center;
  box-shadow: 0 0.0625rem 0.25rem rgba(0, 0, 0, 0.05);
}
.search-bar .search-input-wrapper uni-input {
  flex: 1;
  height: 1.25rem;
  font-size: 0.875rem;
  margin: 0 0.3125rem;
}
.search-bar .search-input-wrapper .iconfont {
  font-size: 1rem;
  color: #999;
}
.filter-tabs {
  display: flex;
  background-color: #fff;
  border-radius: 0.375rem;
  margin-bottom: 0.625rem;
  padding: 0.125rem;
  box-shadow: 0 0.0625rem 0.25rem rgba(0, 0, 0, 0.05);
}
.filter-tabs .filter-tab {
  flex: 1;
  text-align: center;
  padding: 0.5rem 0;
  font-size: 0.875rem;
  position: relative;
  color: #666;
  transition: all 0.3s;
  border-radius: 0.25rem;
}
.filter-tabs .filter-tab.active {
  color: #fff;
  background-color: #1890ff;
  font-weight: 500;
}
.hes-modern-list .hes-card {
  border-radius: 0.375rem;
  padding: 0.625rem 0.75rem;
  margin-bottom: 0.625rem;
  box-shadow: 0 0.0625rem 0.375rem rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}
.hes-modern-list .hes-card.yellow-card {
  background-color: #ffd466;
  color: #333;
}
.hes-modern-list .hes-card.green-card {
  background-color: #2dcea3;
  color: #fff;
}
.hes-modern-list .hes-card.blue-card {
  background-color: #4fb5ee;
  color: #fff;
}
.hes-modern-list .hes-card.gray-card {
  background-color: #a0a0a0;
  color: #fff;
}
.hes-modern-list .hes-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.375rem;
}
.hes-modern-list .hes-card .card-header .station-name {
  font-size: 1rem;
  font-weight: 600;
  max-width: 60%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.hes-modern-list .hes-card .card-header .connection-time {
  font-size: 0.75rem;
  opacity: 0.9;
}
.hes-modern-list .hes-card .operation-mode {
  font-size: 0.875rem;
  margin-bottom: 0.375rem;
}
.hes-modern-list .hes-card .temperature-info {
  margin-bottom: 0.5rem;
}
.hes-modern-list .hes-card .temperature-info .network-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.hes-modern-list .hes-card .temperature-info .network-row .network-label {
  font-size: 0.875rem;
  min-width: 2.8125rem;
}
.hes-modern-list .hes-card .temperature-info .network-row .temp-value {
  font-size: 0.875rem;
  font-weight: 500;
  margin-right: 0.625rem;
}
.hes-modern-list .hes-card .temperature-info .network-row .second-label {
  margin-left: 0.3125rem;
}
.hes-modern-list .hes-card .card-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.hes-modern-list .hes-card .card-actions .status-badge {
  padding: 0.1875rem 0.625rem;
  border-radius: 0.9375rem;
  font-size: 0.75rem;
  background-color: rgba(255, 255, 255, 0.25);
}
.hes-modern-list .hes-card .card-actions .action-buttons {
  display: flex;
}
.hes-modern-list .hes-card .card-actions .action-buttons .action-btn {
  padding: 0.1875rem 0.9375rem;
  margin-left: 0.5rem;
  font-size: 0.8125rem;
  border-radius: 0.9375rem;
  background-color: rgba(255, 255, 255, 0.25);
}
.load-more,
.no-more {
  text-align: center;
  padding: 0.9375rem 0;
  font-size: 0.8125rem;
  color: #999;
}