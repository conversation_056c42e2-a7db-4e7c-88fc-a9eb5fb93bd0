package com.heating.controller;

import com.heating.dto.ApiResponse;
import com.heating.dto.ManagerHeatUnitDto;
import com.heating.service.ManagerHeatUnitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;   
import com.heating.exception.GlobalExceptionHandler;

import java.util.List;

/**
 * 管理人员与热用户关联Controller
 */
@RestController
@RequestMapping("/api/manager-heat-units")
public class ManagerHeatUnitController {

    @Autowired
    private ManagerHeatUnitService managerHeatUnitService;

    /**
     * 创建管理人员与热用户关联
     */
    @PostMapping
    public ResponseEntity<ApiResponse<Long>> create(@RequestBody ManagerHeatUnitDto dto) {
        try {
            Long id = managerHeatUnitService.save(dto);
            return ResponseEntity.ok(ApiResponse.success("关联创建成功", id));
        } catch (Exception e) {
            return ResponseEntity.ok(ApiResponse.error("关联创建失败: " + e.getMessage()));  
        }
    }

    /**
     * 更新管理人员与热用户关联
     */
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<ManagerHeatUnitDto>> update(@PathVariable Long id, @RequestBody ManagerHeatUnitDto dto) {
        try {
            ManagerHeatUnitDto updated = managerHeatUnitService.update(id, dto);
            return ResponseEntity.ok(ApiResponse.success("关联更新成功", updated));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.ok(ApiResponse.error("关联更新失败: " + e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.ok(ApiResponse.error("关联更新失败: " + e.getMessage()));
        }
    }

    /**
     * 删除管理人员与热用户关联
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> delete(@PathVariable Long id) {
        try {
            managerHeatUnitService.delete(id);
            return ResponseEntity.ok(ApiResponse.success("关联删除成功", null));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.ok(ApiResponse.error("关联删除失败: " + e.getMessage()));
        }
    }

    /**
     * 获取管理人员与热用户关联详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<ManagerHeatUnitDto>> getById(@PathVariable Long id) {
        try {
            ManagerHeatUnitDto dto = managerHeatUnitService.getById(id);
            return ResponseEntity.ok(ApiResponse.success("获取关联详情成功", dto));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.ok(ApiResponse.error("获取关联详情失败: " + e.getMessage()));
        }
    }

    /**
     * 获取指定管理人员的热用户列表
     */
    @GetMapping("/manager/{managerId}")
    public ResponseEntity<ApiResponse<List<ManagerHeatUnitDto>>> getByManagerId(@PathVariable Long managerId) {
        try {
            List<ManagerHeatUnitDto> list = managerHeatUnitService.getByManagerId(managerId);
            return ResponseEntity.ok(ApiResponse.success("获取管理热用户列表成功", list));
        } catch (Exception e) {
            return ResponseEntity.ok(ApiResponse.error("获取管理热用户列表失败: " + e.getMessage()));
        }
    }

    /**
     * 获取管理指定热用户的人员列表
     */
    @GetMapping("/heat-unit/{heatUnitId}")
    public ResponseEntity<ApiResponse<List<ManagerHeatUnitDto>>> getByHeatUnitId(@PathVariable Long heatUnitId) {
        try {
            List<ManagerHeatUnitDto> list = managerHeatUnitService.getByHeatUnitId(heatUnitId);
            return ResponseEntity.ok(ApiResponse.success("获取热用户管理人员列表成功", list));
        } catch (Exception e) {
            return ResponseEntity.ok(ApiResponse.error("获取热用户管理人员列表失败: " + e.getMessage()));
        }
    }
} 