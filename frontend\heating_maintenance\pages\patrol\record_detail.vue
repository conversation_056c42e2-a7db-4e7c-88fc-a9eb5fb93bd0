<template>
  <view class="page-container">
    <!-- 顶部信息卡片 -->
    <view class="order-info-card">
      <view class="order-header">
        <view class="order-number">
          <text>{{ recordInfo.planName || "-" }}</text>
        </view>
        <view class="order-status" :class="getStatusClass(recordInfo.status)">
          {{ getStatusText(recordInfo.status) }}
        </view>
      </view>

      <view class="order-location">
        <view class="location-info">
          <text class="location-name">{{ recordInfo.locations || "未知"}}</text>
        </view>
      </view>

      <view class="patrol-info">
        <view class="patrol-executor">
          <text class="patrol-label">执行人员：</text>
          <text class="patrol-value">{{ recordInfo.executorName || "未知" }}</text>
        </view>
        <view class="patrol-date">
          <text class="patrol-label">执行日期：</text>
          <text class="patrol-value">{{ formatDateOnly(recordInfo.executionDate) }}</text>
        </view>
      </view>
    </view>

    <!-- 详情选项卡 -->
    <view class="detail-tabs">
      <view
        v-for="(tab, index) in tabs"
        :key="index"
        class="tab-item"
        :class="{ active: currentTab === index }"
        @click="switchTab(index)"
      >
        {{ tab.name }}
      </view>
    </view>

    <!-- 加载中状态显示 -->
   <!-- <view class="loading-container" v-if="loading">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view> -->

    <!-- 错误提示 -->
    <view class="error-container" v-if="showError">
      <view class="error-icon">!</view>
      <text class="error-message">{{ errorMessage }}</text>
      <button class="retry-button" @click="loadPatrolResults">重试</button>
    </view>

    <!-- 详情内容区域 - 使用普通view替代swiper -->
    <view class="content-container" v-if="!loading && !showError">
      <!-- 巡检信息 Tab -->
      <view class="tab-content" v-if="currentTab === 0">
        <view class="scroll-view-content">
          <view class="info-section">
            <view class="section-content">
              <view class="info-item">
                <text class="info-label">所属计划</text>
                <text class="info-value">{{ planInfo?.planName || recordInfo.planName || "-" }}</text>
              </view>
              <view class="info-item" v-if="planInfo?.planNo">
                <text class="info-label">计划编号</text>
                <text class="info-value">{{ planInfo.planNo }}</text>
              </view>
              <view class="info-item" v-if="planInfo?.patrolType">
                <text class="info-label">巡检类型</text>
                <text class="info-value">{{ planInfo.patrolType }}</text>
              </view>
              <view class="info-item" v-if="planInfo?.scheduleType">
                <text class="info-label">周期类型</text>
                <text class="info-value">{{ getScheduleTypeText(planInfo.scheduleType) }}</text>
              </view>
              <view class="info-item" v-if="planInfo?.scheduleType === 'weekly' && planInfo?.scheduleWeekDays">
                <text class="info-label">执行日</text>
                <text class="info-value">{{ formatWeekDays(planInfo.scheduleWeekDays) }}</text>
              </view>
              <view class="info-item" v-if="planInfo?.scheduleType === 'monthly' && planInfo?.scheduleMonthDays">
                <text class="info-label">执行日</text>
                <text class="info-value">{{ formatMonthDays(planInfo.scheduleMonthDays) }}</text>
              </view>
              <view class="info-item" v-if="planInfo?.scheduleType === 'custom' && planInfo?.scheduleInterval">
                <text class="info-label">执行间隔</text>
                <text class="info-value">{{ planInfo.scheduleInterval }} 天</text>
              </view>
              <view class="info-item">
                <text class="info-label">开始日期</text>
                <text class="info-value">{{ formatDateOnly(planInfo?.startDate) }}</text>
              </view>
              <view class="info-item" v-if="planInfo?.endDate">
                <text class="info-label">结束日期</text>
                <text class="info-value">{{ formatDateOnly(planInfo.endDate) }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">开始时间</text>
                <text class="info-value">{{ formatTimeOnly(recordInfo.startTime) }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">结束时间</text>
                <text class="info-value">{{ formatTimeOnly(recordInfo.endTime) }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">巡检地点</text>
                <text class="info-value">{{ recordInfo.locations || "-" }}</text>
              </view>
              <view class="info-item" v-if="recordInfo.executorPhone">
                <text class="info-label">联系电话</text>
                <text class="info-value">{{ recordInfo.executorPhone }}</text>
              </view>
              <view class="info-item" v-if="recordInfo.remark">
                <text class="info-label">备注</text>
                <text class="info-value">{{ recordInfo.remark }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 巡检任务/结果 Tab -->
      <view class="tab-content" v-if="currentTab === 1">
        <view class="scroll-view-content">
          <view class="patrol-results-header">
            <text class="results-title">{{ recordInfo.status === 'pending' || recordInfo.status === 'overdue' ? '巡检任务' : '巡检结果' }}</text>
            <view class="results-summary">
              <text class="results-count">共 {{ patrolResults.length }} 项</text>
              <text
                class="abnormal-count"
                v-if="resultSummary && resultSummary.abnormalCount > 0 && recordInfo.status !== 'pending' && recordInfo.status !== 'overdue'"
              >
                异常 {{ resultSummary.abnormalCount }} 项
              </text>
            </view>
          </view>

          <view v-if="patrolResults.length === 0" class="empty-results">
            <image src="/static/images/empty.png" mode="aspectFit"></image>
            <text>{{ recordInfo.status === 'pending' || recordInfo.status === 'overdue' ? '暂无巡检任务' : '暂无巡检结果' }}</text>
          </view>

          <view v-else class="task-list">
            <view
              class="task-item"
              v-for="(result, index) in patrolResults"
              :key="index"
              @click="toggleTaskDetails(index)"
            >
              <view class="task-header">
                <view class="task-left">
                  <view class="task-status" 
                    :class="recordInfo.status === 'pending' ? 'pending' : 
                           recordInfo.status === 'overdue' ? 'overdue' : 
                           result.checkResult === 'normal' ? 'completed' : 'abnormal'"></view>
                  <text class="task-title">{{
                    result.itemName || `巡检项 ${index + 1}`
                  }}</text>
                </view>
                <text
                  class="task-status-text"
                  :class="recordInfo.status === 'pending' ? 'status-pending' : 
                          recordInfo.status === 'overdue' ? 'status-overdue' : 
                          result.checkResult === 'normal' ? 'status-normal' : 'status-abnormal'"
                >
                  {{ getTaskStatusText(result) }}
                </text>
              </view>
              <view class="task-details" v-if="result.showDetails">
                <view class="detail-row" v-if="result.deviceName">
                  <text class="detail-label">设备信息</text>
                   <text class="detail-value">{{ result.deviceName }}{{ result.deviceType ? ` (${result.deviceType})` : '' }}</text>
                </view>
                <view class="detail-row" v-if="result.categoryName">
                  <text class="detail-label">检查类别</text>
                  <text class="detail-value">{{ result.categoryName }}</text>
                </view>
                <view class="detail-row" v-if="result.checkMethod">
                  <text class="detail-label">检查方法</text>
                  <text class="detail-value">{{ result.checkMethod }}</text>
                </view>
                <view class="detail-row" v-if="result.checkDescription">
                  <text class="detail-label">描述说明</text>
                  <text class="detail-value">{{ result.checkDescription }}</text>
                </view>
                <view class="detail-row" v-if="result.paramValue">
                  <text class="detail-label">参数值</text>
                  <text class="detail-value" :class="{ 'value-abnormal': result.checkResult === 'abnormal' }"
                    >{{ result.paramValue
                    }}{{ result.unit ? " " + result.unit : "" }}</text
                  >
                </view>
                <view class="detail-row" v-if="result.normalRange">
                  <text class="detail-label">正常范围</text>
                  <text class="detail-value">{{ result.normalRange }}</text>
                </view>
                <view class="detail-row" v-if="result.description">
                  <text class="detail-label">备注</text>
                  <text class="detail-value">{{ result.description }}</text>
                </view>

                <!-- 图片展示 -->
                <view
                  class="task-images"
                  v-if="result.images && result.images.length > 0"
                >
                  <text class="images-title">巡检照片</text>
                  <view class="image-list">
                    <image 
                      v-for="(img, imgIndex) in result.images" 
                      :key="imgIndex" 
                      :src="getFullImageUrl(img)" 
                      @click="previewImage(result.images, imgIndex)" 
                      mode="aspectFill"
                    ></image>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 照片汇总 Tab -->
      <view class="tab-content" v-if="currentTab === 2">
        <view class="scroll-view-content">
          <view v-if="getAllImages().length > 0" class="all-images">
            <view
              v-for="(imageInfo, index) in getAllImages()"
              :key="index"
              class="image-card"
              @click="previewImage(getAllImagesUrls(), index)"
            >
              <image :src="getFullImageUrl(imageInfo.url)" mode="aspectFill"></image>
              <view class="image-item-name">{{ imageInfo.itemName }}</view>
            </view>
          </view>
          
          <view v-else class="empty-results">
            <view class="empty-content">
              <image src="/static/images/empty.png" mode="aspectFit"></image>
              <text>暂无巡检照片</text>
            </view>
          </view>
        </view>
      </view>
    </view>

	<!-- 底部操作按钮 -->
	<PermissionCheck permission="patrol:plans:execute">
	 <view class="action-buttons" v-if="recordInfo.status === 'pending' ||  recordInfo.status === 'overdue'">
	 		<view class="action-btn start"  @click="startPatrol(recordInfo.id,recordInfo.patrolPlanId)">
	 			<text>开始巡检</text>
	 		</view>
	 	</view>
	</PermissionCheck>
  </view>
</template>

<script>
import { patrolApi } from "@/utils/api.js";
import uploadUtils from "@/utils/upload.js";
import PermissionCheck from "@/components/PermissionCheck.vue";

export default {
  components: {
    PermissionCheck
  },
  data() {
    return {
      loading: true,
      showError: false,
      errorMessage: "",
      recordId: null,
      recordInfo: {},
      patrolResults: [],
      resultSummary: null,
      planInfo: null,
      currentTab: 0,
      tabs: [
        { name: '巡检信息' },
        { name: '巡检任务及结果' },
        { name: '巡检照片' }
      ]
    };
  },
  onLoad(options) {
    if (options && options.id) {
      this.recordId = options.id;
      this.loadPatrolResults();
    } else {
      this.showError = true;
      this.errorMessage = "未找到记录ID，无法加载详情";
      this.loading = false;
    }
  },
  onReady() {
    // 确保初始化时显示第一个选项卡
    this.currentTab = 0;
  },
  methods: {
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        pending: "待执行",
        processing: "执行中",
        completed: "已完成",
        overdue: "已超时"
      };
      return statusMap[status] || "未知";
    },

    // 获取状态样式类
    getStatusClass(status) {
      const statusClassMap = {
        pending: "status-pending",
        processing: "status-processing",
        completed: "status-completed",
        overdue: "status-overdue"
      };
      return statusClassMap[status] || "";
    },
    // 开始巡检
    startPatrol(id,planId) {
      uni.navigateTo({
        url: `/pages/patrol/execute?id=${id}&&planId=${planId}`
      });
    },
    // 加载巡检结果
    loadPatrolResults() {
      // 检查API是否可用
      if (!patrolApi || !patrolApi.getPatrolResultDetail) {
        this.showError = true;
        this.errorMessage = "API服务不可用";
        this.loading = false;
        return;
      }
	  // 显示加载提示
	  uni.showLoading({
	    title: "加载中...",
	  });
      // 加载巡检结果详情
      patrolApi
        .getPatrolResultDetail(this.recordId)
        .then((res) => {
          this.loading = false;

          if (res.code === 200 && res.data) {
            // 处理API响应数据
            this.recordInfo = res.data.recordInfo || {};
            
            // 处理巡检结果列表，确保图片数据格式正确
            if (res.data.resultList && Array.isArray(res.data.resultList)) {
              this.patrolResults = res.data.resultList.map(result => {
                // 确保images字段是数组
                if (result.images && !Array.isArray(result.images)) {
                  // 如果是字符串，尝试解析JSON
                  try {
                    result.images = JSON.parse(result.images);
                  } catch (e) {
                    // 如果解析失败，将其转为数组
                    result.images = result.images ? [result.images] : [];
                  }
                }
                return result;
              });
            } else {
              this.patrolResults = [];
            }
            
            this.resultSummary = res.data.summary || null;
            this.planInfo = res.data.planInfo || null;
			 uni.hideLoading();
          } else {
            // 只显示警告，不阻止显示其他信息
			 uni.hideLoading();
            console.warn("获取巡检结果失败:", res.message);
            this.patrolResults = [];
          }
        })
        .catch((err) => {
			 uni.hideLoading();
          this.loading = false;
          console.error("加载巡检结果失败:", err);
          uni.showToast({
            title: "网络异常，无法加载巡检结果",
            icon: "none",
            duration: 2000,
          });
          this.patrolResults = [];
        });
    },
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },

    // 预览图片
    previewImage(images, current) {
      // 将所有图片路径转换为完整URL
      const fullUrls = images.map(path => this.getFullImageUrl(path));
      uni.previewImage({
        urls: fullUrls,
        current: fullUrls[current],
      });
    },

    // 获取完整的图片URL
    getFullImageUrl(path) {
      if (!path) return '';
      
      // 如果已经是完整URL，直接返回
      if (path.startsWith('http')) {
        return path;
      }
      
      // 使用uploadUtils获取完整URL
      return uploadUtils.getFileUrl(path);
    },

    // 格式化日期（不含时间）
    formatDateOnly(date) {
      if (!date) return "-";

      // 处理数组格式的日期 [year, month, day]
      if (Array.isArray(date)) {
        if (date.length >= 3) {
          const year = date[0];
          const month = String(date[1]).padStart(2, "0");
          const day = String(date[2]).padStart(2, "0");
          return `${year}-${month}-${day}`;
        }
        return date.join("-"); // 如果无法解析，返回用-连接的数组
      }

      // 其他格式处理
      try {
        const d = new Date(date);
        if (!isNaN(d.getTime())) {
          const year = d.getFullYear();
          const month = String(d.getMonth() + 1).padStart(2, "0");
          const day = String(d.getDate()).padStart(2, "0");
          return `${year}-${month}-${day}`;
        }
        return String(date);
      } catch (e) {
        console.error("格式化日期出错:", e);
        return String(date);
      }
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return "-";

      // 处理数组格式的日期时间 [year, month, day]或[year, month, day, hour, minute, second]
      if (Array.isArray(dateTime)) {
        if (dateTime.length >= 3) {
          const year = dateTime[0];
          const month = String(dateTime[1]).padStart(2, "0");
          const day = String(dateTime[2]).padStart(2, "0");

          // 如果包含时间部分
          if (dateTime.length >= 5) {
            const hour = String(dateTime[3]).padStart(2, "0");
            const minute = String(dateTime[4]).padStart(2, "0");
            const second =
              dateTime.length > 5 ? String(dateTime[5]).padStart(2, "0") : "00";
            return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
          }

          return `${year}-${month}-${day}`;
        }
        return dateTime.join("-"); // 如果无法解析，返回用-连接的数组
      }

      // 确保dateTime是字符串类型
      const dateTimeStr = String(dateTime);

      // 处理ISO格式日期时间
      if (dateTimeStr.includes("T")) {
        const date = new Date(dateTimeStr);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        const hours = String(date.getHours()).padStart(2, "0");
        const minutes = String(date.getMinutes()).padStart(2, "0");
        const seconds = String(date.getSeconds()).padStart(2, "0");
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      }

      // 如果是数字类型（时间戳），转换为日期字符串
      if (!isNaN(dateTime) && typeof dateTime === "number") {
        const date = new Date(dateTime);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        const hours = String(date.getHours()).padStart(2, "0");
        const minutes = String(date.getMinutes()).padStart(2, "0");
        const seconds = String(date.getSeconds()).padStart(2, "0");
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      }

      // 对于其他格式的字符串，尝试用Date解析
      try {
        const date = new Date(dateTimeStr);
        if (!isNaN(date.getTime())) {
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, "0");
          const day = String(date.getDate()).padStart(2, "0");
          const hours = String(date.getHours()).padStart(2, "0");
          const minutes = String(date.getMinutes()).padStart(2, "0");
          const seconds = String(date.getSeconds()).padStart(2, "0");

          // 确认是否有时间部分
          if (hours !== "00" || minutes !== "00" || seconds !== "00") {
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
          } else {
            return `${year}-${month}-${day}`;
          }
        }

        // 如果Date解析失败，尝试按原样返回或处理特殊格式
        return dateTimeStr;
      } catch (e) {
        console.error("格式化日期时间出错:", e, dateTimeStr);
        return dateTimeStr; // 如果无法格式化，返回原始值
      }
    },

    // 格式化时间，只提取时间部分 (HH:MM:SS)
    formatTimeOnly(dateTime) {
      if (!dateTime) return "-";

      // 先获取完整的日期时间格式
      const fullDateTime = this.formatDateTime(dateTime);

      // 如果包含空格（日期和时间分隔符），提取时间部分
      if (fullDateTime.includes(" ")) {
        return fullDateTime.split(" ")[1];
      }

      return fullDateTime; // 如果没有空格，可能只是时间或格式不符合预期，直接返回
    },

    // 获取巡检计划类型文本
    getScheduleTypeText(scheduleType) {
      if (!scheduleType) return "-";
      const scheduleTypeMap = {
        daily: "每日巡检",
        weekly: "每周巡检",
        monthly: "每月巡检",
        custom: "自定义巡检"
      };
      return scheduleTypeMap[scheduleType] || scheduleType;
    },

    // 切换任务详情显示/隐藏
    toggleTaskDetails(index) {
      // 如果patrolResults[index]没有showDetails属性，添加它
      if (this.patrolResults[index] && !this.patrolResults[index].hasOwnProperty('showDetails')) {
        this.$set(this.patrolResults[index], 'showDetails', true);
      } else {
        this.$set(this.patrolResults[index], 'showDetails', !this.patrolResults[index].showDetails);
      }
    },

    // 格式化周执行日文本
    formatWeekDays(weekDays) {
      if (!weekDays || !Array.isArray(weekDays)) return "-";
      
      const weekDayNames = {
        1: "周一",
        2: "周二",
        3: "周三",
        4: "周四",
        5: "周五",
        6: "周六",
        7: "周日"
      };
      
      return weekDays.map(day => weekDayNames[day] || `周${day}`).join(", ");
    },

    // 格式化月执行日文本
    formatMonthDays(monthDays) {
      if (!monthDays || !Array.isArray(monthDays)) return "-";
      return monthDays.map(day => `${day}日`).join(", ");
    },

    // 获取巡检项目状态文本
    getTaskStatusText(result) {
      if (this.recordInfo.status === 'pending') {
        return '待执行';
      } else if (this.recordInfo.status === 'overdue') {
        return '已超时';
      } else {
        return result.checkResult === 'normal' ? '正常' : '异常';
      }
    },

    // 获取所有图片信息
    getAllImages() {
      const images = [];
      this.patrolResults.forEach(result => {
        if (result.images && Array.isArray(result.images)) {
          result.images.forEach(img => {
            images.push({
              url: this.getFullImageUrl(img),
              itemName: result.itemName || `巡检项 ${result.index + 1}`
            });
          });
        }
      });
      return images;
    },

    // 获取所有图片URL
    getAllImagesUrls() {
      const urls = [];
      this.getAllImages().forEach(image => {
        urls.push(image.url);
      });
      return urls;
    },

    // 切换选项卡
    switchTab(index) {
      this.currentTab = index;
      // 添加延迟，确保DOM更新后再滚动到顶部
      setTimeout(() => {
        uni.pageScrollTo({
          scrollTop: 0,
          duration: 0
        });
      }, 50);
    },
  },
};
</script>

<style lang="scss">
.page-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  box-sizing: border-box;
}

.order-info-card {
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  margin: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  padding: 30rpx;

  .order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;

    .order-number {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .order-status {
      padding: 6rpx 16rpx;
      border-radius: 6rpx;
      font-size: 24rpx;
      text-align: center;
      min-width: 80rpx;

      &.status-pending {
        background-color: rgba(250, 173, 20, 0.1);
        color: #faad14; // 黄色
      }

      &.status-processing {
        background-color: rgba(24, 144, 255, 0.1);
        color: #1890ff; // 蓝色
      }

      &.status-completed {
        background-color: rgba(82, 196, 26, 0.1);
        color: #52c41a; // 绿色
      }

      &.status-overdue {
        background-color: rgba(245, 34, 45, 0.1);
        color: #f5222d; // 红色
      }
    }
  }

  .order-location {
    margin-bottom: 20rpx;

    .location-info {
      font-size: 28rpx;
      color: #666;
      
      .location-name {
        font-size: 32rpx;
        color: #333;
        font-weight: bold;
      }
    }
  }

  .patrol-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20rpx;

    .patrol-executor, .patrol-date {
      .patrol-label {
        font-size: 24rpx;
        color: #999;
        margin-right: 10rpx;
      }

      .patrol-value {
        font-size: 28rpx;
        color: #333;
        font-weight: bold;
      }
    }
  }
}

.detail-tabs {
  display: flex;
  background-color: #fff;
  margin: 0 20rpx 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: sticky; /* 使选项卡固定在顶部 */
  top: 0;
  z-index: 10;

  .tab-item {
    flex: 1;
    text-align: center;
    height: 80rpx;
    line-height: 80rpx;
    font-size: 28rpx;
    color: #666;
    position: relative;

    &.active {
      color: #1890ff;
      font-weight: bold;

      &::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 40%;
        height: 4rpx;
        background-color: #1890ff;
        border-radius: 2rpx;
      }
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 40vh;

  .loading-spinner {
    width: 70rpx;
    height: 70rpx;
    border: 6rpx solid #f3f3f3;
    border-top: 6rpx solid #1890ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20rpx;
  }

  .loading-text {
    font-size: 28rpx;
    color: #666;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  width: 100%;
  box-sizing: border-box;

  .error-icon {
    width: 80rpx;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    background-color: #fff2f0;
    border-radius: 50%;
    color: #f5222d;
    font-size: 50rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
  }

  .error-message {
    font-size: 28rpx;
    color: #666;
    text-align: center;
    margin-bottom: 30rpx;
  }

  .retry-button {
    background-color: #1890ff;
    color: #fff;
    font-size: 28rpx;
    padding: 16rpx 40rpx;
    border-radius: 8rpx;
    border: none;
  }
}

.content-container {
  flex: 1;
  overflow: visible; /* 改为visible，允许内容自然流动 */
  padding-bottom: 120rpx; /* 为底部按钮留出空间 */
}

.tab-content {
  height: auto;
  min-height: 60vh; /* 确保内容区域有足够的高度 */
  padding: 0 20rpx;
  box-sizing: border-box;
}

.scroll-view-content {
  height: auto; /* 移除固定高度限制，改为自适应内容高度 */
  max-height: none; /* 移除最大高度限制 */
  overflow: visible; /* 确保内容可见 */
}

.info-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
    padding-left: 20rpx;
    // border-left: 6rpx solid #1890ff;
  }

  .section-content {
    .info-item {
      display: flex;
      padding: 16rpx 0;
      border-bottom: 2rpx solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }

      .info-label {
        width: 160rpx;
        font-size: 28rpx;
        color: #666;
      }

      .info-value {
        flex: 1;
        font-size: 28rpx;
        color: #333;

        &.normal-count {
          color: #52c41a;
          font-weight: bold;
        }
        
        &.abnormal-count {
          color: #f5222d;
          font-weight: bold;
        }
        
        &.status-value {
          font-weight: bold;
          
          &.pending {
            color: #faad14; // 黄色 - 待执行
          }
          
          &.processing {
            color: #1890ff; // 蓝色 - 执行中
          }
          
          &.completed {
            color: #52c41a; // 绿色 - 已完成
          }
          
          &.overdue {
            color: #f5222d; // 红色 - 已超时
          }
        }
      }
    }
  }
}

.patrol-results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

  .results-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
  }

  .results-summary {
    display: flex;
    align-items: center;

    .results-count {
      font-size: 26rpx;
      color: #999;
      margin-right: 16rpx;
    }

    .abnormal-count {
      font-size: 26rpx;
      color: #f5222d;
      font-weight: bold;
    }
  }
}

.empty-results {
  padding: 0;
  text-align: center;
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  min-height: 500rpx; /* 增加最小高度，确保有足够空间 */
  display: flex;
  align-items: center;
  justify-content: center;

  .empty-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40rpx 0;
  }

  image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 30rpx;
  }

  text {
    font-size: 28rpx;
    color: #999;
    display: block;
    width: 100%;
    text-align: center;
    line-height: 1.5;
  }
}

.task-list {
  .task-item {
    padding: 20rpx;
    background-color: #fff;
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

    .task-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16rpx;

      .task-left {
        display: flex;
        align-items: center;

        .task-status {
          width: 20rpx;
          height: 20rpx;
          border-radius: 50%;
          margin-right: 10rpx;

          &.completed {
            background-color: #52c41a;
          }

          &.abnormal {
            background-color: #f5222d;
          }
          
          &.pending {
            background-color: #faad14;
          }
          
          &.overdue {
            background-color: #f5222d;
          }
        }

        .task-title {
          font-size: 30rpx;
          font-weight: bold;
          color: #333;
        }
      }

      .task-status-text {
        padding: 4rpx 16rpx;
        border-radius: 20rpx;
        font-size: 24rpx;
        
        &.status-normal {
          background-color: rgba(82, 196, 26, 0.1);
          color: #52c41a;
        }
        
        &.status-abnormal {
          background-color: rgba(245, 34, 45, 0.1);
          color: #f5222d;
        }
        
        &.status-pending {
          background-color: rgba(250, 173, 20, 0.1);
          color: #faad14; // 黄色
        }
        
        &.status-overdue {
          background-color: rgba(245, 34, 45, 0.1);
          color: #f5222d; // 红色
        }
      }
    }

    .task-details {
      background-color: #f8f8f8;
      border-radius: 8rpx;
      padding: 16rpx;
      margin-top: 16rpx;

      .detail-row {
        display: flex;
        margin-bottom: 16rpx;

        .detail-label {
          width: 120rpx;
          font-size: 26rpx;
          color: #666;
        }

        .detail-value {
          flex: 1;
          font-size: 26rpx;
          color: #333;
          word-break: break-all;
          
          &.value-abnormal {
            color: #f5222d;
          }
        }
      }

      .task-images {
        margin-top: 20rpx;

        .images-title {
          font-size: 28rpx;
          font-weight: bold;
          color: #333;
          margin-bottom: 10rpx;
        }

        .image-list {
          display: flex;
          flex-wrap: wrap;
          margin: 0 -10rpx;

          image {
            width: 33.33%;
            height: 200rpx;
            padding: 10rpx;
            box-sizing: border-box;
            flex-shrink: 0;
            border-radius: 8rpx;
          }
        }
      }
    }
  }
}

.all-images {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

  .image-card {
    width: 33.33%;
    padding: 10rpx;
    box-sizing: border-box;
    position: relative;
    height: 220rpx;

    image {
      width: 100%;
      height: 180rpx;
      object-fit: cover;
      border-radius: 8rpx;
    }

    .image-item-name {
      font-size: 24rpx;
      color: #666;
      margin-top: 10rpx;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
   .action-buttons {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		padding: 20rpx;
		background-color: #fff;
		display: flex;
		justify-content: center;
		box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);
		
		.action-btn {
			flex: 1;
			height: 80rpx;
			line-height: 80rpx;
			text-align: center;
			border-radius: 8rpx;
			font-size: 30rpx;
			margin: 0 10rpx;
			
			&.start {
				background-color: $uni-color-primary;
				color: #fff;
			}
			
			&.continue {
				background-color: $uni-color-success;
				color: #fff;
			}
			
			&.complete {
				background-color: $uni-color-warning;
				color: #fff;
			}
		}
	}
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin: 20rpx 0;
  padding-left: 20rpx;
  border-left: 6rpx solid #1890ff;
}
</style>
