package com.heating.entity.permission;

import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 系统权限实体类
 */
@Entity
@Table(name = "t_sys_permission")
@Data
public class TSysPermission {
    
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 权限编码
     */
    @Column(name = "permission_code")
    private String permissionCode;
    
    /**
     * 权限名称
     */
    @Column(name = "permission_name")
    private String permissionName;
    
    /**
     * 关联菜单名称
     */
    @Column(name = "menu_name")
    private String menuName;
    
    /**
     * 页面路径
     */
    @Column(name = "path")
    private String path;
    
    /**
     * 权限描述
     */
    @Column(name = "description")
    private String description;
    
    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;
} 