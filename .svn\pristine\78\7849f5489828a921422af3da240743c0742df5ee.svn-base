<template>
	<view class="transfer-container">
		<view class="form-card">
			<view class="form-title">转派工单</view>
			
			<view class="form-item">
				<view class="form-label required">转派原因</view>
				<textarea class="form-textarea" v-model="formData.transferReason" placeholder="请输入转派原因"></textarea>
			</view>
			
			<view class="form-item">
				<view class="form-label required">转派人员</view>
				<view class="picker-box" @click="showUserPicker">
					<text class="picker-text" :class="{'placeholder': !formData.repairUserName}">
						{{formData.repairUserName || '请选择转派人员'}}
					</text>
					<text class="iconfont icon-right"></text>
				</view>
			</view>
		</view>
		
		<view class="action-buttons">
			<button class="btn-cancel" @click="cancelTransfer">取消</button>
			<button class="btn-submit" @click="submitTransfer">确认转派</button>
		</view>
		
		<!-- 人员选择弹窗 -->
		<uni-popup ref="userPopup" type="bottom">
			<view class="popup-container">
				<view class="popup-header">
					<text class="cancel" @click="closeUserPicker">取消</text>
					<text class="title">选择转派人员</text>
					<text class="confirm" @click="confirmUserSelect">确认</text>
				</view>
				
				<!-- 搜索框 -->
				<view class="search-box">
					<view class="search-input-wrap">
						<text class="iconfont icon-search"></text>
						<input 
							type="text" 
							class="search-input" 
							v-model="searchKeyword" 
							placeholder="搜索姓名或手机号"
							confirm-type="search"
							@input="handleSearch"
						/>
						<text class="iconfont icon-close" v-if="searchKeyword" @click="clearSearch"></text>
					</view>
				</view>
				
				<view class="popup-body">
					<view 
						v-for="(user, index) in filteredUsers" 
						:key="index" 
						class="user-item"
						:class="{'active': isUserSelected(user)}"
						@click="selectUser(user)"
					>
						<view class="user-info">
							<text class="user-name">{{user.name || user.userName}}</text>
							<text class="user-phone">{{user.phone || ''}}</text>
						</view>
						<view class="select-indicator" v-if="isUserSelected(user)">
							<text class="select-text">已选</text>
							<text class="iconfont icon-check"></text>
						</view>
					</view>
					<view v-if="filteredUsers.length === 0" class="empty-tip">
						{{searchKeyword ? '未找到匹配的人员' : '暂无可选择的人员'}}
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import { userApi } from "@/utils/api.js";
	import { workOrderApi } from "@/utils/api.js";
	export default {
		data() {
			return {
				orderId: null,
				formData: {
					transferReason: '',
					repairUserId: null,
					repairUserName: ''
				},
				repairUsers: [],
				filteredUsers: [],
				searchKeyword: '',
				selectedUser: null,
				rules: {
					transferReason: [
						{ required: true, message: '请输入转派原因' }
					],
					repairUserId: [
						{ required: true, message: '请选择转派人员' }
					]
				}
			}
		},
		onLoad(options) {
			if (options.id) {
				this.orderId = options.id;
				// 实际项目中可以加载维修人员列表
				 this.loadRepairUsers();
			} else {
				this.showError('缺少工单ID');
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			}
		},
		methods: {
			// 加载维修人员列表
			loadRepairUsers() {
				userApi.getInspectorList({ role: 'repair' })
					.then(res => {
						if (res.code === 200 && res.data) {
							// 记录原始数据便于调试
							console.log('API返回的人员数据:', JSON.stringify(res.data));
							// 确保数据格式正确
							this.repairUsers = res.data.map(user => ({
								userId: user.userId || user.id,
								name: user.name || user.userName,
								phone: user.phone || user.phoneNumber || ''
							}));
							// 初始化过滤后的用户列表
							this.filteredUsers = [...this.repairUsers];
						}
					})
					.catch(err => {
						console.error('获取人员列表失败:', err);
						this.showError('获取人员列表失败');
					});
			},
			
			// 判断用户是否被选中
			isUserSelected(user) {
				if (!this.selectedUser) return false;
				
				// 优先比较userId，如果不存在则比较id
				const userIdToCompare = user.userId || user.id;
				const selectedUserId = this.selectedUser.userId || this.selectedUser.id;
				
				return userIdToCompare === selectedUserId;
			},
			
			// 处理搜索输入
			handleSearch() {
				if (!this.searchKeyword) {
					this.filteredUsers = [...this.repairUsers];
					return;
				}
				
				const keyword = this.searchKeyword.toLowerCase();
				this.filteredUsers = this.repairUsers.filter(user => {
					const name = (user.name || user.userName || '').toLowerCase();
					const phone = (user.phone || '').toLowerCase();
					return name.includes(keyword) || phone.includes(keyword);
				});
			},
			
			// 清除搜索
			clearSearch() {
				this.searchKeyword = '';
				this.filteredUsers = [...this.repairUsers];
			},
			
			// 显示人员选择器
			showUserPicker() {
				// 重置搜索条件
				this.searchKeyword = '';
				this.filteredUsers = [...this.repairUsers];
				this.$refs.userPopup.open();
			},
			
			// 关闭人员选择器
			closeUserPicker() {
				this.$refs.userPopup.close();
			},
			
			// 选择人员
			selectUser(user) {
				console.log('选择的用户:', JSON.stringify(user));
				this.selectedUser = { ...user }; // 创建用户对象的副本，避免引用问题
			},
			
			// 确认人员选择
			confirmUserSelect() {
				console.log('确认选择，当前选中:', JSON.stringify(this.selectedUser));
				if (this.selectedUser) {
					// 使用灵活的属性获取，避免字段名不匹配问题
					this.formData.repairUserId = this.selectedUser.userId || this.selectedUser.id;
					this.formData.repairUserName = this.selectedUser.name || this.selectedUser.userName;
					
					console.log('更新表单数据:', JSON.stringify(this.formData));
					this.closeUserPicker();
				} else {
					this.showError('请选择一个维修人员');
				}
			},
			
			// 取消转派
			cancelTransfer() {
				uni.navigateBack();
			},
			
			// 验证表单
			validateForm() {
				let isValid = true;
				
				for (const key in this.rules) {
					const value = this.formData[key];
					const rules = this.rules[key];
					
					for (const rule of rules) {
						if (rule.required && !value && value !== 0) {
							this.showError(rule.message);
							isValid = false;
							break;
						}
					}
					
					if (!isValid) break;
				}
				
				return isValid;
			},
			
			// 提交转派
			submitTransfer() {
				if (!this.validateForm()) return;
				
				// 显示确认对话框
				uni.showModal({
					title: '转派确认',
					content: '该工单仅可转派一次，请确认目标负责人无误后再进行操作。',
					confirmText: '确认转派',
					cancelText: '再次确认',
					success: (res) => {
						if (res.confirm) {
							this.doTransfer();
						}
					}
				});
			},
			
			// 执行转派操作
			doTransfer() {
				// 显示加载提示
				uni.showLoading({
					title: '提交中...'
				});
				
				// 构造请求参数
				const params = {
					orderId: this.orderId,
					transferUserId: this.getCurrentUserId(),
					repairUserId: this.formData.repairUserId,
					transferReason: this.formData.transferReason
				};
				
				console.log('提交的参数:', JSON.stringify(params));
				
				// 实际项目中应该调用API
				workOrderApi.transferOrder(params)
					.then(res => {
						if (res.code === 200) {
							this.showSuccess('工单已转派');
							setTimeout(() => {
								uni.navigateBack({
									delta: 1
								});
							}, 1500);
						} else {
							this.showError(res.message || '提交失败');
						}
					})
					.catch(err => {
						console.error('转派失败:', err);
						this.showError('网络异常，请稍后重试');
					})
					.finally(() => {
						uni.hideLoading();
					});
			},
			
			// 获取当前用户ID
			getCurrentUserId() {
				// 实际项目中从全局状态或本地存储获取
				return uni.getStorageSync("userId") || 0;
			},
			
			// 显示成功提示
			showSuccess(message) {
				uni.showToast({
					title: message,
					icon: 'success'
				});
			},
			
			// 显示错误提示
			showError(message) {
				uni.showToast({
					title: message,
					icon: 'none'
				});
			}
		}
	}
</script>

<style lang="scss">
	.transfer-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding: 20rpx;
	}
	
	.form-card {
		background-color: #fff;
		border-radius: 12rpx;
		margin-bottom: 20rpx;
		padding: 30rpx;
		
		.form-title {
			font-size: 32rpx;
			font-weight: bold;
			color: $uni-text-color;
			margin-bottom: 30rpx;
			padding-bottom: 20rpx;
			border-bottom: 1rpx solid #eee;
		}
		
		.form-item {
			margin-bottom: 30rpx;
			
			.form-label {
				font-size: 28rpx;
				color: $uni-text-color;
				margin-bottom: 16rpx;
				
				&.required::before {
					content: '*';
					color: #f5222d;
					margin-right: 6rpx;
				}
			}
			
			.form-textarea {
				width: 100%;
				height: 200rpx;
				padding: 20rpx;
				background-color: #f5f5f5;
				border-radius: 8rpx;
				font-size: 28rpx;
				color: $uni-text-color;
				box-sizing: border-box;
			}
			
			.picker-box {
				display: flex;
				justify-content: space-between;
				align-items: center;
				width: 100%;
				height: 80rpx;
				padding: 0 20rpx;
				background-color: #f5f5f5;
				border-radius: 8rpx;
				box-sizing: border-box;
				
				.picker-text {
					font-size: 28rpx;
					color: $uni-text-color;
					
					&.placeholder {
						color: #999;
					}
				}
				
				.iconfont {
					font-size: 28rpx;
					color: #999;
				}
			}
		}
	}
	
	.action-buttons {
		display: flex;
		padding: 20rpx 0;
		
		button {
			flex: 1;
			height: 80rpx;
			line-height: 80rpx;
			text-align: center;
			border-radius: 40rpx;
			font-size: 28rpx;
			margin: 0 10rpx;
			
			&::after {
				border: none;
			}
		}
		
		.btn-cancel {
			background-color: #f5f5f5;
			color: $uni-text-color;
			border: 1px solid #ddd;
		}
		
		.btn-submit {
			background-color: $uni-color-primary;
			color: #fff;
		}
	}
	
	.popup-container {
		background-color: #fff;
		border-radius: 24rpx 24rpx 0 0;
		overflow: hidden;
		
		.popup-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 30rpx;
			border-bottom: 1rpx solid #eee;
			
			.title {
				font-size: 32rpx;
				font-weight: bold;
				color: $uni-text-color;
			}
			
			.cancel, .confirm {
				font-size: 28rpx;
			}
			
			.cancel {
				color: #999;
			}
			
			.confirm {
				color: $uni-color-primary;
			}
		}
		
		.search-box {
			padding: 20rpx 30rpx;
			border-bottom: 1rpx solid #eee;
			
			.search-input-wrap {
				display: flex;
				align-items: center;
				background-color: #f5f5f5;
				border-radius: 36rpx;
				padding: 0 20rpx;
				height: 72rpx;
				
				.iconfont {
					color: #999;
					font-size: 32rpx;
					margin-right: 10rpx;
				}
				
				.search-input {
					flex: 1;
					height: 72rpx;
					font-size: 28rpx;
				}
				
				.icon-close {
					padding: 10rpx;
				}
			}
		}
		
		.popup-body {
			max-height: 60vh;
			padding-bottom: 50rpx;
			
			.user-item {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 30rpx;
				border-bottom: 1rpx solid #eee;
				position: relative;
				
				&.active {
					background-color: #e6f7ff;
					border-left: 8rpx solid $uni-color-primary;
				}
				
				.user-info {
					display: flex;
					flex-direction: column;
					
					.user-name {
						font-size: 28rpx;
						color: $uni-text-color;
						margin-bottom: 8rpx;
						font-weight: bold;
					}
					
					.user-phone {
						font-size: 24rpx;
						color: #999;
					}
				}
				
				.select-indicator {
					display: flex;
					align-items: center;
					color: $uni-color-primary;
					
					.select-text {
						font-size: 28rpx;
						color: $uni-color-primary;
						margin-right: 10rpx;
					}
					
					.iconfont {
						font-size: 40rpx;
						color: $uni-color-primary;
					}
				}
			}
			
			.empty-tip {
				text-align: center;
				color: #999;
				padding: 30rpx;
			}
		}
	}
</style> 