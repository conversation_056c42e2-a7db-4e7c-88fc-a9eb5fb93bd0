// utils/auth.js

/**
 * 获取本地存储的用户权限编码列表
 * @returns {Array<string>} 用户权限编码列表 (例如: ['user:list', 'user:create', 'dashboard:view'])
 *                         如果未找到或格式不正确，则返回空数组。
 */
function getStoredUserPermissions() {
    const permissions = uni.getStorageSync('userPermissions');
    // 确保返回的是一个数组
    if (Array.isArray(permissions)) {
      return permissions;
    }
    // console.warn('从本地存储获取的用户权限非数组格式:', permissions);
    return [];
  }

/**
 * 获取本地存储的系统权限映射表
 * @returns {Object} 权限映射表 (例如: {'user:list': {id:1, name:'用户列表', menu:'用户管理', path:'/pages/user'}})
 *                  如果未找到或格式不正确，则返回空对象。
 */
function getStoredPermissionsMap() {
  const permissionsMap = uni.getStorageSync('permissionsMap');
  // 确保返回的是一个对象
  if (permissionsMap && typeof permissionsMap === 'object') {
    return permissionsMap;
  }
  // console.warn('从本地存储获取的权限映射表非对象格式:', permissionsMap);
  return {};
}

/**
 * 获取用户的认证令牌
 * @returns {string} 用户的认证令牌，如果不存在则返回空字符串
 */
export function getToken() {
  return uni.getStorageSync('token') || '';
}
  
/**
 * 检查当前登录用户是否拥有特定的权限编码
 * @param {string} requiredPermissionCode - 需要检查的权限编码 (例如: 'user:view')
 * @returns {boolean} - true 如果用户拥有该权限, false 则表示没有。
 */
export function hasPermission(requiredPermissionCode) {
  // 如果没有提供需要检查的权限码，可以根据业务逻辑决定是允许还是拒绝。
  // 这里我们假设：如果一个操作/页面没有明确要求权限，则默认允许。
  if (!requiredPermissionCode) {
    return true; 
  }
  const userPermissions = getStoredUserPermissions();
  return userPermissions.includes(requiredPermissionCode);
}

/**
 * 获取权限相关的详细信息
 * @param {string} permissionCode - 权限编码
 * @returns {Object|null} 权限详细信息对象，不存在时返回null
 */
export function getPermissionInfo(permissionCode) {
  if (!permissionCode) {
    return null;
  }
  const permissionsMap = getStoredPermissionsMap();
  return permissionsMap[permissionCode] || null;
}

/**
 * 获取当前用户有权限访问的菜单列表
 * @returns {Array} 用户有权限的菜单列表，包含菜单名称和路径
 */
export function getAuthorizedMenus() {
  const userPermissions = getStoredUserPermissions();
  const permissionsMap = getStoredPermissionsMap();
  const menuMap = {}; // 用于去重
  
  // 遍历用户拥有的权限编码
  userPermissions.forEach(code => {
    const permInfo = permissionsMap[code];
    if (permInfo) {
      // 使用menuName作为key进行去重
      menuMap[permInfo.menu] = {
        name: permInfo.menu,
        path: permInfo.path
      };
    }
  });
  
  // 转换为数组
  return Object.values(menuMap);
}
  
/**
 * 检查用户是否有权访问某个页面或执行某个操作，并在无权限时进行处理。
 * @param {string} requiredPermissionCode - 访问页面或执行操作所需的权限编码。
 * @param {boolean} [redirectUnauthorized=true] - 如果无权限，是否自动重定向到登录页。
 * @returns {boolean} - true 表示有权限, false 表示无权限。
 */
export function checkAccess(requiredPermissionCode, redirectUnauthorized = true) {
  if (!hasPermission(requiredPermissionCode)) {
    const userPermissions = getStoredUserPermissions(); // 获取当前用户权限用于日志
    console.warn(`权限不足: 需要权限 '${requiredPermissionCode}', 用户当前权限: ${JSON.stringify(userPermissions)}`);
    
    uni.showToast({
      title: '您没有足够的权限访问此页面或执行此操作。',
      icon: 'none',
      duration: 2500
    });

    if (redirectUnauthorized) {
      // 无权限时，可以跳转到登录页、首页或专门的无权限提示页面。
      // 这里我们重定向到登录页。
      setTimeout(() => {
        uni.reLaunch({
          url: '/pages/user/login' // 修改为您的登录页路径
          // 或者跳转到自定义的无权限页面: url: '/pages/common/no-permission'
        });
      }, 2500);
    }
    return false;
  }
  return true;
}

/**
 * 页面访问守卫函数。
 * 在页面的 onLoad 生命周期函数中调用，以控制页面访问权限。
 * @param {string} requiredPermissionCode - 访问此页面所需的权限编码。
 *                                         这个编码应该与您在 `t_sys_permission` 表中定义的 `permission_code` 相对应。
 * @returns {boolean} - true 表示允许访问页面, false 表示不允许且已处理跳转。
 */
export function guardPageAccess(requiredPermissionCode) {
  if (!checkAccess(requiredPermissionCode, true)) {
    // checkAccess 函数内部已经处理了提示和跳转逻辑。
    // 返回 false 可以让调用方知道访问被拒绝，以便可能停止后续的页面加载逻辑。
    return false;
  }
  return true;
}