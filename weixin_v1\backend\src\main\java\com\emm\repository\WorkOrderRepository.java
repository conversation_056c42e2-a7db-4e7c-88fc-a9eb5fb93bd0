package com.emm.repository;

import com.emm.model.WorkOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.springframework.stereotype.Repository;
import java.util.Date;

@Repository
public interface WorkOrderRepository extends JpaRepository<WorkOrder, String> {

    @Query(value = 
        "SELECT new map(" +
            "wo.orderId as order_id, " +
            "h.name as station_name, " +
            "f.faultType as fault_type, " + 
            "f.faultLevel as fault_level, " +
            "wo.orderStatus as order_status, " +
            "function('DATE_FORMAT', wo.createdAt, '%Y-%m-%d %H:%i') as created_time" +
        ") " +
        "FROM WorkOrder wo " +
            "LEFT JOIN Hes h ON wo.stationId = h.id " +
            "LEFT JOIN Fault f ON wo.faultId = f.faultId " +
        "WHERE " +
            "(:date IS NULL OR function('DATE', wo.createdAt) = :date) " +
            "AND (:status IS NULL OR wo.orderStatus = :status) " +
            "AND (:userId IS NULL OR wo.repairUserId = :userId)" +
        "ORDER BY wo.createdAt DESC",
        countQuery = 
        "SELECT COUNT(wo) " +
        "FROM WorkOrder wo " +
        "WHERE " +
            "(:date IS NULL OR function('DATE', wo.createdAt) = :date) " +
            "AND (:status IS NULL OR wo.orderStatus = :status) " +
            "AND (:userId IS NULL OR wo.repairUserId = :userId)"
    )
    List<Map<String, Object>> findWorkOrderList(
        @Param("date") Date date, 
        @Param("status") String status,
        @Param("userId") Long userId
    );

    @Query(
        "SELECT new map(" +
            "wo.orderId as order_id, " +
            "wo.faultId as fault_id, " +
            "wo.stationId as station_id, " +
            "wo.repairUserId as repair_user_id, " +
            "u.name as repair_user_name, " +
            "wo.repairContent as repair_content, " +
            "wo.repairResult as repair_result, " +
            "wo.orderStatus as order_status, " +
            "h.name as station_name, " +
            "f.faultType as fault_type, " + 
            "f.faultLevel as fault_level, " +
            "f.faultDesc as fault_desc, " +
            "wo.repairTime as repair_time, " +
            "wo.createdAt as created_time, " +
            "wo.updatedAt as updated_time" +
        ") " +
        "FROM WorkOrder wo " +
            "LEFT JOIN FETCH Hes h ON wo.stationId = h.id " +
            "LEFT JOIN FETCH Fault f ON wo.faultId = f.faultId " +
            "LEFT JOIN FETCH User u ON wo.repairUserId = u.id " +
        "WHERE wo.orderId = :orderId"
    )
    Optional<Map<String, Object>> findWorkOrderDetail(@Param("orderId") String orderId);

    @Query(
        "SELECT new map(" +
            "wa.fileType as file_type, " +
            "wa.filePath as file_path " +
        ") " +
        "FROM WorkOrderAttachment wa " +
        "WHERE wa.orderId = :orderId " +
        "ORDER BY wa.createdAt DESC"
    )
    List<Map<String, Object>> findWorkOrderAttachments(@Param("orderId") String orderId);

    @Query(
        "SELECT new map(" +
            "wa.fileType as file_type, " +
            "wa.filePath as file_path" +
        ") " +
        "FROM FaultAttachment wa " +
            "LEFT JOIN WorkOrder wo ON wa.faultId = wo.faultId " +
        "WHERE wo.orderId = :orderId " +
        "ORDER BY wa.createdAt DESC"
    )
    List<Map<String, Object>> findFaultAttachments(@Param("orderId") String orderId);

    @Query(
        "SELECT new map(" +
            "ol.operationType as operation_type, " +
            "ol.operationDesc as operation_desc, " +
            "ol.operatorId as operator_id, " +
            "ol.createdAt as created_time, " +
            "u.name as operator_name" +
        ") " +
        "FROM OperationLog ol " +
            "LEFT JOIN FETCH User u ON ol.operatorId = u.id " +
        "WHERE ol.orderId = :orderId " +
        "ORDER BY ol.createdAt DESC"
    )
    List<Map<String, Object>> findOperationLogs(@Param("orderId") String orderId);

    @Query(
        "SELECT new map(" +
            "wo.orderStatus as status, " +
            "COUNT(wo) as count" +
        ") " +
        "FROM WorkOrder wo " +
        "WHERE (:date IS NULL OR function('DATE', wo.createdAt) = :date) " +
        "GROUP BY wo.orderStatus"
    )
    List<Map<String, Object>> countWorkOrdersByStatus(@Param("date") LocalDate date);


} 