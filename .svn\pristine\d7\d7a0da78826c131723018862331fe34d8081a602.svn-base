package com.heating.service;

import com.heating.dto.*;
import com.heating.dto.user.LoginRequest;
import com.heating.dto.user.RegisterRequest;
import com.heating.dto.user.UserInfoUpdateRequest;
import com.heating.dto.user.WorkStats;
import com.heating.dto.user.UserListRequest;
import com.heating.dto.user.UserListResponse;
import com.heating.entity.user.TUser;

import java.util.Map;
import java.util.List;

public interface AuthService {
    // 用户名登录方法 
    Map<String, Object> userLogin(LoginRequest request);
    // 手机号登录方法
    Map<String, Object> phoneLogin(LoginRequest request);
    // 获取用户信息方法
    Map<String, Object> getUserInfo(Long userId);
    // 更新用户信息方法
    boolean updateUserInfo(Long userId, UserInfoUpdateRequest request);
    // 权限获取方法
    List<String> getPermissionsByRole(String roleCode);
    // 获取工作统计的方法
    WorkStats calculateWorkStats(Long userId);
    // 用户注册方法
    boolean userRegister(RegisterRequest request);
    // 获取用户列表方法
    List<UserListResponse> getUserList(UserListRequest request);
    // 修改密码方法
    boolean changePassword(Long userId, String oldPassword, String newPassword);
} 