.container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.detail-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
  margin-bottom: 20rpx;
}

.fault-no {
  font-size: 28rpx;
  color: #333;
}

.status {
  padding: 4rpx 16rpx;
  border-radius: 4rpx;
  font-size: 24rpx;
}

/* 故障状态样式 */
.status.pending {
  background: #fef0f0;
  color: #f56c6c;
}

.status.confirmed {
  background: #f0f9eb;
  color: #67c23a;
}

.status.returned {
  background: #f4f4f5;
  color: #909399;
}

.info-list {
  margin-top: 20rpx;
}

.info-item {
  display: flex;
  margin-bottom: 24rpx;
  line-height: 1.5;
}

.info-item .label {
  width: 140rpx;
  color: #666;
  font-size: 28rpx;
}

.info-item .value {
  flex: 1;
  color: #333;
  font-size: 28rpx;
}

.info-item.desc {
  align-items: flex-start;
}

/* 故障等级样式 */
.level-1 {
  color: #e6a23c;
}

.level-2 {
  color: #f56c6c;
}

.level-3 {
  color: #f56c6c;
  font-weight: bold;
}

.section {
  margin-top: 40rpx;
}

.section-title {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 20rpx;
  padding-left: 16rpx;
  border-left: 6rpx solid #409eff;
}

.attachment-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.attachment-item {
  width: 220rpx;
  position: relative;
}

.attachment-item image,
.attachment-item video {
  width: 100%;
  height: 220rpx;
  border-radius: 8rpx;
}

.upload-time {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

.order-list {
  margin-top: 16rpx;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  margin-bottom: 16rpx;
}

.order-info {
  flex: 1;
}

.order-content {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.order-meta {
  font-size: 24rpx;
  color: #999;
}

.order-status {
  font-size: 26rpx;
  padding: 4rpx 16rpx;
  border-radius: 4rpx;
}

.action-bar {
  margin-top: 40rpx;
  display: flex;
  gap: 20rpx;
}

.btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.btn.confirm {
  background: #409eff;
  color: #fff;
}

.btn.reject {
  background: #fff;
  color: #f56c6c;
  border: 1rpx solid #f56c6c;
}

/* 只添加维修人员选择弹窗相关样式 */
.repair-user-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.popup-content {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  background: #fff;
  border-radius: 12rpx;
  overflow: hidden;
}

.popup-title {
  text-align: center;
  font-size: 32rpx;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #eee;
}

.repair-user-list {
  max-height: 400rpx;
  overflow-y: auto;
}

.repair-user-item {
  display: flex;
  align-items: center;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #eee;
}

.user-name {
  margin-left: 20rpx;
  font-size: 28rpx;
  color: #333;
}

.popup-footer {
  display: flex;
  padding: 20rpx;
  border-top: 1rpx solid #eee;
}

.popup-footer .btn {
  flex: 1;
  margin: 0 10rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
}

.popup-footer .cancel {
  background: #f5f5f5;
  color: #666;
}

.popup-footer .confirm {
  background: #1890ff;
  color: #fff;
} 