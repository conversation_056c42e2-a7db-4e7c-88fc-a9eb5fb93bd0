package com.heating.entity.patrol;

import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;
import java.time.LocalDate;
@Data
@Entity
@Table(name = "t_patrol_record")
public class TPatrolRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "patrol_plan_id")
    private int patrolPlanId;
    
    @Column(name = "executor_id")
    private Long executorId;
    
    @Column(name = "start_time")
    private LocalDateTime startTime;
    
    @Column(name = "end_time")
    private LocalDateTime endTime;
    
    private String status;
    private String remark;
    
    @Column(name = "create_time")
    private LocalDateTime createTime;
    
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    @Column(name = "execution_date")
    private LocalDate executionDate;

    @Column(name = "is_late_entry")
    private int isLateEntry;


} 