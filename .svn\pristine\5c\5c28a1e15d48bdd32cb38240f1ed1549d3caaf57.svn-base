package com.heating.dto.patrol;

import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;

@Data
public class PatrolRecordListResponse {
    private Long id;
    private Integer patrolPlanId;
    private String planName;
    private String patrolType;
    private Long executorId;
    private String executorName;
    private LocalDate executionDate;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private String status;
    private String remark;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
} 