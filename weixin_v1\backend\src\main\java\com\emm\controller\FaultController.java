package com.emm.controller;

import com.emm.model.Fault;
import com.emm.model.FaultAttachment;
import com.emm.service.FaultService;
import com.emm.dto.FaultReportRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;

import java.sql.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional; 

@RestController
@RequestMapping("/api/fault")
@Validated
public class FaultController {

    private static final Logger logger = LoggerFactory.getLogger(FaultController.class);

    @Autowired
    private FaultService faultService;

    @PostMapping("/report")
    public ResponseEntity<?> reportFault(@RequestBody FaultReportRequest request) {
        logger.info("Accessing POST /api/fault/report with request: {}", request);
        try {
            request.validate();

            // 设置默认的故障等级，如果为空的话
            if (request.getFaultLevel() == null || request.getFaultLevel().trim().isEmpty()) {
                request.setFaultLevel("1"); // 设置默认等级
            }

            // 创建故障对象
            Fault fault = new Fault();
            fault.setStationId(request.getStationId());
            fault.setFaultType(request.getFaultType());
            fault.setFaultLevel(request.getFaultLevel());
            fault.setFaultDesc(request.getFaultDesc());
            fault.setOccurTime(request.getOccurTime());
            fault.setReportUserId(request.getReportUserId());

            // 处理附件
            List<FaultAttachment> attachments = new ArrayList<>();

            // 处理图片
            if (request.getImages() != null) {
                for (String imagePath : request.getImages()) {
                    FaultAttachment attachment = new FaultAttachment();
                    attachment.setFileType("image");
                    attachment.setFilePath(imagePath);
                    attachments.add(attachment);
                }
            }
            
            // 处理视频
            if (request.getVideoUrl() != null && !request.getVideoUrl().trim().isEmpty()) {
                FaultAttachment attachment = new FaultAttachment();
                attachment.setFileType("video");
                attachment.setFilePath(request.getVideoUrl());
                attachments.add(attachment);
            }

            String faultId = faultService.reportFault(fault, attachments);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            Map<String, String> data = new HashMap<>();
            data.put("faultId", faultId);
            response.put("data", data);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error in POST /api/fault/report: {}", e.getMessage(), e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to report fault: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @PostMapping("/confirm")
    public ResponseEntity<?> confirmFault(@RequestBody Map<String, Object> request) {
        logger.info("Accessing POST /api/fault/confirm");
        try {
            String faultId = request.get("fault_id").toString();
            String action = request.get("action").toString();
            Long repairUserId = Long.parseLong(request.get("repair_user_id").toString());
            Long operatorId = Long.parseLong(request.get("operator_id").toString());

            faultService.confirmFault(faultId, action, repairUserId, operatorId);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error in POST /api/fault/confirm: {}", e.getMessage());
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/list")
    public ResponseEntity<?> getFaultList(
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String date) {
        logger.info("Accessing GET /api/fault/list with status: {} and date: {}", status, date);
        try {
                Date sqlDate = null;
                if (date != null && !date.trim().isEmpty()) {
                    try {
                        LocalDate localDate = LocalDate.parse(date);
                        sqlDate = Date.valueOf(localDate);
                    } catch (Exception e) {
                        throw new IllegalArgumentException("无效的日期格式，请使用 yyyy-MM-dd 格式");
                    }
                } 
                List<Map<String, Object>> faults = faultService.getFaultList(status, sqlDate);
                Map<String, Object> response = new HashMap<>();
                response.put("success", true);
                response.put("data", faults);
                return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error in GET /api/fault/list: {}", e.getMessage());
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/{faultId}")
    public ResponseEntity<?> getFaultDetail(@PathVariable String faultId) {
        logger.info("Accessing GET /api/fault/{}", faultId);
        try {
            Map<String, Object> faultDetail = faultService.getFaultDetail(faultId);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", faultDetail);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error in GET /api/fault/{}: {}", faultId, e.getMessage());
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}