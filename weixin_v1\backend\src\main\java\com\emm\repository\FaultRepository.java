package com.emm.repository;

import com.emm.model.Fault;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.sql.Date;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Repository
public interface FaultRepository extends JpaRepository<Fault, String> {

    @Query("SELECT new map(f.faultId as fault_id, h.name as station_name, f.faultType as fault_type, " +
           "f.faultLevel as fault_level, f.occurTime as occur_time, f.reportTime as report_time, " +
           "u.name as report_user_name, f.faultStatus as fault_status) " +
           "FROM Fault f " +
           "LEFT JOIN Hes h ON f.stationId = h.id " +
           "LEFT JOIN User u ON f.reportUserId = u.id " +
           "WHERE (:status IS NULL OR f.faultStatus = :status) " +
           "AND (:date IS NULL OR DATE(f.occurTime) = :date) " +
           "ORDER BY f.createdAt DESC")
    List<Map<String, Object>> findFaultList(@Param("status") String status, @Param("date") Date date);

    @Query(value = "SELECT " +
            "f.fault_id as fault_id, " +
            "f.station_id as station_id, " +
            "h.Name as station_name, " +
            "f.fault_type as fault_type, " +
            "f.fault_level as fault_level, " +
            "f.fault_desc as fault_desc, " +
            "f.fault_status as fault_status, " +
            "DATE_FORMAT(f.report_time, '%Y-%m-%d %H:%i') as report_time, " +
            "DATE_FORMAT(f.occur_time, '%Y-%m-%d %H:%i') as occur_time, " +
            "DATE_FORMAT(f.created_at, '%Y-%m-%d %H:%i') as created_time, " +
            "u.name as report_user_name " +
            "FROM fault f " +
            "LEFT JOIN t_userapp u ON f.report_user_id = u.id " +
            "LEFT JOIN t_hes h ON h.id = f.station_id " +
            "WHERE f.fault_id = :faultId", 
            nativeQuery = true)
    Map<String, Object> findFaultDetail(@Param("faultId") String faultId);

    @Query("SELECT new map(" +
           "COUNT(*) as total, " +
           "SUM(CASE WHEN f.faultStatus = '待确认' THEN 1 ELSE 0 END) as pending, " +
           "SUM(CASE WHEN f.faultStatus = '已确认' THEN 1 ELSE 0 END) as processing, " +
           "SUM(CASE WHEN f.faultStatus = '已退回' THEN 1 ELSE 0 END) as returned) " +
           "FROM Fault f " +
           "WHERE DATE(f.occurTime) = CURRENT_DATE")
    Map<String, Object> getFaultStatistics();

    @Query("SELECT new map(f.faultId as id, h.name as station_name, f.faultType as type, " +
           "f.faultLevel as level, f.faultDesc as description, " +
           "f.occurTime as time, " +
           "CASE " +
           "    WHEN f.faultStatus = '待确认' THEN 'pending' " +
           "    WHEN f.faultStatus = '已确认' THEN 'processing' " +
           "    WHEN f.faultStatus = '已退回' THEN 'returned' " +
           "END as status, " +
           "f.faultStatus as statusText) " +
           "FROM Fault f " +
           "LEFT JOIN Hes h ON f.stationId = h.id " +
           "WHERE DATE(f.occurTime) = CURRENT_DATE " +
           "ORDER BY f.occurTime DESC")
    List<Map<String, Object>> findRecentFaults();
}