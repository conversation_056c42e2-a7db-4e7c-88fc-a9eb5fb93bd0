
.no-permission-item[data-v-9e1a7520] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0.5;
}
.no-permission-text[data-v-9e1a7520] {
  font-size: 0.75rem;
  color: #999;
  text-align: center;
}

/* 确保permission-check组件继承其父容器的布局特性 */
[data-v-9e1a7520] .permission-check {
  display: inherit;
  width: inherit;
  height: inherit;
  flex: inherit;
  position: inherit;
}

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.fault-detail-container {
  padding: 0.9375rem;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.detail-card {
  background-color: #fff;
  border-radius: 0.375rem;
  padding: 0.9375rem;
  margin-bottom: 0.9375rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.detail-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.9375rem;
}
.detail-card .card-header .card-title {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
  position: relative;
  padding-left: 0.625rem;
}
.detail-card .card-header .card-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 0.25rem;
  height: 1rem;
  background-color: #1890ff;
  border-radius: 0.125rem;
}
.detail-card .card-header .fault-status, .detail-card .card-header .work-order-status {
  padding: 0.1875rem 0.625rem;
  border-radius: 0.125rem;
  font-size: 0.75rem;
}
.detail-card .card-header .fault-status.pending, .detail-card .card-header .work-order-status.pending {
  background-color: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}
.detail-card .card-header .fault-status.confirmed, .detail-card .card-header .work-order-status.confirmed {
  background-color: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}
.detail-card .card-header .fault-status.rejected, .detail-card .card-header .work-order-status.rejected {
  background-color: rgba(245, 34, 45, 0.1);
  color: #f5222d;
}
.detail-card .card-header .fault-status.in_progress, .detail-card .card-header .work-order-status.in_progress {
  background-color: rgba(250, 173, 20, 0.1);
  color: #faad14;
}
.detail-card .card-header .fault-status.completed, .detail-card .card-header .work-order-status.completed {
  background-color: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}
.detail-card .info-group {
  display: flex;
  flex-direction: column;
  gap: 0.625rem;
}
.detail-card .info-group .info-item {
  display: flex;
  align-items: flex-start;
}
.detail-card .info-group .info-item .info-label {
  min-width: 5rem;
  font-size: 0.875rem;
  color: #666;
}
.detail-card .info-group .info-item .info-value {
  flex: 1;
  font-size: 0.875rem;
  color: #333;
  word-break: break-all;
}
.detail-card .info-group .info-item .info-value.level-tag {
  display: inline-block;
  padding: 0.125rem 0.5rem;
  border-radius: 0.125rem;
}
.detail-card .info-group .info-item .info-value.level-tag.minor {
  background-color: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}
.detail-card .info-group .info-item .info-value.level-tag.normal {
  background-color: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}
.detail-card .info-group .info-item .info-value.level-tag.critical {
  background-color: rgba(250, 173, 20, 0.1);
  color: #faad14;
}
.detail-card .info-group .info-item .info-value.level-tag.emergency {
  background-color: rgba(245, 34, 45, 0.1);
  color: #f5222d;
}
.detail-card .fault-desc {
  font-size: 0.875rem;
  color: #333;
  line-height: 1.6;
}
.detail-card .image-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -0.3125rem;
}
.detail-card .image-grid .image-item {
  width: 33.33%;
  padding: 0.3125rem;
  box-sizing: border-box;
}
.detail-card .image-grid .image-item uni-image {
  width: 100%;
  height: 6.25rem;
  border-radius: 0.25rem;
  background-color: #f5f5f5;
}
.detail-card .video-container {
  margin-top: 0.625rem;
}
.detail-card .video-container uni-video {
  width: 100%;
  height: 12.5rem;
  border-radius: 0.25rem;
  background-color: #000;
}
.detail-card .video-container .video-fallback {
  width: 100%;
  height: 12.5rem;
  background-color: #f5f5f5;
  border-radius: 0.25rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.detail-card .video-container .video-fallback .play-icon {
  width: 2.5rem;
  height: 2.5rem;
  margin-bottom: 0.625rem;
}
.detail-card .video-container .video-fallback .fallback-text {
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 0.625rem;
}
.detail-card .video-container .video-fallback .btn-open-browser {
  background-color: #1890ff;
  color: #fff;
  font-size: 0.875rem;
  padding: 0.3125rem 0.9375rem;
  border-radius: 0.25rem;
  margin-top: 0.625rem;
}
.detail-card .video-debug-info {
  margin-top: 0.625rem;
  padding: 0.625rem;
  background-color: #f8f8f8;
  border-radius: 0.25rem;
  font-size: 0.75rem;
}
.detail-card .video-debug-info .debug-title {
  font-weight: bold;
  margin-bottom: 0.3125rem;
  display: block;
}
.detail-card .video-debug-info .debug-text {
  color: #666;
  word-break: break-all;
  display: block;
  margin-bottom: 0.1875rem;
}
.detail-card .btn-view-order {
  margin-top: 0.9375rem;
  padding: 0.625rem 0;
  text-align: center;
  border-radius: 0.25rem;
  background-color: #1890ff;
  color: #fff;
  font-size: 0.875rem;
}
.action-buttons {
  display: flex;
  gap: 0.9375rem;
  margin-top: 1.5625rem;
  margin-bottom: 0.9375rem;
}
.action-buttons uni-button {
  flex: 1;
  margin: 0;
  height: 2.75rem;
  line-height: 2.75rem;
  font-size: 0.9375rem;
  border-radius: 0.25rem;
}
.action-buttons uni-button.btn-confirm {
  background-color: #1890ff;
  color: #fff;
}
.action-buttons uni-button.btn-reject {
  background-color: #fff;
  color: #f5222d;
  border: 0.03125rem solid #f5222d;
}
.status-tip {
  background-color: rgba(245, 34, 45, 0.1);
  padding: 0.625rem 0.9375rem;
  border-radius: 0.25rem;
  margin: 0.9375rem 0;
}
.status-tip uni-text {
  font-size: 0.875rem;
  color: #f5222d;
}