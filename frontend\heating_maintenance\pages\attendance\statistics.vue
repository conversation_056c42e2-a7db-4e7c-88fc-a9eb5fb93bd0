<template>
	<view class="statistics-container">
		<!-- 顶部筛选区域 -->
		<view class="filter-section">
			<view class="month-selector">
				<text class="current-month">{{ currentYear }}年{{ currentMonth }}月</text>
				<view class="month-arrows">
					<uni-icons type="left" size="20" color="#333" @click="prevMonth"></uni-icons>
					<uni-icons type="right" size="20" color="#333" @click="nextMonth"></uni-icons>
				</view>
			</view>
			<view class="staff-selector" @click="openStaffSelector">
				<text>{{ selectedStaff ? selectedStaff.name : '全部人员' }}</text>
				<uni-icons type="bottom" size="14" color="#666"></uni-icons>
			</view>
		</view>

		<!-- 统计概览卡片 -->
		<view class="statistics-cards">
			<view class="stat-card">
				<view class="stat-value">{{ statistics.normalDays }}</view>
				<view class="stat-label">正常出勤</view>
			</view>
			<view class="stat-card">
				<view class="stat-value">{{ statistics.lateDays }}</view>
				<view class="stat-label">迟到</view>
			</view>
			<view class="stat-card">
				<view class="stat-value">{{ statistics.earlyDays }}</view>
				<view class="stat-label">早退</view>
			</view>
			<view class="stat-card">
				<view class="stat-value">{{ statistics.absentDays }}</view>
				<view class="stat-label">缺勤</view>
			</view>
		</view>

		<!-- 日历区域 -->
		<view class="main-calendar">
			<view class="calendar-view">
				<view class="calendar-header">
					<view v-for="(day, index) in weekDays" :key="index" class="header-item">{{ day }}</view>
				</view>
				<view class="calendar-days">
					<view v-for="(day, index) in calendarDays" :key="index"
						class="calendar-day"
						:class="{
							'current-month': day.isCurrentMonth,
							'today': day.isToday,
							'normal': day.status === 'normal',
							'late': day.status === 'late',
							'early': day.status === 'early',
							'absent': day.status === 'absent'
						}"
						@click="viewDayDetail(day)">
						<text class="day-number">{{ day.day }}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 添加图表区域 -->
		<view class="charts-section">
			<view class="section-title">
				<text>考勤数据分析</text>
			</view>
			
			<view class="charts-container">
				<!-- 柱状图 -->
				<view class="chart-card">
					<view class="chart-title">月度考勤统计</view>
					<view class="chart-container">
						<canvas canvas-id="attendanceBarChart" style="width: 100%; height: 400rpx;"></canvas>
					</view>
				</view>
				
				<!-- 饼图 -->
				<view class="chart-card">
					<view class="chart-title">考勤类型分布</view>
					<view class="chart-container">
						<canvas canvas-id="attendancePieChart" style="width: 100%; height: 400rpx;"></canvas>
					</view>
				</view>
			</view>
		</view>

		<!-- 考勤详细记录 -->
		<view class="attendance-records">
			<view class="record-header">
				<text>考勤记录</text>
				<view class="header-actions">
					<text class="export-btn" @click="exportAttendanceData">导出统计</text>
					<text class="view-all" @click="viewAllRecords">查看全部</text>
				</view>
			</view>
			<view v-if="records.length > 0" class="record-list">
				<view v-for="(record, index) in records" :key="index" class="record-item">
					<view class="record-date">
						<text class="day">{{ record.date || formatDate(record.clockTime) }}</text>
						<text class="week">{{ record.week || formatWeek(record.clockTime) }}</text>
					</view>
					<view class="record-time">
						<view class="time-item">
							<text class="time-label">上班打卡</text>
							<text class="time-value" :class="{'abnormal': record.clockInStatus !== 'normal'}">
								{{ record.clockInTime || '未打卡' }}
							</text>
							<text v-if="record.clockInStatus === 'late'" class="status-tag late">迟到</text>
						</view>
						<view class="time-item">
							<text class="time-label">下班打卡</text>
							<text class="time-value" :class="{'abnormal': record.clockOutStatus !== 'normal'}">
								{{ record.clockOutTime || '未打卡' }}
							</text>
							<text v-if="record.clockOutStatus === 'early'" class="status-tag early">早退</text>
						</view>
					</view>
					<view class="record-location">
						<text class="location-label">打卡地点</text>
						<text class="location-value">{{ record.location || formatLocation(record) }}</text>
					</view>
				</view>
			</view>
			<view v-else class="empty-records">
				<image src="/static/images/empty.png" mode="aspectFit"></image>
				<text>暂无考勤记录</text>
			</view>
		</view>

		<!-- 人员选择器弹窗 -->
		<uni-popup ref="staffPopup" type="bottom">
			<view class="staff-popup">
				<view class="popup-header">
					<text>选择人员</text>
					<text @click="closeStaffSelector" class="close-btn">关闭</text>
				</view>
				<view class="search-box">
					<uni-icons type="search" size="16" color="#999"></uni-icons>
					<input type="text" v-model="searchKeyword" placeholder="搜索人员姓名" />
				</view>
				<scroll-view scroll-y="true" class="staff-list">
					<view class="staff-item" @click="selectStaff(null)">
						<text>全部人员</text>
						<uni-icons v-if="!selectedStaff" type="checkmarkempty" size="18" color="#007AFF"></uni-icons>
					</view>
					<view 
						v-for="(staff, index) in filteredStaffList" 
						:key="index" 
						class="staff-item"
						@click="selectStaff(staff)">
						<text>{{ staff.name }}</text>
						<uni-icons 
							v-if="selectedStaff && selectedStaff.id === staff.id" 
							type="checkmarkempty" 
							size="18" 
							color="#007AFF">
						</uni-icons>
					</view>
				</scroll-view>
			</view>
		</uni-popup>

		<!-- 日考勤详情弹窗 -->
		<uni-popup ref="dayDetailPopup" type="bottom">
			<view class="day-detail-popup">
				<view class="popup-header">
					<text>{{ selectedDate }} 考勤详情</text>
					<text class="close-btn" @click="closeDayDetailPopup">关闭</text>
				</view>
				
				<!-- 饼图统计 -->
				<canvas v-if="dayDetailData.length > 0" canvas-id="dayAttendanceChart" class="day-stats-chart" @touchstart="touchChart"></canvas>
				
				<!-- 状态统计 -->
				<view v-if="dayDetailData.length > 0" class="day-stats-summary">
					<view class="stat-item normal">
						<text class="stat-value">{{ normalCount }}</text>
						<text class="stat-label">正常</text>
					</view>
					<view class="stat-item late">
						<text class="stat-value">{{ lateCount }}</text>
						<text class="stat-label">迟到</text>
					</view>
					<view class="stat-item early">
						<text class="stat-value">{{ earlyCount }}</text>
						<text class="stat-label">早退</text>
					</view>
					<view class="stat-item absent">
						<text class="stat-value">{{ absentCount }}</text>
						<text class="stat-label">缺勤</text>
					</view>
				</view>
				
				<!-- 选中人员详情 -->
				<view v-if="dayDetailData.length > 0 && selectedStaffDetail" class="staff-detail-section">
					<view class="detail-item">
						<text class="detail-label">姓名</text>
						<text class="detail-value">{{ selectedStaffDetail.name }}</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">上班时间</text>
						<view class="detail-value-box">
							<text class="detail-value">{{ selectedStaffDetail.checkInTime || '无打卡' }}</text>
							<text v-if="selectedStaffDetail.isLate" class="status-tag late">迟到</text>
						</view>
					</view>
					<view class="detail-item">
						<text class="detail-label">下班时间</text>
						<view class="detail-value-box">
							<text class="detail-value">{{ selectedStaffDetail.checkOutTime || '无打卡' }}</text>
							<text v-if="selectedStaffDetail.isEarly" class="status-tag early">早退</text>
						</view>
					</view>
					<view class="detail-item">
						<text class="detail-label">工作时长</text>
						<text class="detail-value">{{ selectedStaffDetail.workHours || '0小时' }}</text>
					</view>
				</view>
				
				<!-- 人员列表 -->
				<view v-if="dayDetailData.length > 0" class="staff-list-section">
					<text class="section-title">人员考勤列表</text>
					
					<!-- 正常考勤人员 -->
					<view v-if="normalStaff.length > 0" class="staff-category">
						<view class="category-header normal">
							<text>正常考勤</text>
							<text>{{ normalStaff.length }}人</text>
						</view>
						<view class="staff-items">
							<view class="staff-item" v-for="(staff, index) in normalStaff" :key="index" @click="selectStaffDetail(staff)">
								<text class="staff-name">{{ staff.name }}</text>
								<text class="staff-time">{{ staff.checkInTime }} - {{ staff.checkOutTime }}</text>
							</view>
						</view>
					</view>
					
					<!-- 迟到人员 -->
					<view v-if="lateStaff.length > 0" class="staff-category">
						<view class="category-header late">
							<text>迟到人员</text>
							<text>{{ lateStaff.length }}人</text>
						</view>
						<view class="staff-items">
							<view class="staff-item" v-for="(staff, index) in lateStaff" :key="index" @click="selectStaffDetail(staff)">
								<text class="staff-name">{{ staff.name }}</text>
								<text class="staff-time">{{ staff.checkInTime }}</text>
							</view>
						</view>
					</view>
					
					<!-- 早退人员 -->
					<view v-if="earlyStaff.length > 0" class="staff-category">
						<view class="category-header early">
							<text>早退人员</text>
							<text>{{ earlyStaff.length }}人</text>
						</view>
						<view class="staff-items">
							<view class="staff-item" v-for="(staff, index) in earlyStaff" :key="index" @click="selectStaffDetail(staff)">
								<text class="staff-name">{{ staff.name }}</text>
								<text class="staff-time">{{ staff.checkOutTime }}</text>
							</view>
						</view>
					</view>
					
					<!-- 缺勤人员 -->
					<view v-if="absentStaff.length > 0" class="staff-category">
						<view class="category-header absent">
							<text>缺勤人员</text>
							<text>{{ absentStaff.length }}人</text>
						</view>
						<view class="staff-items">
							<view class="staff-item" v-for="(staff, index) in absentStaff" :key="index" @click="selectStaffDetail(staff)">
								<text class="staff-name">{{ staff.name }}</text>
								<text class="staff-time">无考勤记录</text>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 无数据提示 -->
				<view v-if="dayDetailData.length === 0" class="empty-day-detail">
					<image src="/static/images/empty.png" mode="aspectFit"></image>
					<text>暂无考勤记录</text>
				</view>
			</view>
		</uni-popup>

		<!-- 导出统计数据弹窗 -->
		<uni-popup ref="exportPopup" type="bottom">
			<view class="export-popup">
				<view class="popup-header">
					<text>导出考勤统计</text>
					<text @click="closeExportPopup" class="close-btn">关闭</text>
				</view>
				<view class="export-form">
					<view class="export-item">
						<text class="export-label">统计时间范围</text>
						<view class="export-date-range">
							<view class="date-select" @click="openDatePicker('start')">
								<text>{{ exportForm.startDate || '开始日期' }}</text>
								<uni-icons type="calendar" size="16" color="#999"></uni-icons>
							</view>
							<text class="range-separator">至</text>
							<view class="date-select" @click="openDatePicker('end')">
								<text>{{ exportForm.endDate || '结束日期' }}</text>
								<uni-icons type="calendar" size="16" color="#999"></uni-icons>
							</view>
						</view>
					</view>
					<view class="export-item">
						<text class="export-label">导出对象</text>
						<view class="export-radio-group">
							<view class="radio-item" @click="setExportType('all')">
								<view class="radio-btn" :class="{ 'active': exportForm.type === 'all' }"></view>
								<text>全部人员</text>
							</view>
							<view class="radio-item" @click="setExportType('selected')">
								<view class="radio-btn" :class="{ 'active': exportForm.type === 'selected' }"></view>
								<text>选定人员</text>
							</view>
						</view>
					</view>
					<view class="export-item" v-if="exportForm.type === 'selected'">
						<text class="export-label">选择人员</text>
						<view class="selected-staff" @click="openStaffMultiSelector">
							<text>已选择 {{ selectedExportStaff.length }} 人</text>
							<uni-icons type="right" size="16" color="#999"></uni-icons>
						</view>
					</view>
					<view class="export-item">
						<text class="export-label">导出格式</text>
						<view class="export-radio-group">
							<view class="radio-item" @click="exportForm.format = 'excel'">
								<view class="radio-btn" :class="{ 'active': exportForm.format === 'excel' }"></view>
								<text>Excel表格</text>
							</view>
							<view class="radio-item" @click="exportForm.format = 'pdf'">
								<view class="radio-btn" :class="{ 'active': exportForm.format === 'pdf' }"></view>
								<text>PDF文档</text>
							</view>
						</view>
					</view>
				</view>
				<view class="export-footer">
					<button class="cancel-btn" @click="closeExportPopup">取消</button>
					<button class="confirm-btn" @click="confirmExport">确认导出</button>
				</view>
			</view>
		</uni-popup>
		
		<!-- 多选人员弹窗 -->
		<uni-popup ref="multiStaffPopup" type="bottom">
			<view class="staff-popup">
				<view class="popup-header">
					<text>选择导出人员</text>
					<text @click="closeMultiStaffSelector" class="close-btn">确定</text>
				</view>
				<view class="search-box">
					<uni-icons type="search" size="16" color="#999"></uni-icons>
					<input type="text" v-model="multiSearchKeyword" placeholder="搜索人员姓名" />
				</view>
				<scroll-view scroll-y="true" class="staff-list">
					<view 
						v-for="(staff, index) in filteredMultiStaffList" 
						:key="index" 
						class="staff-item"
						@click="toggleExportStaff(staff)">
						<text>{{ staff.name }}</text>
						<uni-icons 
							v-if="isStaffSelected(staff)" 
							type="checkbox-filled" 
							size="18" 
							color="#007AFF">
						</uni-icons>
						<uni-icons 
							v-else
							type="checkbox" 
							size="18" 
							color="#999">
						</uni-icons>
					</view>
				</scroll-view>
			</view>
		</uni-popup>
		
		<!-- 日期选择器 -->
		<uni-calendar 
			ref="calendar"
			:insert="false"
			@confirm="selectDate"
		></uni-calendar>
	</view>
</template>

<script>
import { attendanceApi, userApi } from '@/utils/api.js';

export default {
	data() {
		return {
			currentYear: new Date().getFullYear(),
			currentMonth: new Date().getMonth() + 1,
			weekDays: ['日', '一', '二', '三', '四', '五', '六'],
			calendarDays: [],
			selectedStaff: null,
			searchKeyword: '',
			staffList: [],
			isLoading: false,
			statistics: {
				normalDays: 0,
				lateDays: 0,
				earlyDays: 0,
				absentDays: 0
			},
			records: [],
			selectedDay: null,
			dayDetailRecord: null,
			exportForm: {
				startDate: '',
				endDate: '',
				type: 'all',
				format: 'excel'
			},
			selectedExportStaff: [],
			multiSearchKeyword: '',
			currentPickerType: 'start',
			exportLoading: false,
			dayRecords: [],
			dayStats: {
				normal: 0,
				late: 0,
				early: 0,
				absent: 0
			},
			selectedDate: '',
			dayDetailPopupVisible: false,
			dayDetailData: [],
			selectedStaffDetail: null
		};
	},
	computed: {
		filteredStaffList() {
			if (!this.searchKeyword) return this.staffList;
			
			return this.staffList.filter(staff => 
				staff.name.includes(this.searchKeyword) || 
				staff.department?.includes(this.searchKeyword)
			);
		},
		filteredMultiStaffList() {
			if (!this.multiSearchKeyword) return this.staffList;
			
			return this.staffList.filter(staff => 
				staff.name.includes(this.multiSearchKeyword) || 
				staff.department?.includes(this.multiSearchKeyword)
			);
		},
		normalCount() {
			return this.dayDetailData.filter(item => !item.isLate && !item.isEarly && !item.isAbsent).length;
		},
		lateCount() {
			return this.dayDetailData.filter(item => item.isLate).length;
		},
		earlyCount() {
			return this.dayDetailData.filter(item => item.isEarly).length;
		},
		absentCount() {
			return this.dayDetailData.filter(item => item.isAbsent).length;
		},
		normalStaff() {
			return this.dayDetailData.filter(item => !item.isLate && !item.isEarly && !item.isAbsent);
		},
		lateStaff() {
			return this.dayDetailData.filter(item => item.isLate);
		},
		earlyStaff() {
			return this.dayDetailData.filter(item => item.isEarly);
		},
		absentStaff() {
			return this.dayDetailData.filter(item => item.isAbsent);
		}
	},
	onLoad() {
		this.generateCalendar();
		this.loadStaffList();
		this.loadAttendanceData();
	},
	methods: {
		// 生成日历数据
		generateCalendar() {
			const year = this.currentYear;
			const month = this.currentMonth;
			
			// 获取当月第一天是星期几
			const firstDay = new Date(year, month - 1, 1).getDay();
			// 获取当月的总天数
			const daysInMonth = new Date(year, month, 0).getDate();
			// 获取上个月的总天数
			const daysInPrevMonth = new Date(year, month - 1, 0).getDate();
			
			const days = [];
			
			// 上个月的日期
			for (let i = firstDay - 1; i >= 0; i--) {
				const prevMonth = month === 1 ? 12 : month - 1;
				const prevYear = month === 1 ? year - 1 : year;
				days.push({
					day: daysInPrevMonth - i,
					month: prevMonth,
					year: prevYear,
					isCurrentMonth: false,
					isToday: false,
					status: null
				});
			}
			
			// 当月的日期
			const today = new Date();
			const todayDate = today.getDate();
			const todayMonth = today.getMonth() + 1;
			const todayYear = today.getFullYear();
			
			for (let i = 1; i <= daysInMonth; i++) {
				days.push({
					day: i,
					month,
					year,
					isCurrentMonth: true,
					isToday: i === todayDate && month === todayMonth && year === todayYear,
					status: null // 初始无状态，将在加载数据后更新
				});
			}
			
			// 下个月的日期
			const remainingDays = 42 - days.length; // 6行7列 = 42格
			for (let i = 1; i <= remainingDays; i++) {
				const nextMonth = month === 12 ? 1 : month + 1;
				const nextYear = month === 12 ? year + 1 : year;
				days.push({
					day: i,
					month: nextMonth,
					year: nextYear,
					isCurrentMonth: false,
					isToday: false,
					status: null
				});
			}
			
			this.calendarDays = days;
		},
		
		// 格式化日期为MM-DD格式
		formatDate(dateString) {
			if (!dateString) return '';
			const date = new Date(dateString);
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			return `${month}-${day}`;
		},
		
		// 格式化星期为"周X"格式
		formatWeek(dateString) {
			if (!dateString) return '';
			const date = new Date(dateString);
			const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
			return weekDays[date.getDay()];
		},
		
		// 格式化位置信息
		formatLocation(record) {
			if (!record || !record.latitude || !record.longitude) return '未知位置';
			return `${record.latitude.toFixed(6)}, ${record.longitude.toFixed(6)}`;
		},
		
		// 加载员工列表
		loadStaffList() {
			attendanceApi.getAllStaff().then(res => {
				if (res.code === 200 && res.data) {
					// 根据接口返回的嵌套数据结构进行解析
					if (res.data.data && Array.isArray(res.data.data.staffList)) {
						this.staffList = res.data.data.staffList;
					} else if (Array.isArray(res.data.staffList)) {
						this.staffList = res.data.staffList;
					} else if (Array.isArray(res.data)) {
						this.staffList = res.data;
					} else {
						console.warn('员工列表数据格式异常:', res);
						// 降级使用用户API
						this.loadInspectorList();
					}
				} else {
					// 降级使用用户API
					this.loadInspectorList();
				}
			}).catch(err => {
				console.error('获取人员列表失败:', err);
				// 降级方案：使用用户API获取人员列表
				this.loadInspectorList();
			});
		},
		
		// 从用户API加载检查员列表（降级方案）
		loadInspectorList() {
			userApi.getInspectorList().then(userRes => {
				if (userRes.code === 200 && userRes.data) {
					if (Array.isArray(userRes.data)) {
						this.staffList = userRes.data;
					} else if (userRes.data.data && Array.isArray(userRes.data.data.staffList)) {
						this.staffList = userRes.data.data.staffList;
					} else {
						console.warn('无法解析人员列表数据');
						this.staffList = [];
					}
				}
			}).catch(userErr => {
				console.error('获取人员列表失败:', userErr);
				uni.showToast({
					title: '获取人员列表失败',
					icon: 'none'
				});
			});
		},
		
		// 上个月
		prevMonth() {
			if (this.currentMonth === 1) {
				this.currentYear -= 1;
				this.currentMonth = 12;
			} else {
				this.currentMonth -= 1;
			}
			this.generateCalendar();
			this.loadAttendanceData();
		},
		
		// 下个月
		nextMonth() {
			if (this.currentMonth === 12) {
				this.currentYear += 1;
				this.currentMonth = 1;
			} else {
				this.currentMonth += 1;
			}
			this.generateCalendar();
			this.loadAttendanceData();
		},
		
		// 打开人员选择器
		openStaffSelector() {
			this.$refs.staffPopup.open();
		},
		
		// 关闭人员选择器
		closeStaffSelector() {
			this.$refs.staffPopup.close();
		},
		
		// 选择人员
		selectStaff(staff) {
			this.selectedStaff = staff;
			this.closeStaffSelector();
			this.loadAttendanceData();
		},
		
		// 加载考勤数据
		loadAttendanceData() {
			this.isLoading = true;
			uni.showLoading({
				title: '加载数据中...'
			});
			
			// 准备查询参数
			const params = {
				year: this.currentYear,
				month: this.currentMonth,
				day: this.selectedDay ? this.selectedDay.day : undefined
			};
			
			// 如果选择了特定员工，添加用户ID参数
			if (this.selectedStaff) {
				params.userId = this.selectedStaff.id;
			}
			
			// 获取考勤统计数据
			attendanceApi.getStats(params).then(res => {
				if (res.code === 200 && res.data) {
					// 检查不同的数据结构
					let summaryData = res.data;
					
					// 如果数据在res.data.data中
					if (res.data.data && typeof res.data.data === 'object') {
						summaryData = res.data.data;
					}
					
					// 获取统计摘要数据
					const summary = summaryData.summary || {};
					
					// 直接使用API返回的统计值，而不是进行计算
					this.statistics = {
						normalDays: summary.normal || 0,
						lateDays: summary.late || 0,
						earlyDays: summary.early || 0,
						absentDays: summary.absent || 0
					};
					
					// 加载完成后重新初始化图表
					setTimeout(() => {
						this.initBarChart();
						this.initPieChart();
					}, 500);
					
					// 如果有图表数据，更新日历状态
					if (summaryData.chart) {
						this.updateCalendarStatus(summaryData.chart);
					}
				}
				
				// 获取考勤记录列表
				return attendanceApi.getRecords(params);
			}).then(res => {
				if (res.code === 200) {
					let recordsData = res.data;
					
					// 检查不同的数据结构
					if (res.data && res.data.data) {
						recordsData = res.data.data;
					}
					
					// 更新考勤记录，处理多种可能的数据结构
					if (recordsData && recordsData.records) {
						this.records = this.processAttendanceRecords(recordsData.records);
					} else if (recordsData && recordsData.list) {
						this.records = this.processAttendanceRecords(recordsData.list);
					} else if (Array.isArray(recordsData)) {
						this.records = this.processAttendanceRecords(recordsData);
					} else if (recordsData && typeof recordsData === 'object' && !Array.isArray(recordsData)) {
						// 可能是日期-记录映射对象
						this.records = this.processAttendanceRecords(recordsData);
					} else {
						console.warn('未能识别的考勤记录数据格式:', res.data);
						this.records = [];
					}
				} else {
					this.records = [];
				}
			}).catch(err => {
				console.error('获取考勤数据失败:', err);
				uni.showToast({
					title: '获取考勤数据失败',
					icon: 'none'
				});
			}).finally(() => {
				this.isLoading = false;
				uni.hideLoading();
			});
		},
		
		// 处理考勤记录数据
		processAttendanceRecords(records) {
			if (!Array.isArray(records) || records.length === 0) {
				return [];
			}
			
			// 如果是对象结构的日期-记录映射，将其转为数组
			if (!Array.isArray(records) && typeof records === 'object') {
				const recordArray = [];
				for (const date in records) {
					if (records.hasOwnProperty(date)) {
						// 为每个日期创建一条记录
						const dateObj = new Date(date);
						const month = String(dateObj.getMonth() + 1).padStart(2, '0');
						const day = String(dateObj.getDate()).padStart(2, '0');
						const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
						const week = weekDays[dateObj.getDay()];
						
						const record = records[date];
						recordArray.push({
							date: date,
							day: `${month}-${day}`,
							week: week,
							clockInTime: record.clockInTime || '未打卡',
							clockInStatus: record.clockInStatus || 'normal',
							clockOutTime: record.clockOutTime || '未打卡',
							clockOutStatus: record.clockOutStatus || 'normal',
							location: record.location || '未知位置'
						});
					}
				}
				return recordArray.slice(0, 5); // 仅显示最近5条记录
			}

			// 检查记录格式并适配
			// 后端现在返回合并后的记录，包含clockInTime和clockOutTime字段
			const firstRecord = records[0];
			
			// 如果记录已包含上下班打卡时间字段，直接使用
			if (firstRecord.clockInTime !== undefined || firstRecord.clockOutTime !== undefined) {
				return records.map(item => {
					return {
						date: item.date || this.formatDate(item.clockTime),
						week: item.week || this.formatWeek(item.clockTime),
						clockInTime: item.clockInTime || '未打卡',
						clockOutTime: item.clockOutTime || '未打卡',
						clockInStatus: item.clockInStatus || 'normal',
						clockOutStatus: item.clockOutStatus || 'normal',
						location: item.location || this.formatLocation(item)
					};
				}).slice(0, 5); // 仅显示最近5条记录
			}
			
			// 处理旧格式数据（向后兼容）
			// 如果是旧格式，包含clockType和clockTime
			else if (firstRecord.clockType && firstRecord.clockTime) {
				// 按日期分组
				const recordsByDate = {};
				
				records.forEach(record => {
					const date = new Date(record.clockTime);
					const dateStr = date.toISOString().split('T')[0]; // YYYY-MM-DD
					
					if (!recordsByDate[dateStr]) {
						recordsByDate[dateStr] = {
							date: this.formatDate(record.clockTime),
							week: this.formatWeek(record.clockTime),
							clockInTime: '',
							clockOutTime: '',
							clockInStatus: 'normal',
							clockOutStatus: 'normal',
							location: record.location || this.formatLocation(record)
						};
					}
					
					// 根据打卡类型设置上下班时间
					if (record.clockType === 'checkin') {
						recordsByDate[dateStr].clockInTime = this.formatTime(record.clockTime);
						recordsByDate[dateStr].clockInStatus = record.status;
					} else if (record.clockType === 'checkout') {
						recordsByDate[dateStr].clockOutTime = this.formatTime(record.clockTime);
						recordsByDate[dateStr].clockOutStatus = record.status;
					}
				});
				
				// 转换为数组并按日期降序排序
				return Object.values(recordsByDate)
					.sort((a, b) => new Date(b.date) - new Date(a.date))
					.slice(0, 5);
			}
			
			// 其他格式，尝试最基本的处理
			else {
				console.warn('未知的考勤记录数据格式，尝试基本处理:', firstRecord);
				return records.map(record => {
					return {
						date: record.date || this.formatDate(record.clockTime || record.createTime),
						week: record.week || this.formatWeek(record.clockTime || record.createTime),
						clockInTime: record.clockInTime || '未打卡',
						clockOutTime: record.clockOutTime || '未打卡',
						clockInStatus: record.clockInStatus || 'normal',
						clockOutStatus: record.clockOutStatus || 'normal',
						location: record.location || this.formatLocation(record)
					};
				}).slice(0, 5);
			}
		},
		
		// 更新日历状态
		updateCalendarStatus(chartData) {
			// 从图表数据中提取每天的状态
			if (!chartData || !chartData.xaxis) return;
			
			// 如果有dailyStatus数据，优先使用它
			if (chartData.dailyStatus && Array.isArray(chartData.dailyStatus)) {
				this.calendarDays.forEach(day => {
					if (day.isCurrentMonth) {
						const dayIndex = day.day - 1; // 数组索引从0开始
						if (dayIndex >= 0 && dayIndex < chartData.dailyStatus.length) {
							// 根据dailyStatus设置状态
							day.status = chartData.dailyStatus[dayIndex] || null;
						}
					}
				});
				return;
			}
			
			// 回退到老的处理逻辑
			if (!chartData.series) return;
			
			const attendanceSeries = chartData.series.find(s => s.name === '出勤率' || s.name === 'attendance');
			const lateSeries = chartData.series.find(s => s.name === '迟到率' || s.name === 'late');
			const absentSeries = chartData.series.find(s => s.name === '缺勤率' || s.name === 'absent');
			const earlySeries = chartData.series.find(s => s.name === '早退率' || s.name === 'early');
			
			// 确保必要的数据系列存在
			if (!attendanceSeries && !lateSeries && !absentSeries && !earlySeries) return;
			
			// 更新日历天的状态
			this.calendarDays.forEach(day => {
				if (day.isCurrentMonth) {
					const dayIndex = day.day - 1; // 数组索引从0开始
					
					// 检查日期索引是否有效
					if (dayIndex >= 0 && dayIndex < chartData.xaxis.length) {
						// 优先级: 缺勤 > 迟到 > 早退 > 正常出勤
						if (absentSeries && absentSeries.data[dayIndex] > 0) {
							day.status = 'absent';
						} else if (lateSeries && lateSeries.data[dayIndex] > 0) {
							day.status = 'late';
						} else if (earlySeries && earlySeries.data[dayIndex] > 0) {
							day.status = 'early';
						} else if (attendanceSeries && attendanceSeries.data[dayIndex] > 0) {
							day.status = 'normal';
						} else {
							day.status = null; // 无数据
						}
					}
				}
			});
		},
		
		// 格式化时间
		formatTime(timeString) {
			if (!timeString) return '未打卡';
			
			try {
				const date = new Date(timeString);
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				return `${hours}:${minutes}`;
			} catch (e) {
				return timeString;
			}
		},
		
		// 查看某天的详细考勤记录
		viewDayDetail(day) {
			if (!day.isCurrentMonth) return;
			
			this.selectedDay = day;
			this.selectedDate = `${day.year}年${day.month}月${day.day}日`;
			
			// 查询当天的详细考勤记录
			const params = {
				year: day.year,
				month: day.month,
				day: day.day
				// 不传userId表示获取所有人员
			};
			
			this.isLoading = true;
			uni.showLoading({
				title: '加载数据中...'
			});
			
			// 使用getRecords而不是getDayAttendance
			attendanceApi.getRecords(params).then(res => {
				if (res.code === 200 && res.data) {
					// 处理返回数据
					let records = [];
					
					// 解析API返回的数据结构
					if (res.data.records) {
						records = res.data.records;
					} else if (res.data.list) {
						records = res.data.list;
					} else if (Array.isArray(res.data)) {
						records = res.data;
					} else if (res.data.data && (res.data.data.records || res.data.data.list)) {
						records = res.data.data.records || res.data.data.list;
					} else if (typeof res.data === 'object' && !Array.isArray(res.data)) {
                        // 处理可能是用户ID为key的对象格式
                        const recordsData = res.data;
                        records = [];
                        for (const key in recordsData) {
                            if (recordsData.hasOwnProperty(key)) {
                                // 添加用户ID和记录
                                const record = recordsData[key];
                                if (record && typeof record === 'object') {
                                    // 确保record是对象且有userId和name属性
                                    record.userId = record.userId || key;
                                    record.userName = record.userName || record.name || '未知';
                                    records.push(record);
                                }
                            }
                        }
                    }
					
					// 保存当天考勤记录
					this.dayRecords = records;
					
					// 将打卡记录按人员分组
					const userClockMap = {};
					
					records.forEach(record => {
						// 获取用户信息，优先使用user对象中的信息
						const userId = record.user?.id || record.userId || record.id || '';
						const userName = record.user?.name || record.userName || record.staffName || record.name || '未知';
						
						if (!userClockMap[userId]) {
							userClockMap[userId] = {
								id: userId,
								name: userName,
								department: record.user?.department || record.department || '',
								checkInTime: '',
								checkOutTime: '',
								isLate: false,
								isEarly: false,
								isAbsent: false,
								workHours: '--'
							};
						}
						
						// 根据打卡类型设置上下班时间
						if (record.clockType === 'checkin') {
							userClockMap[userId].checkInTime = this.formatTime(record.clockTime);
							userClockMap[userId].isLate = record.status === 'late';
						} else if (record.clockType === 'checkout') {
							userClockMap[userId].checkOutTime = this.formatTime(record.clockTime);
							userClockMap[userId].isEarly = record.status === 'early';
						} else if (record.checkInTime || record.checkOutTime) {
                            // 处理已经分离的上下班时间记录
                            if (record.checkInTime) {
                                userClockMap[userId].checkInTime = this.formatTime(record.checkInTime);
                                userClockMap[userId].isLate = record.clockInStatus === 'late';
                            }
                            if (record.checkOutTime) {
                                userClockMap[userId].checkOutTime = this.formatTime(record.checkOutTime);
                                userClockMap[userId].isEarly = record.clockOutStatus === 'early';
                            }
                        }
						
						// 设置缺勤状态
						if (record.status === 'absent' || (record.clockInStatus === 'absent' && record.clockOutStatus === 'absent')) {
							userClockMap[userId].isAbsent = true;
						}
					});
					
					// 处理工作时长
					Object.values(userClockMap).forEach(user => {
						if (user.checkInTime && user.checkOutTime && 
                           user.checkInTime !== '未打卡' && user.checkOutTime !== '未打卡') {
							// 找到对应的记录计算工作时长
							const checkInRecord = records.find(r => 
								(r.user?.id === user.id || r.userId === user.id) && r.clockType === 'checkin'
							);
							const checkOutRecord = records.find(r => 
								(r.user?.id === user.id || r.userId === user.id) && r.clockType === 'checkout'
							);
							
							if (checkInRecord && checkOutRecord) {
								user.workHours = this.calculateWorkHours(checkInRecord.clockTime, checkOutRecord.clockTime);
							} else if (records.find(r => r.userId === user.id && r.checkInTime && r.checkOutTime)) {
                                // 直接使用记录中的时间
                                const record = records.find(r => r.userId === user.id);
                                user.workHours = this.calculateWorkHours(record.checkInTime, record.checkOutTime);
                            }
						}
					});
					
					// 检查缺勤员工（列表中不存在的人员）
					// 如果有所有员工列表信息，可以添加缺勤员工
					if (this.staffList && this.staffList.length > 0) {
                        this.staffList.forEach(staff => {
                            const userId = staff.id;
                            // 如果员工不在打卡记录中，标记为缺勤
                            if (userId && !userClockMap[userId]) {
                                userClockMap[userId] = {
                                    id: userId,
                                    name: staff.name || '未知',
                                    department: staff.department || '',
                                    checkInTime: '',
                                    checkOutTime: '',
                                    isLate: false,
                                    isEarly: false,
                                    isAbsent: true,
                                    workHours: '--'
                                };
                            }
                        });
                    }
					
					// 转换为数组
					this.dayDetailData = Object.values(userClockMap);
					
					// 统计各状态人数
					this.dayStats = {
						normal: this.dayDetailData.filter(item => !item.isLate && !item.isEarly && !item.isAbsent).length,
						late: this.dayDetailData.filter(item => item.isLate).length,
						early: this.dayDetailData.filter(item => item.isEarly).length,
						absent: this.dayDetailData.filter(item => item.isAbsent).length
					};
					
					// 渲染当天考勤饼图
					setTimeout(() => {
						this.renderDayPieChart();
					}, 300);
					
					// 如果是查看单个员工的详情
					if (this.selectedStaff) {
						// 查找当前选中员工的记录
						const staffRecord = this.dayDetailData.find(r => 
							r.id == this.selectedStaff.id
						);
						
						if (staffRecord) {
							this.selectedStaffDetail = staffRecord;
						} else {
							this.selectedStaffDetail = null;
						}
					} else {
						// 不显示具体员工详情
						this.selectedStaffDetail = null;
					}
				} else {
					this.dayRecords = [];
					this.dayDetailData = [];
					this.dayStats = {
						normal: 0,
						late: 0,
						early: 0,
						absent: 0
					};
					this.selectedStaffDetail = null;
				}
				
				this.$refs.dayDetailPopup.open();
			}).catch(err => {
				console.error('获取考勤详情失败:', err);
				
				this.dayRecords = [];
				this.dayDetailData = [];
				this.dayStats = {
					normal: 0,
					late: 0,
					early: 0,
					absent: 0
				};
				this.selectedStaffDetail = null;
				this.$refs.dayDetailPopup.open();
			}).finally(() => {
				this.isLoading = false;
				uni.hideLoading();
			});
		},
		
		// 渲染当天考勤饼图
		renderDayPieChart() {
			if (this.dayDetailData.length === 0) return;
			
			const ctx = uni.createCanvasContext('dayAttendanceChart');
			
			// 获取设备信息和屏幕宽度
			const sysInfo = uni.getSystemInfoSync();
			const screenWidth = sysInfo.windowWidth;
			
			// 饼图数据
			const pieData = [
				{ name: '正常', value: this.normalCount, color: '#4cd964' },
				{ name: '迟到', value: this.lateCount, color: '#f0ad4e' },
				{ name: '早退', value: this.earlyCount, color: '#f56c6c' },
				{ name: '缺勤', value: this.absentCount, color: '#dd524d' }
			];
			
			// 过滤掉数值为0的数据
			const filteredData = pieData.filter(item => item.value > 0);
			
			if (filteredData.length === 0) return;
			
			// 调整画布尺寸和位置，确保饼图完全显示
			const canvasWidth = screenWidth - 60; // 减去padding
			const canvasHeight = 240; // 固定高度
			const centerX = canvasWidth / 2;
			const centerY = canvasHeight / 2;
			// 使用较小的半径以确保图例有足够空间
			const radius = Math.min(centerX, centerY) * 0.6;
			
			// 计算总数
			const total = filteredData.reduce((sum, item) => sum + item.value, 0);
			
			// 清空画布
			ctx.clearRect(0, 0, canvasWidth, canvasHeight);
			
			// 绘制饼图
			let startAngle = 0;
			filteredData.forEach(item => {
				const sliceAngle = (item.value / total) * 2 * Math.PI;
				
				ctx.beginPath();
				ctx.moveTo(centerX, centerY);
				ctx.arc(centerX, centerY, radius, startAngle, startAngle + sliceAngle);
				ctx.closePath();
				
				ctx.setFillStyle(item.color);
				ctx.fill();
				
				startAngle += sliceAngle;
			});
			
			// 为每个数据项添加图例，使用右侧布局
			let legendY = 40;
			const legendSpacing = 30;
			
			filteredData.forEach(item => {
				// 绘制小色块
				ctx.setFillStyle(item.color);
				ctx.fillRect(canvasWidth - 100, legendY - 8, 16, 16);
				
				// 绘制文字
				ctx.setFillStyle('#333');
				ctx.setFontSize(12);
				ctx.setTextAlign('right');
				ctx.fillText(`${item.name}: ${item.value}`, canvasWidth - 10, legendY);
				
				legendY += legendSpacing;
			});
			
			// 中心白色圆
			ctx.beginPath();
			ctx.arc(centerX, centerY, radius * 0.5, 0, 2 * Math.PI);
			ctx.setFillStyle('#ffffff');
			ctx.fill();
			
			// 中心文字
			ctx.setFillStyle('#333333');
			ctx.setFontSize(14);
			ctx.setTextAlign('center');
			ctx.fillText('考勤分布', centerX, centerY - 7);
			ctx.setFontSize(16);
			ctx.fillText(total + '人', centerX, centerY + 15);
			
			ctx.draw();
		},
		
		// 计算工作时长
		calculateWorkHours(clockInTime, clockOutTime) {
			if (!clockInTime || !clockOutTime) return '--';
			
			try {
				const clockIn = new Date(clockInTime);
				const clockOut = new Date(clockOutTime);
				
				const diffMs = clockOut - clockIn;
				if (diffMs <= 0) return '--';
				
				const hours = Math.floor(diffMs / 3600000);
				const minutes = Math.floor((diffMs % 3600000) / 60000);
				
				return `${hours}小时${minutes}分钟`;
			} catch (e) {
				return '--';
			}
		},
		
		// 关闭日详情弹窗
		closeDayDetailPopup() {
			this.$refs.dayDetailPopup.close();
			this.dayDetailPopupVisible = false;
			this.selectedStaffDetail = null;
		},
		
		// 查看全部记录
		viewAllRecords() {
			uni.navigateTo({
				url: '/pages/attendance/all-records'
			});
		},
		
		// 预览图片
		previewImage(current, urls) {
			uni.previewImage({
				current,
				urls
			});
		},
		
		// 导出考勤数据
		exportAttendanceData() {
			// 设置默认日期范围为当前月份第一天到最后一天
			const year = this.currentYear;
			const month = this.currentMonth;
			const firstDay = new Date(year, month - 1, 1);
			const lastDay = new Date(year, month, 0);
			
			// 格式化日期为 YYYY-MM-DD
			const formatDate = (date) => {
				const y = date.getFullYear();
				const m = String(date.getMonth() + 1).padStart(2, '0');
				const d = String(date.getDate()).padStart(2, '0');
				return `${y}-${m}-${d}`;
			};
			
			this.exportForm.startDate = formatDate(firstDay);
			this.exportForm.endDate = formatDate(lastDay);
			this.exportForm.type = 'all';
			this.exportForm.format = 'excel';
			this.selectedExportStaff = [];
			
			this.$refs.exportPopup.open();
		},
		
		// 关闭导出弹窗
		closeExportPopup() {
			this.$refs.exportPopup.close();
		},
		
		// 设置导出类型
		setExportType(type) {
			this.exportForm.type = type;
			if (type === 'all') {
				this.selectedExportStaff = [];
			}
		},
		
		// 打开日期选择器
		openDatePicker(type) {
			this.currentPickerType = type;
			this.$refs.calendar.open();
		},
		
		// 日期选择回调
		selectDate(e) {
			const dateStr = e.fulldate;
			if (this.currentPickerType === 'start') {
				this.exportForm.startDate = dateStr;
				
				// 如果开始日期晚于结束日期，更新结束日期
				if (this.exportForm.endDate && new Date(dateStr) > new Date(this.exportForm.endDate)) {
					this.exportForm.endDate = dateStr;
				}
			} else {
				this.exportForm.endDate = dateStr;
				
				// 如果结束日期早于开始日期，更新开始日期
				if (this.exportForm.startDate && new Date(dateStr) < new Date(this.exportForm.startDate)) {
					this.exportForm.startDate = dateStr;
				}
			}
		},
		
		// 打开多选人员选择器
		openStaffMultiSelector() {
			this.$refs.multiStaffPopup.open();
		},
		
		// 关闭多选人员选择器
		closeMultiStaffSelector() {
			this.$refs.multiStaffPopup.close();
		},
		
		// 切换选择导出人员
		toggleExportStaff(staff) {
			const index = this.selectedExportStaff.findIndex(item => item.id === staff.id);
			if (index >= 0) {
				this.selectedExportStaff.splice(index, 1);
			} else {
				this.selectedExportStaff.push(staff);
			}
		},
		
		// 检查员工是否被选中
		isStaffSelected(staff) {
			return this.selectedExportStaff.some(item => item.id === staff.id);
		},
		
		// 确认导出考勤数据
		confirmExport() {
			// 验证表单
			if (!this.exportForm.startDate || !this.exportForm.endDate) {
				uni.showToast({
					title: '请选择完整的时间范围',
					icon: 'none'
				});
				return;
			}
			
			if (this.exportForm.type === 'selected' && this.selectedExportStaff.length === 0) {
				uni.showToast({
					title: '请至少选择一名员工',
					icon: 'none'
				});
				return;
			}
			
			// 防止重复点击
			if (this.exportLoading) return;
			this.exportLoading = true;
			
			// 显示加载提示
			uni.showLoading({
				title: '正在导出数据...'
			});
			
			// 准备请求参数
			const params = {
				startDate: this.exportForm.startDate,
				endDate: this.exportForm.endDate,
				format: this.exportForm.format
			};
			
			// 如果是选定人员，添加用户ID列表
			if (this.exportForm.type === 'selected') {
				params.userIds = this.selectedExportStaff.map(staff => staff.id);
			}
			
			// 调用导出API
			attendanceApi.exportAttendance(params).then(res => {
				if (res.code === 200 && res.data) {
					// 成功获取到导出文件URL
					const fileUrl = res.data.fileUrl || res.data;
					
					// 下载文件
					this.downloadFile(fileUrl);
					
					// 关闭弹窗
					this.closeExportPopup();
					
					uni.showToast({
						title: '导出成功',
						icon: 'success'
					});
				} else {
					throw new Error(res.message || '导出失败');
				}
			}).catch(err => {
				console.error('导出考勤数据失败:', err);
				uni.showToast({
					title: err.message || '导出失败，请稍后重试',
					icon: 'none'
				});
			}).finally(() => {
				uni.hideLoading();
				this.exportLoading = false;
			});
		},
		
		// 下载文件
		downloadFile(url) {
			// 检查平台
			// #ifdef APP-PLUS
			// APP端下载文件
			const downloadTask = uni.downloadFile({
				url: url,
				success: (res) => {
					if (res.statusCode === 200) {
						const filePath = res.tempFilePath;
						// 打开文件
						uni.openDocument({
							filePath: filePath,
							showMenu: true,
							success: () => {
								console.log('打开文档成功');
							},
							fail: (err) => {
								console.error('打开文档失败', err);
								uni.showToast({
									title: '无法打开文件',
									icon: 'none'
								});
							}
						});
					}
				},
				fail: (err) => {
					console.error('下载文件失败', err);
					uni.showToast({
						title: '下载文件失败',
						icon: 'none'
					});
				}
			});
			
			// 监听下载进度
			downloadTask.onProgressUpdate((res) => {
				console.log('下载进度:', res.progress);
			});
			// #endif
			
			// #ifdef H5
			// H5端直接打开链接
			window.open(url);
			// #endif
			
			// #ifdef MP-WEIXIN || MP-ALIPAY
			// 小程序端使用保存文件到本地
			uni.showLoading({
				title: '正在下载...'
			});
			
			uni.downloadFile({
				url: url,
				success: (res) => {
					if (res.statusCode === 200) {
						const tempFilePath = res.tempFilePath;
						// 保存文件到本地
						uni.saveFile({
							tempFilePath: tempFilePath,
							success: (saveRes) => {
								uni.hideLoading();
								const savedFilePath = saveRes.savedFilePath;
								
								uni.showModal({
									title: '导出成功',
									content: '文件已保存到本地，是否查看？',
									success: (res) => {
										if (res.confirm) {
											// 打开文件
											uni.openDocument({
												filePath: savedFilePath,
												showMenu: true,
												success: () => {
													console.log('打开文档成功');
												},
												fail: (err) => {
													console.error('打开文档失败', err);
													uni.showToast({
														title: '无法打开该类型文件',
														icon: 'none'
													});
												}
											});
										}
									}
								});
							},
							fail: (err) => {
								uni.hideLoading();
								console.error('保存文件失败', err);
								uni.showToast({
									title: '保存文件失败',
									icon: 'none'
								});
							}
						});
					}
				},
				fail: (err) => {
					uni.hideLoading();
					console.error('下载文件失败', err);
					uni.showToast({
						title: '下载文件失败',
						icon: 'none'
					});
				}
			});
			// #endif
		},
		// 页面准备好后的回调
		onReady() {
			// 页面加载完成后初始化图表
			setTimeout(() => {
				this.initBarChart();
				this.initPieChart();
			}, 500);
		},
		// 初始化柱状图
		initBarChart() {
			// 获取设备信息
			const sysInfo = uni.getSystemInfoSync();
			// 按照设备像素比计算canvas大小
			const pixelRatio = sysInfo.pixelRatio || 2;
			const canvasWidth = sysInfo.windowWidth * 0.9; // 屏幕宽度的90%
			const canvasHeight = 400 * (sysInfo.windowWidth / 750); // 根据rpx换算为px
			
			// 使用Canvas绘制简单的柱状图
			const ctx = uni.createCanvasContext('attendanceBarChart', this);
			
			// 设置画布背景
			ctx.setFillStyle('#FFFFFF');
			ctx.fillRect(0, 0, canvasWidth, canvasHeight);
			
			// 图表数据
			const data = [
				{ label: '正常', value: this.statistics.normalDays, color: '#4cd964' },
				{ label: '迟到', value: this.statistics.lateDays, color: '#f0ad4e' },
				{ label: '早退', value: this.statistics.earlyDays, color: '#5bc0de' },
				{ label: '缺勤', value: this.statistics.absentDays, color: '#dd524d' }
			];
			
			// 计算最大值
			const maxValue = Math.max(...data.map(item => item.value), 1);
			
			// 图表布局参数
			const padding = { left: 40, right: 20, top: 20, bottom: 60 };
			const chartWidth = canvasWidth - padding.left - padding.right;
			const chartHeight = canvasHeight - padding.top - padding.bottom;
			const barWidth = chartWidth / data.length / 1.5; // 调整柱宽
			const barSpacing = barWidth / 2;
			
			// 绘制Y轴
			ctx.beginPath();
			ctx.setStrokeStyle('#CCCCCC');
			ctx.setLineWidth(1);
			ctx.moveTo(padding.left, padding.top);
			ctx.lineTo(padding.left, canvasHeight - padding.bottom);
			ctx.stroke();
			
			// 绘制X轴
			ctx.beginPath();
			ctx.moveTo(padding.left, canvasHeight - padding.bottom);
			ctx.lineTo(canvasWidth - padding.right, canvasHeight - padding.bottom);
			ctx.stroke();
			
			// 绘制柱状图
			data.forEach((item, index) => {
				const x = padding.left + (barWidth + barSpacing * 2) * index + barSpacing * 2;
				const barHeight = item.value > 0 ? (item.value / maxValue) * chartHeight : 0;
				const y = canvasHeight - padding.bottom - barHeight;
				
				// 绘制柱子
				ctx.setFillStyle(item.color);
				ctx.fillRect(x, y, barWidth, barHeight);
				
				// 绘制数值
				ctx.setFillStyle('#333333');
				ctx.setFontSize(12);
				ctx.setTextAlign('center');
				ctx.fillText(item.value.toString(), x + barWidth / 2, y - 10);
				
				// 绘制标签
				ctx.setFillStyle('#666666');
				ctx.setFontSize(12);
				ctx.fillText(item.label, x + barWidth / 2, canvasHeight - padding.bottom + 20);
			});
			
			// 绘制Y轴刻度
			const yStep = maxValue / 5;
			for (let i = 0; i <= 5; i++) {
				const y = canvasHeight - padding.bottom - (i / 5) * chartHeight;
				const value = Math.round(i * yStep);
				
				ctx.setFillStyle('#999999');
				ctx.setFontSize(10);
				ctx.setTextAlign('right');
				ctx.fillText(value.toString(), padding.left - 5, y + 3);
				
				// 绘制网格线
				ctx.beginPath();
				ctx.setStrokeStyle('#EEEEEE');
				ctx.setLineWidth(0.5);
				ctx.moveTo(padding.left, y);
				ctx.lineTo(canvasWidth - padding.right, y);
				ctx.stroke();
			}
			
			// 执行绘制
			ctx.draw();
		},
		
		// 初始化饼图
		initPieChart() {
			// 获取设备信息
			const sysInfo = uni.getSystemInfoSync();
			// 按照设备像素比计算canvas大小
			const pixelRatio = sysInfo.pixelRatio || 2;
			const canvasWidth = sysInfo.windowWidth * 0.9; // 屏幕宽度的90%
			const canvasHeight = 400 * (sysInfo.windowWidth / 750); // 根据rpx换算为px
			const centerX = canvasWidth / 2;
			const centerY = canvasHeight / 2;
			const radius = Math.min(centerX, centerY) * 0.7;
			
			// 使用Canvas绘制简单的饼图
			const ctx = uni.createCanvasContext('attendancePieChart', this);
			
			// 设置画布背景
			ctx.setFillStyle('#FFFFFF');
			ctx.fillRect(0, 0, canvasWidth, canvasHeight);
			
			// 图表数据
			const total = this.statistics.normalDays + this.statistics.lateDays + 
						this.statistics.earlyDays + this.statistics.absentDays || 1;
			
			const data = [
				{ label: '正常', value: this.statistics.normalDays, color: '#4cd964' },
				{ label: '迟到', value: this.statistics.lateDays, color: '#f0ad4e' },
				{ label: '早退', value: this.statistics.earlyDays, color: '#5bc0de' },
				{ label: '缺勤', value: this.statistics.absentDays, color: '#dd524d' }
			];
			
			// 只绘制有值的数据项
			const validData = data.filter(item => item.value > 0);
			
			// 绘制标题
			ctx.setFillStyle('#333333');
			ctx.setFontSize(14);
			ctx.setTextAlign('center');
			ctx.fillText('考勤类型分布', centerX, 20);
			
			// 处理空数据的情况
			if (validData.length === 0) {
				ctx.setFillStyle('#999999');
				ctx.setFontSize(14);
				ctx.setTextAlign('center');
				ctx.fillText('暂无数据', centerX, centerY);
				ctx.draw();
				return;
			}
			
			// 绘制饼图
			let startAngle = 0;
			validData.forEach(item => {
				const percentage = item.value / total;
				if (percentage <= 0) return; // 跳过零值
				
				const endAngle = startAngle + percentage * 2 * Math.PI;
				
				// 绘制扇形
				ctx.beginPath();
				ctx.moveTo(centerX, centerY);
				ctx.arc(centerX, centerY, radius, startAngle, endAngle, false);
				ctx.setFillStyle(item.color);
				ctx.fill();
				
				// 计算标签位置
				const labelAngle = startAngle + (endAngle - startAngle) / 2;
				const labelDistance = radius * 1.2;
				const labelX = centerX + Math.cos(labelAngle) * labelDistance;
				const labelY = centerY + Math.sin(labelAngle) * labelDistance;
				
				// 绘制标签连线
				if (percentage > 0.05) {
					const lineEndX = centerX + Math.cos(labelAngle) * radius;
					const lineEndY = centerY + Math.sin(labelAngle) * radius;
					
					ctx.beginPath();
					ctx.setStrokeStyle(item.color);
					ctx.setLineWidth(1);
					ctx.moveTo(lineEndX, lineEndY);
					ctx.lineTo(labelX, labelY);
					ctx.stroke();
					
					// 绘制标签
					ctx.setFillStyle('#333333');
					ctx.setFontSize(12);
					ctx.setTextAlign('center');
					ctx.fillText(`${item.label}: ${Math.round(percentage * 100)}%`, labelX, labelY);
				}
				
				// 更新起始角度
				startAngle = endAngle;
			});
			
			// 绘制中心洞
			ctx.beginPath();
			ctx.arc(centerX, centerY, radius * 0.5, 0, 2 * Math.PI, false);
			ctx.setFillStyle('#FFFFFF');
			ctx.fill();
			
			// 绘制总天数
			ctx.setFillStyle('#333333');
			ctx.setFontSize(16);
			ctx.setTextAlign('center');
			ctx.fillText(total.toString(), centerX, centerY - 5);
			ctx.setFontSize(12);
			ctx.fillText('考勤总天数', centerX, centerY + 15);
			
			// 执行绘制
			ctx.draw();
		},
		onShow() {
			// 页面显示时重新绘制图表
			setTimeout(() => {
				this.initBarChart();
				this.initPieChart();
			}, 300);
		},
		onResize() {
			// 屏幕尺寸变化时重新绘制图表
			this.initBarChart();
			this.initPieChart();
		},
		// 打开日详情弹窗
		openDayDetailPopup(date) {
			this.selectedDate = date;
			this.dayDetailPopupVisible = true;
			this.loadDayDetailData(date);
			this.$refs.dayDetailPopup.open();
		},
		
		// 加载日详情数据
		loadDayDetailData(date) {
			// 清空之前的数据
			this.dayDetailData = [];
			this.selectedStaffDetail = null;
			
			// 模拟请求API数据
			uni.showLoading({
				title: '加载中...'
			});
			
			// 请求数据，根据实际API调整
			this.$http.get('/attendance/daily-detail', {
				params: {
					date: date,
					deptId: this.currentDept.id
				}
			}).then(res => {
				if (res.code === 200) {
					this.dayDetailData = res.data;
					// 初始化饼图
					this.$nextTick(() => {
						this.renderDayPieChart();
					});
				} else {
					uni.showToast({
						title: res.msg || '获取日详情失败',
						icon: 'none'
					});
				}
			}).catch(err => {
				uni.showToast({
					title: '获取日详情失败',
					icon: 'none'
				});
			}).finally(() => {
				uni.hideLoading();
			});
		},
		
		// 选择人员详情
		selectStaffDetail(staff) {
			this.selectedStaffDetail = staff;
		},
		
		// 点击饼图触摸事件
		touchChart(e) {
			// 可以根据需要添加饼图的交互效果
		},
		
		// 日历日期点击事件，调用openDayDetailPopup
		dayClick(day) {
			// 根据日历组件的事件获取选中的日期，格式化为YYYY-MM-DD
			const date = this.formatDate(day);
			this.openDayDetailPopup(date);
		},
		
		// 格式化日期为YYYY-MM-DD
		formatDate(date) {
			const year = date.getFullYear();
			const month = (date.getMonth() + 1).toString().padStart(2, '0');
			const day = date.getDate().toString().padStart(2, '0');
			return `${year}-${month}-${day}`;
		},
		formatWeek(date) {
			const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
			return days[new Date(date).getDay()];
		},
		formatLocation(record) {
			return `${record.latitude.toFixed(6)}, ${record.longitude.toFixed(6)}`;
		}
	}
};
</script>

<style lang="scss">
.statistics-container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

// 顶部筛选区域
.filter-section {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
	
	.month-selector {
		display: flex;
		align-items: center;
		
		.current-month {
			font-size: 32rpx;
			font-weight: bold;
			margin-right: 10rpx;
		}
		
		.month-arrows {
			display: flex;
			align-items: center;
		}
	}
	
	.staff-selector {
		display: flex;
		align-items: center;
		background-color: #fff;
		padding: 10rpx 20rpx;
		border-radius: 30rpx;
		font-size: 28rpx;
		
		text {
			margin-right: 10rpx;
		}
	}
}

// 统计概览卡片
.statistics-cards {
	display: flex;
	justify-content: space-between;
	margin-bottom: 20rpx;
	
	.stat-card {
		flex: 1;
		margin: 0 10rpx;
		background-color: #fff;
		border-radius: 10rpx;
		padding: 20rpx;
		text-align: center;
		box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05);
		
		&:first-child {
			margin-left: 0;
		}
		
		&:last-child {
			margin-right: 0;
		}
		
		.stat-value {
			font-size: 36rpx;
			font-weight: bold;
			color: #333;
		}
		
		.stat-label {
			font-size: 24rpx;
			color: #999;
			margin-top: 10rpx;
		}
		
		&:nth-child(1) .stat-value {
			color: #4cd964;
		}
		
		&:nth-child(2) .stat-value {
			color: #f0ad4e;
		}
		
		&:nth-child(3) .stat-value {
			color: #5bc0de;
		}
		
		&:nth-child(4) .stat-value {
			color: #dd524d;
		}
	}
}

// 日历区域
.main-calendar {
	background-color: #fff;
	border-radius: 10rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05);
	
	.calendar-header {
		display: flex;
		margin-bottom: 20rpx;
		
		.header-item {
			flex: 1;
			text-align: center;
			font-size: 28rpx;
			color: #666;
		}
	}
	
	.calendar-days {
		display: flex;
		flex-wrap: wrap;
		
		.calendar-day {
			width: calc(100% / 7);
			height: 80rpx;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			position: relative;
			margin-bottom: 10rpx;
			
			.day-number {
				font-size: 28rpx;
				color: #333;
			}
			
			&.current-month {
				.day-number {
					color: #007AFF;
				}
			}
			
			&.today {
				.day-number {
					width: 60rpx;
					height: 60rpx;
					background-color: #007AFF;
					color: #fff;
					border-radius: 50%;
					display: flex;
					justify-content: center;
					align-items: center;
				}
			}
			
			&.normal {
				.day-number {
					color: #4cd964;
				}
			}
			
			&.late {
				.day-number {
					color: #f0ad4e;
				}
			}
			
			&.early {
				.day-number {
					color: #5bc0de;
				}
			}
			
			&.absent {
				.day-number {
					color: #dd524d;
				}
			}
		}
	}
}

// 添加图表区域
.charts-section {
	background-color: #fff;
	border-radius: 10rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05);
	
	.section-title {
		font-size: 30rpx;
		font-weight: bold;
		margin-bottom: 20rpx;
	}
	
	.charts-container {
		display: flex;
		flex-direction: column;
		
		.chart-card {
			width: 100%;
			margin-bottom: 20rpx;
			background-color: #fff;
			border-radius: 10rpx;
			text-align: center;
			
			.chart-title {
				font-size: 28rpx;
				font-weight: bold;
				margin-bottom: 10rpx;
			}
			
			.chart-container {
				height: 400rpx;
				width: 100%;
				position: relative;
				padding: 10rpx 0 30rpx 0;
				
				canvas {
					width: 100%;
					height: 100%;
				}
			}
		}
	}
}

// 考勤详细记录
.attendance-records {
	background-color: #fff;
	border-radius: 10rpx;
	padding: 20rpx;
	box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05);
	
	.record-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
		
		text {
			font-size: 30rpx;
			font-weight: bold;
		}
		
		.view-all {
			font-size: 26rpx;
			color: #007AFF;
			font-weight: normal;
		}
	}
	
	.record-list {
		.record-item {
			display: flex;
			padding: 20rpx 0;
			border-bottom: 1rpx solid #f5f5f5;
			
			&:last-child {
				border-bottom: none;
			}
			
			.record-date {
				width: 100rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				
				.day {
					font-size: 32rpx;
					font-weight: bold;
					color: #333;
				}
				
				.week {
					font-size: 24rpx;
					color: #999;
					margin-top: 5rpx;
				}
			}
			
			.record-time {
				flex: 1;
				margin-left: 20rpx;
				
				.time-item {
					display: flex;
					align-items: center;
					margin-bottom: 10rpx;
					
					.time-label {
						width: 120rpx;
						font-size: 26rpx;
						color: #999;
					}
					
					.time-value {
						font-size: 28rpx;
						color: #333;
						margin-right: 10rpx;
						
						&.abnormal {
							color: #f0ad4e;
						}
					}
					
					.status-tag {
						font-size: 22rpx;
						padding: 2rpx 10rpx;
						border-radius: 20rpx;
						
						&.late {
							background-color: #fef0e5;
							color: #f0ad4e;
						}
						
						&.early {
							background-color: #e5f5fa;
							color: #5bc0de;
						}
					}
				}
			}
			
			.record-location {
				flex: 1;
				display: flex;
				flex-direction: column;
				margin-top: 10rpx;
				
				.location-label {
					font-size: 26rpx;
					color: #999;
					margin-bottom: 5rpx;
				}
				
				.location-value {
					font-size: 26rpx;
					color: #666;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
				}
			}
		}
	}
	
	.empty-records {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 60rpx 0;
		
		image {
			width: 200rpx;
			height: 200rpx;
			margin-bottom: 20rpx;
		}
		
		text {
			font-size: 28rpx;
			color: #999;
		}
	}
}

// 弹窗样式
.staff-popup {
	background-color: #fff;
	border-top-left-radius: 20rpx;
	border-top-right-radius: 20rpx;
	padding-bottom: 30rpx;
	
	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #f5f5f5;
		
		text {
			font-size: 32rpx;
			font-weight: bold;
		}
		
		.close-btn {
			font-size: 28rpx;
			color: #007AFF;
		}
	}
	
	.search-box {
		display: flex;
		align-items: center;
		background-color: #f5f5f5;
		margin: 20rpx 30rpx;
		padding: 10rpx 20rpx;
		border-radius: 10rpx;
		
		input {
			flex: 1;
			height: 60rpx;
			margin-left: 10rpx;
			font-size: 28rpx;
		}
	}
	
	.staff-list {
		max-height: 600rpx;
		
		.staff-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 20rpx 30rpx;
			border-bottom: 1rpx solid #f5f5f5;
			
			text {
				font-size: 30rpx;
				color: #333;
			}
		}
	}
}

/* 日考勤详情弹窗样式 */
.day-detail-popup {
	background-color: #fff;
	border-radius: 24rpx 24rpx 0 0;
	padding: 30rpx;
	max-height: 80vh;
	overflow-y: auto;
}

.popup-header {
	display: flex;
	justify-content: space-between;
	margin-bottom: 30rpx;
	font-size: 32rpx;
	font-weight: bold;
}

.close-btn {
	color: #999;
	font-size: 28rpx;
}

.day-stats-chart {
	width: 100%;
	height: 350rpx;
	margin-bottom: 20rpx;
}

.day-stats-summary {
	display: flex;
	justify-content: space-between;
	margin-bottom: 30rpx;
	border-radius: 10rpx;
	background-color: #f8f8f8;
	padding: 20rpx 10rpx;
}

.stat-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	width: 25%;
}

.stat-value {
	font-size: 34rpx;
	font-weight: bold;
	margin-bottom: 6rpx;
}

.stat-item.normal .stat-value {
	color: #4cd964;
}

.stat-item.late .stat-value {
	color: #f0ad4e;
}

.stat-item.early .stat-value {
	color: #f56c6c;
}

.stat-item.absent .stat-value {
	color: #dd524d;
}

.stat-label {
	font-size: 24rpx;
	color: #666;
}

.section-title {
	font-size: 28rpx;
	font-weight: bold;
	margin: 20rpx 0;
	color: #333;
	border-left: 8rpx solid #007aff;
	padding-left: 20rpx;
}

.staff-detail-section {
	margin-bottom: 30rpx;
	background-color: #f8f8f8;
	border-radius: 10rpx;
	padding: 20rpx;
}

.staff-list-section {
	margin-bottom: 30rpx;
}

.staff-category {
	margin-bottom: 20rpx;
	border-radius: 10rpx;
	overflow: hidden;
	background-color: #f8f8f8;
}

.category-header {
	display: flex;
	justify-content: space-between;
	padding: 15rpx 20rpx;
	font-size: 26rpx;
	color: #fff;
}

.category-header.normal {
	background-color: #4cd964;
}

.category-header.late {
	background-color: #f0ad4e;
}

.category-header.early {
	background-color: #f56c6c;
}

.category-header.absent {
	background-color: #dd524d;
}

.staff-items {
	padding: 10rpx 20rpx;
}

.staff-item {
	display: flex;
	justify-content: space-between;
	padding: 15rpx 0;
	border-bottom: 1px solid #eee;
}

.staff-item:last-child {
	border-bottom: none;
}

.staff-name {
	font-size: 26rpx;
	color: #333;
}

.staff-time {
	font-size: 24rpx;
	color: #666;
}

.empty-day-detail {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 50rpx 0;
}

.empty-day-detail image {
	width: 200rpx;
	height: 200rpx;
	margin-bottom: 20rpx;
}

.empty-day-detail text {
	font-size: 28rpx;
	color: #999;
}

/* 现有的日详情内容样式 */
.day-detail-content {
	background-color: #fff;
	border-radius: 10rpx;
}

.detail-item {
	display: flex;
	justify-content: space-between;
	padding: 15rpx 0;
	border-bottom: 1px solid #eee;
}

.detail-label {
	color: #666;
	font-size: 26rpx;
}

.detail-value {
	color: #333;
	font-size: 26rpx;
}

.detail-value-box {
	display: flex;
	align-items: center;
}

.status-tag {
	display: inline-block;
	padding: 2rpx 10rpx;
	font-size: 22rpx;
	border-radius: 6rpx;
	margin-left: 10rpx;
	color: #fff;
}

.status-tag.late {
	background-color: #f0ad4e;
}

.status-tag.early {
	background-color: #f56c6c;
}

.detail-photos {
	margin-top: 15rpx;
}

.photo-list {
	display: flex;
	flex-wrap: wrap;
	margin-top: 10rpx;
}

.photo-list image {
	width: 160rpx;
	height: 160rpx;
	margin: 10rpx;
	border-radius: 8rpx;
}
</style> 