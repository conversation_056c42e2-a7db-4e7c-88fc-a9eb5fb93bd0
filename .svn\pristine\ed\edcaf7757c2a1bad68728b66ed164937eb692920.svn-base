package com.heating.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository; 
import com.heating.entity.device.TDevice;

import java.util.List;
 

@Repository
public interface DeviceRepository extends JpaRepository<TDevice, Long> {
    @Query("SELECT d FROM TDevice d WHERE " +
           "(:status IS NULL OR d.status = :status) AND " +
           "(:area IS NULL OR d.building LIKE %:area%) AND " +
           "(:type IS NULL OR d.type = :type) AND " +
           "(:keyword IS NULL OR d.name LIKE %:keyword% OR d.id = :keyword)")
    Page<TDevice> findDevices(@Param("status") TDevice.DeviceStatus status,
                           @Param("area") String area,
                           @Param("type") String type,
                           @Param("keyword") String keyword,
                           Pageable pageable); 

    /**
     * 统计指定状态的设备数量
     * @param status 设备状态
     * @return 设备数量
     */
    long countByStatus(TDevice.DeviceStatus status);
    
    /**
     * 根据热用户ID查询设备列表
     * @param heatUnitId 热用户ID
     * @return 设备列表
     */
    List<TDevice> findByHeatUnitId(Long heatUnitId);
} 