package com.emm.model;

import javax.persistence.*;
import java.time.LocalDateTime; 
import java.util.Objects;

@Entity
@Table(name = "work_order_attachment")
public class WorkOrderAttachment {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "order_id")
    private String orderId;

    @Column(name = "file_type")
    private String fileType;

    @Column(name = "file_path")
    private String filePath;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    // Getters
    public Long getId() {
        return id;
    }

    public String getOrderId() {
        return orderId;
    }

    public String getFileType() {
        return fileType;
    }

    public String getFilePath() {
        return filePath;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    // Setters
    public void setId(Long id) {
        this.id = id;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    // equals 方法
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        WorkOrderAttachment that = (WorkOrderAttachment) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(orderId, that.orderId) &&
                Objects.equals(fileType, that.fileType) &&
                Objects.equals(filePath, that.filePath) &&
                Objects.equals(createdAt, that.createdAt);
    }

    // hashCode 方法
    @Override
    public int hashCode() {
        return Objects.hash(id, orderId, fileType, filePath, createdAt);
    }

    // toString 方法
    @Override
    public String toString() {
        return "WorkOrderAttachment{" +
                "id=" + id +
                ", orderId='" + orderId + '\'' +
                ", fileType='" + fileType + '\'' +
                ", filePath='" + filePath + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }
}