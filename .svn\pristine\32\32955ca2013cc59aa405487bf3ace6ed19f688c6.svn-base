<template>
  <BaseTabBar>
    <view class="hes-list-container">
      <!-- 搜索栏 -->
      <view class="search-bar">
        <view class="search-input-wrapper">
          <text class="iconfont icon-search"></text>
          <input
            type="text"
            placeholder="搜索换热站名称/编号"
            v-model="searchKeyword"
            confirm-type="search"
            @confirm="handleSearch"
          />
          <text
            class="iconfont icon-clear"
            v-if="searchKeyword"
            @click="clearSearch"
          ></text>
        </view>
      </view>

      <!-- 状态筛选 -->
      <view class="filter-tabs">
        <view
          class="filter-tab"
          v-for="(tab, index) in statusTabs"
          :key="index"
          :class="{ active: currentTab === tab.value }"
          @click="switchTab(tab.value)"
        >
          {{ tab.label }}
        </view>
      </view>

      <!-- 换热站列表 - 新设计 -->
      <view class="hes-modern-list">
        <view
          class="hes-card"
          v-for="(station, index) in stationList"
          :key="index"
          :class="getCardColorClass(station)"
          @click="navigateToDetail(station.id)"
        >
          <!-- 站点名称和时间信息 -->
          <view class="card-header">
            <view class="station-name">{{ station.name }} [{{ station.id }}]</view>
            <view class="connection-time">{{
              formatDateTime(station.last_connect_time)
            }}</view>
          </view>

          <!-- 运行模式 -->
          <view class="operation-mode">
            控制模式：{{ station.operation_mode === "fixed" ? "定频" : "变频" }}
          </view>

          <!-- 温度信息 - 显示在一行 -->
          <view class="temperature-info">
            <view class="network-row">
              <text class="network-label">一次网:</text>
              <text class="temp-value"
                >{{ station.primary_supply_temp || "--" }}/{{
                  station.primary_return_temp || "--"
                }}°C</text
              >
              <text class="network-label second-label">二次网:</text>
              <text class="temp-value"
                >{{ station.secondary_supply_temp || "--" }}/{{
                  station.secondary_return_temp || "--"
                }}°C</text
              >
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="card-actions">
            <view class="status-badge" :class="station.status">
              {{ getStatusText(station.status) }}
            </view>
            <view class="action-buttons">
              <view
                class="action-btn control"
                @click.stop="navigateToControl(station.id)"
              >
                控制
              </view>
              <view class="action-btn detail" @click.stop="navigateToDetail(station.id)">
                详情
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore" @click="loadMore">加载更多</view>
      <view class="no-more" v-else>没有更多数据了</view>
    </view>
  </BaseTabBar>
</template>

<script>
import { heatingStationApi } from "@/utils/api.js";
import BaseTabBar from "@/components/BaseTabBar.vue";

export default {
  components: {
    BaseTabBar,
  },
  data() {
    return {
      searchKeyword: "",
      currentTab: "all",
      statusTabs: [
        { label: "全部", value: "all" },
        { label: "正常", value: "online" },
        { label: "异常", value: "warning" },
        { label: "故障", value: "fault" },
      ],
      stationList: [],
      page: 1,
      pageSize: 10,
      hasMore: true,
      loading: false,
    };
  },
  onLoad() {
    this.loadStationList();
  },
  methods: {
    // 加载换热站列表
    loadStationList() {
      if (this.loading) return;
      this.loading = true;

      // 构建请求参数
      const params = {
        status: this.currentTab !== "all" ? this.currentTab : "", // online/offline/fault，可选
        keyword: this.searchKeyword || "", // 搜索关键词，可选
      };

      console.log("加载换热站列表，参数:", params);

      // 调用API获取换热站列表
      heatingStationApi
        .getList(params)
        .then((res) => {
          console.log("换热站列表获取成功:", res);
          if (res && res.data && res.data.list) {
            // 处理数据
            const processedList = res.data.list.map((station) => {
              // 为模拟设计添加示例数据（实际项目中应删除这些模拟数据，使用真实API返回的数据）
              return {
                ...station,
                // 如果API没有返回这些字段，添加示例数据
                last_connect_time: station.last_connect_time || new Date().toISOString(),
                operation_mode:
                  station.operation_mode || (Math.random() > 0.5 ? "fixed" : "variable"),
                primary_supply_temp:
                  station.primary_supply_temp || Math.floor(Math.random() * 15) + 70,
                primary_return_temp:
                  station.primary_return_temp || Math.floor(Math.random() * 10) + 50,
                secondary_supply_temp:
                  station.secondary_supply_temp || Math.floor(Math.random() * 10) + 50,
                secondary_return_temp:
                  station.secondary_return_temp || Math.floor(Math.random() * 10) + 30,
                enabled: station.enabled !== undefined ? station.enabled : true,
              };
            });

            if (this.page === 1) {
              // 首次加载或刷新
              this.stationList = processedList;
            } else {
              // 加载更多
              this.stationList = [...this.stationList, ...processedList];
            }

            // 判断是否还有更多数据
            this.hasMore = res.data.list.length >= this.pageSize;
          } else {
            // 没有数据或格式不正确
            if (this.page === 1) {
              this.stationList = [];
            }
            this.hasMore = false;
          }
        })
        .catch((err) => {
          console.error("加载换热站列表失败:", err);
          uni.showToast({
            title: "加载失败，请重试",
            icon: "none",
          });
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 格式化日期时间
    formatDateTime(dateTimeStr) {
      if (!dateTimeStr) return "暂无数据";
      try {
        const date = new Date(dateTimeStr);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        const hours = String(date.getHours()).padStart(2, "0");
        const minutes = String(date.getMinutes()).padStart(2, "0");
        const seconds = String(date.getSeconds()).padStart(2, "0");

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      } catch (e) {
        console.error("日期格式化错误:", e);
        return dateTimeStr || "暂无数据";
      }
    },

    // 获取卡片颜色类
    getCardColorClass(station) {
      // 根据状态返回不同颜色
      if (!station.enabled) return "gray-card"; // 未启用的站点显示为灰色
      if (station.status === "online") return "green-card"; // 在线显示为绿色
      if (station.status === "offline") return "yellow-card"; // 离线显示为黄色
      return "blue-card"; // 其他状态显示为蓝色
    },

    // 搜索处理
    handleSearch() {
      console.log(`搜索关键词: ${this.searchKeyword}`);
      this.page = 1; // 重置页码
      this.loadStationList();
    },

    // 清除搜索
    clearSearch() {
      this.searchKeyword = "";
      this.page = 1; // 重置页码
      this.loadStationList();
    },

    // 切换状态标签
    switchTab(tab) {
      if (this.currentTab === tab) return;
      this.currentTab = tab;
      this.page = 1; // 重置页码
      this.loadStationList();
      console.log(`切换到标签: ${tab}`);
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        online: "在线",
        offline: "离线",
        warning: "异常",
        fault: "故障",
      };
      return statusMap[status] || "未知";
    },

    // 加载更多
    loadMore() {
      if (this.loading || !this.hasMore) return;
      this.page++;
      this.loadStationList();
    },

    // 导航到详情页
    navigateToDetail(id) {
      // 显示加载中
      uni.showLoading({
        title: "加载详情...",
        mask: true,
      });

      // 调用API获取换热站详情
      heatingStationApi
        .getDetail(id)
        .then((res) => {
          console.log("换热站详情数据:", res);
          if (res.code === 200) {
            // 成功获取数据后跳转到详情页，并将详情数据传递过去
            uni.navigateTo({
              url: `/pages/hes/detail?id=${id}`,
              success: function (navigateRes) {
                // 传递数据给打开的页面
                navigateRes.eventChannel.emit("acceptStationDetail", res.data);
              },
            });
          } else {
            uni.showToast({
              title: res.message || "获取详情失败",
              icon: "none",
            });
          }
        })
        .catch((err) => {
          console.error("获取换热站详情失败:", err);
          uni.showToast({
            title: "获取详情失败，请重试",
            icon: "none",
          });
        })
        .finally(() => {
          uni.hideLoading();
        });
    },

    // 导航到控制页
    navigateToControl(id) {
      uni.navigateTo({
        url: `/pages/hes/control?id=${id}`,
      });
    },

    // 导航到告警页
    navigateToAlarm(id) {
      uni.navigateTo({
        url: `/pages/hes/alarms?id=${id}`,
      });
    },
  },
};
</script>

<style lang="scss">
.hes-list-container {
  padding: 20rpx;
  box-sizing: border-box;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.search-bar {
  margin-bottom: 20rpx;

  .search-input-wrapper {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 16rpx 20rpx;
    display: flex;
    align-items: center;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

    input {
      flex: 1;
      height: 40rpx;
      font-size: 28rpx;
      margin: 0 10rpx;
    }

    .iconfont {
      font-size: 32rpx;
      color: #999;
    }
  }
}

.filter-tabs {
  display: flex;
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 4rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

  .filter-tab {
    flex: 1;
    text-align: center;
    padding: 16rpx 0;
    font-size: 28rpx;
    position: relative;
    color: #666;
    transition: all 0.3s;
    border-radius: 8rpx;

    &.active {
      color: #fff;
      background-color: #1890ff;
      font-weight: 500;
    }
  }
}

// 卡片列表样式 - 按需求调整
.hes-modern-list {
  .hes-card {
    border-radius: 12rpx;
    padding: 20rpx 24rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;

    // 颜色样式按图示调整
    &.yellow-card {
      background-color: #ffd466;
      color: #333;
    }

    &.green-card {
      background-color: #2dcea3;
      color: #fff;
    }

    &.blue-card {
      background-color: #4fb5ee;
      color: #fff;
    }

    &.gray-card {
      background-color: #a0a0a0;
      color: #fff;
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12rpx;

      .station-name {
        font-size: 32rpx;
        font-weight: 600;
        max-width: 60%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .connection-time {
        font-size: 24rpx;
        opacity: 0.9;
      }
    }

    .operation-mode {
      font-size: 28rpx;
      margin-bottom: 12rpx;
    }

    .temperature-info {
      margin-bottom: 16rpx;

      .network-row {
        display: flex;
        align-items: center;
        flex-wrap: wrap;

        .network-label {
          font-size: 28rpx;
          min-width: 90rpx;
        }

        .temp-value {
          font-size: 28rpx;
          font-weight: 500;
          margin-right: 20rpx;
        }

        .second-label {
          margin-left: 10rpx;
        }
      }
    }

    .card-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .status-badge {
        padding: 6rpx 20rpx;
        border-radius: 30rpx;
        font-size: 24rpx;
        background-color: rgba(255, 255, 255, 0.25);
      }

      .action-buttons {
        display: flex;

        .action-btn {
          padding: 6rpx 30rpx;
          margin-left: 16rpx;
          font-size: 26rpx;
          border-radius: 30rpx;
          background-color: rgba(255, 255, 255, 0.25);
        }
      }
    }
  }
}

.load-more,
.no-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 26rpx;
  color: #999;
}
</style>
