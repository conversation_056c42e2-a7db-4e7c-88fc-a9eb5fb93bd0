package com.heating.dto.device;

import lombok.Data;

/**
 * 设备巡检项响应DTO
 */
@Data
public class DevicePatrolItemResponse {
    /**
     * 设备巡检项ID
     */
    private Long id;
    
    /**
     * 设备ID
     */
    private Long deviceId;
    
    /**
     * 巡检项字典ID
     */
    private Long patrolItemDictId;
    
    /**
     * 巡检项名称
     */
    private String itemName;
    
    /**
     * 巡检项类别名称
     */
    private String categoryName;
    
    /**
     * 参数类型
     */
    private String paramType;
    
    /**
     * 单位
     */
    private String unit;
    
    /**
     * 正常范围
     */
    private String normalRange;
    
    /**
     * 检查方法
     */
    private String checkMethod;
    
    /**
     * 重要性
     */
    private String importance;
    
    /**
     * 描述说明
     */
    private String description;
} 