<template>
  <BaseTabBar>
	  <view class="top-style">
		<view class="top-bg"></view>
		<view class="top-circles">
			<view class="circle circle-1"></view>
			<view class="circle circle-2"></view>
		</view>
		<!-- <view class="top-wave"></view> -->
	  </view>
    <view class="home-container">
      <!-- 权限检查调试区域 -->
    <!--  <view class="debug-section" v-if="showDebug">
        <view class="section-title">权限检查调试</view>
        <view class="debug-grid">
        
          <view class="debug-row">
            <text class="debug-label">kebab-case:</text>
            <permission-check permission="home:patrol-plans">
              <view class="debug-success">✓ 有权限 (kebab-case 正常)</view>
            </permission-check>
          </view>


          <view class="debug-row">
            <text class="debug-label">PascalCase:</text>
            <PermissionCheck permission="home:patrol-plans">
              <view class="debug-success">✓ 有权限 (PascalCase 正常)</view>
            </PermissionCheck>
          </view>

        
          <view class="debug-row">
            <text class="debug-label">无权限测试:</text>
            <permission-check permission="invalid:permission" :show-tip="true">
              <view class="debug-success">这不应该显示</view>
            </permission-check>
          </view>
        </view>
        <button class="debug-toggle" @click="showDebug = false">关闭调试区域</button>
        <button class="debug-toggle" @click="refreshPermissions">刷新权限</button>
      </view> -->
      
      <!-- 顶部状态栏 -->
      <view class="status-bar">
        <view class="weather-info">
          <text class="temp">23°C</text>
          <text class="city">西安市</text>
        </view>
        <view class="date-info">
          <text>{{ currentDate }}</text>
          <text>{{ currentWeekDay }}</text>
        </view>
      </view>

      <!-- 数据概览 -->
	   <PermissionCheck permission="home:data-show">
		  <view class="data-overview">	 
			<view class="section-title">数据概览</view>
			<view class="stat-cards">
			  <view class="stat-card">
				<text class="stat-value">{{ statsData.heatUnitCount }}</text>
				<text class="stat-label">热用户总数</text>
			  </view>
			  <view class="stat-card">
				<text class="stat-value">{{ statsData.hesOnlineRate }}</text>
				<text class="stat-label">换热站在线率</text>
			  </view>
			  <view class="stat-card">
				<text class="stat-value">{{ statsData.weeklyAlarms }}</text>
				<text class="stat-label">本周故障数</text>
			  </view>
			</view>
		  </view>
      </PermissionCheck>
	
      <!-- 快捷功能 -->
      <view class="quick-access">
        <view class="section-title">快捷功能</view>
        <view class="quick-grid">
          <!-- 巡检计划 - 用于测试的kebab-case形式 -->
          <PermissionCheck permission="home:patrol-plans">
            <view class="quick-item" @click="navTo('/pages/patrol/plans')">
              <view class="quick-icon-wrapper">
                <image class="quick-icon" src="/static/icons/patrol.png"></image>
              </view>
              <text class="quick-text">巡检计划</text>
            </view>
          </PermissionCheck>

          <!-- 巡检记录 -->
          <PermissionCheck permission="home:patrol-record">
            <view class="quick-item" @click="navTo('/pages/patrol/records')">
              <view class="quick-icon-wrapper">
                <image class="quick-icon" src="/static/icons/record.png"></image>
              </view>
              <text class="quick-text">巡检工单</text>
            </view>
          </PermissionCheck>

          <!-- 故障上报 -->
          <PermissionCheck permission="home:fault-report">
            <view class="quick-item" @click="navTo('/pages/fault/report')">
              <view class="quick-icon-wrapper">
                <image class="quick-icon" src="/static/icons/fault.png"></image>
              </view>
              <text class="quick-text">故障上报</text>
            </view>
          </PermissionCheck>

          <!-- 故障列表 -->
          <PermissionCheck permission="home:fault-list">
            <view class="quick-item" @click="navigateTo('/pages/fault/list')">
              <view class="quick-icon-wrapper">
                <image class="quick-icon" src="/static/icons/list.png"></image>
              </view>
              <text class="quick-text">故障列表</text>
            </view>
          </PermissionCheck>

          <!-- 换热站控制 -->
          <PermissionCheck permission="home:hes-control">
            <view class="quick-item" @click="navigateTo('/pages/hes/control')">
              <view class="quick-icon-wrapper">
                <image class="quick-icon" src="/static/icons/control.png"></image>
              </view>
              <text class="quick-text">换热站控制</text>
            </view>
          </PermissionCheck>

          <!-- 阀门控制 -->
          <PermissionCheck permission="home:valves-control">
            <view class="quick-item" @click="navigateTo('/pages/valves/control')">
              <view class="quick-icon-wrapper">
                <image class="quick-icon" src="/static/icons/valve.png"></image>
              </view>
              <text class="quick-text">阀门控制</text>
            </view>
          </PermissionCheck>

          <!-- 室温上报 -->
          <PermissionCheck permission="home:temp-report">
            <view class="quick-item" @click="showTempReportModal">
              <view class="quick-icon-wrapper">
                <text class="iconfont icon-temperature quick-icon-font"></text>
              </view>
              <text class="quick-text">室温上报</text>
            </view>
          </PermissionCheck>
		   <PermissionCheck permission="home:workorder-list">
				 <view class="quick-item" @click="navigateTo('/pages/workorder/list')">
				  <view class="quick-icon-wrapper">
				   <text class="iconfont icon-temperature quick-icon-font"></text>
				  </view>
				  <text class="item-text">维修工单</text>
				</view>
		   </PermissionCheck>
		   
          <!-- 缴费统计 -->
          <PermissionCheck permission="home:payment">
            <view class="quick-item" @click="navigateTo('/pages/payment/stats')">
              <view class="quick-icon-wrapper">
                <image class="quick-icon" src="/static/icons/stats.png"></image>
              </view>
              <text class="quick-text">缴费统计</text>
            </view>
          </PermissionCheck>

          <!-- 人员打卡 -->
          <PermissionCheck permission="home:attendance-clock">
            <view class="quick-item" @click="navTo('/pages/attendance/clock-in')">
              <view class="quick-icon-wrapper">
                <text class="iconfont icon-checkin quick-icon-font"></text>
              </view>
              <text class="quick-text">人员打卡</text>
            </view>
          </PermissionCheck>

          <!-- 考勤统计 -->
        <!--  <PermissionCheck permission="home:attendance">
            <view class="quick-item" @click="navTo('/pages/attendance/statistics')">
              <view class="quick-icon-wrapper">
                <text class="iconfont icon-chart-bar quick-icon-font"></text>
              </view>
              <text class="quick-text">考勤统计</text>
            </view>
          </PermissionCheck> -->
		  
		 <PermissionCheck permission="home:device-list">
		   <view class="quick-item" @click="navTo('/pages/device/list')">
		     <view class="quick-icon-wrapper">
		       <text class="iconfont icon-chart-bar quick-icon-font"></text>
		     </view>
		     <text class="quick-text">设备管理</text>
		   </view>
		 </PermissionCheck> 
        </view>
      </view>

      <!-- 最近工单 -->
	   <PermissionCheck permission="home:workorder-list">
		   
		  <view class="recent-tasks repair-tasks">
			<view class="section-header">
			  <view class="section-title">
                最近维修工单
              </view>
			  <text class="view-more repair-more" @click="navigateTo('/pages/workorder/list')">查看更多</text>
			</view>

			<view class="task-list repair-list">
			  <view
				class="task-item repair-item"
				v-for="(task, index) in recentTasks"
				:key="index"
				@click="viewOrderDetail(task.id)"
			  >
				<view class="task-header repair-item-header">
				  <text class="task-code">{{ task.code }}</text>
				  <view class="status-tag repair-status" :class="task.status">{{ task.statusText }}</view>
				</view>
				<view class="task-info">
				  <text class="task-title repair-item-title">{{ task.title }}</text>
				  <text class="task-desc">{{ task.desc }}</text>
				</view>
				<view class="task-footer">
				  <text class="task-time">{{ task.time }}</text>
				  <view class="task-action repair-action">查看详情</view>
				</view>
			  </view>
			</view>
		
		  </view>
      </PermissionCheck> 
	  <!-- 最近巡检工单 -->
	   <PermissionCheck permission="home:patrol-record">   
	  		  <view class="recent-tasks patrol-tasks">
	  			<view class="section-header">
	  			  <view class="section-title">
                    最近巡检工单
                  </view>
	  			  <text class="view-more patrol-more" @click="navigateTo('/pages/patrol/records')">查看更多</text>
	  			</view>
	  
	  			<view class="task-list patrol-list">
	  			  <view
	  				class="task-item patrol-item"
	  				v-for="(task, index) in patrolTasks"
	  				:key="index"
	  				@click="viewPatrolDetail(task.id)"
	  			  >
	  				<view class="task-header patrol-item-header">
	  				  <text class="task-code">{{ task.code }}</text>
	  				  <view class="status-tag patrol-status" :class="task.status">{{ task.statusText }}</view>
	  				</view>
	  				<view class="task-info">
	  				  <text class="task-title patrol-item-title">{{ task.title }}</text>
	  				  <text class="task-desc">{{ task.desc }}</text>
	  				</view>
	  				<view class="task-footer">
	  				  <text class="task-time">{{ task.time }}</text>
	  				  <view class="task-action patrol-action">查看详情</view>
	  				</view>
	  			  </view>
	  			</view>
	  			
	  			<!-- 底部空白区域，确保内容不被底部导航栏遮挡 -->
	  			<view class="bottom-space"></view>
	  		  </view>
	  </PermissionCheck> 
	  
      <!-- 室温上报弹窗 -->
      <uni-popup ref="tempReportPopup" type="center">
        <view class="temp-report-modal">
          <view class="modal-title">室温上报</view>
          <view class="modal-content">
            <view class="form-item">
              <text class="form-label">热用户名称</text>
              <picker
                class="form-input"
                @change="handleHeatUnitChange"
                :value="heatUnitIndex"
                :range="heatUnitOptions"
              >
                <view class="picker-text">{{ heatUnitOptions[heatUnitIndex] }}</view>
              </picker>
            </view>

            <view class="form-item">
              <text class="form-label">楼栋号</text>
              <input
                class="form-input"
                type="text"
                v-model="buildingNo"
                placeholder="请输入楼栋号，如：1号楼"
              />
            </view>

            <view class="form-item">
              <text class="form-label">单元号</text>
              <input
                class="form-input"
                type="text"
                v-model="unitNo"
                placeholder="请输入单元号，如：1单元"
              />
            </view>

            <view class="form-item">
              <text class="form-label">户号</text>
              <input
                class="form-input"
                type="text"
                v-model="roomNo"
                placeholder="请输入户号，如：101"
              />
            </view>

            <view class="form-item">
              <text class="form-label">当前室内温度</text>
              <view class="temp-slider-container">
                <slider
                  @change="handleTempChange"
                  :value="reportTemp"
                  :min="10"
                  :max="35"
                  show-value
                />
                <text class="temp-value">{{ reportTemp }}°C</text>
              </view>
            </view>

            <view class="form-item">
              <text class="form-label">当前室外温度</text>
              <view class="outdoor-temp">{{ outdoorTemp }}°C</view>
            </view>

            <view class="form-item">
              <text class="form-label">图片上传</text>
              <view class="upload-container">
                <view class="upload-list">
                  <view
                    class="upload-item"
                    v-for="(item, index) in uploadImages"
                    :key="index"
                  >
                    <image class="upload-image" :src="item" mode="aspectFill"></image>
                    <text class="upload-delete" @click="deleteImage(index)">×</text>
                  </view>
                  <view
                    class="upload-button"
                    @click="chooseImage"
                    v-if="uploadImages.length < 3"
                  >
                    <text class="upload-icon">+</text>
                    <text class="upload-text">上传图片</text>
                  </view>
                </view>
              </view>
            </view>

            <view class="form-item">
              <text class="form-label">备注信息</text>
              <textarea
                class="form-textarea"
                placeholder="请输入备注信息(选填)"
                v-model="reportRemark"
              />
            </view>
          </view>
          <view class="modal-footer">
            <button class="btn-cancel" @click="cancelTempReport">取消</button>
            <button class="btn-submit" @click="submitTempReport">提交</button>
          </view>
        </view>
      </uni-popup>
      
      <!-- 底部安全区域，确保在iPhone等设备上有足够的底部间距 -->
      <view class="safe-area-bottom"></view>
    </view>
  </BaseTabBar>
</template>

<script>
import { heatUnitApi, temperatureReportApi, homeApi, patrolApi } from "@/utils/api.js";
import PermissionCheck from "@/components/PermissionCheck.vue"; // 导入权限检查组件
import BaseTabBar from "@/components/BaseTabBar.vue";

export default {
  components: {
    PermissionCheck, // 本地注册组件
    BaseTabBar,
  },
  data() {
    return {
      title: "智慧供暖运维系统",
      statsData: {
        heatUnitCount: 356,
        hesOnlineRate: "98.5%",
        weeklyAlarms: 12,
      },
      recentTasks: [],
      patrolTasks: [], // 巡检工单列表
      // 室温上报相关数据
      heatUnitList: [], // 热用户列表数据
      heatUnitOptions: [], // 热用户名称列表
      heatUnitIndex: 0,
      buildingNo: "",
      unitNo: "",
      roomNo: "",
      reportTemp: 22,
      reportRemark: "",
      outdoorTemp: 0,
      uploadImages: [],
      latitude: null,
      longitude: null,
      timer: null,
      showDebug: true,
    };
  },
  computed: {
    // 直接从$store.state获取状态，不使用mapState
    // ...mapState('attendance', [
    //   'isUploadingLocation', // 是否正在上传位置
    //   'clockInTime',         // 上班打卡时间
    //   'clockOutTime'         // 下班打卡时间
    // ]),
    isUploadingLocation() {
      return this.$store.state.attendance.isUploadingLocation;
    },
    clockInTime() {
      return this.$store.state.attendance.clockInTime;
    },
    clockOutTime() {
      return this.$store.state.attendance.clockOutTime;
    },
    currentDate() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, "0");
      const day = String(now.getDate()).padStart(2, "0");
      return `${year}年${month}月${day}日`;
    },
    currentWeekDay() {
      const weekDays = [
        "星期日",
        "星期一",
        "星期二",
        "星期三",
        "星期四",
        "星期五",
        "星期六",
      ];
      return weekDays[new Date().getDay()];
    },
  },
  onLoad() {
    // 页面加载时获取数据
    this.loadHomeData();
    // 启动定时器，每分钟更新一次时间
    this.timer = setInterval(() => {
      this.$forceUpdate();
    }, 60000);

    // 延迟检查考勤状态，确保网络和其他依赖已准备好
    setTimeout(() => {
      // 检查今日打卡状态并启动位置上传（如果已上班打卡）
      this.checkAttendanceStatus();
    }, 1000);
  },
  onUnload() {
    // 页面卸载时清除定时器
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  },
  onShow() {
    // 页面显示时获取数据
    this.loadHomeData();
    
    // 延迟检查考勤状态，确保网络和其他依赖已准备好
    setTimeout(() => {
      // 检查今日打卡状态并启动位置上传（如果已上班打卡）
      this.checkAttendanceStatus();
    }, 1000);
  },
  methods: {
    // 检查今日打卡状态
    checkAttendanceStatus() {
      //console.log('检查今日打卡状态...');
      
      // 获取是否开启定位上传的设置
      const isPositioning = uni.getStorageSync('isPositioning') || 0;
    //  console.log('定位上传设置:', isPositioning);
      
      // 只有当开启了定位上传功能，才执行后续的定位上传逻辑
      if (isPositioning === 1) {
        // 直接使用$store.dispatch调用actions
        this.$store.dispatch('attendance/getTodayClockRecord')
          .then((data) => {
            // 获取最新状态
            const clockIn = this.$store.state.attendance.clockInTime;
            const clockOut = this.$store.state.attendance.clockOutTime;
            const isUploading = this.$store.state.attendance.isUploadingLocation;
            
            console.log('获取今日打卡记录成功:', {
              '上班打卡': clockIn || '未打卡',
              '下班打卡': clockOut || '未打卡',
              '是否上传位置': isUploading ? '是' : '否'
            });
          })
          .catch(err => {
            console.error('获取今日打卡记录失败:', err);
            // 发生错误时，确保停止位置上传，避免异常情况下继续上传
            this.$store.dispatch('attendance/stopLocationUpload');
          });
      } else {
        console.log('未开启定位上传功能，不进行位置上传');
        // 确保停止位置上传
        this.$store.dispatch('attendance/stopLocationUpload');
      }
    },
    
    // 启动位置上传
    startLocationUpload() {
      this.$store.dispatch('attendance/startLocationUpload');
    },
    
    // 停止位置上传
    stopLocationUpload() {
      this.$store.dispatch('attendance/stopLocationUpload');
    },
    
    // 加载首页数据
    loadHomeData() {
      // 获取最近工单数据
      this.getRecentOrders();

      // 获取统计数据
      this.getStatisticsData();
      
      // 获取最近巡检工单数据
      this.getRecentPatrolTasks();
    },

    // 获取最近工单
    getRecentOrders() {
      homeApi
        .getRecentOrders(3)
        .then((res) => {
          if (res.code === 200 && Array.isArray(res.data.list)) {
            // 获取当前用户ID
            const currentUserId = uni.getStorageSync("userId");
            
            // 格式化工单数据
            this.recentTasks = res.data.list.map((item) => {
              // 检查是否已转派给当前用户
              let statusText = item.orderStatus;
              if (item.transferUserId && item.transferUserId === currentUserId) {
                statusText = "已转派";
              }
              
              return {
                id: item.orderId,
                code: item.orderNo,
                title: `${item.heatUnitName} ${item.faultType}`,
                desc: item.faultLevel,
                time: item.createdTime,
                status: this.getStatusClass(statusText), // 使用可能已修改的状态文本来获取样式类
                statusText: statusText, // 使用可能已修改的状态文本
                transferUserId: item.transferUserId // 保存转派用户ID以备后续使用
              };
            });
          } else {
            console.error("获取最近工单失败:", res);
          }
        })
        .catch((err) => {
          console.error("获取最近工单异常:", err);
        });
    },

    // 获取统计数据
    getStatisticsData() {
      homeApi
        .getHeatUnitCount()
        .then((res) => {
          if (res.code === 200 && res.data) {
            // 更新统计数据
            this.statsData.heatUnitCount = res.data.count || 0;
          } else {
            console.error("获取热用户统计数据失败:", res);
          }
        })
        .catch((err) => {
          console.error("获取热用户统计数据异常:", err);
        });

      homeApi
        .getHeatUnitOnlineRate()
        .then((res) => {
          if (res.code === 200 && res.data) {
            // 更新统计数据
            this.statsData.hesOnlineRate = res.data.onlineRate || "0%";
          } else {
            console.error("获取换热站在线率统计数据失败:", res);
          }
        })
        .catch((err) => {
          console.error("获取换热站在线率统计数据异常:", err);
        });

      homeApi
        .getWeeklyFaultCount()
        .then((res) => {
          if (res.code === 200 && res.data) {
            // 更新统计数据
            this.statsData.weeklyAlarms = res.data.count || 0;
          } else {
            console.error("获取本周故障告警统计数据失败:", res);
          }
        })
        .catch((err) => {
          console.error("获取本周故障告警统计数据异常:", err);
        });
    },

    // 根据工单状态获取状态样式类
    getStatusClass(status) {
      switch (status) {
        case "待接单":
          return "warning";
        case "进行中":
        case "维修中":
          return "primary";
        case "已完成":
          return "success";
        case "已转派":
          return "info"; // 为已转派状态添加一个特殊的样式类
        default:
          return "error";
      }
    },

    // 页面导航
    navTo(url) {
      uni.navigateTo({
        url,
      });
    },

    // 兼容旧方法
    navigateTo(url) {
      this.navTo(url);
    },

    // 获取热用户列表
    getHeatUnitList() {
      return new Promise((resolve, reject) => {
        heatUnitApi
          .getList()
          .then((res) => {
            console.log("热用户列表接口响应:", res);
            if (Array.isArray(res.data)) {
              this.heatUnitList = res.data;
              this.heatUnitOptions = this.heatUnitList.map((item) => item.name);
              if (this.heatUnitList.length === 0) {
                reject(new Error("暂无热用户数据"));
              } else {
                resolve();
              }
            } else {
              reject(new Error(res.message || "获取热用户列表失败"));
            }
          })
          .catch((err) => {
            console.error("请求失败详情:", err);
            reject(new Error("网络请求失败，请检查网络连接"));
          });
      });
    },

    // 显示室温上报弹窗
    async showTempReportModal() {
      let loadingShown = false;
      try {
        loadingShown = true;
        uni.showLoading({
          title: "加载中...",
          mask: true,
        });

        // 先获取热用户列表
        await this.getHeatUnitList();

        // 并行获取室外温度和位置信息
        await Promise.all([this.getOutdoorTemperature(), this.getLocation()]);

        // 数据加载成功后打开弹窗
        this.$refs.tempReportPopup.open();
      } catch (error) {
        console.error("加载数据失败:", error);
        uni.showToast({
          title: error.message || "加载失败，请重试",
          icon: "none",
          duration: 3000,
        });
      } finally {
        if (loadingShown) {
          uni.hideLoading();
        }
      }
    },

    handleTempChange(e) {
      this.reportTemp = e.detail.value;
    },

    // 获取室外温度
    getOutdoorTemperature() {
      return new Promise((resolve, reject) => {
        // 模拟获取室外温度
        setTimeout(() => {
          const temp = (Math.random() * 10 + 15).toFixed(1);
          this.outdoorTemp = temp;
          resolve(temp);
        }, 500);
      });
    },

    // 获取位置信息
    getLocation() {
      return new Promise((resolve, reject) => {
        uni.getLocation({
          type: "gcj02",
          isHighAccuracy: true,
          highAccuracyExpireTime: 3000,
          success: (res) => {
            this.latitude = res.latitude;
            this.longitude = res.longitude;
            resolve(res);
          },
          fail: (err) => {
            console.error("获取位置失败:", err);
            // 使用默认坐标（西安市中心）
            this.latitude = 34.343147;
            this.longitude = 108.939621;

            // 根据错误类型显示不同提示
            let errorMsg = "获取位置失败";
            if (err.errMsg.includes("permission")) {
              errorMsg = "请授权位置权限";
            } else if (err.errMsg.includes("timeout")) {
              errorMsg = "获取位置超时";
            }

            uni.showToast({
              title: errorMsg,
              icon: "none",
            });

            resolve({
              latitude: this.latitude,
              longitude: this.longitude,
            });
          },
        });
      });
    },

    // 选择图片
    chooseImage() {
      uni.chooseImage({
        count: 3 - this.uploadImages.length,
        sizeType: ["compressed"],
        sourceType: ["camera", "album"],
        success: (res) => {
          this.uploadImages = [...this.uploadImages, ...res.tempFilePaths];
        },
      });
    },

    // 删除图片
    deleteImage(index) {
      this.uploadImages.splice(index, 1);
    },

    // 取消室温上报
    cancelTempReport() {
      this.resetForm();
      this.$refs.tempReportPopup.close();
    },

    // 重置表单
    resetForm() {
      this.heatUnitIndex = 0;
      this.buildingNo = "";
      this.unitNo = "";
      this.roomNo = "";
      this.reportTemp = 22;
      this.reportRemark = "";
      this.uploadImages = [];
    },

    // 上传图片
    async uploadImage(filePath) {
      return new Promise((resolve, reject) => {
        uni.uploadFile({
          url: "/api/upload/image",
          filePath: filePath,
          name: "file",
          header: {
            Authorization: "Bearer " + uni.getStorageSync("token"),
          },
          success: (res) => {
            try {
              const data = JSON.parse(res.data);
              if (data.code === 200) {
                resolve(data.data.url);
              } else {
                reject(new Error(data.message || "上传失败"));
              }
            } catch (e) {
              reject(new Error("解析响应失败"));
            }
          },
          fail: (err) => {
            reject(new Error("网络请求失败"));
          },
        });
      });
    },

    // 提交室温上报
    async submitTempReport() {
      try {
        // 表单验证
        if (this.heatUnitOptions.length === 0) {
          throw new Error("请等待热用户数据加载");
        }

        const validations = [
          { value: this.buildingNo.trim(), message: "请输入楼栋号" },
          { value: this.unitNo.trim(), message: "请输入单元号" },
          { value: this.roomNo.trim(), message: "请输入户号" },
        ];

        for (const validation of validations) {
          if (!validation.value) {
            throw new Error(validation.message);
          }
        }

        uni.showLoading({
          title: "正在提交",
          mask: true,
        });

        // 上传图片
        const uploadedImages = [];
        if (this.uploadImages.length > 0) {
          try {
            for (const filePath of this.uploadImages) {
              const imageUrl = await this.uploadImage(filePath);
              uploadedImages.push(imageUrl);
            }
          } catch (error) {
            throw new Error("图片上传失败：" + error.message);
          }
        }

        // 构建提交数据
        const reportData = {
          heat_unit_name: this.heatUnitOptions[this.heatUnitIndex],
          building_no: this.buildingNo.trim(),
          unit_no: this.unitNo.trim(),
          room_no: this.roomNo.trim(),
          indoor_temp: this.reportTemp,
          outdoor_temp: this.outdoorTemp,
          latitude: this.latitude,
          longitude: this.longitude,
          images: uploadedImages,
          videos: [], // Assuming no videos are uploaded in this context
          remark: this.reportRemark.trim(),
          report_user_id: uni.getStorageSync("userId"), // Assuming user_id is stored in local storage
        };

        // 使用 temperatureReportApi 提交数据
        const response = await temperatureReportApi.submit(reportData);

        if (response.code === 200) {
          uni.showToast({
            title: "室温上报成功",
            icon: "success",
          });

          this.resetForm();
          this.$refs.tempReportPopup.close();
        } else {
          throw new Error(response.message || "提交失败");
        }
      } catch (error) {
        console.error("提交失败:", error);
        // 更详细的错误信息显示
        let errorMsg = "提交失败，请重试";

        if (error.errMsg) {
          // 网络错误
          if (error.errMsg.includes("timeout")) {
            errorMsg = "请求超时，请检查网络连接";
          } else if (error.errMsg.includes("network")) {
            errorMsg = "网络连接异常，请检查网络";
          }
        } else if (error.message) {
          // 服务器返回的错误
          errorMsg = error.message;
        }

        uni.showToast({
          title: errorMsg,
          icon: "none",
          duration: 2000,
        });
      } finally {
        uni.hideLoading();
      }
    },

    handleHeatUnitChange(e) {
      this.heatUnitIndex = e.detail.value;
    },

    // 查看工单详情
    viewOrderDetail(id) {
      uni.navigateTo({
        url: `/pages/workorder/detail?id=${id}`,
      });
    },

    // 获取最近巡检工单数据
    getRecentPatrolTasks() {
      patrolApi
        .getLimitedPatrolRecords(3)
        .then((res) => {
          if (res.code === 200 && Array.isArray(res.data)) {
            // 格式化巡检工单数据
			//console.log(res.data)
            this.patrolTasks = res.data.map((item) => {
              return {
                id: item.id,
                code: `${item.patrolType}`,
                title: item.planName || "巡检计划",
                desc: `执行人:${item.executorName || "未指定"}`,
                time: this.formatPatrolDateTime(item.executionDate || item.createTime),
                status: this.getPatrolStatusClass(item.status),
                statusText: this.getPatrolStatusText(item.status),
              };
            });
          } else {
            console.error("获取最近巡检工单失败:", res);
          }
        })
        .catch((err) => {
          console.error("获取最近巡检工单异常:", err);
        });
    },
    
    // 获取巡检状态文本
    getPatrolStatusText(status) {
      switch (status) {
        case "pending":
          return "待执行";
        case "processing":
          return "执行中";
        case "completed":
          return "已完成";
        case "overdue":
          return "已超时";
        default:
          return status || "未知";
      }
    },
    
    // 获取巡检状态样式类
    getPatrolStatusClass(status) {
      switch (status) {
        case "pending":
          return "warning";
        case "processing":
          return "primary";
        case "completed":
          return "success";
        case "overdue":
          return "error";
        default:
          return "error";
      }
    },
    
    // 格式化巡检工单日期时间
    formatPatrolDateTime(dateTime) {
      if (!dateTime) return "";
      
      // 处理数组格式的日期时间
      if (Array.isArray(dateTime)) {
        if (dateTime.length >= 3) {
          const year = dateTime[0];
          const month = String(dateTime[1]).padStart(2, "0");
          const day = String(dateTime[2]).padStart(2, "0");
          return `${year}-${month}-${day}`;
        }
        return dateTime.join("-");
      }
      
      // 处理字符串格式
      const dateTimeStr = String(dateTime);
      
      // 如果包含T，说明是ISO格式
      if (dateTimeStr.includes("T")) {
        const date = new Date(dateTimeStr);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        return `${year}-${month}-${day}`;
      }
      
      // 如果是数字类型（时间戳）
      if (!isNaN(dateTime) && typeof dateTime === "number") {
        const date = new Date(dateTime);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        return `${year}-${month}-${day}`;
      }
      
      // 如果包含空格，可能是完整的日期时间格式，只取日期部分
      if (dateTimeStr.includes(" ")) {
        return dateTimeStr.split(" ")[0];
      }
      
      return dateTimeStr;
    },

    // 新增方法：查看巡检工单详情
    viewPatrolDetail(id) {
      uni.navigateTo({
        url: `/pages/patrol/record_detail?id=${id}`,
      });
    },
  },
};
</script>

<style lang="scss">
.home-container {
  padding: 0 30rpx;
  box-sizing: border-box;
  padding-bottom: 120rpx; /* 增加底部padding，避免内容被底部导航栏遮挡 */
}

/* 顶部样式增强 */
.top-style {
	height: 80rpx;
	width: 100%;
	padding: 0;
	background: linear-gradient(135deg, #35a6c8, #1e88e5);
	position: relative;
	overflow: hidden;
	z-index: 10;
	position: sticky;
	top: 0;
	z-index: 999;
}

.top-bg {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, #35a6c8, #1e88e5);
	opacity: 0.9;
	z-index: 1;
}

.top-circles {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 2;
}

.circle {
	position: absolute;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.1);
}

.circle-1 {
	width: 200rpx;
	height: 200rpx;
	top: -80rpx;
	right: 60rpx;
}

.circle-2 {
	width: 300rpx;
	height: 300rpx;
	top: 20rpx;
	left: -120rpx;
}

.top-wave {
	position: absolute;
	bottom: -2rpx;
	left: 0;
	width: 100%;
	height: 40rpx;
	background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%23ffffff' fill-opacity='1' d='M0,192L48,176C96,160,192,128,288,138.7C384,149,480,203,576,208C672,213,768,171,864,154.7C960,139,1056,149,1152,154.7C1248,160,1344,160,1392,160L1440,160L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E");
	background-size: 100% 100%;
	z-index: 3;
}

/* 调试区域样式 */
.debug-section {
  background-color: #f8f9fa;
  border: 2rpx solid #dee2e6;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;

  .debug-grid {
    margin: 20rpx 0;
  }

  .debug-row {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;
    padding: 10rpx 0;
    border-bottom: 1rpx solid #eee;
  }

  .debug-label {
    width: 200rpx;
    font-size: 26rpx;
    color: #666;
    font-weight: bold;
  }

  .debug-success {
    color: #52c41a;
    font-weight: bold;
  }

  .debug-toggle {
    font-size: 24rpx;
    padding: 10rpx 20rpx;
    background: #eee;
    border: none;
    border-radius: 8rpx;
  }
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  margin-top: 10rpx;
  .weather-info {
    display: flex;
    flex-direction: column;

    .temp {
      font-size: 36rpx;
      font-weight: bold;
    }

    .city {
      font-size: 24rpx;
      color: $uni-text-color-grey;
    }
  }

  .date-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;

    text {
      font-size: 24rpx;
      color: $uni-text-color-grey;
    }
  }
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;

  &::before {
    content: "";
    position: absolute;
    left: 0;
    top: 8rpx;
    width: 8rpx;
    height: 32rpx;
    background-color: $uni-color-primary;
    border-radius: 4rpx;
  }
}

.stat-cards {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;

  .stat-card {
    width: 30%;
    height: 160rpx;
    background-color: #fff;
    border-radius: 8rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .stat-value {
      font-size: 40rpx;
      font-weight: bold;
      color: $uni-color-primary;
      margin-bottom: 10rpx;
    }

    .stat-label {
      font-size: 24rpx;
      color: $uni-text-color-grey;
    }
  }
}

.quick-access {
  margin-bottom: 40rpx;

  .section-title {
    margin-bottom: 30rpx;
  }
}

.quick-grid {
  display: flex !important;
  flex-wrap: wrap !important;
  justify-content: flex-start !important;
  align-items: flex-start !important;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);
  padding: 30rpx 20rpx 10rpx;
  position: relative;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 200rpx;
    background: linear-gradient(
      180deg,
      rgba(245, 247, 250, 0.8) 0%,
      rgba(255, 255, 255, 0) 100%
    );
    border-radius: 20rpx 20rpx 0 0;
    z-index: 0;
  }
}

.quick-item {
  width: 25% !important;
  box-sizing: border-box !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: flex-start !important;
  margin-bottom: 30rpx !important;
  position: relative !important;
  z-index: 1 !important;
  padding: 10rpx !important;
  transition: all 0.3s ease;

  &:active {
    transform: translateY(6rpx) scale(0.95);
  }

  .quick-icon-wrapper {
    width: 110rpx !important;
    height: 110rpx !important;
    margin-bottom: 16rpx !important;
    border-radius: 28rpx !important;
    overflow: hidden !important;
    position: relative !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.15) !important;
  }

  .quick-icon {
    width: 60% !important;
    height: 60% !important;
    object-fit: contain !important;
    filter: brightness(0) invert(1) !important;
  }

  .quick-icon-font {
    font-size: 55rpx !important;
    color: #ffffff !important;
    text-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.2) !important;
  }

  .quick-text {
    font-size: 26rpx !important;
    color: $uni-text-color !important;
    font-weight: 500 !important;
    line-height: 1.4 !important;
    text-align: center !important;
    letter-spacing: 1rpx !important;
    margin-top: 6rpx !important;
  }
}

/* 快捷图标样式 - 确保每个图标有独特颜色 */
.quick-item:nth-of-type(1) .quick-icon-wrapper {
  background: linear-gradient(135deg, #6a9eef, #4483e5) !important;
}

.quick-item:nth-of-type(2) .quick-icon-wrapper {
  background: linear-gradient(135deg, #60c6a8, #3aaf8f) !important;
}

.quick-item:nth-of-type(3) .quick-icon-wrapper {
  background: linear-gradient(135deg, #f3a768, #ee8c3c) !important;
}

.quick-item:nth-of-type(4) .quick-icon-wrapper {
  background: linear-gradient(135deg, #e47474, #e05555) !important;
}

.quick-item:nth-of-type(5) .quick-icon-wrapper {
  background: linear-gradient(135deg, #8387ea, #5a5fd3) !important;
}

.quick-item:nth-of-type(6) .quick-icon-wrapper {
  background: linear-gradient(135deg, #55c1e3, #35a6c8) !important;
}

.quick-item:nth-of-type(7) .quick-icon-wrapper {
  background: linear-gradient(135deg, #e680b3, #d65698) !important;
}

.quick-item:nth-of-type(8) .quick-icon-wrapper {
  background: linear-gradient(135deg, #9dc75b, #7bae35) !important;
}

.quick-item:nth-of-type(9) .quick-icon-wrapper {
  background: linear-gradient(135deg, #6a9eef, #4483e5) !important;
}

.quick-item:nth-of-type(10) .quick-icon-wrapper {
  background: linear-gradient(135deg, #60c6a8, #3aaf8f) !important;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;

  .view-more {
    font-size: 24rpx;
    color: $uni-color-primary;
  }
}

.task-list {
  .task-item {
    background-color: #fff;
    border-radius: 8rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
    padding: 24rpx;
    margin-bottom: 20rpx;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    width: 100%;
    box-sizing: border-box;

    &:active {
      background-color: #f9f9f9;
    }
    
    &:last-child {
      margin-bottom: 30rpx; /* 确保最后一个项目底部有足够的间距 */
    }

    .task-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16rpx;
      width: 100%;

      .task-code {
        font-size: 28rpx;
        color: $uni-text-color-grey;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .status-tag {
        flex-shrink: 0;
        min-width: 80rpx;
        text-align: center;
      }
    }

    .task-info {
      margin-bottom: 16rpx;
      width: 100%;

      .task-title {
        font-size: 32rpx;
        font-weight: bold;
        color: $uni-text-color;
        margin-bottom: 8rpx;
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .task-desc {
        font-size: 28rpx;
        color: $uni-text-color-grey;
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .task-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;

      .task-time {
        font-size: 24rpx;
        color: $uni-text-color-grey;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .task-action {
        font-size: 24rpx;
        color: $uni-color-primary;
        flex-shrink: 0;
        margin-left: 10rpx;
      }
    }
  }
}

.status-tag {
  display: inline-block;
  padding: 6rpx 16rpx;
  font-size: 24rpx;
  border-radius: 30rpx;
  min-width: 80rpx;
  text-align: center;
  font-weight: 600;

  &.primary {
    background-color: rgba(24, 144, 255, 0.15);
    color: #1890ff;
  }

  &.success {
    background-color: rgba(82, 196, 26, 0.15);
    color: #52c41a;
  }

  &.warning {
    background-color: rgba(250, 173, 20, 0.15);
    color: #faad14;
  }

  &.error {
    background-color: rgba(255, 0, 0, 0.1);
    color: #FF0000;
  }
  
  &.info {
    background-color: rgba(144, 147, 153, 0.1);
    color: #909399; /* 使用灰色调表示已转派状态 */
  }
}

.temp-report-modal {
  background-color: #fff;
  width: 650rpx;
  border-radius: 16rpx;
  overflow: hidden;

  .modal-title {
    text-align: center;
    font-size: 32rpx;
    font-weight: bold;
    padding: 30rpx 0;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
  }

  .modal-content {
    padding: 30rpx;
    max-height: 800rpx;
    overflow-y: auto;

    .form-item {
      margin-bottom: 30rpx;

      .form-label {
        display: block;
        font-size: 28rpx;
        color: $uni-text-color;
        margin-bottom: 16rpx;
      }

      .form-input {
        width: 100%;
        height: 80rpx;
        line-height: 80rpx;
        padding: 0 20rpx;
        border-radius: 8rpx;
        background-color: #f5f5f5;
        box-sizing: border-box;

        .picker-text {
          font-size: 28rpx;
        }
      }

      .temp-slider-container {
        margin-top: 20rpx;
        position: relative;

        .temp-value {
          position: absolute;
          right: 0;
          top: -50rpx;
          font-size: 32rpx;
          color: $uni-color-primary;
          font-weight: bold;
        }
      }

      .outdoor-temp {
        font-size: 32rpx;
        color: $uni-color-primary;
        font-weight: bold;
        padding: 10rpx 0;
      }

      .upload-container {
        .upload-list {
          display: flex;
          flex-wrap: wrap;
          margin: 0 -10rpx;

          .upload-item,
          .upload-button {
            width: 180rpx;
            height: 180rpx;
            margin: 10rpx;
            border-radius: 8rpx;
            overflow: hidden;
            position: relative;
          }

          .upload-item {
            .upload-image {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }

            .upload-delete {
              position: absolute;
              top: 0;
              right: 0;
              width: 40rpx;
              height: 40rpx;
              background-color: rgba(0, 0, 0, 0.5);
              color: #fff;
              text-align: center;
              line-height: 40rpx;
              font-size: 24rpx;
              z-index: 1;
            }
          }

          .upload-button {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background-color: #f5f5f5;
            border: 1rpx dashed #ddd;

            .upload-icon {
              font-size: 60rpx;
              color: #999;
              line-height: 1;
              margin-bottom: 10rpx;
            }

            .upload-text {
              font-size: 24rpx;
              color: #999;
            }
          }
        }
      }

      .form-textarea {
        width: 100%;
        height: 160rpx;
        padding: 20rpx;
        border-radius: 8rpx;
        background-color: #f5f5f5;
        box-sizing: border-box;
        font-size: 28rpx;
      }
    }
  }

  .modal-footer {
    display: flex;
    border-top: 1rpx solid rgba(0, 0, 0, 0.1);

    button {
      flex: 1;
      border: none;
      height: 100rpx;
      line-height: 100rpx;
      font-size: 32rpx;
      border-radius: 0;

      &::after {
        border: none;
      }
    }

    .btn-cancel {
      background-color: #f5f5f5;
      color: $uni-text-color;
    }

    .btn-submit {
      background-color: $uni-color-primary;
      color: #fff;
    }
  }
}

/* 无权限提示样式 */
.no-permission {
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  padding: 0 40rpx;

  .no-permission-text {
    font-size: 32rpx;
    color: #999;
    text-align: center;
    margin-bottom: 30rpx;
  }
}

/* 确保组件不生成额外DOM */
::v-deep PermissionCheck {
  display: contents !important;
}

.recent-tasks {
  // margin-bottom: 10rpx;
  position: relative; /* 确保定位正确 */
  z-index: 1; /* 确保层级正确 */
}

.bottom-space {
  height: 20rpx; /* 底部空白区域高度 */
  width: 100%;
}

.safe-area-bottom {
  height: env(safe-area-inset-bottom, 0);
  width: 100%;
}

/* 巡检工单特殊样式 */
.patrol-tasks {
  margin-top: 30rpx;
  margin-bottom: 40rpx;
}



.patrol-more {
  color: #3aaf8f;
}

.patrol-list {
  background: linear-gradient(180deg, rgba(96, 198, 168, 0.05));
  padding: 10rpx;
  border-radius: 0 0 12rpx 12rpx;
}

.patrol-item {
  background: #fff;
  border-left: 8rpx solid #3aaf8f;
  border-radius: 4rpx 8rpx 8rpx 4rpx;
  box-shadow: 0 4rpx 16rpx rgba(58, 175, 143, 0.1);
  transition: all 0.3s ease;
  
  &:active {
    transform: translateY(2rpx);
    box-shadow: 0 2rpx 8rpx rgba(58, 175, 143, 0.1);
  }
}

.patrol-item-header {
  padding-bottom: 16rpx;
  border-bottom: 1rpx dashed rgba(58, 175, 143, 0.2);
}

.patrol-item-title {
  color: #3aaf8f;
  font-weight: 600;
}

.patrol-status {
  &.primary {
    background-color: rgba(58, 175, 143, 0.15);
    color: #3aaf8f;
  }
}

.patrol-action {
  color: #3aaf8f;
  font-weight: 600;
  background-color: rgba(58, 175, 143, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

/* 维修工单特殊样式 */
.repair-tasks {
  margin-top: 30rpx;
  margin-bottom: 40rpx;
}

.repair-more {
  color: #e05555;
}

.repair-list {
  background: linear-gradient(180deg, rgba(228, 116, 116, 0.05));
  padding: 10rpx;
  border-radius: 0 0 12rpx 12rpx;
}

.repair-item {
  background: #fff;
  border-left: 8rpx solid #e05555;
  border-radius: 4rpx 8rpx 8rpx 4rpx;
  box-shadow: 0 4rpx 16rpx rgba(224, 85, 85, 0.1);
  transition: all 0.3s ease;
  
  &:active {
    transform: translateY(2rpx);
    box-shadow: 0 2rpx 8rpx rgba(224, 85, 85, 0.1);
  }
}

.repair-item-header {
  padding-bottom: 16rpx;
  border-bottom: 1rpx dashed rgba(224, 85, 85, 0.2);
}

.repair-item-title {
  color: #e05555;
  font-weight: 600;
}

.repair-status {
  &.primary {
    background-color: rgba(224, 85, 85, 0.15);
    color: #e05555;
  }
  
  &.warning {
    background-color: rgba(250, 173, 20, 0.15);
    color: #faad14;
  }
}

.repair-action {
  color: #e05555;
  font-weight: 600;
  background-color: rgba(58, 175, 143, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}
</style>
