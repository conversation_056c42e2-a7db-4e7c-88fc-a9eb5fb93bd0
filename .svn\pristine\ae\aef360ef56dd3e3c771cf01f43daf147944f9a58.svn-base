package com.heating.dto.user;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户基本信息DTO，用于在其他响应中包含用户信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserBasicInfo {
    /**
     * 用户ID
     */
    private Long id;
    
    /**
     * 用户姓名
     */
    private String name;
    
    /**
     * 头像
     */
    private String avatar;
    
    /**
     * 部门
     */
    private String department;
} 