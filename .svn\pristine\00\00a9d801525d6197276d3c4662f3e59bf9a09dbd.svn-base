<template>
  <view class="container">
    <!-- 页面头部 -->
    <view class="header">
      <view class="header-left" @click="cancel">
        <text class="header-icon">×</text>
        <text class="header-cancel">取消</text>
      </view>
      <text class="header-title">{{ title }}</text>
      <view class="header-right" @click="confirm">
        <text class="header-confirm">确定</text>
      </view>
    </view>
    
    <!-- 搜索框 -->
    <view class="search-box">
      <view class="search-input-wrapper">
        <text class="search-icon">🔍</text>
        <input 
          type="text" 
          placeholder="搜索" 
          class="search-input"
          v-model="searchKeyword"
          @input="handleSearch"
        />
        <text 
          class="clear-icon" 
          v-if="searchKeyword" 
          @click="clearSearch"
        >×</text>
      </view>
    </view>
    
    <!-- 已选择项 -->
    <view class="selected-area" v-if="selectedItems.length > 0">
      <view class="selected-header">
        <text class="selected-title">已选择 ({{ selectedItems.length }})</text>
        <text class="selected-clear" @click="clearSelection">清空</text>
      </view>
      <scroll-view class="selected-list" scroll-x>
        <view 
          class="selected-item" 
          v-for="item in selectedItems" 
          :key="item[idField]"
          @click="toggleItem(item)"
        >
          <text class="selected-item-name">{{ item[nameField] }}</text>
          <text class="selected-item-remove">×</text>
        </view>
      </scroll-view>
    </view>
    
    <!-- 列表区域 -->
    <scroll-view 
      class="items-list" 
      scroll-y 
      :style="{ height: listHeight }"
    >
      <view 
        class="list-item" 
        v-for="item in filteredItems" 
        :key="item[idField]"
        @click="toggleItem(item)"
      >
        <view class="item-content">
          <text class="item-name">{{ item[nameField] }}</text>
          <view 
            class="checkbox" 
            :class="{ checked: isItemSelected(item) }"
          >
            <text class="checkbox-inner" v-if="isItemSelected(item)">✓</text>
          </view>
        </view>
      </view>
      
      <!-- 空列表提示 -->
      <view class="empty-tip" v-if="filteredItems.length === 0">
        <text class="empty-text">{{ searchKeyword ? '未找到匹配的结果' : '暂无可选项' }}</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 标题
      title: '多选',
      // 数据列表
      items: [],
      // 已选择的项
      selectedIds: [],
      // 名称字段
      nameField: 'name',
      // ID字段
      idField: 'id',
      // 搜索关键词
      searchKeyword: '',
      // 列表高度
      listHeight: '70vh'
    };
  },
  computed: {
    // 过滤后的列表
    filteredItems() {
      if (!this.searchKeyword) {
        return this.items;
      }
      
      const keyword = this.searchKeyword.toLowerCase();
      return this.items.filter(item => {
        const name = item[this.nameField].toLowerCase();
        return name.includes(keyword);
      });
    },
    
    // 已选择的项目
    selectedItems() {
      return this.items.filter(item => this.selectedIds.includes(item[this.idField]));
    }
  },
  onLoad() {
    // 获取传入的数据
    const eventChannel = this.getOpenerEventChannel();
    if (eventChannel) {
      eventChannel.on('initData', (data) => {
        if (data) {
          this.title = data.title || '多选';
          this.items = data.items || [];
          this.selectedIds = data.selectedIds || [];
          this.nameField = data.nameField || 'name';
          this.idField = data.idField || 'id';
        }
      });
    }
    
    // 根据屏幕高度计算列表高度
    const systemInfo = uni.getSystemInfoSync();
    const windowHeight = systemInfo.windowHeight;
    const headerHeight = 100; // 头部和搜索框的大致高度 (单位: rpx)
    const selectedAreaHeight = 160; // 已选择区域的大致高度 (单位: rpx)
    
    // 转换为 px
    const headerPx = headerHeight / 750 * systemInfo.windowWidth;
    const selectedPx = selectedAreaHeight / 750 * systemInfo.windowWidth;
    
    // 计算列表高度
    this.listHeight = `${windowHeight - headerPx - selectedPx}px`;
  },
  methods: {
    // 检查项目是否已选择
    isItemSelected(item) {
      return this.selectedIds.includes(item[this.idField]);
    },
    
    // 切换项目选择状态
    toggleItem(item) {
      const id = item[this.idField];
      const index = this.selectedIds.indexOf(id);
      
      if (index > -1) {
        // 移除选择
        this.selectedIds.splice(index, 1);
      } else {
        // 添加选择
        this.selectedIds.push(id);
      }
    },
    
    // 处理搜索
    handleSearch() {
      // 防抖处理可以在这里添加
    },
    
    // 清除搜索
    clearSearch() {
      this.searchKeyword = '';
    },
    
    // 清空选择
    clearSelection() {
      this.selectedIds = [];
    },
    
    // 取消操作
    cancel() {
      uni.navigateBack();
    },
    
    // 确认选择
    confirm() {
      // 获取事件通道
      const eventChannel = this.getOpenerEventChannel();
      
      // 传递选择结果
      if (eventChannel) {
        eventChannel.emit('selectResult', {
          selectedItems: this.selectedItems,
          selectedIds: this.selectedIds
        });
      }
      
      // 返回上一页
      uni.navigateBack();
    }
  }
};
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-bottom: 1px solid #eee;
}

.header-left,
.header-right {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 40rpx;
  color: #666;
  margin-right: 6rpx;
}

.header-cancel {
  font-size: 30rpx;
  color: #666;
}

.header-title {
  font-size: 34rpx;
  font-weight: 500;
  color: #333;
}

.header-confirm {
  font-size: 30rpx;
  color: #1890ff;
  font-weight: 500;
}

.search-box {
  padding: 20rpx 30rpx;
  background-color: #fff;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background-color: #f0f0f0;
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
}

.search-icon {
  font-size: 30rpx;
  color: #999;
  margin-right: 10rpx;
}

.search-input {
  flex: 1;
  height: 60rpx;
  font-size: 28rpx;
}

.clear-icon {
  font-size: 36rpx;
  color: #999;
  padding: 0 6rpx;
}

.selected-area {
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-bottom: 1px solid #eee;
}

.selected-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.selected-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.selected-clear {
  font-size: 28rpx;
  color: #1890ff;
}

.selected-list {
  white-space: nowrap;
  padding-bottom: 10rpx;
}

.selected-item {
  display: inline-flex;
  align-items: center;
  background-color: rgba(24, 144, 255, 0.1);
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
  margin-right: 16rpx;
}

.selected-item-name {
  font-size: 26rpx;
  color: #1890ff;
  margin-right: 6rpx;
}

.selected-item-remove {
  font-size: 28rpx;
  color: #1890ff;
}

.items-list {
  flex: 1;
  background-color: #fff;
}

.list-item {
  padding: 20rpx 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-name {
  font-size: 30rpx;
  color: #333;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkbox.checked {
  background-color: #1890ff;
  border-color: #1890ff;
}

.checkbox-inner {
  color: #fff;
  font-size: 24rpx;
}

.empty-tip {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
</style> 