package com.emm.service;

import com.emm.model.DictData;
import com.emm.repository.DictRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DictService {

    private static final Logger logger = LoggerFactory.getLogger(DictService.class);

    @Autowired
    private DictRepository dictRepository;

    public List<DictData> getDictData(Long dictId) {
        logger.info("Fetching dict data for dictId: {}", dictId);
        // 假设status和mark的默认值都是1
        Integer status = 1;
        Integer mark = 1;
        try {
            List<DictData> result = dictRepository.findByDictIdAndStatusAndMarkOrderBySortAsc(dictId, status, mark);
            logger.info("Found {} dict data entries for dictId: {}", result.size(), dictId);
            return result;
        } catch (DataAccessException e) {
            logger.error("Error accessing data for dictId: {}", dictId, e);
            throw new RuntimeException("Error accessing dict data", e);
        } catch (Exception e) {
            logger.error("Unexpected error when fetching dict data for dictId: {}", dictId, e);
            throw new RuntimeException("Unexpected error when fetching dict data", e);
        }
    }
}