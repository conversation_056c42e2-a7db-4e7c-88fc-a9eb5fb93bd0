/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.rules-container {
  padding: 0.9375rem;
  background-color: #f5f7fa;
  min-height: 100vh;
}
.page-header {
  margin-bottom: 1.25rem;
}
.page-header .page-title {
  font-size: 1.125rem;
  font-weight: bold;
  color: #333;
}
.rules-form {
  background-color: #fff;
  border-radius: 0.5rem;
  padding: 0.9375rem;
  box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.05);
}
.rules-form .form-item {
  margin-bottom: 0.9375rem;
}
.rules-form .form-item .form-label {
  font-size: 0.9375rem;
  color: #333;
  margin-bottom: 0.5rem;
}
.rules-form .form-item .form-input {
  background-color: #f5f7fa;
  border-radius: 0.25rem;
  padding: 0.625rem;
}
.rules-form .form-item .form-input uni-input {
  width: 100%;
  height: 1.875rem;
  font-size: 0.875rem;
}
.rules-form .form-item .form-input .picker-value {
  height: 1.875rem;
  line-height: 1.875rem;
  font-size: 0.875rem;
}
.rules-form .form-item .form-desc {
  font-size: 0.75rem;
  color: #999;
  margin-top: 0.3125rem;
}
.rules-form .form-actions {
  margin-top: 1.5625rem;
}
.rules-form .form-actions .btn-save {
  width: 100%;
  height: 2.8125rem;
  background: linear-gradient(135deg, #4483e5, #6a9eef);
  color: #fff;
  font-size: 1rem;
  border-radius: 1.40625rem;
  display: flex;
  justify-content: center;
  align-items: center;
}
.rules-form .form-actions .btn-save:disabled {
  background: linear-gradient(135deg, #ccc, #999);
  opacity: 0.7;
}