.container {
  padding: 30rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.user-card {
  background: linear-gradient(135deg, #4CAF50, #2196F3);
  border-radius: 16rpx;
  padding: 40rpx;
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.avatar-box {
  margin-right: 30rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.user-info {
  flex: 1;
  color: #fff;
}

.name {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
  display: block;
}

.role {
  font-size: 28rpx;
  opacity: 0.9;
}

.menu-list {
  margin-bottom: 40rpx;
}

.menu-group {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-content {
  display: flex;
  align-items: center;
}

.menu-content .wx-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  color: #4CAF50;
}

.menu-text {
  font-size: 28rpx;
  color: #333;
}

.menu-item .wx-icon:last-child {
  font-size: 24rpx;
  color: #999;
}

.logout-btn {
  margin-top: 60rpx;
}

.logout-btn button {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
  border-radius: 44rpx;
} 