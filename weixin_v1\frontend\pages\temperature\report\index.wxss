.container {
  padding: 30rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.form-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.required::after {
  content: '*';
  color: #f44336;
  margin-left: 4rpx;
}

/* 位置选择样式 */
.location-box {
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.location-info {
  flex: 1;
  margin-right: 20rpx;
}

.location-info .address {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.location-info .detail {
  font-size: 24rpx;
  color: #999;
}

.placeholder {
  font-size: 28rpx;
  color: #999;
}

/* 温度输入样式 */
.temp-input {
  display: flex;
  align-items: center;
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx;
}

.temp-input input {
  flex: 1;
  font-size: 28rpx;
}

.unit {
  font-size: 28rpx;
  color: #666;
  margin-left: 10rpx;
}

/* 图片上传样式优化 */
.upload-box {
  margin-bottom: 20rpx;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 16rpx;
}

.image-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
}

.image-item image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.delete-btn {
  position: absolute;
  top: -16rpx;
  right: -16rpx;
  background: rgba(0, 0, 0, 0.5);
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.delete-btn .wx-icon {
  font-size: 24rpx;
  color: #fff;
}

/* 新增上传按钮样式 */
.upload-actions {
  display: flex;
  gap: 20rpx;
}

.upload-btn {
  width: 160rpx;
  height: 160rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-btn .wx-icon {
  font-size: 48rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.upload-text {
  font-size: 24rpx;
  color: #999;
}

.tips {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 备注输入样式 */
.remark {
  width: 100%;
  height: 200rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
}

.word-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

/* 提交按钮样式 */
.submit-btn {
  margin-top: 40rpx;
}

.submit-btn button {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
  border-radius: 44rpx;
}

/* 选择框样式 */
.picker-box {
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
}

.picker-arrow {
  font-size: 24rpx;
  color: #999;
}

/* 选择框禁用状态 */
.picker-box.disabled {
  background: #f5f5f5;
  color: #999;
} 
 
.outdoor-temp {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx;
}

.update-time.red {
  color: #ff0000;
  font-size: 24rpx;
} 

/* 照片选择区域样式 */
.photo-section {
  margin: 32rpx 0;
}

.section-title {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 24rpx;
  font-weight: 500;
}

/* 照片操作按钮区域 */
.photo-buttons {
  display: flex;
  gap: 20rpx;  /* 按钮之间的间距 */
}

/* 单个按钮容器 */
.photo-btn {
  width: 160rpx;
  height: 160rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
 

/* 按钮点击效果 */
.photo-btn:active {
  background: #f5f5f5;
}

/* 照片预览区域 */
.photo-preview {
  margin-top: 24rpx;
  width: 100%;
  height: 360rpx;  /* 调整预览图片高度 */
  background: #f5f5f5;
  border-radius: 8rpx;
  overflow: hidden;
  position: relative;
}

.photo-preview image {
  width: 100%;
  height: 100%;
  object-fit: contain;  /* 确保图片完整显示 */
}

/* 删除按钮优化 */
.delete-btn {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 44rpx;
  height: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
}

.delete-btn image {
  width: 24rpx;
  height: 24rpx;
}
 