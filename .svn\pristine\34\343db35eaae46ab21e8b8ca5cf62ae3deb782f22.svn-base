package com.heating.service;

import com.heating.dto.attendance.*;


public interface AttendanceService {
    
    /**
     * 用户打卡
     * @param request 打卡请求
     * @return 打卡响应
     */
    AttendanceClockResponse clockIn(AttendanceClockRequest request);

    /**
     * 查询考勤记录
     * @param userId 用户ID（可选）
     * @param year 年份（可选）
     * @param month 月份（可选）
     * @param day 天数（可选）
     * @param status 状态（可选）
     * @return 考勤记录响应
     */
    AttendanceRecordResponse getAttendanceRecords(Long userId, Integer year, Integer month, Integer day, String status);
    
    /**
     * 按日期范围查询考勤记录
     * @param userId 用户ID（可选）
     * @param startDate 开始日期（格式：yyyy-MM-dd）
     * @param endDate 结束日期（格式：yyyy-MM-dd）
     * @param status 状态（可选）
     * @return 考勤记录响应
     */
    AttendanceRecordResponse getAttendanceRecordsByDateRange(Long userId, String startDate, String endDate, String status);
    
    /**
     * 查询考勤统计
     * @param userId 用户ID（可选）
     * @param year 年份（可选）
     * @param month 月份（可选）    
     * @param day 天数（可选）
     * @return 考勤统计响应
     */
    AttendanceStatsResponse getAttendanceStats(Long userId, Integer year, Integer month, Integer day);
    
    /**
     * 获取今日打卡记录
     * @param userId 用户ID
     * @return 今日打卡记录
     */
    AttendanceTodayResponse getTodayAttendance(Long userId);
    
    /**
     * 获取最近打卡记录
     * @param userId 用户ID
     * @param days 天数
     * @return 最近打卡记录
     */
    AttendanceRecentResponse getRecentAttendance(Long userId, Integer days);
    
    /**
     * 获取打卡规则
     * @return 打卡规则
     */
    AttendanceRulesResponse getAttendanceRules();
    
    /**
     * 检查考勤范围
     * @param request 考勤范围检查请求
     * @return 考勤范围检查响应
     */
    AttendanceCheckAreaResponse checkAttendanceArea(AttendanceCheckAreaRequest request);
    
    /**
     * 提交补卡申请
     * @param request 补卡申请请求
     * @return 补卡申请响应
     */
    AttendanceSupplementResponse submitSupplement(AttendanceSupplementRequest request);
    
    /**
     * 获取员工考勤列表
     * @param query 查询条件
     * @return 员工考勤列表
     */
    AttendanceStaffResponse getStaffAttendance(AttendanceStaffQuery query);
    
    /**
     * 获取部门考勤统计
     * @param query 查询条件
     * @return 部门考勤统计
     */
    AttendanceDepartmentStatsResponse getDepartmentStats(AttendanceDepartmentStatsQuery query);
    
    /**
     * 审批补卡申请
     * @param request 审批补卡申请请求
     * @return 审批补卡申请响应
     */
    AttendanceSupplementApproveResponse approveSupplement(AttendanceSupplementApproveRequest request);
    
    /**
     * 获取所有员工列表
     * @return 所有员工列表
     */
    AttendanceStaffListResponse getAllStaff();

    /**
     * 更新上下班时间设置
     * @param clockInTime 上班时间
     * @param clockOutTime 下班时间
     * @return 更新后的打卡规则
     */
    AttendanceRulesResponse updateAttendanceRules(String clockInTime, String clockOutTime);
} 