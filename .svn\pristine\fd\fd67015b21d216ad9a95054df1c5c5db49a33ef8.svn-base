package com.heating.dto.patrol;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 巡检项目类别响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PatrolCategoryResponse {
    private Long id;
    private String categoryCode;
    private String categoryName;
    private Long parentId;
    private String description;
    private Integer sortOrder;
    private Boolean isActive;
} 