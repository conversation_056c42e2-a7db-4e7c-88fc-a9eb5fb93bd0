package com.heating.service.impl;

import com.heating.dto.order.AcceptOrderRequest;
import com.heating.dto.order.CompleteOrderRequest;
import com.heating.dto.order.CreateOrderRequest;
import com.heating.dto.order.OrderAttachmentRequest;
import com.heating.dto.order.TransferOrderRequest;
import com.heating.dto.order.WorkOrderBaseInfoResponse;
import com.heating.dto.order.WorkOrderDetailResponse;
import com.heating.dto.order.WorkOrderMessageResponse;
import com.heating.entity.fault.TFaultAttachment;
import com.heating.entity.order.TOperationLog;
import com.heating.entity.order.TWorkOrder;
import com.heating.entity.order.TWorkOrderAttachment;
import com.heating.entity.order.TWorkOrderMaterial;
import com.heating.repository.OperationLogRepository;
import com.heating.repository.WorkOrderAttachmentRepository;
import com.heating.repository.WorkOrderMaterialRepository;
import com.heating.repository.WorkOrderRepository;
import com.heating.service.WorkOrderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.ArrayList;
import java.util.stream.Collectors;
import java.util.HashMap;

@Service
public class WorkOrderServiceImpl implements WorkOrderService {
    
    private static final Logger logger = LoggerFactory.getLogger(WorkOrderServiceImpl.class);
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

    @Autowired
    private WorkOrderRepository workOrderRepository;
    
    @Autowired
    private WorkOrderAttachmentRepository attachmentRepository;
    
    @Autowired
    private OperationLogRepository operationLogRepository;
    
    @Autowired
    private WorkOrderMaterialRepository workOrderMaterialRepository;
 
    /**
     * 完成工单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void completeOrder(CompleteOrderRequest request) {
        logger.info("开始处理工单完成请求: orderId={}", request.getOrderId());
        try {
            // 获取工单
            Optional<TWorkOrder> result = workOrderRepository.findById(request.getOrderId());
            if (result.isEmpty()) {
                logger.warn("工单完成失败：工单不存在 orderId={}", request.getOrderId());
                return;
            }
            // 更新工单
            TWorkOrder workOrder = result.get();
            workOrder.setOrderStatus("已完成");
            workOrder.setRepairUserId(request.getRepairUserId());
            workOrder.setRepairContent(request.getRepairContent());
            workOrder.setRepairResult(request.getRepairResult());
            
            // 处理维修耗材及数量，将Map转为JSON字符串
            if (request.getRepairMaterialsQuantity() != null && !request.getRepairMaterialsQuantity().isEmpty()) {
                try {
                    // 使用Jackson将Map转换为JSON字符串
                    com.fasterxml.jackson.databind.ObjectMapper objectMapper = new com.fasterxml.jackson.databind.ObjectMapper();
                    String materialsQuantityJson = objectMapper.writeValueAsString(request.getRepairMaterialsQuantity());
                    workOrder.setRepairMaterialsQuantity(materialsQuantityJson);
                    
                    // 保存耗材信息到工单耗材表
                    saveMaterialsToDatabase(request.getOrderId(), request.getRepairMaterialsQuantity());
                } catch (Exception e) {
                    logger.error("维修耗材及数量JSON转换失败: {}", e.getMessage());
                }
            }

            // string 转 LocalDateTime
            LocalDateTime repairTime = LocalDateTime.parse(request.getRepairTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            workOrder.setRepairTime(repairTime);
            workOrder.setUpdatedAt(LocalDateTime.now());
            workOrderRepository.save(workOrder);
            
            // 保存维修附件
            if (request.getAttachment() != null && !request.getAttachment().isEmpty()) {
                List<OrderAttachmentRequest> attachmentList = request.getAttachment();
                for (OrderAttachmentRequest attachmentRequest : attachmentList) {
                    TWorkOrderAttachment attachment = new TWorkOrderAttachment();
                    attachment.setWorkOrderId(request.getOrderId());
                    attachment.setFileType(attachmentRequest.getFileType());
                    attachment.setFilePath(attachmentRequest.getFilePath());
                    attachment.setCreatedAt(LocalDateTime.now());
                    attachmentRepository.save(attachment);
                }
            }

            // 记录操作日志
            TOperationLog log = new TOperationLog();
            log.setWorkOrderId(workOrder.getId()); 
            log.setOperationType("工单完成");
            log.setOperationDesc("维修人员完成工单");
            log.setOperatorId(workOrder.getRepairUserId());
            log.setCreatedAt(LocalDateTime.now());
            operationLogRepository.save(log);
            
            logger.info("工单处理完成: orderId={}", request.getOrderId());
        } catch (Exception e) {
            logger.error("工单处理失败: orderId={}, error={}", request.getOrderId(), e.getMessage()); 
        }
    }

    /**
     * 保存耗材信息到工单耗材表
     * 
     * @param workOrderId 工单ID
     * @param materialsQuantity 耗材及数量Map
     */
    private void saveMaterialsToDatabase(Long workOrderId, Map<String, Integer> materialsQuantity) {
        logger.info("开始保存工单耗材信息: workOrderId={}, 耗材数量={}", workOrderId, materialsQuantity.size());
        try {
            // 遍历耗材Map，保存到工单耗材表
            for (Map.Entry<String, Integer> entry : materialsQuantity.entrySet()) {
                String materialName = entry.getKey();
                Integer quantity = entry.getValue();
                
                TWorkOrderMaterial material = new TWorkOrderMaterial();
                material.setWorkOrderId(workOrderId);
                material.setMaterialName(materialName);
                material.setQuantityUsed(new BigDecimal(quantity));
                material.setRecordedAt(LocalDateTime.now());
                
                workOrderMaterialRepository.save(material);
            }
            logger.info("工单耗材信息保存成功: workOrderId={}", workOrderId);
        } catch (Exception e) {
            logger.error("保存工单耗材信息失败: workOrderId={}, error={}", workOrderId, e.getMessage());
            throw e; // 抛出异常以便事务回滚
        }
    }

    /**
     * 获取工单列表
     * @param date 日期过滤
     * @param status 状态过滤
     * @param userId 用户ID过滤
     * @param orderNo 工单号过滤
     * @param role 用户角色
     * @param limit 限制返回记录数
     * @param page 页码
     * @param pageSize 每页数量
     * @return 工单列表和分页信息
     */
    @Override
    public Map<String, Object> getWorkOrderList(Date date, String status, Long userId, String orderNo, String role, Integer limit, Integer page, Integer pageSize) {
        try {
            // 处理多角色情况（角色可能是逗号分隔的字符串）
            boolean hasAdminRole = false;
            if (role != null && !role.isEmpty()) {
                String[] roles = role.split(",");
                for (String r : roles) {
                    String roleTrim = r.trim();
                    if ("admin".equals(roleTrim) || "manage".equals(roleTrim)) {
                        hasAdminRole = true;
                        break;
                    }
                }
            }
            
            // 如果角色包含admin或manage，则不根据userId过滤（获取所有记录）
            if (hasAdminRole) {
                userId = null;
            }
            // 如果不包含admin或manage且userId为null，则返回空结果
            if (!hasAdminRole && userId == null) {
                Map<String, Object> emptyResult = new HashMap<>();
                emptyResult.put("list", new ArrayList<>());
                emptyResult.put("total", 0);
                emptyResult.put("page", page != null ? page : 1);
                emptyResult.put("pageSize", pageSize != null ? pageSize : 10);
                emptyResult.put("totalPages", 0);
                return emptyResult;
            }

            List<Map<String, Object>> results = new ArrayList<>();
            long total = 0;
            
            // 参数处理
            page = (page == null || page < 1) ? 1 : page;
            pageSize = (pageSize == null || pageSize < 1) ? 10 : pageSize;
            
            if (limit != null) {
                results = workOrderRepository.findWorkOrderListWithLimit(date, status, userId, orderNo, limit);
                total = results.size(); // 当使用limit时，总数就是结果集大小
            } else {
                // 计算偏移量
                int offset = (page - 1) * pageSize;

                // 根据用户角色选择不同的查询方法
                if (hasAdminRole) {
                    // 管理员用户：使用原有查询方法
                    results = workOrderRepository.findWorkOrderListWithPaging(date, status, userId, orderNo, offset, pageSize);
                    total = workOrderRepository.countWorkOrderList(date, status, userId, orderNo);
                } else {
                    // 非管理员用户：使用新的查询方法，返回transfer_user_id或repair_user_id匹配用户ID的工单
                    results = workOrderRepository.findWorkOrderListWithTransferInfo(date, status, userId, orderNo, offset, pageSize);
                    total = workOrderRepository.countWorkOrderListWithTransferInfo(date, status, userId, orderNo);
                }
            }

            // 计算总页数
            int totalPages = (int) Math.ceil((double) total / pageSize);
            
            // 格式化返回数据
            List<WorkOrderBaseInfoResponse> workOrders = new ArrayList<>();
            for (Map<String, Object> result : results) {
                WorkOrderBaseInfoResponse response = new WorkOrderBaseInfoResponse();
                response.setOrderId((Long) result.get("orderId"));
                response.setOrderNo((String) result.get("orderNo"));
                response.setHeatUnitName((String) result.get("heatUnitName"));
                response.setFaultType((String) result.get("faultType"));
                response.setFaultLevel((String) result.get("faultLevel"));
                response.setOrderStatus((String) result.get("orderStatus"));
                response.setCreatedTime((String) result.get("createdTime"));
                
                // 如果结果中包含转派相关字段，则设置到返回对象中
                if (result.containsKey("repairUserId")) {
                    response.setRepairUserId((Long) result.get("repairUserId"));
                }
                if (result.containsKey("transferUserId")) {
                    response.setTransferUserId((Integer) result.get("transferUserId"));
                }
                
                workOrders.add(response);
            }
            
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("list", workOrders);
            result.put("total", total);
            result.put("page", page);
            result.put("pageSize", pageSize);
            result.put("totalPages", totalPages);
            
            logger.info("查询工单列表完成: 当前页={}, 每页数量={}, 总记录数={}, 总页数={}", 
                       page, pageSize, total, totalPages);
            return result;
        } catch (Exception e) {
            logger.error("查询工单列表失败: error={}", e.getMessage());
            throw new RuntimeException("获取工单列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取工单详情
     */
    @Override
    public WorkOrderDetailResponse getWorkOrderDetail(long orderId) {
        try {
            Optional<Map<String, Object>> result = workOrderRepository.findWorkOrderDetail(orderId); 
            if (result.isEmpty()) {   
                logger.warn("工单详情查询失败：工单不存在 orderId={}", orderId);
                return null;
            } 
            Map<String, Object> workOrderMap = result.get();

            List<TFaultAttachment> faultAttachmentList = workOrderRepository.findFaultAttachments(orderId); 
             // 遍历faultAttachmentList，将每个TFaultAttachment转换为Attachments
             List<WorkOrderDetailResponse.AttachmentDto> faultAttachments = faultAttachmentList.stream().map(attachment -> {
                 WorkOrderDetailResponse.AttachmentDto attachments = new WorkOrderDetailResponse.AttachmentDto(attachment.getFileType(), attachment.getFilePath()); 
                return attachments;
             }).collect(Collectors.toList());

            List<TWorkOrderAttachment> workOrderAttachmentList = workOrderRepository.findWorkOrderAttachments(orderId);
             // 遍历workOrderAttachmentList，将每个TWorkOrderAttachment转换为Attachments
             List<WorkOrderDetailResponse.AttachmentDto> workOrderAttachments = workOrderAttachmentList.stream().map(attachment -> {
                WorkOrderDetailResponse.AttachmentDto attachments = new WorkOrderDetailResponse.AttachmentDto(attachment.getFileType(), attachment.getFilePath());
                return attachments;
             }).collect(Collectors.toList());

             // 遍历operationLogsList，将每个TOperationLog转换为OperationLogs
             List<TOperationLog> operationLogsList = workOrderRepository.findOperationLogs(orderId);
             List<WorkOrderDetailResponse.OperationLogDto> operationLogs = operationLogsList.stream().map(log -> {
                WorkOrderDetailResponse.OperationLogDto operationLog = new WorkOrderDetailResponse.OperationLogDto(log.getOperationType(), 
                log.getOperationDesc(),
                log.getOperatorName(), 
                log.getCreatedAt().format(DATE_TIME_FORMATTER) );
                return operationLog;
             }).collect(Collectors.toList());
 
            WorkOrderDetailResponse workOrderDetail = new WorkOrderDetailResponse(
                (String) workOrderMap.get("orderNo"),
                (Long) workOrderMap.get("faultId"),
                (String) workOrderMap.get("heatUnitName"),
                (Long) workOrderMap.get("repairUserId"),
                (String) workOrderMap.get("repairUserName"),
                (Integer) workOrderMap.get("transferUserId"),
                (String) workOrderMap.get("transferUserName"),
                (String) workOrderMap.get("transferReason"),
                (String) workOrderMap.get("transferTime"),
                (String) workOrderMap.get("repairContent"),
                (String) workOrderMap.get("repairResult"),
                (String) workOrderMap.get("address"),
                (String) workOrderMap.get("orderStatus"),
                (String) workOrderMap.get("faultType"),
                (String) workOrderMap.get("faultLevel"),
                (String) workOrderMap.get("faultDesc"),
                (String) workOrderMap.get("repairTime"),
                processRepairMaterialsQuantity((String) workOrderMap.get("repairMaterialsQuantity")),
                (String) workOrderMap.get("createdTime"),
                (String) workOrderMap.get("updatedTime"), 
                faultAttachments,
                workOrderAttachments,
                operationLogs
            );

            return workOrderDetail;
        } catch (Exception e) {
            logger.error("查询工单详情失败: orderId={}, error={}", orderId, e.getMessage());
            return null;
        }
    }
     
    /**
     * 创建工单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createWorkOrder(CreateOrderRequest request) {
        logger.info("开始创建工单: orderNo={}", request.getOrderNo());
        // 检查工单是否存在
        if (workOrderRepository.findByOrderNo(request.getOrderNo()).isPresent()) {
            throw new RuntimeException("工单已存在: " + request.getOrderNo());
        }   
        try {
            // 创建工单对象
            TWorkOrder workOrder = new TWorkOrder();
            workOrder.setOrderNo(request.getOrderNo());
            workOrder.setFaultId(request.getFaultId());
            workOrder.setRepairUserId(request.getRepairUserId());
            workOrder.setRepairContent(request.getRepairContent());
            workOrder.setOrderStatus(request.getOrderStatus());
            workOrder.setCreatedAt(LocalDateTime.now());
            workOrder.setUpdatedAt(LocalDateTime.now());
            
            // 保存工单
            workOrderRepository.save(workOrder);
            
            // 记录操作日志
            TOperationLog log = new TOperationLog();
            log.setWorkOrderId(workOrder.getId()); 
            log.setOperationType("工单创建");
            log.setOperationDesc("创建新工单");
            log.setOperatorId(workOrder.getRepairUserId()); // 假设创建者是维修人员
            log.setCreatedAt(LocalDateTime.now());
            operationLogRepository.save(log);
            
            logger.info("工单创建成功: id={}, orderNo={}", workOrder.getId(), workOrder.getOrderNo());
        } catch (Exception e) {
            logger.error("工单创建失败: orderNo={}, error={}", request.getOrderNo(), e.getMessage());
            throw new RuntimeException("工单创建失败: " + e.getMessage());
        }
    }

    

    @Override
    public void updateWorkOrderStatus(AcceptOrderRequest request) {
        logger.info("开始处理工单状态修改请求: orderId={}", request.getOrderId());  
        try {
            // 获取工单
            Optional<TWorkOrder> result = workOrderRepository.findById(request.getOrderId());
            if (result.isEmpty()) {
                logger.warn("工单状态修改失败：工单不存在 orderId={}", request.getOrderId());
                return;
            }

            // 更新工单状态
            TWorkOrder workOrder = result.get();
            workOrder.setOrderStatus(request.getOrderStatus());
            workOrder.setRepairUserId(request.getRepairUserId());
            workOrder.setUpdatedAt(LocalDateTime.now());
            workOrderRepository.save(workOrder);
            
            // 记录操作日志
            TOperationLog log = new TOperationLog();
            log.setWorkOrderId(workOrder.getId());
            log.setOperationType("工单状态修改");
            log.setOperationDesc("维修人员修改工单状态:" + request.getOrderStatus());
            log.setOperatorId(workOrder.getRepairUserId());
            log.setCreatedAt(LocalDateTime.now());
            operationLogRepository.save(log);

            logger.info("工单状态修改成功: orderId={}", request.getOrderId());  
        } catch (Exception e) {
            logger.error("工单状态修改失败: orderId={}, error={}", request.getOrderId(), e.getMessage());
        }
    }

    /**
     * 处理维修耗材数量JSON字符串，转换为Map
     */
    private Map<String, Integer> processRepairMaterialsQuantity(String repairMaterialsQuantityJson) {
        if (repairMaterialsQuantityJson == null || repairMaterialsQuantityJson.isEmpty()) {
            return null;
        }
        
        try {
            com.fasterxml.jackson.databind.ObjectMapper objectMapper = new com.fasterxml.jackson.databind.ObjectMapper();
            return objectMapper.readValue(repairMaterialsQuantityJson, 
                   new com.fasterxml.jackson.core.type.TypeReference<Map<String, Integer>>() {});
        } catch (Exception e) {
            logger.error("维修耗材数量JSON解析失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 获取工单消息（待接单状态的工单）
     * 从工单信息表中获取状态为'待接单'的记录列表
     * @param userId 用户ID
     * @param role 用户角色
     * @return 待接单的工单消息列表
     */
    @Override
    public List<WorkOrderMessageResponse> getWorkOrderMessages(Long userId, String role) {
        logger.info("开始获取工单消息（待接单状态的工单）: userId={}, role={}", userId, role);
        try {
            // 处理多角色情况（角色可能是逗号分隔的字符串）
            boolean hasAdminRole = false;
            if (role != null && !role.isEmpty()) {
                String[] roles = role.split(",");
                for (String r : roles) {
                    String roleTrim = r.trim();
                    if ("admin".equals(roleTrim) || "manage".equals(roleTrim)) {
                        hasAdminRole = true;
                        break;
                    }
                }
            }
            
            // 根据角色判断查询方式
            List<Map<String, Object>> pendingOrders;
            
            // 如果角色包含admin或manage，则获取所有记录
            if (hasAdminRole) {
                pendingOrders = workOrderRepository.findWorkOrdersByStatus("待接单");
            } 
            // 否则根据userId过滤
            else if (userId != null) {
                pendingOrders = workOrderRepository.findWorkOrdersByStatusAndUserId("待接单", userId);
            }
            // 如果不包含admin或manage且userId为null，则返回空列表
            else {
                logger.info("非管理员用户且未提供用户ID，返回空列表");
                return new ArrayList<>();
            }
            
            // 将查询结果转换为DTO
            List<WorkOrderMessageResponse> messages = new ArrayList<>();
            for (Map<String, Object> order : pendingOrders) {
                WorkOrderMessageResponse message = new WorkOrderMessageResponse(
                    (Long) order.get("id"),
                    (String) order.get("faultSource"),
                    (String) order.get("faultDesc"),
                    (String) order.get("orderNo"),
                    // 转换时间格式
                    order.get("createdTime") instanceof String 
                        ? LocalDateTime.parse((String) order.get("createdTime"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"))
                        : (LocalDateTime) order.get("createdTime"),
                    order.get("updatedTime") instanceof String
                        ? LocalDateTime.parse((String) order.get("updatedTime"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"))
                        : (LocalDateTime) order.get("updatedTime")
                );
                messages.add(message);
            }
            
            logger.info("获取工单消息成功，共找到{}条待接单工单", messages.size());
            return messages;
        } catch (Exception e) {
            logger.error("获取工单消息失败: {}", e.getMessage());
            throw new RuntimeException("获取工单消息失败: " + e.getMessage());
        }
    }

    /**
     * 转派工单
     * @param request 转派工单请求
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transferOrder(TransferOrderRequest request) {
        logger.info("开始处理工单转派请求: orderId={}", request.getOrderId());
        try {
            // 获取工单
            Optional<TWorkOrder> result = workOrderRepository.findById(request.getOrderId());
            if (result.isEmpty()) {
                logger.warn("工单转派失败：工单不存在 orderId={}", request.getOrderId());
                throw new RuntimeException("工单不存在");
            }
            
            // 更新工单
            TWorkOrder workOrder = result.get();
            
            // 更新转派相关字段
            workOrder.setRepairUserId(request.getRepairUserId());
            workOrder.setTransferUserId(request.getTransferUserId().intValue());
            workOrder.setTransferReason(request.getTransferReason());   
            workOrder.setTransferTime(LocalDateTime.now());
            workOrder.setUpdatedAt(LocalDateTime.now());
            
            // 保存工单
            workOrderRepository.save(workOrder);
            
            // 记录操作日志
            TOperationLog log = new TOperationLog();
            log.setWorkOrderId(workOrder.getId());
            log.setOperationType("工单转派");
            log.setOperationDesc("工单已转派给ID为" + request.getRepairUserId() + "的维修人员");
            log.setOperatorId(request.getTransferUserId());
            log.setCreatedAt(LocalDateTime.now());
            operationLogRepository.save(log);
            
            logger.info("工单转派成功: orderId={}, 转派人ID={}, 目标维修人员ID={}", 
                request.getOrderId(), request.getTransferUserId(), request.getRepairUserId());
        } catch (Exception e) {
            logger.error("工单转派失败: orderId={}, error={}", request.getOrderId(), e.getMessage());
            throw e; // 抛出异常以便事务回滚
        }
    }

} 