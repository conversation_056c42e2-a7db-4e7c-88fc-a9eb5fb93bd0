package com.heating.dto.temperature;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

@Data
public class RoomTemperatureRequest {

    @JsonProperty("heat_unit_name")
    private String heatUnitName;

    @JsonProperty("building_no")
    private String buildingNo;

    @JsonProperty("unit_no")
    private String unitNo;

    @JsonProperty("room_no")
    private String roomNo;

    @JsonProperty("indoor_temp")
    private Double indoorTemp;

    @JsonProperty("outdoor_temp")
    private Double outdoorTemp;

    @JsonProperty("latitude")
    private Double latitude;

    @JsonProperty("longitude")
    private Double longitude;

    @JsonProperty("images")
    private List<String> images;

    @JsonProperty("videos")
    private List<String> videos; 

    @JsonProperty("remark")
    private String remark;

    @JsonProperty("report_user_id")
    private Long reportUserId;  
}