<view class="container">
  <!-- 日期选择 -->
  <!-- 新增筛选区域 -->
  <view class="filter-section">
    <view class="filter-row">
      <!-- 日期选择 -->
      <!-- <picker mode="date" value="{{selectedDate}}" bindchange="bindDateChange">
        <view class="date-picker">
          <text class="value">{{selectedDate || '选择日期'} }}</text> 
        </view>
      </picker> -->

       <!-- 日期筛选 -->
      <picker mode="date" bindchange="bindDateChange">
        <view class="date-picker">
          <text class="value">{{selectedDate || '选择日期'}}</text>
        </view>
      </picker>

      <!-- 筛选小区 -->
      <picker bindchange="selectCommunity" range="{{communities}}" range-key="name">
        <view class="filter-type">
          <text class="value">{{selectedCommunity.name || '请选择小区'}}</text>
        </view>
      </picker> 
    </view> 
  </view>

  <!-- 温度统计卡片 -->
  <view class="temp-stats-card">
    <view class="stats-row">
      <view class="stat-item">
        <text class="label">平均温度</text>
        <text class="value">{{stats.avgTemp}}°C</text>
      </view>
      <view class="stat-item">
        <text class="label">最高温度</text>
        <text class="value high">{{stats.maxTemp}}°C</text>
      </view>
    </view>
    <view class="stats-row">
      <view class="stat-item">
        <text class="label">最低温度</text>
        <text class="value low">{{stats.minTemp}}°C</text>
      </view>
      <view class="stat-item">
        <text class="label">采集数量</text>
        <text class="value">{{stats.count}}户</text>
      </view>
    </view>
  </view>

  <!-- 温度列表 -->
  <view class="temp-list">
    <view class="temp-item" 
          wx:for="{{temperatures}}" 
          wx:key="id"
          bindtap="goToDetail"
          data-id="{{item.id}}">
      <view class="room-info">
        <text>{{item.community_name}}</text>
        <text>{{item.building_no}}栋 {{item.unit_no}}单元 {{item.room_no}}室</text>
      </view>
      <view class="temp-info">
        <text class="temp {{item.status}}">{{item.indoor_temp}}°C</text>
        <text class="time">{{item.report_time}}</text>
      </view>
      <text class="remark" wx:if="{{item.remark}}">备注：{{item.remark}}</text>
    </view>
  </view>
</view> 