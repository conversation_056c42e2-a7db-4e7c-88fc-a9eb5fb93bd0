const fs = require('fs');
const path = require('path');

const filePath = path.join(process.cwd(), 'pages/patrol/create.vue');

// Read the file line by line
fs.readFile(filePath, 'utf8', (err, data) => {
  if (err) {
    console.error('Error reading file:', err);
    return;
  }

  // Split into lines
  const lines = data.split('\n');
  
  // Find the getImportanceClass method
  let importanceIndex = -1;
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].includes('getImportanceClass(importance)')) {
      importanceIndex = i;
      break;
    }
  }
  
  if (importanceIndex > 0) {
    // Look for the closing braces
    for (let i = importanceIndex; i < lines.length; i++) {
      if (lines[i].trim() === '}' && lines[i+1].trim() === '}' && lines[i+2].trim() === '}') {
        // Add the comma after the first closing brace
        lines[i] = lines[i] + ',';
        console.log('Added comma after getImportanceClass method');
        break;
      }
    }
  }
  
  // Find the end of the style section
  let styleEndIndex = -1;
  for (let i = lines.length - 1; i >= 0; i--) {
    if (lines[i].trim() === '</style>') {
      styleEndIndex = i;
      break;
    }
  }
  
  if (styleEndIndex > 0) {
    // Remove extra closing brace before </style>
    if (lines[styleEndIndex-1].trim() === '}' && lines[styleEndIndex-2].trim() === '' && lines[styleEndIndex-3].trim() === '}') {
      // Remove the extra closing brace
      lines.splice(styleEndIndex-1, 1);
      console.log('Removed extra closing brace in style section');
    }
  }
  
  // Write the file back
  fs.writeFile(filePath, lines.join('\n'), 'utf8', (err) => {
    if (err) {
      console.error('Error writing file:', err);
      return;
    }
    console.log('File fixed successfully!');
  });
}); 