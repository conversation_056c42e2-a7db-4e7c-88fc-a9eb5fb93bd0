package com.emm.model;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "room_temperature")
public class RoomTemperature {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "community_name")
    private String communityName;
    
    @Column(name = "building_no")
    private String buildingNo;
    
    @Column(name = "unit_no")
    private String unitNo;
    
    @Column(name = "room_no")
    private String roomNo;
    
    @Column(name = "indoor_temp")
    private Double indoorTemp;
    
    @Column(name = "outdoor_temp")
    private Double outdoorTemp;
    
    @Column
    private String latitude;
    
    @Column
    private String longitude;
    
    @Column(name = "report_time")
    private LocalDateTime reportTime;
    
    @Column(name = "image_url")
    private String imageUrl;
    
    @Column(name = "video_url")
    private String videoUrl;

    @Column(name = "remark")
    private String remark;

    // Getters
    public Long getId() {
        return id;
    }

    public String getCommunityName() {
        return communityName;
    }

    public String getBuildingNo() {
        return buildingNo;
    }

    public String getUnitNo() {
        return unitNo;
    }

    public String getRoomNo() {
        return roomNo;
    }

    public Double getIndoorTemp() {
        return indoorTemp;
    }

    public Double getOutdoorTemp() {
        return outdoorTemp;
    }

    public String getLatitude() {
        return latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public LocalDateTime getReportTime() {
        return reportTime;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public String getRemark() {
        return remark;
    }


    // Setters
    public void setId(Long id) {
        this.id = id;
    }

    public void setCommunityName(String communityName) {
        this.communityName = communityName;
    }

    public void setBuildingNo(String buildingNo) {
        this.buildingNo = buildingNo;
    }

    public void setUnitNo(String unitNo) {
        this.unitNo = unitNo;
    }

    public void setRoomNo(String roomNo) {
        this.roomNo = roomNo;
    }

    public void setIndoorTemp(Double indoorTemp) {
        this.indoorTemp = indoorTemp;
    }

    public void setOutdoorTemp(Double outdoorTemp) {
        this.outdoorTemp = outdoorTemp;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public void setReportTime(LocalDateTime reportTime) {
        this.reportTime = reportTime;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // equals 方法
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        RoomTemperature that = (RoomTemperature) o;

        if (id != null ? !id.equals(that.id) : that.id != null) return false;
        if (communityName != null ? !communityName.equals(that.communityName) : that.communityName != null) return false;
        if (buildingNo != null ? !buildingNo.equals(that.buildingNo) : that.buildingNo != null) return false;
        if (unitNo != null ? !unitNo.equals(that.unitNo) : that.unitNo != null) return false;
        if (roomNo != null ? !roomNo.equals(that.roomNo) : that.roomNo != null) return false;
        if (indoorTemp != null ? !indoorTemp.equals(that.indoorTemp) : that.indoorTemp != null) return false;
        if (outdoorTemp != null ? !outdoorTemp.equals(that.outdoorTemp) : that.outdoorTemp != null) return false;
        if (latitude != null ? !latitude.equals(that.latitude) : that.latitude != null) return false;
        if (longitude != null ? !longitude.equals(that.longitude) : that.longitude != null) return false;
        if (reportTime != null ? !reportTime.equals(that.reportTime) : that.reportTime != null) return false;
        if (imageUrl != null ? !imageUrl.equals(that.imageUrl) : that.imageUrl != null) return false;
        return videoUrl != null ? videoUrl.equals(that.videoUrl) : that.videoUrl == null;
    }

    // hashCode 方法
    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (communityName != null ? communityName.hashCode() : 0);
        result = 31 * result + (buildingNo != null ? buildingNo.hashCode() : 0);
        result = 31 * result + (unitNo != null ? unitNo.hashCode() : 0);
        result = 31 * result + (roomNo != null ? roomNo.hashCode() : 0);
        result = 31 * result + (indoorTemp != null ? indoorTemp.hashCode() : 0);
        result = 31 * result + (outdoorTemp != null ? outdoorTemp.hashCode() : 0);
        result = 31 * result + (latitude != null ? latitude.hashCode() : 0);
        result = 31 * result + (longitude != null ? longitude.hashCode() : 0);
        result = 31 * result + (reportTime != null ? reportTime.hashCode() : 0);
        result = 31 * result + (imageUrl != null ? imageUrl.hashCode() : 0);
        result = 31 * result + (videoUrl != null ? videoUrl.hashCode() : 0);
        return result;
    }

    // toString 方法
    @Override
    public String toString() {
        return "RoomTemperature{" +
                "id=" + id +
                ", communityName='" + communityName + '\'' +
                ", buildingNo='" + buildingNo + '\'' +
                ", unitNo='" + unitNo + '\'' +
                ", roomNo='" + roomNo + '\'' +
                ", indoorTemp=" + indoorTemp +
                ", outdoorTemp=" + outdoorTemp +
                ", latitude='" + latitude + '\'' +
                ", longitude='" + longitude + '\'' +
                ", reportTime=" + reportTime +
                ", imageUrl='" + imageUrl + '\'' +
                ", videoUrl='" + videoUrl + '\'' +
                ", remark='" + remark + '\'' +
                '}';
    }
}