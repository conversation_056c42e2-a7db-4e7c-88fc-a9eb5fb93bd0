<template>
	<view class="all-records-container">
		<!-- 顶部筛选栏 -->
		<view class="filter-bar">
			<view class="date-range-selector" @click="openDatePicker">
				<text>{{ displayDateRange }}</text>
				<uni-icons type="bottom" size="14" color="#666"></uni-icons>
			</view>
			<view class="staff-selector" @click="openStaffSelector">
				<text>{{ selectedStaff ? selectedStaff.name : '全部人员' }}</text>
				<uni-icons type="bottom" size="14" color="#666"></uni-icons>
			</view>
			<view class="status-selector" @click="openStatusSelector">
				<text>{{ selectedStatus ? statusTypes[selectedStatus].label : '全部状态' }}</text>
				<uni-icons type="bottom" size="14" color="#666"></uni-icons>
			</view>
		</view>
		
		<!-- 搜索栏 -->
		<view class="search-bar">
			<uni-icons type="search" size="16" color="#999"></uni-icons>
			<input type="text" v-model="searchKeyword" placeholder="搜索人员姓名" />
			<text v-if="searchKeyword" @click="clearSearch" class="clear-btn">清除</text>
		</view>
		
		<!-- 统计信息 -->
		<view class="statistics-info">
			<text>共 {{ totalRecords }} 条记录</text>
			<!-- <view class="status-count">
				<view class="status-item">
					<text class="normal">正常: {{ statistics.normalCount }}</text>
				</view>
				<view class="status-item">
					<text class="late">迟到: {{ statistics.lateCount }}</text>
				</view>
				<view class="status-item">
					<text class="early">早退: {{ statistics.earlyCount }}</text>
				</view>
				<view class="status-item">
					<text class="absent">缺勤: {{ statistics.absentCount }}</text>
				</view>
			</view> -->
		</view>
		
		<!-- 记录列表 -->
		<view class="records-list">
			<view v-if="records.length > 0">
				<view v-for="(group, index) in groupedRecords" :key="index" class="record-group">
					<view class="group-header">
						<text class="date">{{ group.date }}</text>
						<text class="week">{{ group.week }}</text>
					</view>
					<view v-for="(record, recordIndex) in group.records" :key="recordIndex" class="record-item">
						<view class="staff-info">
							<image :src="record.avatar" mode="aspectFill" class="avatar"></image>
							<view class="staff-details">
								<text class="staff-name">{{ record.staffName }}</text>
								<text class="staff-dept">{{ record.department }}</text>
							</view>
						</view>
						<view class="record-details">
							<view class="time-record">
								<view class="time-item">
									<text class="time-label">上班打卡</text>
									<text class="time-value" :class="{'abnormal': record.clockInStatus !== 'normal'}">
										{{ record.clockInTime }}
									</text>
									<text v-if="record.clockInStatus === 'late'" class="status-tag late">迟到</text>
								</view>
								<view class="time-item">
									<text class="time-label">下班打卡</text>
									<text class="time-value" :class="{'abnormal': record.clockOutStatus !== 'normal'}">
										{{ record.clockOutTime }}
									</text>
									<text v-if="record.clockOutStatus === 'early'" class="status-tag early">早退</text>
								</view>
							</view>
							<view class="location-info">
								<text class="location-label">打卡地点</text>
								<text class="location-value">{{ record.location }}</text>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 加载更多 -->
				<view v-if="hasMoreData" class="load-more" @click="loadMoreRecords">
					<text>加载更多</text>
				</view>
				<view v-else class="no-more">
					<text>没有更多数据了</text>
				</view>
			</view>
			<view v-else class="empty-records">
				<image src="/static/images/empty.png" mode="aspectFit"></image>
				<text>暂无考勤记录</text>
			</view>
		</view>
		
		<!-- 日期选择器弹窗 -->
		<uni-popup ref="datePopup" type="bottom">
			<view class="date-popup">
				<view class="popup-header">
					<text>选择日期范围</text>
					<text @click="closeDatePicker" class="close-btn">关闭</text>
				</view>
				<view class="date-options">
					<view 
						v-for="(option, index) in dateOptions" 
						:key="index" 
						class="date-option" 
						:class="{ active: dateRange === option.value }"
						@click="selectDateRange(option.value)">
						<text>{{ option.label }}</text>
						<uni-icons v-if="dateRange === option.value" type="checkmarkempty" size="18" color="#007AFF"></uni-icons>
					</view>
					<view class="custom-date-range">
						<text>自定义日期范围</text>
						<view class="date-inputs">
							<picker mode="date" :value="customDateRange.start" @change="onStartDateChange" class="date-picker">
								<view class="date-input">
									<text>{{ customDateRange.start ? customDateRange.start : '开始日期' }}</text>
								</view>
							</picker>
							<text class="date-separator">至</text>
							<picker mode="date" :value="customDateRange.end" @change="onEndDateChange" class="date-picker">
								<view class="date-input">
									<text>{{ customDateRange.end ? customDateRange.end : '结束日期' }}</text>
								</view>
							</picker>
						</view>
						<view class="custom-date-actions">
							<button class="btn-apply" @click="applyCustomDateRange" :disabled="!isCustomDateRangeValid">应用</button>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
		
		<!-- 人员选择器弹窗 -->
		<uni-popup ref="staffPopup" type="bottom">
			<view class="staff-popup">
				<view class="popup-header">
					<text>选择人员</text>
					<text @click="closeStaffSelector" class="close-btn">关闭</text>
				</view>
				<view class="search-box">
					<uni-icons type="search" size="16" color="#999"></uni-icons>
					<input type="text" v-model="staffSearchKeyword" placeholder="搜索人员姓名" />
				</view>
				<scroll-view scroll-y="true" class="staff-list">
					<view class="staff-item" @click="selectStaff(null)">
						<view class="staff-info">
							<text class="staff-name">全部人员</text>
						</view>
						<uni-icons v-if="!selectedStaff" type="checkmarkempty" size="18" color="#007AFF"></uni-icons>
					</view>
					<view 
						v-for="(staff, index) in filteredStaffList" 
						:key="index" 
						class="staff-item"
						@click="selectStaff(staff)">
						<view class="staff-info">
							<text class="staff-name">{{ staff.name }}</text>
						</view>
						<uni-icons 
							v-if="selectedStaff && selectedStaff.id === staff.id" 
							type="checkmarkempty" 
							size="18" 
							color="#007AFF">
						</uni-icons>
					</view>
				</scroll-view>
			</view>
		</uni-popup>
		
		<!-- 状态选择器弹窗 -->
		<uni-popup ref="statusPopup" type="bottom">
			<view class="status-popup">
				<view class="popup-header">
					<text>选择状态</text>
					<text @click="closeStatusSelector" class="close-btn">关闭</text>
				</view>
				<view class="status-list">
					<view class="status-item" @click="selectStatus(null)">
						<text>全部状态</text>
						<uni-icons v-if="!selectedStatus" type="checkmarkempty" size="18" color="#007AFF"></uni-icons>
					</view>
					<view 
						v-for="(status, key) in statusTypes" 
						:key="key"
						class="status-item"
						@click="selectStatus(key)">
						<text>{{ status.label }}</text>
						<uni-icons 
							v-if="selectedStatus === key" 
							type="checkmarkempty" 
							size="18" 
							color="#007AFF">
						</uni-icons>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import { attendanceApi, userApi } from '@/utils/api.js';

export default {
	data() {
		return {
			dateRange: 'thisMonth',
			customDateRange: {
				start: '',
				end: ''
			},
			dateOptions: [
				{ label: '本月', value: 'thisMonth' },
				{ label: '上月', value: 'lastMonth' },
				{ label: '近7天', value: 'last7Days' },
				{ label: '近30天', value: 'last30Days' }
			],
			selectedStaff: null,
			staffSearchKeyword: '',
			staffList: [],
			selectedStatus: null,
			statusTypes: {
				normal: { label: '正常', color: '#4cd964' },
				late: { label: '迟到', color: '#f0ad4e' },
				early: { label: '早退', color: '#5bc0de' },
				absent: { label: '缺勤', color: '#dd524d' }
			},
			searchKeyword: '',
			currentDatePickerType: 'start',
			page: 1,
			pageSize: 20,
			hasMoreData: true,
			totalRecords: 0,
			statistics: {
				normalCount: 0,
				lateCount: 0,
				earlyCount: 0,
				absentCount: 0
			},
			records: [],
			isLoading: false
		};
	},
	computed: {
		displayDateRange() {
			switch (this.dateRange) {
				case 'thisMonth':
					return '本月';
				case 'lastMonth':
					return '上月';
				case 'last7Days':
					return '近7天';
				case 'last30Days':
					return '近30天';
				case 'custom':
					return `${this.customDateRange.start} 至 ${this.customDateRange.end}`;
				default:
					return '选择日期';
			}
		},
		filteredStaffList() {
			if (!this.staffSearchKeyword) return this.staffList;
			
			return this.staffList.filter(staff => 
				staff.name.includes(this.staffSearchKeyword) || 
				(staff.department && staff.department.includes(this.staffSearchKeyword))
			);
		},
		groupedRecords() {
			const groups = {};
			
			this.records.forEach(record => {
				if (!groups[record.date]) {
					groups[record.date] = {
						date: record.date,
						week: record.week,
						records: []
					};
				}
				groups[record.date].records.push(record);
			});
			
			return Object.values(groups);
		},
		isCustomDateRangeValid() {
			return this.customDateRange.start && this.customDateRange.end;
		}
	},
	onLoad() {
		this.loadStaffList();
		this.loadRecords();
	},
	methods: {
		// 加载员工列表
		loadStaffList() {
			// 先尝试使用考勤API获取员工列表
			attendanceApi.getAllStaff().then(res => {
				console.log('获取员工列表结果:', res);
				if (res.code === 200 && res.data && res.data.length > 0) {
					this.staffList = res.data;
				} else {
					// 如果考勤API没有返回员工列表或返回空列表，尝试使用用户API
					this.loadStaffListFallback();
				}
			}).catch(err => {
				console.error('获取员工列表失败:', err);
				// 降级方案：使用用户API获取人员列表
				this.loadStaffListFallback();
			});
		},
		
		// 降级方案：使用用户API获取员工列表
		loadStaffListFallback() {
			userApi.getInspectorList().then(userRes => {
				console.log('降级获取员工列表结果:', userRes);
				if (userRes.code === 200 && userRes.data) {
					// 转换数据结构以匹配考勤API的格式
					this.staffList = userRes.data.map(user => ({
						id: user.id,
						name: user.name,
						department: user.department || '未知部门'
					}));
				} else {
					// 如果API都失败，使用默认数据
					this.staffList = [
						{ id: '001', name: '张工', department: '运维部' },
						{ id: '002', name: '李工', department: '工程部' },
						{ id: '003', name: '王工', department: '管理部' },
						{ id: '004', name: '赵工', department: '技术部' },
						{ id: '005', name: '刘工', department: '客服部' }
					];
				}
			}).catch(err => {
				console.error('降级获取员工列表失败:', err);
				// 使用默认数据
				this.staffList = [
					{ id: '001', name: '张工', department: '运维部' },
					{ id: '002', name: '李工', department: '工程部' },
					{ id: '003', name: '王工', department: '管理部' },
					{ id: '004', name: '赵工', department: '技术部' },
					{ id: '005', name: '刘工', department: '客服部' }
				];
			});
		},
		
		// 获取日期范围参数
		getDateRangeParams() {
			const now = new Date();
			const year = now.getFullYear();
			const month = now.getMonth() + 1;
			const day = now.getDate();
			
			let startDate = '';
			let endDate = '';
			
			switch (this.dateRange) {
				case 'thisMonth':
					startDate = `${year}-${String(month).padStart(2, '0')}-01`;
					endDate = this.getLastDayOfMonth(year, month);
					break;
				case 'lastMonth':
					const lastMonth = month === 1 ? 12 : month - 1;
					const lastMonthYear = month === 1 ? year - 1 : year;
					startDate = `${lastMonthYear}-${String(lastMonth).padStart(2, '0')}-01`;
					endDate = this.getLastDayOfMonth(lastMonthYear, lastMonth);
					break;
				case 'last7Days':
					const last7Date = new Date(now);
					last7Date.setDate(day - 6);
					startDate = this.formatDate(last7Date);
					endDate = this.formatDate(now);
					break;
				case 'last30Days':
					const last30Date = new Date(now);
					last30Date.setDate(day - 29);
					startDate = this.formatDate(last30Date);
					endDate = this.formatDate(now);
					break;
				case 'custom':
					startDate = this.customDateRange.start;
					endDate = this.customDateRange.end;
					break;
			}
			
			return { startDate, endDate };
		},
		
		// 获取月份的最后一天
		getLastDayOfMonth(year, month) {
			const lastDay = new Date(year, month, 0).getDate();
			return `${year}-${String(month).padStart(2, '0')}-${String(lastDay).padStart(2, '0')}`;
		},
		
		// 格式化日期为YYYY-MM-DD
		formatDate(date) {
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			return `${year}-${month}-${day}`;
		},
		
		// 加载考勤记录
		loadRecords() {
			if (this.isLoading) return;
			
			this.isLoading = true;
			uni.showLoading({
				title: '加载数据中...'
			});
			
			// 准备查询参数
			const { startDate, endDate } = this.getDateRangeParams();
			
			const params = {
				page: this.page,
				pageSize: this.pageSize,
				startDate,
				endDate
			};
			
			console.log('查询参数:', params);
			
			// 添加人员ID参数
			if (this.selectedStaff) {
				params.userId = this.selectedStaff.id;
			}
			
			// 添加状态参数
			if (this.selectedStatus) {
				params.status = this.selectedStatus;
			}
			
			// 添加搜索关键词
			if (this.searchKeyword) {
				params.keyword = this.searchKeyword;
			}
			
			// 调用考勤API获取记录
			attendanceApi.getRecords(params).then(res => {
				if (res.code === 200 && res.data) {
					// 解析返回的数据
					const summary = res.data.summary || {};
					this.statistics = {
						normalCount: summary.workdays ? (summary.workdays - summary.late - summary.absent - summary.early) : 0,
						lateCount: summary.late || 0,
						earlyCount: summary.early || 0,
						absentCount: summary.absent || 0
					};
					
					this.totalRecords = res.data.records ? res.data.records.length : 0;
					
					// 格式化记录数据
					const formattedRecords = this.processAttendanceRecords(res.data.records || []);
					
					// 第一页数据直接赋值，加载更多时追加
					if (this.page === 1) {
						this.records = formattedRecords;
					} else {
						this.records = [...this.records, ...formattedRecords];
					}
					
					// 判断是否还有更多数据
					this.hasMoreData = formattedRecords.length === this.pageSize;
				} else {
					uni.showToast({
						title: res.message || '获取考勤记录失败',
						icon: 'none'
					});
				}
			}).catch(err => {
				console.error('获取考勤记录失败:', err);
				uni.showToast({
					title: '获取考勤记录失败',
					icon: 'none'
				});
			}).finally(() => {
				uni.hideLoading();
				this.isLoading = false;
			});
		},
		
		// 处理考勤记录数据
		processAttendanceRecords(records) {
			if (!Array.isArray(records) || records.length === 0) {
				return [];
			}
			
			// 检查记录格式并适配
			const firstRecord = records[0];
			
			// 如果记录已包含上下班打卡时间字段，直接使用
			if (firstRecord.clockInTime !== undefined || firstRecord.clockOutTime !== undefined) {
				return records.map(item => {
					// 获取用户信息
					const user = item.user || {};
					
					return {
						id: item.id,
						date: item.date || this.formatDateFromString(item.clockTime || item.createTime),
						week: item.week || this.formatWeekFromString(item.clockTime || item.createTime),
						staffName: user.name || '未知',
						department: user.department || '未知部门',
						avatar: user.avatar || '/static/images/avatar.jpg',
						clockInTime: item.clockInTime || '未打卡',
						clockInStatus: item.clockInStatus || 'normal',
						clockOutTime: item.clockOutTime || '未打卡',
						clockOutStatus: item.clockOutStatus || 'normal',
						location: item.location || this.formatLocation(item)
					};
				});
			}
			
			// 处理旧格式数据（向后兼容）
			// 按日期和用户ID分组
			const recordsByDateAndUser = {};
			
			records.forEach(record => {
				if (!record.clockTime) {
					console.warn('记录缺少clockTime字段:', record);
					return;
				}
				
				const date = new Date(record.clockTime);
				const dateStr = date.toISOString().split('T')[0]; // YYYY-MM-DD
				const userId = record.userId || (record.user ? record.user.id : 'unknown');
				const key = `${dateStr}-${userId}`;
				
				if (!recordsByDateAndUser[key]) {
					// 获取用户信息
					const user = record.user || {};
					
					recordsByDateAndUser[key] = {
						id: record.id,
						date: this.formatDateFromString(record.clockTime),
						week: this.formatWeekFromString(record.clockTime),
						staffName: user.name || record.userName || '未知',
						department: user.department || record.department || '未知部门',
						avatar: user.avatar || record.avatar || '/static/images/avatar.jpg',
						clockInTime: '未打卡',
						clockOutTime: '未打卡',
						clockInStatus: 'normal',
						clockOutStatus: 'normal',
						userId: userId,
						location: record.location || (record.latitude && record.longitude ? 
							`${record.latitude.toFixed(6)}, ${record.longitude.toFixed(6)}` : '未知位置')
					};
				}
				
				// 根据打卡类型设置上下班时间
				if (record.clockType === 'checkin') {
					recordsByDateAndUser[key].clockInTime = this.formatTime(record.clockTime);
					recordsByDateAndUser[key].clockInStatus = record.status || 'normal';
				} else if (record.clockType === 'checkout') {
					recordsByDateAndUser[key].clockOutTime = this.formatTime(record.clockTime);
					recordsByDateAndUser[key].clockOutStatus = record.status || 'normal';
				}
			});
			
			// 转换为数组并返回
			return Object.values(recordsByDateAndUser);
		},
		
		// 格式化时间
		formatTime(timeString) {
			if (!timeString) return '未打卡';
			
			try {
				const date = new Date(timeString);
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				return `${hours}:${minutes}`;
			} catch (e) {
				return timeString;
			}
		},
		
		// 加载更多记录
		loadMoreRecords() {
			if (!this.hasMoreData || this.isLoading) return;
			
			this.page++;
			this.loadRecords();
		},
		
		// 搜索相关
		clearSearch() {
			this.searchKeyword = '';
			this.page = 1;
			this.loadRecords();
		},
		
		// 日期选择相关
		openDatePicker() {
			this.$refs.datePopup.open();
		},
		
		closeDatePicker() {
			this.$refs.datePopup.close();
		},
		
		selectDateRange(range) {
			this.dateRange = range;
			this.closeDatePicker();
			this.page = 1;
			this.loadRecords();
		},
		
		onStartDateChange(e) {
			this.customDateRange.start = e.detail.value;
		},
		
		onEndDateChange(e) {
			this.customDateRange.end = e.detail.value;
		},
		
		applyCustomDateRange() {
			this.dateRange = 'custom';
			this.closeDatePicker();
			this.page = 1;
			this.loadRecords();
		},
		
		// 人员选择相关
		openStaffSelector() {
			this.$refs.staffPopup.open();
		},
		
		closeStaffSelector() {
			this.$refs.staffPopup.close();
		},
		
		selectStaff(staff) {
			this.selectedStaff = staff;
			this.closeStaffSelector();
			this.page = 1;
			this.loadRecords();
		},
		
		// 状态选择相关
		openStatusSelector() {
			this.$refs.statusPopup.open();
		},
		
		closeStatusSelector() {
			this.$refs.statusPopup.close();
		},
		
		selectStatus(status) {
			this.selectedStatus = status;
			this.closeStatusSelector();
			this.page = 1;
			this.loadRecords();
		},
		
		// 从字符串格式化日期为YYYY-MM-DD
		formatDateFromString(dateString) {
			if (!dateString) return '';
			try {
				const date = new Date(dateString);
				return this.formatDate(date);
			} catch (e) {
				console.error('日期格式化错误:', e);
				return '';
			}
		},
		
		// 从字符串格式化星期
		formatWeekFromString(dateString) {
			if (!dateString) return '';
			try {
				const date = new Date(dateString);
				const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
				return weekDays[date.getDay()];
			} catch (e) {
				console.error('星期格式化错误:', e);
				return '';
			}
		},
		
		// 格式化位置信息
		formatLocation(record) {
			if (!record) return '未知位置';
			if (record.location) return record.location;
			if (record.latitude && record.longitude) {
				return `${record.latitude.toFixed(6)}, ${record.longitude.toFixed(6)}`;
			}
			return '未知位置';
		}
	}
};
</script>

<style lang="scss">
.all-records-container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

// 顶部筛选栏
.filter-bar {
	display: flex;
	justify-content: space-between;
	padding: 20rpx 0;
	
	.date-range-selector, .staff-selector, .status-selector {
		flex: 1;
		background-color: #fff;
		padding: 15rpx 20rpx;
		border-radius: 10rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 26rpx;
		margin-right: 10rpx;
		box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05);
		
		&:last-child {
			margin-right: 0;
		}
		
		text {
			width: 80%;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}
	}
}

// 搜索栏
.search-bar {
	background-color: #fff;
	border-radius: 10rpx;
	padding: 15rpx 20rpx;
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05);
	
	input {
		flex: 1;
		height: 60rpx;
		margin: 0 10rpx;
		font-size: 28rpx;
	}
	
	.clear-btn {
		font-size: 26rpx;
		color: #999;
	}
}

// 统计信息
.statistics-info {
	background-color: #fff;
	border-radius: 10rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
	display: flex;
	flex-direction: column;
	box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05);
	
	text {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 10rpx;
	}
	
	.status-count {
		display: flex;
		flex-wrap: wrap;
		
		.status-item {
			margin-right: 20rpx;
			
			text {
				font-size: 24rpx;
				
				&.normal {
					color: #4cd964;
				}
				
				&.late {
					color: #f0ad4e;
				}
				
				&.early {
					color: #5bc0de;
				}
				
				&.absent {
					color: #dd524d;
				}
			}
		}
	}
}

// 记录列表
.records-list {
	background-color: #fff;
	border-radius: 10rpx;
	padding: 20rpx;
	box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05);
	
	.record-group {
		margin-bottom: 30rpx;
		
		.group-header {
			display: flex;
			align-items: center;
			padding: 15rpx 10rpx;
			border-bottom: 1rpx solid #f5f5f5;
			background-color: #f9f9f9;
			border-radius: 8rpx 8rpx 0 0;
			
			.date {
				font-size: 28rpx;
				font-weight: bold;
				color: #333;
			}
			
			.week {
				font-size: 24rpx;
				color: #666;
				margin-left: 10rpx;
				background-color: #eaeaea;
				padding: 2rpx 10rpx;
				border-radius: 4rpx;
			}
		}
		
		.record-item {
			display: flex;
			flex-direction: column;
			padding: 20rpx 10rpx;
			border-bottom: 1rpx solid #f5f5f5;
			
			&:last-child {
				border-bottom: none;
			}
			
			.staff-info {
				display: flex;
				align-items: center;
				margin-bottom: 20rpx;
				
				.avatar {
					width: 80rpx;
					height: 80rpx;
					border-radius: 50%;
					border: 2rpx solid #eaeaea;
					box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
				}
				
				.staff-details {
					margin-left: 20rpx;
					
					.staff-name {
						font-size: 30rpx;
						font-weight: bold;
						color: #333;
					}
					
					.staff-dept {
						font-size: 24rpx;
						color: #666;
						margin-top: 5rpx;
						background-color: #f5f5f5;
						display: inline-block;
						padding: 2rpx 10rpx;
						border-radius: 4rpx;
					}
				}
			}
			
			.record-details {
				padding-left: 100rpx;
				
				.time-record {
					display: flex;
					flex-wrap: wrap;
					
					.time-item {
						display: flex;
						align-items: center;
						margin-right: 30rpx;
						margin-bottom: 15rpx;
						
						.time-label {
							font-size: 26rpx;
							color: #666;
							margin-right: 10rpx;
							background-color: #f9f9f9;
							padding: 4rpx 10rpx;
							border-radius: 4rpx;
						}
						
						.time-value {
							font-size: 28rpx;
							color: #333;
							font-weight: 500;
							
							&.abnormal {
								color: #f0ad4e;
							}
						}
						
						.status-tag {
							font-size: 22rpx;
							padding: 4rpx 12rpx;
							border-radius: 20rpx;
							margin-left: 10rpx;
							
							&.late {
								background-color: #fef0e5;
								color: #f0ad4e;
								border: 1rpx solid #f0ad4e;
							}
							
							&.early {
								background-color: #e5f5fa;
								color: #5bc0de;
								border: 1rpx solid #5bc0de;
							}
						}
					}
				}
				
				.location-info {
					display: flex;
					flex-wrap: wrap;
					align-items: center;
					margin-top: 5rpx;
					
					.location-label {
						font-size: 26rpx;
						color: #666;
						margin-right: 10rpx;
						background-color: #f9f9f9;
						padding: 4rpx 10rpx;
						border-radius: 4rpx;
					}
					
					.location-value {
						font-size: 26rpx;
						color: #666;
						flex: 1;
						word-break: break-all;
					}
				}
			}
		}
	}
	
	.load-more, .no-more {
		text-align: center;
		padding: 30rpx 0;
		
		text {
			font-size: 26rpx;
			color: #999;
		}
	}
	
	.empty-records {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 60rpx 0;
		
		image {
			width: 200rpx;
			height: 200rpx;
			margin-bottom: 20rpx;
		}
		
		text {
			font-size: 28rpx;
			color: #999;
		}
	}
}

// 弹窗样式
.date-popup, .staff-popup, .status-popup {
	background-color: #fff;
	border-top-left-radius: 20rpx;
	border-top-right-radius: 20rpx;
	
	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #f5f5f5;
		
		text {
			font-size: 32rpx;
			font-weight: bold;
		}
		
		.close-btn {
			font-size: 28rpx;
			color: #007AFF;
		}
	}
}

// 日期选择器
.date-popup {
	.date-options {
		padding: 0 30rpx 30rpx;
		
		.date-option {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 20rpx 0;
			border-bottom: 1rpx solid #f5f5f5;
			
			text {
				font-size: 30rpx;
				color: #333;
			}
			
			&.active text {
				color: #007AFF;
			}
		}
		
		.custom-date-range {
			padding: 20rpx 0;
			
			text {
				font-size: 30rpx;
				color: #333;
				display: block;
				margin-bottom: 15rpx;
			}
			
			.date-inputs {
				display: flex;
				align-items: center;
				
				.date-picker {
					flex: 1;
				}
				
				.date-input {
					background-color: #f5f5f5;
					padding: 15rpx 20rpx;
					border-radius: 10rpx;
					
					text {
						font-size: 28rpx;
						color: #666;
						margin-bottom: 0;
					}
				}
				
				.date-separator {
					margin: 0 20rpx;
					font-size: 28rpx;
					color: #999;
				}
			}
			
			.custom-date-actions {
				margin-top: 20rpx;
				display: flex;
				justify-content: flex-end;
				
				.btn-apply {
					background-color: #007AFF;
					color: #fff;
					font-size: 28rpx;
					padding: 10rpx 30rpx;
					border-radius: 8rpx;
					
					&:disabled {
						background-color: #cccccc;
					}
				}
			}
		}
	}
}

// 人员选择器
.staff-popup {
	.search-box {
		display: flex;
		align-items: center;
		background-color: #f5f5f5;
		margin: 20rpx 30rpx;
		padding: 10rpx 20rpx;
		border-radius: 10rpx;
		
		input {
			flex: 1;
			height: 60rpx;
			margin-left: 10rpx;
			font-size: 28rpx;
		}
	}
	
	.staff-list {
		max-height: 600rpx;
		padding: 0 15rpx;
		
		.staff-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 20rpx 15rpx;
			border-bottom: 1rpx solid #f5f5f5;
			margin-bottom: 5rpx;
			border-radius: 8rpx;
			
			&:active {
				background-color: #f9f9f9;
			}
			
			.staff-info {
				display: flex;
				flex-direction: column;
				
				.staff-name {
					font-size: 30rpx;
					color: #333;
					font-weight: 500;
				}
				
				.staff-dept {
					font-size: 24rpx;
					color: #666;
					margin-top: 6rpx;
					background-color: #f5f5f5;
					display: inline-block;
					padding: 2rpx 10rpx;
					border-radius: 4rpx;
					max-width: 400rpx;
				}
			}
		}
	}
}

// 状态选择器
.status-popup {
	.status-list {
		padding-bottom: 30rpx;
		
		.status-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 20rpx 30rpx;
			border-bottom: 1rpx solid #f5f5f5;
			
			text {
				font-size: 30rpx;
				color: #333;
			}
		}
	}
}
</style> 