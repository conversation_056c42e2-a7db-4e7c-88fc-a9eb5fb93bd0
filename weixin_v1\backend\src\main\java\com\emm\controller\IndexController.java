package com.emm.controller;

import com.emm.service.IndexService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api")
public class IndexController {

    @Autowired
    private IndexService indexService;

    @GetMapping("/stats")
    public ResponseEntity<?> getStats() {
        try {
            Map<String, Object> stats = indexService.getStats();
            Map<String, Object> stats_response = new HashMap<>();
            stats_response.put("stats", stats); 
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", stats_response);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/recent-faults")
    public ResponseEntity<?> getRecentFaults() {
        try {
            List<Map<String, Object>> faults = indexService.getRecentFaults();
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", faults);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/station-temps")
    public ResponseEntity<?> getStationTemps() {
        try {
            Map<String, Object> stationTemps = indexService.getStationTemps();
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", stationTemps);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/community-temps")
    public ResponseEntity<?> getCommunityTemps() {
        try {
            Map<String, List<Map<String, Object>>> communityTemps = indexService.getCommunityTemps();
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", communityTemps);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/communities")
    public ResponseEntity<?> getCommunities() {
        try {
            List<Map<String, Object>> communities = indexService.getCommunities();
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", communities);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}