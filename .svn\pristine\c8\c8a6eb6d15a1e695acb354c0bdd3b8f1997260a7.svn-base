<template>
	<view class="hes-detail-container">
		<!-- 基本信息 -->
		<view class="detail-card">
			<view class="card-header">
				<text class="card-title">基本信息</text>
				<view class="hes-status" :class="stationInfo.status">{{ getStatusText(stationInfo.status) }}</view>
			</view>
			
			<view class="info-item">
				<text class="info-label">换热站名称</text>
				<text class="info-value">{{ stationInfo.name }}</text>
			</view>
			<view class="info-item">
				<text class="info-label">换热站编号</text>
				<text class="info-value">{{ stationInfo.id }}</text>
			</view>
			<view class="info-item">
				<text class="info-label">所在地址</text>
				<text class="info-value">{{ stationInfo.address }}</text>
			</view>
			<view class="info-item">
				<text class="info-label">管理人员</text>
				<text class="info-value">{{ stationInfo.manager || '未分配' }}</text>
			</view>
			<view class="info-item">
				<text class="info-label">联系电话</text>
				<text class="info-value">{{ stationInfo.phone || '未设置' }}</text>
			</view>
			<view class="info-item">
				<text class="info-label">投入使用</text>
				<text class="info-value">{{ formatDate(stationInfo.startDate) }}</text>
			</view>
		</view>
		
		<!-- 运行数据 -->
		<view class="detail-card">
			<view class="card-header">
				<text class="card-title">运行数据</text>
				<text class="refresh-btn" @click="refreshData">刷新</text>
			</view>
			
			<view class="data-grid">
				<view class="data-item" v-for="(item, index) in runningData" :key="index">
					<view class="data-value" :class="{ warning: item.isWarning, danger: item.isDanger }">
						{{ item.value }}
					</view>
					<view class="data-label">{{ item.label }}</view>
				</view>
			</view>
			
			<view class="data-chart">
				<view class="chart-header">
					<text class="chart-title">温度趋势 (24小时)</text>
					<view class="chart-legend">
						<view class="legend-item">
							<view class="legend-color supply"></view>
							<text class="legend-text">供水温度</text>
						</view>
						<view class="legend-item">
							<view class="legend-color return"></view>
							<text class="legend-text">回水温度</text>
						</view>
					</view>
				</view>
				
				<!-- 图表区域，实际项目中使用echart等图表库 -->
				<view class="chart-placeholder">
					<text>温度趋势图</text>
				</view>
			</view>
		</view>
		
		<!-- 供热范围 -->
		<view class="detail-card">
			<view class="card-header">
				<text class="card-title">供热范围</text>
			</view>
			
			<view class="heating-area">
				<view class="area-info">
					<view class="area-item">
						<text class="area-label">供热面积</text>
						<text class="area-value">{{ stationInfo.heatingArea || 0 }} m²</text>
					</view>
					<view class="area-item">
						<text class="area-label">覆盖小区</text>
						<text class="area-value">{{ stationInfo.coverCommunities || 0 }} 个</text>
					</view>
					<view class="area-item">
						<text class="area-label">覆盖用户</text>
						<text class="area-value">{{ stationInfo.coverUsers || 0 }} 户</text>
					</view>
				</view>
				
				<view class="community-list">
					<view class="community-item" v-for="(item, index) in communities" :key="index">
						<text class="community-name">{{ item.name }}</text>
						<text class="community-address">{{ item.address }}</text>
						<text class="community-units">{{ item.buildingCount }}栋楼 {{ item.userCount }}户</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 设备列表 -->
		<view class="detail-card">
			<view class="card-header">
				<text class="card-title">关联设备</text>
				<text class="view-all" @click="navigateToDeviceList">查看全部</text>
			</view>
			
			<view class="device-list">
				<view class="device-item" v-for="(device, index) in devices" :key="index" @click="navigateToDeviceDetail(device.deviceId)">
					<view class="device-status" :class="device.status"></view>
					<view class="device-info">
						<view class="device-name">{{ device.name }}</view>
						<view class="device-type">{{ device.type }}</view>
					</view>
					<view class="device-alarm" v-if="device.alarmCount > 0">
						<text class="alarm-count">{{ device.alarmCount }}</text>
					</view>
					<text class="iconfont icon-arrow-right"></text>
				</view>
				
				<view class="empty-tip" v-if="devices.length === 0">
					<text>暂无关联设备</text>
				</view>
			</view>
		</view>
		
		<!-- 告警记录 -->
		<view class="detail-card" v-if="alarms.length > 0">
			<view class="card-header">
				<text class="card-title">最近告警</text>
				<text class="view-all" @click="navigateToAlarmList">查看全部</text>
			</view>
			
			<view class="alarm-list">
				<view class="alarm-item" v-for="(alarm, index) in alarms" :key="index">
					<view class="alarm-level" :class="alarm.level"></view>
					<view class="alarm-content">
						<view class="alarm-title">{{ alarm.title }}</view>
						<view class="alarm-time">{{ alarm.time }}</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 操作按钮 -->
		<view class="action-buttons">
			<view class="action-btn" @click="navigateToControl">
				<text class="iconfont icon-control"></text>
				<text>远程控制</text>
			</view>
			<view class="action-btn" @click="navigateToReport">
				<text class="iconfont icon-chart"></text>
				<text>运行报表</text>
			</view>
			<view class="action-btn" @click="navigateToMaintenance">
				<text class="iconfont icon-maintain"></text>
				<text>维护记录</text>
			</view>
		</view>
	</view>
</template>

<script>
import { heatingStationApi } from '@/utils/api.js';

export default {
	data() {
		return {
			stationId: '',
			stationDetail: {}, // 原始API返回的详情数据
			stationInfo: {
				id: '',
				name: '',
				status: 'normal',
				address: '',
				manager: '',
				phone: '',
				startDate: '',
				heatingArea: 0,
				coverCommunities: 0,
				coverUsers: 0
			},
			runningData: [],
			communities: [],
			devices: [],
			alarms: [],
			isLoading: false
		}
	},
	onLoad(options) {
		// 获取路由参数中的站点ID
		this.stationId = options.id;
		
		// 从上一个页面获取详情数据（如果有）
		const eventChannel = this.getOpenerEventChannel();
		eventChannel.on('acceptStationDetail', (data) => {
			console.log('从列表页接收到详情数据:', data);
			this.stationDetail = data;
			this.parseStationDetail();
		});
		
		// 如果没有通过事件通道传递数据，则主动加载
		if (!this.stationDetail || !this.stationDetail.basic_info) {
			this.loadStationDetail();
		}
	},
	methods: {
		// 加载换热站详情
		loadStationDetail() {
			this.isLoading = true;
			uni.showLoading({ title: '加载中...' });
			
			heatingStationApi.getDetail(this.stationId)
				.then(res => {
					console.log('换热站详情API返回:', res);
					if (res.code === 200 && res.data) {
						this.stationDetail = res.data;
						this.parseStationDetail();
					} else {
						uni.showToast({
							title: res.message || '获取详情失败',
							icon: 'none'
						});
					}
				})
				.catch(err => {
					console.error('获取换热站详情失败:', err);
					uni.showToast({
						title: '获取详情失败，请重试',
						icon: 'none'
					});
				})
				.finally(() => {
					this.isLoading = false;
					uni.hideLoading();
				});
		},
		
		// 解析站点详情数据
		parseStationDetail() {
			if (!this.stationDetail) return;
			
			const basicInfo = this.stationDetail.basic_info || {};
			const realtimeData = this.stationDetail.realtime_data || {};
			
			// 更新基本信息
			this.stationInfo = {
				id: basicInfo.id || this.stationId,
				name: basicInfo.name || '',
				status: basicInfo.status || 'normal',
				address: basicInfo.address || '',
				manager: basicInfo.manager_name || '',
				phone: basicInfo.contact_phone || '',
				startDate: basicInfo.installation_date || '',
				heatingArea: basicInfo.heating_area || 0,
				coverCommunities: basicInfo.community_count || 0,
				coverUsers: basicInfo.user_count || 0
			};
			
			// 更新运行数据
			this.parseRunningData(realtimeData);
			
			// 解析设备状态
			this.parseDevices(realtimeData?.equipment_status);
			
			// 解析告警信息
			this.loadAlarms();
			
			// 解析小区信息
			this.loadCommunities();
		},
		
		// 解析运行数据
		parseRunningData(realtimeData) {
			if (!realtimeData) return;
			
			const primary = realtimeData.primary_system || {};
			const secondary = realtimeData.secondary_system || {};
			
			this.runningData = [
				{
					label: '一次供水温度',
					value: primary.supply_temp ? `${primary.supply_temp}°C` : '--',
					isWarning: primary.supply_temp > 95,
					isDanger: primary.supply_temp > 105
				},
				{
					label: '一次回水温度',
					value: primary.return_temp ? `${primary.return_temp}°C` : '--',
					isWarning: primary.return_temp > 80,
					isDanger: primary.return_temp > 90
				},
				{
					label: '二次供水温度',
					value: secondary.supply_temp ? `${secondary.supply_temp}°C` : '--',
					isWarning: secondary.supply_temp > 75,
					isDanger: secondary.supply_temp > 85
				},
				{
					label: '二次回水温度',
					value: secondary.return_temp ? `${secondary.return_temp}°C` : '--',
					isWarning: secondary.return_temp > 65,
					isDanger: secondary.return_temp > 75
				},
				{
					label: '一次供水压力',
					value: primary.supply_pressure ? `${primary.supply_pressure}MPa` : '--',
					isWarning: primary.supply_pressure > 0.8,
					isDanger: primary.supply_pressure > 1.0
				},
				{
					label: '一次回水压力',
					value: primary.return_pressure ? `${primary.return_pressure}MPa` : '--',
					isWarning: primary.return_pressure > 0.5,
					isDanger: primary.return_pressure > 0.7
				}
			];
			
			// 可以继续添加其他运行数据
			if (primary.flow_rate) {
				this.runningData.push({
					label: '流量',
					value: `${primary.flow_rate}m³/h`,
					isWarning: false,
					isDanger: false
				});
			}
			
			if (primary.power) {
				this.runningData.push({
					label: '热负荷',
					value: `${(primary.power / 1000).toFixed(2)}MW`,
					isWarning: primary.power > 1800,
					isDanger: primary.power > 2000
				});
			}
		},
		
		// 解析设备数据
		parseDevices(equipmentStatus) {
			if (!equipmentStatus) return;
			
			this.devices = [];
			
			// 处理泵设备
			if (equipmentStatus.pumps && equipmentStatus.pumps.length > 0) {
				equipmentStatus.pumps.forEach((pump, index) => {
					this.devices.push({
						deviceId: pump.id || `pump_${index + 1}`,
						name: pump.name || `水泵 ${index + 1}`,
						type: '泵',
						status: pump.status === 'running' ? 'online' : (pump.status === 'fault' ? 'fault' : 'offline'),
						alarmCount: 0 // 告警数可以从另外的接口获取
					});
				});
			}
			
			// 处理阀门设备
			if (equipmentStatus.valves && equipmentStatus.valves.length > 0) {
				equipmentStatus.valves.forEach((valve, index) => {
					this.devices.push({
						deviceId: valve.id || `valve_${index + 1}`,
						name: valve.name || `阀门 ${index + 1}`,
						type: '阀门',
						status: valve.status === 'normal' ? 'online' : 'fault',
						alarmCount: 0
					});
				});
			}
		},
		
		// 加载社区列表
		loadCommunities() {
			// 如果API中包含社区信息，可以从API数据中解析
			// 暂时使用模拟数据，实际项目中应该调用API
			if (this.stationDetail && this.stationDetail.communities) {
				this.communities = this.stationDetail.communities.map(item => ({
					id: item.id,
					name: item.name,
					address: item.address,
					buildingCount: item.building_count,
					userCount: item.user_count
				}));
			} else {
				// 如果需要单独获取社区数据，可以在这里调用相关API
				// 例如: heatingStationApi.getCommunities(this.stationId)
				//       .then(res => { this.communities = res.data; })
				this.communities = [];
			}
		},
		
		// 加载告警记录
		loadAlarms() {
			// 调用告警API获取最新告警
			heatingStationApi.getAlarmList({ hes_id: this.stationId, limit: 5 })
				.then(res => {
					if (res.code === 200 && res.data && res.data.list) {
						this.alarms = res.data.list.map(alarm => ({
							id: alarm.id,
							title: alarm.alarm_name,
							level: this.getAlarmLevel(alarm.level),
							time: alarm.start_time,
							deviceId: alarm.device_id
						}));
					}
				})
				.catch(err => {
					console.error('获取告警列表失败:', err);
					this.alarms = [];
				});
		},
		
		// 获取告警级别
		getAlarmLevel(level) {
			if (level === 'urgent' || level === 'critical') return 'error';
			if (level === 'warning' || level === 'notice') return 'warning';
			return 'warning'; // 默认警告级别
		},
		
		// 刷新数据
		refreshData() {
			uni.showLoading({
				title: '刷新中...'
			});
			
			this.loadStationDetail();
		},
		
		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				'online': '在线',
				'offline': '离线',
				'normal': '正常',
				'warning': '异常',
				'fault': '故障'
			};
			return statusMap[status] || '未知';
		},
		
		// 格式化日期
		formatDate(dateString) {
			if (!dateString) return '未知';
			
			try {
				const date = new Date(dateString);
				return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
			} catch (e) {
				console.error('日期格式化错误:', e);
				return dateString;
			}
		},
		
		// 导航到设备列表
		navigateToDeviceList() {
			uni.navigateTo({
				url: `/pages/device/list?stationId=${this.stationId}`
			});
		},
		
		// 导航到设备详情
		navigateToDeviceDetail(deviceId) {
			uni.navigateTo({
				url: `/pages/device/detail?id=${deviceId}`
			});
		},
		
		// 导航到告警列表
		navigateToAlarmList() {
			uni.navigateTo({
				url: `/pages/hes/alarms?id=${this.stationId}`
			});
		},
		
		// 导航到远程控制页面
		navigateToControl() {
			uni.navigateTo({
				url: `/pages/hes/control?id=${this.stationId}`
			});
		},
		
		// 导航到运行报表页面
		navigateToReport() {
			uni.navigateTo({
				url: `/pages/hes/report?id=${this.stationId}`
			});
		},
		
		// 导航到维护记录页面
		navigateToMaintenance() {
			uni.navigateTo({
				url: `/pages/hes/maintenance?id=${this.stationId}`
			});
		}
	}
}
</script>

<style lang="scss">
	.hes-detail-container {
		padding: 20rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
	}
	
	.detail-card {
		background-color: #fff;
		border-radius: 12rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
		
		.card-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 24rpx;
			
			.card-title {
				font-size: 32rpx;
				font-weight: bold;
				color: $uni-text-color;
				position: relative;
				padding-left: 20rpx;
				
				&::before {
					content: '';
					position: absolute;
					left: 0;
					top: 8rpx;
					width: 6rpx;
					height: 28rpx;
					background-color: $uni-color-primary;
					border-radius: 3rpx;
				}
			}
			
			.hes-status {
				padding: 4rpx 16rpx;
				font-size: 24rpx;
				border-radius: 20rpx;
				
				&.normal {
					background-color: rgba(82, 196, 26, 0.1);
					color: $uni-color-success;
				}
				
				&.warning {
					background-color: rgba(250, 173, 20, 0.1);
					color: $uni-color-warning;
				}
				
				&.fault {
					background-color: rgba(245, 34, 45, 0.1);
					color: $uni-color-error;
				}
			}
			
			.refresh-btn, .view-all {
				font-size: 26rpx;
				color: $uni-color-primary;
			}
		}
	}
	
	.info-item {
		display: flex;
		margin-bottom: 20rpx;
		
		.info-label {
			width: 180rpx;
			font-size: 28rpx;
			color: $uni-text-color-grey;
		}
		
		.info-value {
			flex: 1;
			font-size: 28rpx;
			color: $uni-text-color;
		}
	}
	
	.data-grid {
		display: flex;
		flex-wrap: wrap;
		margin-bottom: 30rpx;
		
		.data-item {
			width: 33.33%;
			text-align: center;
			margin-bottom: 30rpx;
			
			.data-value {
				font-size: 36rpx;
				font-weight: bold;
				color: $uni-text-color;
				margin-bottom: 8rpx;
				
				&.warning {
					color: $uni-color-warning;
				}
				
				&.danger {
					color: $uni-color-error;
				}
			}
			
			.data-label {
				font-size: 26rpx;
				color: $uni-text-color-grey;
			}
		}
	}
	
	.data-chart {
		border-top: 1rpx solid #eee;
		padding-top: 20rpx;
		
		.chart-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 20rpx;
			
			.chart-title {
				font-size: 28rpx;
				font-weight: bold;
			}
			
			.chart-legend {
				display: flex;
				
				.legend-item {
					display: flex;
					align-items: center;
					margin-left: 20rpx;
					
					.legend-color {
						width: 20rpx;
						height: 6rpx;
						margin-right: 8rpx;
						
						&.supply {
							background-color: $uni-color-primary;
						}
						
						&.return {
							background-color: $uni-color-warning;
						}
					}
					
					.legend-text {
						font-size: 24rpx;
						color: $uni-text-color-grey;
					}
				}
			}
		}
		
		.chart-placeholder {
			height: 300rpx;
			background-color: #f8f8f8;
			border-radius: 8rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			
			text {
				font-size: 28rpx;
				color: $uni-text-color-grey;
			}
		}
	}
	
	.heating-area {
		.area-info {
			display: flex;
			margin-bottom: 30rpx;
			
			.area-item {
				flex: 1;
				text-align: center;
				
				.area-label {
					font-size: 26rpx;
					color: $uni-text-color-grey;
					margin-bottom: 8rpx;
					display: block;
				}
				
				.area-value {
					font-size: 32rpx;
					color: $uni-text-color;
					font-weight: bold;
				}
			}
		}
		
		.community-list {
			border-top: 1rpx solid #eee;
			padding-top: 20rpx;
			
			.community-item {
				display: flex;
				flex-direction: column;
				padding: 20rpx 0;
				border-bottom: 1rpx solid #eee;
				
				&:last-child {
					border-bottom: none;
				}
				
				.community-name {
					font-size: 28rpx;
					font-weight: bold;
					margin-bottom: 8rpx;
				}
				
				.community-address {
					font-size: 26rpx;
					color: $uni-text-color-grey;
					margin-bottom: 8rpx;
				}
				
				.community-units {
					font-size: 26rpx;
					color: $uni-text-color;
				}
			}
		}
	}
	
	.device-list {
		.device-item {
			display: flex;
			align-items: center;
			padding: 20rpx 0;
			border-bottom: 1rpx solid #eee;
			
			&:last-child {
				border-bottom: none;
			}
			
			.device-status {
				width: 12rpx;
				height: 12rpx;
				border-radius: 50%;
				margin-right: 16rpx;
				
				&.online {
					background-color: $uni-color-success;
				}
				
				&.offline {
					background-color: $uni-text-color-grey;
				}
				
				&.fault {
					background-color: $uni-color-error;
				}
			}
			
			.device-info {
				flex: 1;
				
				.device-name {
					font-size: 28rpx;
					color: $uni-text-color;
					margin-bottom: 4rpx;
				}
				
				.device-type {
					font-size: 24rpx;
					color: $uni-text-color-grey;
				}
			}
			
			.device-alarm {
				background-color: $uni-color-error;
				color: #fff;
				border-radius: 16rpx;
				padding: 0 10rpx;
				margin-right: 16rpx;
				
				.alarm-count {
					font-size: 24rpx;
				}
			}
			
			.iconfont {
				font-size: 32rpx;
				color: $uni-text-color-grey;
			}
		}
		
		.empty-tip {
			padding: 40rpx 0;
			text-align: center;
			font-size: 28rpx;
			color: $uni-text-color-grey;
		}
	}
	
	.alarm-list {
		.alarm-item {
			display: flex;
			align-items: center;
			padding: 16rpx 0;
			border-bottom: 1rpx solid #eee;
			
			&:last-child {
				border-bottom: none;
			}
			
			.alarm-level {
				width: 12rpx;
				height: 12rpx;
				border-radius: 50%;
				margin-right: 16rpx;
				
				&.warning {
					background-color: $uni-color-warning;
				}
				
				&.error {
					background-color: $uni-color-error;
				}
			}
			
			.alarm-content {
				flex: 1;
				
				.alarm-title {
					font-size: 28rpx;
					color: $uni-text-color;
					margin-bottom: 4rpx;
				}
				
				.alarm-time {
					font-size: 24rpx;
					color: $uni-text-color-grey;
				}
			}
		}
	}
	
	.action-buttons {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: #fff;
		display: flex;
		padding: 20rpx 30rpx;
		box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);
		
		.action-btn {
			flex: 1;
			display: flex;
			flex-direction: column;
			align-items: center;
			color: $uni-color-primary;
			
			.iconfont {
				font-size: 48rpx;
				margin-bottom: 8rpx;
			}
			
			text {
				font-size: 26rpx;
			}
		}
	}
</style> 