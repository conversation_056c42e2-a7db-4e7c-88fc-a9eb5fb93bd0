package com.heating.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.heating.dto.order.WorkOrderBaseInfoResponse;
import com.heating.dto.order.WorkOrderDetailResponse;
import com.heating.entity.fault.TFaultAttachment;
import com.heating.entity.order.TOperationLog;
import com.heating.entity.order.TWorkOrder;
import com.heating.entity.order.TWorkOrderAttachment;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
@Repository
public interface WorkOrderRepository extends JpaRepository<TWorkOrder, Long> {

    @Query(value =
        "SELECT " +
            "wo.id as orderId, " +
            "wo.orderNo as orderNo, " +
            "h.name as heatUnitName, " +
            "f.faultType as faultType, " + 
            "f.faultLevel as faultLevel, " +
            "wo.orderStatus as orderStatus, " +
            "DATE_FORMAT(wo.createdAt, '%Y-%m-%d %H:%i') as createdTime " +
        "FROM TWorkOrder wo " +
            "LEFT JOIN TFault f ON f.id = wo.faultId " +
            "LEFT JOIN THeatUnit h ON f.heatUnitId = h.id " +
        "WHERE 1=1 " +
            "AND (:date IS NULL OR DATE(wo.createdAt) = :date) " +
            "AND (:status IS NULL OR wo.orderStatus = :status) " +
            "AND (:userId IS NULL OR wo.repairUserId = :userId) " +
        "ORDER BY wo.createdAt DESC")   
    List<Map<String, Object>> findWorkOrderList(
        @Param("date") Date date, 
        @Param("status") String status,
        @Param("userId") Long userId
    );

    @Query(value =
            "SELECT " +
                    "wo.id as orderId, " +
                    "wo.orderNo as orderNo, " +
                    "h.name as heatUnitName, " +
                    "f.faultType as faultType, " +
                    "f.faultLevel as faultLevel, " +
                    "wo.orderStatus as orderStatus, " +
                    "wo.repairUserId as repairUserId, " +
                    "wo.transferUserId as transferUserId, " +
                    "DATE_FORMAT(wo.createdAt, '%Y-%m-%d %H:%i') as createdTime " +
                    "FROM TWorkOrder wo " +
                    "LEFT JOIN TFault f ON f.id = wo.faultId " +
                    "LEFT JOIN THeatUnit h ON f.heatUnitId = h.id " +
                    "WHERE 1=1 " +
                    "AND (:date IS NULL OR DATE(wo.createdAt) = :date) " +
                    "AND (:status IS NULL OR wo.orderStatus = :status) " +
                    "AND (:orderNo IS NULL OR wo.orderNo LIKE CONCAT('%', :orderNo, '%')) " +
                    "AND (:userId IS NULL OR wo.repairUserId = :userId OR wo.transferUserId = :userId) " +
                    "ORDER BY wo.createdAt DESC " +
                    "LIMIT :limit")
    List<Map<String, Object>> findWorkOrderListWithLimit(
            @Param("date") Date date,
            @Param("status") String status,
            @Param("userId") Long userId,
            @Param("orderNo") String orderNo,
            @Param("limit") Integer limit
    );

    @Query(value =
        "SELECT " + 
            "wo.orderNo as orderNo, " +
            "wo.faultId as faultId, " +
            "h.name as heatUnitName, " +
            "wo.repairUserId as repairUserId, " +
            "u.name as repairUserName, " +
            "wo.transferUserId as transferUserId, " +
            "tu.name as transferUserName, " +
            "wo.transferReason as transferReason, " +
            "DATE_FORMAT(wo.transferTime, '%Y-%m-%d %H:%i') as transferTime, " +
            "wo.repairContent as repairContent, " +
            "wo.repairResult as repairResult, " +
            "wo.orderStatus as orderStatus, " + 
            "f.faultType as faultType, " + 
            "f.faultLevel as faultLevel, " +
            "f.address as address, " +
            "f.faultDesc as faultDesc, " +
            "DATE_FORMAT(wo.repairTime, '%Y-%m-%d %H:%i') as repairTime, " +
            "wo.repairMaterialsQuantity as repairMaterialsQuantity, " +
            "DATE_FORMAT(wo.createdAt, '%Y-%m-%d %H:%i') as createdTime, " +
            "DATE_FORMAT(wo.updatedAt, '%Y-%m-%d %H:%i') as updatedTime " +
        "FROM TWorkOrder wo " +
            "LEFT JOIN TFault f ON wo.faultId = f.id " +  
            "LEFT JOIN THeatUnit h ON f.heatUnitId = h.id " +
            "LEFT JOIN TUser u ON wo.repairUserId = u.id " +
            "LEFT JOIN TUser tu ON wo.transferUserId = tu.id " +
        "WHERE wo.id = :orderId"
    )
    Optional<Map<String, Object>> findWorkOrderDetail(@Param("orderId") long orderId);

    @Query(value =
        "SELECT wa FROM TWorkOrderAttachment wa " +
        "WHERE wa.workOrderId = :orderId " +
        "ORDER BY wa.createdAt DESC"
    )
    List<TWorkOrderAttachment> findWorkOrderAttachments(@Param("orderId") long orderId);

    @Query(value =
        "SELECT wa FROM TFaultAttachment wa " +
            "LEFT JOIN TWorkOrder wo ON wa.faultId = wo.faultId " +
        "WHERE wo.id = :orderId " +
        "ORDER BY wa.createdAt DESC"
    )
    List<TFaultAttachment> findFaultAttachments(@Param("orderId") long orderId);

    @Query(value =
            "SELECT NEW TOperationLog(" +
            "ol.id, " +
            "ol.workOrderId, " +
            "ol.operationType, " +
            "ol.operationDesc, " +
            "ol.operatorId, " +
            "ol.createdAt, " +
            "u.name) " +
            "FROM TOperationLog ol " +
            "LEFT JOIN TUser u ON ol.operatorId = u.id " +
            "WHERE ol.workOrderId = :orderId " +
            "ORDER BY ol.createdAt DESC"
    )
    List<TOperationLog> findOperationLogs(@Param("orderId") long orderId);

    @Query(value =
        "SELECT " +
            "wo.orderStatus as status, " +
            "COUNT(*) as count " +
        "FROM TWorkOrder wo " +
        "WHERE (:date IS NULL OR DATE(wo.createdAt) = :date) " +
        "GROUP BY wo.orderStatus"
    )
    List<Map<String, Object>> countWorkOrdersByStatus(@Param("date") LocalDate date);

    Optional<TWorkOrder> findByOrderNo(String orderNo);

    Optional<TWorkOrder> findByFaultId(long faultId);

    /**
     * 根据工单状态查询工单信息
     * 从工单信息表中，获取状态为指定状态（如'待接单'）的记录列表
     * @param status 工单状态
     * @return 符合条件的工单列表
     */
    @Query(value =
        "SELECT " +
            "wo.id as id, " +
            "wo.orderNo as orderNo, " +
            "f.faultSource as faultSource, " +
            "f.faultDesc as faultDesc, " +
            "wo.createdAt as createdTime, " +
            "wo.updatedAt as updatedTime " +
        "FROM TWorkOrder wo " +
            "LEFT JOIN TFault f ON wo.faultId = f.id " +
        "WHERE wo.orderStatus = :status " +
        "ORDER BY wo.createdAt DESC")
    List<Map<String, Object>> findWorkOrdersByStatus(@Param("status") String status);

    /**
     * 根据工单状态和用户ID查询工单信息
     * 从工单信息表中，获取状态为指定状态（如'待接单'）且维修人员ID为指定值的记录列表
     * @param status 工单状态
     * @param userId 用户ID
     * @return 符合条件的工单列表
     */
    @Query(value =
        "SELECT " +
            "wo.id as id, " +
            "wo.orderNo as orderNo, " +
            "f.faultSource as faultSource, " +
            "f.faultDesc as faultDesc, " +
            "wo.createdAt as createdTime, " +
            "wo.updatedAt as updatedTime " +
        "FROM TWorkOrder wo " +
            "LEFT JOIN TFault f ON wo.faultId = f.id " +
        "WHERE wo.orderStatus = :status " +
            "AND wo.repairUserId = :userId " +
        "ORDER BY wo.createdAt DESC")
    List<Map<String, Object>> findWorkOrdersByStatusAndUserId(@Param("status") String status, @Param("userId") Long userId);

    /**
     * 分页查询工单列表
     */
    @Query(value = "SELECT " +
            "wo.id as orderId, " +
            "wo.order_no as orderNo, " +
            "hu.name as heatUnitName, " +
            "f.fault_type as faultType, " +
            "f.fault_level as faultLevel, " +
            "wo.order_status as orderStatus, " +
            "wo.repair_user_id as repairUserId, " +
            "wo.transfer_user_id as transferUserId, " +
            "DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime " +
            "FROM t_work_order wo " +
            "LEFT JOIN t_fault f ON wo.fault_id = f.id " +
            "LEFT JOIN t_heat_unit hu ON f.heat_unit_id = hu.id " +
            "WHERE (:status IS NULL OR wo.order_status = :status) " +
            "AND (:orderNo IS NULL OR wo.order_no LIKE CONCAT('%', :orderNo, '%')) " +
            "AND (:date IS NULL OR DATE(wo.created_at) = :date) " +
            "AND (:userId IS NULL OR wo.repair_user_id = :userId) " +
            "ORDER BY wo.created_at DESC " +
            "LIMIT :offset, :pageSize", nativeQuery = true)
    List<Map<String, Object>> findWorkOrderListWithPaging(
            @Param("date") Date date,
            @Param("status") String status,
            @Param("userId") Long userId,
            @Param("orderNo") String orderNo,
            @Param("offset") int offset,
            @Param("pageSize") int pageSize);

    /**
     * 统计工单总数
     */
    @Query(value = "SELECT COUNT(*) " +
            "FROM t_work_order wo " +
            "WHERE (:status IS NULL OR wo.order_status = :status) " +
            "AND (:orderNo IS NULL OR wo.order_no LIKE CONCAT('%', :orderNo, '%')) " +
            "AND (:date IS NULL OR DATE(wo.created_at) = :date) " +
            "AND (:userId IS NULL OR wo.repair_user_id = :userId)", nativeQuery = true)
    long countWorkOrderList(
            @Param("date") Date date,
            @Param("status") String status,
            @Param("userId") Long userId,
            @Param("orderNo") String orderNo);

    /**
     * 分页查询工单列表（包含转派信息）
     * 查询条件：用户ID匹配repair_user_id或transfer_user_id
     */
    @Query(value = "SELECT " +
            "wo.id as orderId, " +
            "wo.order_no as orderNo, " +
            "hu.name as heatUnitName, " +
            "f.fault_type as faultType, " +
            "f.fault_level as faultLevel, " +
            "wo.order_status as orderStatus, " +
            "wo.repair_user_id as repairUserId, " +
            "wo.transfer_user_id as transferUserId, " +
            "DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime " +
            "FROM t_work_order wo " +
            "LEFT JOIN t_fault f ON wo.fault_id = f.id " +
            "LEFT JOIN t_heat_unit hu ON f.heat_unit_id = hu.id " +
            "WHERE (:status IS NULL OR wo.order_status = :status) " +
            "AND (:orderNo IS NULL OR wo.order_no LIKE CONCAT('%', :orderNo, '%')) " +
            "AND (:date IS NULL OR DATE(wo.created_at) = :date) " +
            "AND (:userId IS NULL OR wo.repair_user_id = :userId OR wo.transfer_user_id = :userId) " +
            "ORDER BY wo.created_at DESC " +
            "LIMIT :offset, :pageSize", nativeQuery = true)
    List<Map<String, Object>> findWorkOrderListWithTransferInfo(
            @Param("date") Date date,
            @Param("status") String status,
            @Param("userId") Long userId,
            @Param("orderNo") String orderNo,
            @Param("offset") int offset,
            @Param("pageSize") int pageSize);
            
    /**
     * 统计工单总数（包含转派信息）
     * 计数条件：用户ID匹配repair_user_id或transfer_user_id
     */
    @Query(value = "SELECT COUNT(*) " +
            "FROM t_work_order wo " +
            "WHERE (:status IS NULL OR wo.order_status = :status) " +
            "AND (:orderNo IS NULL OR wo.order_no LIKE CONCAT('%', :orderNo, '%')) " +
            "AND (:date IS NULL OR DATE(wo.created_at) = :date) " +
            "AND (:userId IS NULL OR wo.repair_user_id = :userId OR wo.transfer_user_id = :userId)", nativeQuery = true)
    long countWorkOrderListWithTransferInfo(
            @Param("date") Date date,
            @Param("status") String status,
            @Param("userId") Long userId,
            @Param("orderNo") String orderNo);
} 