package com.heating.entity.device;

import com.heating.converter.JsonConverter;
import jakarta.persistence.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Entity
@Table(name = "t_device")
public class TDevice {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id; 

    @Column(name = "no", nullable = false, length = 50)
    private String no;

    @Column(name = "heat_unit_id", nullable = false)
    private Long heatUnitId;

    @Column(name = "device_parent", nullable = false, length = 50)
    private String deviceParent;

    @Column(name = "sub_id", nullable = false)
    private Long subId;

    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @Column(name = "type", nullable = false, length = 50)
    private String type;

    @Column(name = "model", nullable = false, length = 50)
    private String model;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private DeviceStatus status = DeviceStatus.online;

    @Column(nullable = false, length = 100)
    private String building;

    @Column(length = 50)
    private String floor;

    @Column(length = 100)
    private String room;
 
    @Column(name = "latitude")
    private Double latitude;    
    
    @Column(name = "longitude")
    private Double longitude;

    @Column(name = "last_maintenance")
    private LocalDateTime lastMaintenance;

    @Column(name = "next_maintenance")
    private LocalDateTime nextMaintenance;

    @Column(name = "manufacturer")
    private String manufacturer;

    @Column(name = "period")
    private Integer period;

    @Column(name = "alarm_count")
    private Integer alarmCount = 0; 

    @Convert(converter = JsonConverter.class)
    @Column(columnDefinition = "json")
    private List<String> staff;

    @Column(name = "create_time", nullable = false, updatable = false)
    private LocalDateTime createTime;

    @Column(name = "update_time", nullable = false)
    private LocalDateTime updateTime; 

    @PrePersist
    protected void onCreate() {
        createTime = LocalDateTime.now();
        updateTime = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updateTime = LocalDateTime.now();
    } 

    public enum DeviceStatus {
        online, offline, fault
    }
} 