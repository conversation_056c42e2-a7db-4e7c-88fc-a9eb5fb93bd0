<view class="container">
  <view class="detail-card">
    <view class="card-header">
      <text class="fault-no">故障编号：{{fault.fault_id}}</text>
      <text class="status {{statusClass}}">{{fault.fault_status}}</text>
    </view>

    <view class="info-list">
      <view class="info-item">
        <text class="label">换热站</text>
        <text class="value">{{fault.station_name}}</text>
      </view>
      <view class="info-item">
        <text class="label">故障类型</text>
        <text class="value">{{fault.fault_type}}</text>
      </view>
      <view class="info-item">
        <text class="label">故障等级</text>
        <text class="level-{{fault.fault_level}}"> {{fault.fault_level === 1 ? '一般' : fault.fault_level === 2 ? '重要' : '紧急'}}
        </text>
      </view>
      <view class="info-item desc">
        <text class="label">故障描述</text>
        <text class="value">{{fault.fault_desc}}</text>
      </view>
      <view class="info-item">
        <text class="label">发生时间</text>
        <text class="value">{{fault.occur_time}}</text>
      </view>
      <view class="info-item">
        <text class="label">上报人员</text>
        <text class="value">{{fault.report_user_name || '未知'}}</text>
      </view>
      <view class="info-item">
        <text class="label">上报时间</text>
        <text class="value">{{fault.report_time}}</text>
      </view>
    </view>

    <!-- 图片附件 -->
    <block wx:if="{{images.length > 0}}">
      <view class="section">
        <view class="section-title">图片附件</view>
        <view class="attachment-list">
          <view class="attachment-item" 
                wx:for="{{images}}" 
                wx:key="file_path">
            <image src="{{item.file_path}}" 
                   mode="aspectFill"
                   bindtap="previewImage"
                   data-url="{{item.file_path}}"/>
            <text class="upload-time">{{item.upload_time}}</text>
          </view>
        </view>
      </view>
    </block>

    <!-- 视频附件 -->
    <block wx:if="{{videos.length > 0}}">
      <view class="section">
        <view class="section-title">视频附件</view>
        <view class="attachment-list">
          <view class="attachment-item" 
                wx:for="{{videos}}" 
                wx:key="file_path">
            <video src="{{item.file_path}}"
                   show-center-play-btn="true"
                   object-fit="cover"/>
            <text class="upload-time">{{item.upload_time}}</text>
          </view>
        </view>
      </view>
    </block>

    <!-- 相关工单 -->
    <block wx:if="{{workOrders.length > 0}}">
      <view class="section">
        <view class="section-title">相关工单</view>
        <view class="order-list">
          <view class="order-item" wx:for="{{workOrders}}" wx:key="order_id"
                bindtap="goToWorkOrder" data-id="{{item.order_id}}">
            <view class="order-info">
              <text class="order-content">{{item.repair_content || '暂无维修内容'}}</text>
              <text class="order-meta">{{item.repair_user_name}} | {{item.create_time}}</text>
            </view>
            <text class="order-status {{item.order_status}}">{{item.order_status}}</text>
          </view>
        </view>
      </view>
    </block>

    <!-- 操作按钮 -->
    <view class="action-bar" wx:if="{{fault.fault_status === '待确认' && wx.getStorageSync('isAdmin')}}">
      <button class="btn confirm" bindtap="showRepairUserSelect">确认故障</button>
      <button class="btn reject" bindtap="handleReject">退回</button>
    </view>

    <!-- 维修人员选择弹窗 --> 
    <view class="repair-user-popup" wx:if="{{showRepairUserSelect}}">
      <view class="popup-mask" bindtap="hideRepairUserSelect"></view>
      <view class="popup-content">
        <view class="popup-title">选择维修人员并生成工单</view>
        <view class="repair-user-list">
          <radio-group bindchange="selectRepairUser">
            <label class="repair-user-item" 
                  wx:for="{{repairUsers}}" 
                  wx:key="id">
              <radio value="{{item.id}}" 
                    checked="{{selectedUserId === item.id}}"
                    data-name="{{item.name}}"/>
              <text class="user-name">{{item.name}}</text>
            </label>
          </radio-group>
        </view>
        <view class="popup-footer">
          <button class="btn cancel" bindtap="hideRepairUserSelect">取消</button>
          <button class="btn confirm" bindtap="handleConfirm">确认</button>
        </view>
      </view>
    </view>  
 

  </view>
</view> 