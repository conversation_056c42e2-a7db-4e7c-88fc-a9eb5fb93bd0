/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-popup[data-v-7db519c7] {
  position: fixed;
  z-index: 99;
}
.uni-popup.top[data-v-7db519c7], .uni-popup.left[data-v-7db519c7], .uni-popup.right[data-v-7db519c7] {
  top: 0;
}
.uni-popup .uni-popup__wrapper[data-v-7db519c7] {
  display: block;
  position: relative;
  /* iphonex 等安全区设置，底部安全区适配 */
}
.uni-popup .uni-popup__wrapper.left[data-v-7db519c7], .uni-popup .uni-popup__wrapper.right[data-v-7db519c7] {
  padding-top: 0;
  flex: 1;
}
.fixforpc-z-index[data-v-7db519c7] {
  z-index: 999;
}
.fixforpc-top[data-v-7db519c7] {
  top: 0;
}

.no-permission-item[data-v-9e1a7520] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0.5;
}
.no-permission-text[data-v-9e1a7520] {
  font-size: 0.75rem;
  color: #999;
  text-align: center;
}

/* 确保permission-check组件继承其父容器的布局特性 */
[data-v-9e1a7520] .permission-check {
  display: inherit;
  width: inherit;
  height: inherit;
  flex: inherit;
  position: inherit;
}

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.patrol-plans-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
  padding-top: 0;
  padding-bottom: 3.75rem;
}

/* 页面标题 */
.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 2.8125rem;
  background-color: #0088ff;
  color: #ffffff;
  font-size: 1.0625rem;
  font-weight: bold;
  box-shadow: 0 0.0625rem 0.1875rem rgba(0, 0, 0, 0.1);
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 999;
}

/* 搜索和筛选区域样式 */
.search-filter-area {
  background-color: #fff;
  border-bottom: 0.03125rem solid #eeeeee;
  box-shadow: 0 0.0625rem 0.1875rem rgba(0, 0, 0, 0.05);
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 999;
}

/* 搜索栏样式 */
.search-bar {
  padding: 0.625rem 0.75rem 0.5rem;
}
.search-bar .search-input-wrapper {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 1.125rem;
  padding: 0.4375rem 0.75rem;
}
.search-bar .search-input-wrapper .iconfont {
  font-size: 0.875rem;
  color: #999;
}
.search-bar .search-input-wrapper uni-input {
  flex: 1;
  height: 1.25rem;
  font-size: 0.875rem;
  margin: 0 0.5rem;
  color: #333;
}

/* 筛选栏样式 */
.filter-bar {
  display: flex;
  padding: 0;
  margin: 0 0.375rem 0.5rem;
  height: 2.1875rem;
}
.filter-bar .filter-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 0.8125rem;
  color: #333;
  height: 100%;
  position: relative;
}
.filter-bar .filter-item:after {
  content: "";
  position: absolute;
  right: 0;
  top: 20%;
  height: 60%;
  width: 0.03125rem;
  background-color: #eee;
}
.filter-bar .filter-item:last-child:after {
  display: none;
}
.filter-bar .filter-item uni-text {
  margin-right: 0.1875rem;
}
.filter-bar .filter-item .iconfont {
  font-size: 0.6875rem;
  color: #999;
  margin-left: 0.125rem;
}
.plan-list {
  padding: 0.625rem;
}
.plan-list .plan-item {
  background-color: #fff;
  border-radius: 0.375rem;
  margin-bottom: 0.625rem;
  padding: 0.75rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
  border: 0.03125rem solid #eeeeee;
}
.plan-list .plan-item .plan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}
.plan-list .plan-item .plan-header .plan-title {
  font-size: 0.9375rem;
  font-weight: bold;
  color: #333;
  max-width: 65%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.plan-list .plan-item .plan-header .plan-status {
  padding: 0.125rem 0.5rem;
  border-radius: 0.75rem;
  font-size: 0.75rem;
  font-weight: normal;
}
.plan-list .plan-item .plan-header .plan-status.pending {
  background-color: rgba(24, 144, 255, 0.08);
  color: #FFA500;
}
.plan-list .plan-item .plan-header .plan-status.processing {
  background-color: rgba(24, 144, 255, 0.15);
  color: #1E90FF;
}
.plan-list .plan-item .plan-header .plan-status.completed {
  background-color: rgba(82, 196, 26, 0.08);
  color: #52c41a;
}
.plan-list .plan-item .plan-header .plan-status.overdue {
  background-color: rgba(245, 34, 45, 0.08);
  color: #f5222d;
}
.plan-list .plan-item .plan-header .plan-status.canceled {
  background-color: rgba(102, 102, 102, 0.08);
  color: #999999;
}
.plan-list .plan-item .plan-info {
  padding: 0.5rem 0;
  border-top: 0.03125rem solid #f2f2f2;
  border-bottom: 0.03125rem solid #f2f2f2;
  margin-bottom: 0.5rem;
}
.plan-list .plan-item .plan-info .info-row {
  display: flex;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
}
.plan-list .plan-item .plan-info .info-row:last-child {
  margin-bottom: 0;
}
.plan-list .plan-item .plan-info .info-row .info-item {
  flex: 0 0 50%;
  display: flex;
  align-items: flex-start;
  min-width: 0;
  box-sizing: border-box;
  padding-right: 0.46875rem;
  margin-bottom: 0.3125rem;
  overflow: hidden;
}
.plan-list .plan-item .plan-info .info-row .info-item.full-width {
  flex: 0 0 100%;
}
.plan-list .plan-item .plan-info .info-row .info-item .item-label {
  width: 4.0625rem;
  flex-shrink: 0;
  font-size: 0.8125rem;
  color: #888888;
}
.plan-list .plan-item .plan-info .info-row .info-item .item-value {
  flex: 1;
  font-size: 0.8125rem;
  color: #333333;
  overflow: visible;
  word-break: break-all;
  white-space: normal;
  max-width: calc(100% - 4.0625rem);
}
.plan-list .plan-item .plan-footer .plan-actions {
  display: flex;
  justify-content: flex-end;
  flex-wrap: wrap;
}
.plan-list .plan-item .plan-footer .plan-actions .action-button {
  display: flex;
  align-items: center;
  padding: 0.25rem 0.625rem;
  border-radius: 0.875rem;
  margin-left: 0.5rem;
  margin-bottom: 0.25rem;
  transition: all 0.2s;
}
.plan-list .plan-item .plan-footer .plan-actions .action-button:active {
  opacity: 0.8;
}
.plan-list .plan-item .plan-footer .plan-actions .action-button .iconfont {
  font-size: 0.75rem;
  margin-right: 0.25rem;
}
.plan-list .plan-item .plan-footer .plan-actions .action-button uni-text {
  font-size: 0.8125rem;
}
.plan-list .plan-item .plan-footer .plan-actions .action-button.start {
  background-color: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}
.plan-list .plan-item .plan-footer .plan-actions .action-button.continue {
  background-color: rgba(82, 196, 26, 0.15);
  color: #52c41a;
}
.plan-list .plan-item .plan-footer .plan-actions .action-button.view {
  background-color: #f5f5f5;
  color: #666666;
  margin-right: 0;
  padding: 0.25rem 0.625rem;
}
.loading-container {
  display: flex;
  justify-content: center;
  padding: 0.9375rem 0;
}
.loading-container .loading-text {
  font-size: 0.875rem;
  color: #999;
}
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5625rem 0;
}
.error-container .error-text {
  font-size: 0.875rem;
  color: #f5222d;
  margin-bottom: 0.625rem;
}
.error-container .retry-btn {
  font-size: 0.875rem;
  color: #fff;
  background-color: #1890ff;
  padding: 0.25rem 0.9375rem;
  border-radius: 0.9375rem;
}
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3.125rem 0;
}
.empty-state .empty-image {
  width: 6.25rem;
  height: 6.25rem;
  margin-bottom: 0.9375rem;
}
.empty-state .empty-text {
  font-size: 0.875rem;
  color: #999;
}
.filter-popup {
  background-color: #fff;
  border-top-left-radius: 0.625rem;
  border-top-right-radius: 0.625rem;
}
.filter-popup .popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.9375rem;
  border-bottom: 0.03125rem solid #eee;
}
.filter-popup .popup-header .popup-title {
  font-size: 1rem;
  font-weight: bold;
}
.filter-popup .popup-header .popup-close {
  font-size: 0.875rem;
  color: #1890ff;
}
.filter-popup .popup-content {
  padding: 0.625rem 0.9375rem;
  max-height: 18.75rem;
  overflow-y: auto;
}
.filter-popup .popup-content .filter-option {
  padding: 0.625rem 0;
  font-size: 0.9375rem;
  color: #333;
  border-bottom: 0.03125rem solid #eee;
}
.filter-popup .popup-content .filter-option.active {
  color: #1890ff;
  font-weight: bold;
}
.filter-popup .popup-content .filter-option:last-child {
  border-bottom: none;
}
.floating-button {
  position: fixed;
  right: 0.9375rem;
  bottom: 0.9375rem;
  width: 3.125rem;
  height: 3.125rem;
  border-radius: 50%;
  background-color: #0088ff;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 136, 255, 0.4);
  z-index: 10;
}
.floating-button .plus-icon {
  font-size: 1.875rem;
  color: #fff;
  font-weight: normal;
  line-height: 1.875rem;
  margin-top: -0.09375rem;
}