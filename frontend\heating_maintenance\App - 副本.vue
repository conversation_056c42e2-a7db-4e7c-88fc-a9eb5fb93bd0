<template>
  <div></div>
</template>

<script>
import CustomTabBar from "./components/CustomTabBar.vue";

export default {
  onLaunch: function () {
    console.log("App Launch");

    // 检查是否有更新
    this.checkAppUpdate();

    // 预加载一些必要的资源
    this.preloadResources();
  },
  onShow: function () {
    console.log("App Show");
  },
  onHide: function () {
    console.log("App Hide");
  },
  methods: {
    checkAppUpdate() {
      // 版本检查逻辑
      console.log("检查应用更新");
    },

    preloadResources() {
      // 预加载一些资源
      console.log("预加载资源");
    },
  },
  globalData: {
    useCustomTabBar: true,
  },
};
</script>

<style lang="scss">
/*每个页面公共css */
@import "./uni.scss";

/* 全局基础样式 */
page {
  background-color: $uni-bg-color-grey;
  font-size: 28rpx;
  color: $uni-text-color;
  font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", Helvetica, Segoe UI,
    Arial, Roboto, "PingFang SC", "miui", "Hiragino Sans GB", "Microsoft Yahei",
    sans-serif;
  padding-bottom: 110rpx;
}
@font-face {
  font-family: 'iconfont';
  src: url('@/static/fonts/iconfont.ttf') format('truetype');
}
.iconfont {
  font-family: 'iconfont';
  font-size: 16px;
  color: #999;
}
/* 通用卡片样式 */
.card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

/* 通用标题样式 */
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;

  &::before {
    content: "";
    position: absolute;
    left: 0;
    top: 8rpx;
    width: 8rpx;
    height: 32rpx;
    background-color: $uni-color-primary;
    border-radius: 4rpx;
  }
}

/* 通用按钮样式 */
.btn-primary {
  background-color: $uni-color-primary;
  color: #fff;
  border-radius: 8rpx;
  height: 80rpx;
  line-height: 80rpx;
  width: 686rpx;
  text-align: center;
  font-size: 32rpx;
  box-shadow: 0 6rpx 16rpx rgba(24, 144, 255, 0.3);
}

.btn-secondary {
  background-color: transparent;
  color: $uni-color-primary;
  border: 1rpx solid $uni-color-primary;
  border-radius: 8rpx;
  height: 64rpx;
  line-height: 64rpx;
  width: 320rpx;
  text-align: center;
  font-size: 28rpx;
}

.btn-warning {
  background-color: $uni-color-warning;
  color: #fff;
  border-radius: 8rpx;
  height: 80rpx;
  line-height: 80rpx;
  width: 686rpx;
  text-align: center;
  font-size: 32rpx;
  box-shadow: 0 6rpx 16rpx rgba(250, 173, 20, 0.3);
}

/* 状态标签样式 */
.status-tag {
  display: inline-block;
  padding: 6rpx 16rpx;
  font-size: 24rpx;
  border-radius: 6rpx;

  &.primary {
    background-color: rgba(24, 144, 255, 0.1);
    color: $uni-color-primary;
  }

  &.success {
    background-color: rgba(82, 196, 26, 0.1);
    color: $uni-color-success;
  }

  &.warning {
    background-color: rgba(250, 173, 20, 0.1);
    color: $uni-color-warning;
  }

  &.error {
    background-color: rgba(245, 34, 45, 0.1);
    color: $uni-color-error;
  }
}

/* 列表项样式 */
.list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);

  &:last-child {
    border-bottom: none;
  }
}

/* 修复页面底部内边距，避免被TabBar遮挡 */
.has-tabbar-padding {
  padding-bottom: calc(110rpx + env(safe-area-inset-bottom));
}
</style>
