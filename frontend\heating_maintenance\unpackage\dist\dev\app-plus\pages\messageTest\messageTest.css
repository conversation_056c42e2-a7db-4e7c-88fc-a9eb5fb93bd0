
.container[data-v-577bde04] {
		padding: 20px;
		text-align: center;
}
.title[data-v-577bde04] {
		font-size: 20px;
		font-weight: bold;
		margin-bottom: 20px;
}
.status[data-v-577bde04] {
		margin-bottom: 20px;
		font-size: 16px;
}
.status .running[data-v-577bde04] {
		color: #18bc37; /* 绿色 */
		font-weight: bold;
}
.status .stopped[data-v-577bde04] {
		color: #e43d33; /* 红色 */
		font-weight: bold;
}
uni-button[data-v-577bde04] {
		margin-top: 10px;
}
.tips[data-v-577bde04] {
		margin-top: 30px;
		font-size: 14px;
		color: #888;
}
