package com.heating.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;

@Entity
@Table(name = "t_manager_heat_unit")
@Data
public class TManagerHeatUnit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "manager_id", nullable = false)
    private Long managerId;
    
    @Column(name = "manager_name", nullable = false, length = 100)
    private String managerName;
    
    @Column(name = "heat_unit_id", nullable = false)
    private Long heatUnitId;
    
    @Column(name = "heat_unit_name", nullable = false, length = 100)
    private String heatUnitName;
    
    @Column(name = "create_time")
    private LocalDateTime createTime;
    
    @Column(name = "update_time")
    private LocalDateTime updateTime;
} 