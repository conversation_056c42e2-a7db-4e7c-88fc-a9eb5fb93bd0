<template>
	<view class="valve-control-page"> 
		
		<!-- 选择区域 -->
		<view class="select-area">
			<view class="form-item">
				<text class="form-label">小区</text>
				<picker @change="handleHeatUnitChange" :value="heatUnitIndex" :range="heatUnitOptions" class="form-picker">
					<view class="picker-content">
						<text class="picker-value">{{ heatUnitOptions[heatUnitIndex] || '请选择' }}</text>
						<text class="iconfont icon-arrow-down"></text>
					</view>
				</picker>
			</view>
			
			<view class="form-item">
				<text class="form-label">楼栋</text>
				<picker @change="handleBuildingChange" :value="buildingIndex" :range="buildingOptions" class="form-picker" :disabled="!heatUnitId">
					<view class="picker-content">
						<text class="picker-value">{{ buildingOptions[buildingIndex] || '请选择' }}</text>
						<text class="iconfont icon-arrow-down"></text>
					</view>
				</picker>
			</view>
			
			<view class="form-item">
				<text class="form-label">单元</text>
				<picker @change="handleUnitChange" :value="unitIndex" :range="unitOptions" class="form-picker" :disabled="!buildingId">
					<view class="picker-content">
						<text class="picker-value">{{ unitOptions[unitIndex] || '请选择' }}</text>
						<text class="iconfont icon-arrow-down"></text>
					</view>
				</picker>
			</view>
			
			<view class="form-item">
				<text class="form-label">户号</text>
				<input class="form-input" type="text" v-model="houseNumber" placeholder="请输入户号，如：101" />
			</view>
			
			<button class="search-btn" @click="searchValves">搜索阀门</button>
		</view>
		
		<!-- 加载中 -->
		<view class="loading-container" v-if="loading">
			<uni-load-more :status="'loading'" :content-text="loadingText"></uni-load-more>
		</view>
		
		<!-- 没有阀门数据 -->
		<view class="empty-container" v-else-if="valvesList.length === 0 && !loading">
			<image src="/static/images/empty.png" mode="aspectFit" class="empty-image"></image>
			<text class="empty-text">未找到相关阀门</text>
		</view>
		
		<!-- 阀门列表 -->
		<view class="valves-list" v-else>
			<view class="valve-card" v-for="(valve, index) in valvesList" :key="index" @click="selectValve(valve)">
				<view class="valve-header">
					<text class="valve-name">{{ valve.name }}</text>
					<view class="status-tag" :class="'status-' + valve.status">{{ getStatusText(valve.status) }}</view>
				</view>
				<view class="valve-info">
					<view class="info-item">
						<text class="info-label">开度</text>
						<text class="info-value">{{ valve.openDegree || 0 }}%</text>
					</view>
					<view class="info-item">
						<text class="info-label">最后操作</text>
						<text class="info-value">{{ valve.lastOperationTime || '无' }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				loading: false,
				loadingText: {
					contentdown: '加载中...',
					contentrefresh: '加载中...',
					contentnomore: '没有更多数据'
				},
				
				// 筛选条件
				heatUnitOptions: ['东区小区', '西区小区', '南区小区'],
				heatUnitIndex: 0,
				heatUnitId: null,
				
				buildingOptions: [],
				buildingIndex: 0,
				buildingId: null,
				
				unitOptions: [],
				unitIndex: 0,
				unitId: null,
				
				houseNumber: '',
				
				// 阀门数据
				valvesList: [],
				currentValve: null,
				
				// 阀门控制
				targetOpenDegree: 0
			}
		},
		onLoad() {
			this.loadHeatUnits();
		},
		methods: {
			// 加载小区列表
			loadHeatUnits() {
				// 实际应用中这里会调用API获取小区列表
				// 这里使用模拟数据
				this.heatUnitOptions = ['东区小区', '西区小区', '南区小区'];
				this.heatUnitIndex = 0;
				this.heatUnitId = 1;
			},
			
			// 小区选择变化
			handleHeatUnitChange(e) {
				this.heatUnitIndex = e.detail.value;
				this.heatUnitId = this.heatUnitIndex + 1; // 假设id从1开始
				
				// 重置楼栋和单元
				this.buildingOptions = [];
				this.buildingIndex = 0;
				this.buildingId = null;
				this.unitOptions = [];
				this.unitIndex = 0;
				this.unitId = null;
				
				// 加载该小区的楼栋列表
				this.loadBuildings();
			},
			
			// 加载楼栋列表
			loadBuildings() {
				// 实际应用中这里会调用API获取楼栋列表
				// 这里使用模拟数据
				setTimeout(() => {
					this.buildingOptions = ['1号楼', '2号楼', '3号楼'];
					this.buildingIndex = 0;
					this.buildingId = 1;
				}, 500);
			},
			
			// 楼栋选择变化
			handleBuildingChange(e) {
				this.buildingIndex = e.detail.value;
				this.buildingId = this.buildingIndex + 1; // 假设id从1开始
				
				// 重置单元
				this.unitOptions = [];
				this.unitIndex = 0;
				this.unitId = null;
				
				// 加载该楼栋的单元列表
				this.loadUnits();
			},
			
			// 加载单元列表
			loadUnits() {
				// 实际应用中这里会调用API获取单元列表
				// 这里使用模拟数据
				setTimeout(() => {
					this.unitOptions = ['1单元', '2单元', '3单元'];
					this.unitIndex = 0;
					this.unitId = 1;
				}, 500);
			},
			
			// 单元选择变化
			handleUnitChange(e) {
				this.unitIndex = e.detail.value;
				this.unitId = this.unitIndex + 1; // 假设id从1开始
			},
			
			// 搜索阀门
			searchValves() {
				this.loading = true;
				this.valvesList = [];
				
				// 构建请求参数
				const params = {
					heat_unit_id: this.heatUnitId
				};
				
				if (this.buildingId) {
					params.building_id = this.buildingId;
				}
				
				if (this.unitId) {
					params.unit_id = this.unitId;
				}
				
				if (this.houseNumber.trim()) {
					params.house_number = this.houseNumber.trim();
				}
				
				// 模拟API调用
				setTimeout(() => {
					// 模拟数据
					this.valvesList = [
						{
							id: 1,
							name: this.heatUnitOptions[this.heatUnitIndex] + ' ' + 
								  this.buildingOptions[this.buildingIndex] + ' ' + 
								  this.unitOptions[this.unitIndex] + ' ' + 
								  (this.houseNumber || '101') + ' 入户阀门',
							status: 'open',
							openDegree: 80,
							lastOperationTime: '2025-04-03 14:35:20',
							operator: '系统',
							operationType: '自动开启(缴费成功)'
						}
					];
					
					this.loading = false;
				}, 1000);
			},
			
			// 选择阀门
			selectValve(valve) {
				uni.navigateTo({
					url: `/pages/valves/detail?id=${valve.id}`
				});
			},
			
			// 获取状态文本
			getStatusText(status) {
				const statusMap = {
					'open': '已开启',
					'closed': '已关闭',
					'error': '异常'
				};
				return statusMap[status] || status;
			},
			
			// 返回上一页
			navigateBack() {
				uni.navigateBack();
			}
		}
	}
</script>

<style lang="scss">
.valve-control-page {
	min-height: 100vh;
	background-color: #f5f7fa;
	padding-bottom: 30rpx;
}

.header {
	display: flex;
	align-items: center;
	height: 88rpx;
	background-color: #fff;
	padding: 0 30rpx;
	position: relative;
	box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
	
	.back-button {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.page-title {
		flex: 1;
		text-align: center;
		font-size: 34rpx;
		font-weight: 500;
		padding-right: 60rpx;
	}
}

.select-area {
	margin: 20rpx 30rpx;
	background-color: #fff;
	border-radius: 12rpx;
	padding: 20rpx 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	
	.form-item {
		margin-bottom: 20rpx;
		
		.form-label {
			font-size: 28rpx;
			color: #333;
			margin-bottom: 10rpx;
			display: block;
		}
		
		.form-picker, .form-input {
			width: 100%;
			height: 80rpx;
			border: 1px solid #e5e5e5;
			border-radius: 8rpx;
			padding: 0 20rpx;
			box-sizing: border-box;
			background-color: #f8f9fc;
			
			.picker-content {
				display: flex;
				justify-content: space-between;
				align-items: center;
				height: 80rpx;
				
				.picker-value {
					font-size: 28rpx;
					color: #333;
				}
				
				.iconfont {
					font-size: 24rpx;
					color: #999;
				}
			}
		}
		
		.form-input {
			font-size: 28rpx;
		}
	}
	
	.search-btn {
		width: 100%;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		background-color: #1989fa;
		color: #fff;
		border-radius: 8rpx;
		font-size: 30rpx;
		margin-top: 20rpx;
	}
}

.loading-container, .empty-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 0;
	
	.empty-image {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 20rpx;
	}
	
	.empty-text {
		font-size: 28rpx;
		color: #999;
	}
}

.valves-list {
	padding: 0 30rpx;
	
	.valve-card {
		background-color: #fff;
		border-radius: 12rpx;
		padding: 24rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		
		.valve-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 16rpx;
			
			.valve-name {
				font-size: 32rpx;
				font-weight: 600;
				color: #333;
			}
		}
		
		.valve-info {
			display: flex;
			flex-wrap: wrap;
			
			.info-item {
				width: 50%;
				margin-bottom: 16rpx;
				
				.info-label {
					font-size: 26rpx;
					color: #666;
					margin-bottom: 4rpx;
					display: block;
				}
				
				.info-value {
					font-size: 28rpx;
					font-weight: 500;
					color: #333;
				}
			}
		}
	}
}

.status-tag {
	padding: 6rpx 16rpx;
	border-radius: 30rpx;
	font-size: 24rpx;
	color: #fff;
	
	&.status-open {
		background-color: #2ecc71;
	}
	
	&.status-closed {
		background-color: #95a5a6;
	}
	
	&.status-error {
		background-color: #e74c3c;
	}
}
</style> 