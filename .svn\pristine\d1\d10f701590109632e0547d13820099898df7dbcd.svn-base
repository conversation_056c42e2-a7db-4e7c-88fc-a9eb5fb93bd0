package com.heating.entity.device;

import jakarta.persistence.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "t_device_parameter")
public class TDeviceParameter {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "device_id", nullable = false, length = 50)
    private String deviceId;

    @Column(name = "param_name", nullable = false, length = 50)
    private String paramName;

    @Column(name = "param_value", nullable = false, columnDefinition = "DECIMAL(10,2)")
    private BigDecimal paramValue;

    @Column(name = "unit", length = 20)
    private String unit;

    @Column(name = "normal_min", columnDefinition = "DECIMAL(10,2)")
    private BigDecimal normalMin;

    @Column(name = "normal_max", columnDefinition = "DECIMAL(10,2)")
    private BigDecimal normalMax;

    @Column(name = "record_time", nullable = false)
    private LocalDateTime recordTime;

    @Column(name = "data_quality", length = 20)
    private String dataQuality;

    @Column(name = "create_time", nullable = false)
    private LocalDateTime createTime;
}
