package com.heating.dto.order;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public record WorkOrderDetailResponse(
    String orderNo,
    long faultId,
    String heatUnitName,
    long repairUserId,
    String repairUserName,
    Integer transferUserId,
    String transferUserName,
    String transferReason,
    String transferTime,
    String repairContent,
    String repairResult,
    String address,
    String orderStatus,
    String faultType,
    String faultLevel,
    String faultDesc,
    String repairTime,
    Map<String, Integer> repairMaterialsQuantity,
    String createdTime,
    String updatedTime,
    List<AttachmentDto> faultAttachments,
    List<AttachmentDto> workOrderAttachments,
    List<OperationLogDto> operationLogs
) {
//    public WorkOrderDetailResponse {
//        if (orderNo == null || orderNo.isBlank()) {
//            throw new IllegalArgumentException("orderNo不能为空");
//        }
//        if (heatUnitName == null || heatUnitName.isBlank()) {
//            throw new IllegalArgumentException("heatUnitName不能为空");
//        }
//        if (repairUserName == null || repairUserName.isBlank()) {
//            throw new IllegalArgumentException("repairUserName不能为空");
//        }
//        if (orderStatus == null || orderStatus.isBlank()) {
//            throw new IllegalArgumentException("orderStatus不能为空");
//        }
//        if (faultType == null || faultType.isBlank()) {
//            throw new IllegalArgumentException("faultType不能为空");
//        }
//        if (faultLevel == null || faultLevel.isBlank()) {
//            throw new IllegalArgumentException("faultLevel不能为空");
//        }
//        if (faultDesc == null || faultDesc.isBlank()) {
//            throw new IllegalArgumentException("faultDesc不能为空");
//        }
//        if (repairTime == null || repairTime.isBlank()) {
//            throw new IllegalArgumentException("repairTime不能为空");
//        }
//        if (createdTime == null || createdTime.isBlank()) {
//            throw new IllegalArgumentException("createdTime不能为空");
//        }
//        if (updatedTime == null || updatedTime.isBlank()) {
//            throw new IllegalArgumentException("updatedTime不能为空");
//        }
//    }

    public record AttachmentDto(
        String fileType,
        String filePath
    ) {
//        public AttachmentDto {
//            if (fileType == null || fileType.isBlank()) {
//                throw new IllegalArgumentException("fileType不能为空");
//            }
//            if (filePath == null || filePath.isBlank()) {
//                throw new IllegalArgumentException("filePath不能为空");
//            }
//        }
    }

    public record OperationLogDto(
        String operationType,
        String operationDesc,
        String operatorName,
        String createdAt
    ) {
//        public OperationLogDto {
//            if (operationType == null || operationType.isBlank()) {
//                throw new IllegalArgumentException("operationType不能为空");
//            }
//            if (operationDesc == null || operationDesc.isBlank()) {
//                throw new IllegalArgumentException("operationDesc不能为空");
//            }
//            if (createdAt == null || createdAt.isBlank()) {
//                throw new IllegalArgumentException("createdAt不能为空");
//            }
//            if (operatorName == null || operatorName.isBlank()) {
//                throw new IllegalArgumentException("operatorName不能为空");
//            }
//        }
    }
    
    /**
     * 工单耗材DTO
     */
    public record MaterialDto(
        Long id,
        Long materialId,
        String materialName,
        BigDecimal quantityUsed,
        String unit,
        String recordedAt
    ) {
    }
}