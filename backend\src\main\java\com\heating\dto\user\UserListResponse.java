package com.heating.dto.user;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户列表响应DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserListResponse {
    /**
     * 用户ID
     */
    private Long id;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 用户姓名
     */
    private String name;
    
    /**
     * 用户头像
     */
    private String avatar;
    
    /**
     * 用户邮箱
     */
    private String email;
    
    /**
     * 用户电话
     */
    private String phone;
    
    /**
     * 用户角色
     */
    private String role;
    
    /**
     * 用户部门
     */
    private String department;

    /**
     * 小区权限
     */
    private String heatUnitId;
}