package com.emm.controller;

import com.emm.service.TemperatureService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;


import java.sql.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.emm.dto.TemperatureRequest;
import com.emm.model.RoomTemperature;

@RestController
@RequestMapping("/api/temperature")
public class TemperatureController {

    private static final Logger logger = LoggerFactory.getLogger(TemperatureController.class);

    @Autowired
    private TemperatureService temperatureService;

    @GetMapping("/outdoor")
    public ResponseEntity<?> getOutdoorTemperature() {
        logger.info("Accessing GET /api/temperature/outdoor");
        try {
            Double temperature = temperatureService.getOutdoorTemperature();
            Map<String, Object> data = new HashMap<>();
            String dateStr = LocalDate.now().toString();
            
            // 处理 null 或 NaN 的情况
            if (temperature == null || temperature.isNaN()) {
                temperature = 100.0;  // 或者其他默认值
                dateStr = "0000-00-00 00:00:00";
            }
            
            data.put("temperature", temperature);
            data.put("record_time", dateStr);
            
            Map<String, Object> response = new HashMap<>();
            response.put("code", 0);
            response.put("data", data);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Error in GET /api/temperature/outdoor: {}", e.getMessage(), e);
            Map<String, Object> response = new HashMap<>();
            response.put("code", 500);
            response.put("msg", e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
    
    @GetMapping("/list")
    public ResponseEntity<?> listTemperatures(
            @RequestParam(required = false) String community_name,
            @RequestParam(required = false) String date) {
        logger.info("Accessing GET /api/temperature/list with community_name: {} and date: {}", community_name, date);
        try { 
            // String date 转换为 LocalDate
            Date sqlDate = null;
            if (date != null && !date.trim().isEmpty()) {
                try {
                    LocalDate localDate = LocalDate.parse(date);
                    sqlDate = Date.valueOf(localDate);
                } catch (Exception e) {
                    throw new IllegalArgumentException("无效的日期格式，请使用 yyyy-MM-dd 格式");
                }
            }
            List<RoomTemperature> temperatures;
            // 执行SQL查询
            temperatures = temperatureService.getTemperatures(community_name,sqlDate);

            // 打印日志
            logger.info("Temperatures: {}", temperatures);

            List<Map<String, Object>> resultList = temperatures.stream()
                .map(temp -> {
                    Map<String, Object> item = new HashMap<>();
                    item.put("id", temp.getId());
                    item.put("community_name", temp.getCommunityName());
                    item.put("building_no", temp.getBuildingNo());
                    item.put("unit_no", temp.getUnitNo());
                    item.put("room_no", temp.getRoomNo());
                    item.put("indoor_temp", temp.getIndoorTemp());
                    item.put("report_time", temp.getReportTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")));
                    
                    // 添加状态
                    double temperature = temp.getIndoorTemp();
                    String status = temperature < 18 ? "low" : temperature > 24 ? "high" : "normal";
                    item.put("status", status);
                    
                    // 添加房间信息
                    item.put("room_info", temp.getBuildingNo() + temp.getUnitNo() + temp.getRoomNo());
                    
                    return item;
                })
                .collect(Collectors.toList());

            Map<String, Object> data = new HashMap<>();
            data.put("list", resultList);
            data.put("total", resultList.size());

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", data);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Error in GET /api/temperature/list: {}", e.getMessage());
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<?> getTemperatureById(@PathVariable Long id) {
        logger.info("Accessing GET /api/temperature/{}", id);
        try {
            RoomTemperature temperature = temperatureService.getTemperatureById(id);
            
            if (temperature == null) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("msg", "记录不存在");
                return ResponseEntity.ok(response);
            }

            // 转换为Map处理返回数据
            Map<String, Object> detail = new HashMap<>();
            detail.put("id", temperature.getId());
            detail.put("community_name", temperature.getCommunityName());
            detail.put("building_no", temperature.getBuildingNo());
            detail.put("unit_no", temperature.getUnitNo());
            detail.put("room_no", temperature.getRoomNo());
            detail.put("indoor_temp", temperature.getIndoorTemp());
            detail.put("outdoor_temp", temperature.getOutdoorTemp());
            detail.put("latitude", temperature.getLatitude());
            detail.put("longitude", temperature.getLongitude());
            detail.put("image_url", temperature.getImageUrl());
            detail.put("remark", temperature.getRemark());
            detail.put("report_time", 
                temperature.getReportTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")));

            // 处理温度状态
            double temp = temperature.getIndoorTemp() != null ? temperature.getIndoorTemp() : 0;
            detail.put("status", temp < 18 ? "low" : temp > 24 ? "high" : "normal");

            // 处理图片URL
            List<String> photos = new ArrayList<>();
            if (temperature.getImageUrl() != null && !temperature.getImageUrl().isEmpty()) {
                photos.add(temperature.getImageUrl());
            }
            detail.put("photos", photos);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", detail);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("Error in GET /api/temperature/{}: {}", id, e.getMessage());
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("msg", e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    @PostMapping
    public ResponseEntity<?> createTemperature(@RequestBody TemperatureRequest temperatureRequest) {
        logger.info("Accessing POST /api/temperature");
        // TODO: Implement create temperature logic
        return ResponseEntity.ok().body("Create temperature endpoint");
    }

    @PutMapping("/{id}")
    public ResponseEntity<?> updateTemperature(@PathVariable Long id, @RequestBody TemperatureRequest temperatureRequest) {
        logger.info("Accessing PUT /api/temperature/{}", id);
        // TODO: Implement update temperature logic
        return ResponseEntity.ok().body("Update temperature endpoint");
    }

    @PostMapping("/report")
    public ResponseEntity<?> reportTemperature(@RequestBody TemperatureRequest request) {
        logger.info("Accessing POST /api/temperature/report with request: {}", request);
        try {
            // 参数验证
            if (request.getBuildingNo() == null || request.getBuildingNo().trim().isEmpty()) {
                throw new IllegalArgumentException("楼号不能为空");
            }
            if (request.getUnitNo() == null || request.getUnitNo().trim().isEmpty()) {
                throw new IllegalArgumentException("单元号不能为空");
            }
            if (request.getRoomNo() == null || request.getRoomNo().trim().isEmpty()) {
                throw new IllegalArgumentException("房间号不能为空");
            }
            if (request.getCommunityName() == null || request.getCommunityName().trim().isEmpty()) {
                throw new IllegalArgumentException("小区名不能为空");
            }

            // 创建新的温度记录实体
            RoomTemperature temperature = new RoomTemperature();
            temperature.setCommunityName(request.getCommunityName().trim());
            temperature.setBuildingNo(request.getBuildingNo().trim());
            temperature.setUnitNo(request.getUnitNo().trim());
            temperature.setRoomNo(request.getRoomNo().trim());
            temperature.setIndoorTemp(request.getIndoorTemp());
            temperature.setOutdoorTemp(request.getOutdoorTemp());
            temperature.setLatitude(request.getLatitude());
            temperature.setLongitude(request.getLongitude());
            temperature.setReportTime(LocalDateTime.now());
            temperature.setImageUrl(request.getImageUrl());
            temperature.setVideoUrl(request.getVideoUrl());

            // 保存温度记录
            temperatureService.saveRoomTemperature(temperature);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "温度上报成功");
            return ResponseEntity.ok(response);

        } catch (IllegalArgumentException e) {
            logger.warn("Invalid request in POST /api/temperature/report: {}", e.getMessage());
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            logger.error("Error in POST /api/temperature/report: {}", e.getMessage(), e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "温度上报失败：" + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
 
}
 