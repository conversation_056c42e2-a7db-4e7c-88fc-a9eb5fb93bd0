<template>
	<view class="about-container">
		<!-- 加载中提示 -->
		<view class="loading-container" v-if="isLoading">
			<view class="loading-spinner"></view>
			<text class="loading-text">加载中...</text>
		</view>
		
		<!-- 内容区域 -->
		<view v-else>
			<view class="logo-section">
				<image class="logo" :src="getFullImageUrl(systemParams.systemLogo)" mode="aspectFit"></image>
				<view class="app-info">
					<text class="app-name">{{ systemParams.systemName }}</text>
					<text class="app-version">版本号: {{ systemParams.systemVersions }}</text>
				</view>
			</view>
			
			<view class="info-section">
				<view class="info-group">
					<view class="group-title">系统信息</view>
					<view class="info-item">
						<text class="item-label">系统名称</text>
						<text class="item-value">{{ systemParams.systemName }}</text>
					</view>
					<view class="info-item">
						<text class="item-label">系统版本</text>
						<text class="item-value">{{ systemParams.systemVersions }} ({{ systemParams.buildNumber }})</text>
					</view>
					<view class="info-item">
						<text class="item-label">发布日期</text>
						<text class="item-value">{{ formatDate(systemParams.releaseDate) }}</text>
					</view>
					<view class="info-item" @click="checkUpdate">
						<text class="item-label">检查更新</text>
						<text class="check-update">检查 <text class="iconfont icon-arrow-right"></text></text>
					</view>
				</view>
				
				<view class="info-group">
					<view class="group-title">开发团队</view>
					<view class="info-item">
						<text class="item-label">开发公司</text>
						<text class="item-value">{{ systemParams.company }}</text>
					</view>
					<view class="info-item">
						<text class="item-label">联系电话</text>
						<text class="item-value">{{ systemParams.mobile }}</text>
					</view>
					<view class="info-item">
						<text class="item-label">官方网站</text>
						<text class="item-value link" @click="openWebsite">{{ systemParams.internetAddr }}</text>
					</view>
					<view class="info-item">
						<text class="item-label">联系邮箱</text>
						<text class="item-value">{{ systemParams.email }}</text>
					</view>
				</view>
				
				<view class="info-group">
					<view class="group-title">使用帮助</view>
					<view class="info-item" @click="navigateTo('/pages/user/agreement')">
						<text class="item-label">用户协议</text>
						<text class="item-value arrow">查看 <text class="iconfont icon-arrow-right"></text></text>
					</view>
					<view class="info-item" @click="navigateTo('/pages/user/faq')">
						<text class="item-label">常见问题</text>
						<text class="item-value arrow">查看 <text class="iconfont icon-arrow-right"></text></text>
					</view>
					<view class="info-item" @click="contactSupport">
						<text class="item-label">联系客服</text>
						<text class="item-value arrow">查看 <text class="iconfont icon-arrow-right"></text></text>
					</view>
				</view>
			</view>
			
			<view class="copyright">
				<text class="copyright-text">{{ systemParams.copyright }}</text>
				<text class="copyright-text">All Rights Reserved</text>
			</view>
		</view>
	</view>
</template>

<script>
import { systemApi } from '../../utils/api';
import uploadUtils from '@/utils/upload.js';

export default {
	data() {
		return {
			systemParams: {
				systemName: '热力维护管理系统',
				systemLogo: '/static/logo.png',
				systemVersions: 'V1.0.0',
				buildNumber: 'Build 20240502',
				copyright: 'Copyright © 2024 陕西杰明新能源有限公司',
				company: '陕西杰明新能源有限公司',
				linkman: '客服',
				mobile: '029-88888888',
				internetAddr: 'www.example.com',
				companyAddr: '陕西省西安市',
				intro: '热力维护管理系统是一款专业的热力设备维护管理软件，提供工单管理、巡检管理、设备管理等功能。'
			},
			isLoading: true
		}
	},
	onLoad() {
		// 加载系统参数
		this.loadSystemParams();
	},
	methods: {
		// 格式化日期
		formatDate(dateStr) {
		  if (!dateStr) return "";
		  const date = new Date(dateStr);
		  if (isNaN(date.getTime())) return dateStr;
		
		  const year = date.getFullYear();
		  const month = (date.getMonth() + 1).toString();
		  const day = date.getDate().toString();
		
		  return `${year}年${month}月${day}日`;
		},
		// 加载系统参数
		loadSystemParams() {
			this.isLoading = true;
			
			systemApi.getSystemParams()
				.then(res => {
					if (res.code === 200 && res.data) {
						// 更新系统参数
						this.systemParams = res.data;
					} else {
						console.error('获取系统参数失败:', res.message);
					}
				})
				.catch(err => {
					console.error('获取系统参数异常:', err);
				})
				.finally(() => {
					this.isLoading = false;
				});
		},
		
		// 导航到页面
		navigateTo(url) {
			uni.navigateTo({
				url: url
			});
		},
		
		// 打开网站
		openWebsite() {
			// 在实际应用中，这里应该使用系统浏览器打开网站
			uni.showModal({
				title: '打开网站',
				content: '是否打开官方网站？',
				success: (res) => {
					if (res.confirm) {
						// 实际应用中使用plus.runtime.openURL或其他方式打开网站
						uni.showToast({
							title: '打开网站功能开发中...',
							icon: 'none'
						});
					}
				}
			});
		},
		// 获取完整的图片URL
		getFullImageUrl(path) {
			console.log(uploadUtils.getFileUrl(path))
			return uploadUtils.getFileUrl(path);
		},
		// 检查更新
		checkUpdate() {
			uni.showLoading({
				title: '检查更新中...'
			});
			
			// 模拟检查更新
			setTimeout(() => {
				uni.hideLoading();
				
				// 这里模拟没有更新的情况
				uni.showModal({
					title: '检查更新',
					content: '当前已是最新版本',
					showCancel: false
				});
				
				// 如果有更新，可以显示如下对话框
				/*
				uni.showModal({
					title: '发现新版本',
					content: '发现新版本 V1.1.0，是否立即更新？\n\n更新内容：\n1. 修复了已知问题\n2. 优化了系统性能\n3. 增加了新功能',
					confirmText: '立即更新',
					success: (res) => {
						if (res.confirm) {
							// 处理更新逻辑
							uni.showToast({
								title: '更新功能开发中...',
								icon: 'none'
							});
						}
					}
				});
				*/
			}, 1500);
		},
		
		// 联系客服
		contactSupport() {
			uni.showModal({
				title: '联系客服',
				content: `客服热线：${this.systemParams.mobile}\n联系人：${this.systemParams.linkman}\n工作时间：周一至周五 9:00-18:00`,
				showCancel: false,
				confirmText: '我知道了'
			});
		}
	}
}
</script>

<style lang="scss">
.about-container {
	background-color: #f5f7fa;
	min-height: 100vh;
	padding-bottom: 30rpx;
}

.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 100vh;
	
	.loading-spinner {
		width: 80rpx;
		height: 80rpx;
		border: 6rpx solid rgba(24, 144, 255, 0.2);
		border-top: 6rpx solid $uni-color-primary;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin-bottom: 20rpx;
	}
	
	.loading-text {
		font-size: 28rpx;
		color: #666;
	}
	
	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}
}

.logo-section {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60rpx 0;
	background-color: #fff;
	
	.logo {
		width: 180rpx;
		height: 180rpx;
		margin-bottom: 30rpx;
		border-radius: 30rpx;
		box-shadow: 0 10rpx 30rpx rgba(24, 144, 255, 0.2);
	}
	
	.app-info {
		display: flex;
		flex-direction: column;
		align-items: center;
		
		.app-name {
			font-size: 36rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 10rpx;
		}
		
		.app-version {
			font-size: 26rpx;
			color: #999;
		}
	}
}

.info-section {
	padding: 20rpx 0;
	
	.info-group {
		margin: 20rpx 30rpx;
		background-color: #fff;
		border-radius: 12rpx;
		padding: 20rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
		
		.group-title {
			font-size: 30rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 20rpx;
			padding-left: 20rpx;
			border-left: 6rpx solid $uni-color-primary;
		}
		
		.info-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 20rpx 10rpx;
			border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
			
			&:last-child {
				border-bottom: none;
			}
			
			.item-label {
				font-size: 28rpx;
				color: #666;
			}
			
			.item-value {
				font-size: 28rpx;
				color: #333;
				
				&.link {
					color: $uni-color-primary;
					text-decoration: underline;
				}
				
				&.arrow {
					color: #999;
					display: flex;
					align-items: center;
					
					.iconfont {
						font-size: 24rpx;
						margin-left: 10rpx;
					}
				}
			}
			
			.check-update {
				color: $uni-color-primary;
				font-size: 28rpx;
				display: flex;
				align-items: center;
				
				.iconfont {
					font-size: 24rpx;
					margin-left: 10rpx;
				}
			}
		}
	}
}

.copyright {
	margin-top: 40rpx;
	padding: 30rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	
	.copyright-text {
		font-size: 24rpx;
		color: #999;
		line-height: 1.6;
	}
}
</style>