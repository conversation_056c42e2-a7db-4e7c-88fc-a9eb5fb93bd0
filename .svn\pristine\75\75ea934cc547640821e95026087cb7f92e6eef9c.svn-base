package com.heating.utils;

import lombok.Data;

@Data
public class Result<T> {
    private Integer code;
    private T data;
    
    public static <T> Result<T> success(T data) {
        Result<T> result = new Result<>();
        result.setCode(200);
        result.setData(data);
        return result;
    }
    
    public static <T> Result<T> error(Integer code, String message) {
        Result<T> result = new Result<>();
        result.setCode(code);
        result.setData((T) message);
        return result;
    }
} 