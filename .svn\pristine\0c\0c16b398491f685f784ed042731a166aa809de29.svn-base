package com.heating.repository;

import com.heating.entity.TManagerHeatUnit;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ManagerHeatUnitRepository extends JpaRepository<TManagerHeatUnit, Long> {
    
    /**
     * 根据管理员ID查询管理的热用户列表
     */
    List<TManagerHeatUnit> findByManagerId(Long managerId);
    
    /**
     * 根据热用户ID查询管理人员列表
     */
    List<TManagerHeatUnit> findByHeatUnitId(Long heatUnitId);
    
    /**
     * 根据管理员ID和热用户ID查询关联记录
     */
    TManagerHeatUnit findByManagerIdAndHeatUnitId(Long managerId, Long heatUnitId);
    
    /**
     * 获取指定管理员管理的热用户数量
     */
    @Query("SELECT COUNT(m) FROM TManagerHeatUnit m WHERE m.managerId = :managerId")
    long countByManagerId(@Param("managerId") Long managerId);
} 