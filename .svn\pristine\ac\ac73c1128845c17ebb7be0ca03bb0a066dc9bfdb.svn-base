<template>
	<view class="device-container">
		<!-- 搜索和筛选区域 -->
		<view class="search-filter-bar">
			<view class="search-box">
				<text class="iconfont icon-search"></text>
				<input type="text" placeholder="搜索设备名称或型号" v-model="searchKeyword" @input="handleSearch" />
				<text class="iconfont icon-clear" v-if="searchKeyword" @click="clearSearch"></text>
			</view>
			<view class="filter-btn" @click="showFilterModal">
				<text class="iconfont icon-filter"></text>
				<text>筛选</text>
			</view>
		</view>
		
		<!-- 状态切换区域 -->
		<view class="status-tabs">
			<view 
				v-for="(tab, index) in statusTabs" 
				:key="index" 
				class="status-tab" 
				:class="{ active: currentStatus === tab.value }"
				@click="switchStatus(tab.value)"
			>
				<text>{{ tab.name }}</text>
				<text class="tab-count">({{ tab.count }})</text>
			</view>
		</view>
		
		<!-- 设备列表 -->
		<scroll-view scroll-y="true" class="device-list" @scrolltolower="loadMoreDevices">
			<view v-if="deviceList.length > 0">
				<view 
					v-for="device in deviceList" 
					:key="device.deviceId" 
					class="device-card"
					@click="navigateToDetail(device.deviceId)"
				>
					<view class="device-status" :class="device.status"></view>
					<view class="device-info">
						<view class="device-name-type">
							<text class="device-name">{{ device.name }}</text>
							<text class="device-type">{{ device.type }}</text>
						</view>
						<view class="device-model">{{ device.model || '未知型号' }}</view>
						<view class="device-location">
							<text class="iconfont icon-location"></text>
							<text>{{ device.location ? `${device.location.building} ${device.location.room}` : '位置未知' }}</text>
						</view>
					</view>
					<view class="device-metrics">
						<view class="metric-item">
							<text class="metric-label">上次维护</text>
							<text class="metric-value">{{ formatDate(device.lastMaintenance) }}</text>
						</view>
						<view class="metric-item">
							<text class="metric-label">下次维护</text>
							<text class="metric-value">{{ formatDate(device.nextMaintenance) }}</text>
						</view>
						<view class="metric-item" v-if="device.alarmCount > 0">
							<text class="metric-label">告警数</text>
							<text class="metric-value alarm">{{ device.alarmCount }}</text>
						</view>
					</view>
					<view class="device-action">
						<text class="iconfont icon-detail"></text>
					</view>
				</view>
				
				<!-- 加载更多 -->
				<view class="loading-more" v-if="isLoading">
					<text>加载中...</text>
				</view>
				<view class="no-more" v-if="noMoreData && !isLoading">
					<text>没有更多数据了</text>
				</view>
			</view>
			
			<!-- 无数据提示 -->
			<view class="empty-list" v-else>
				<image src="/static/images/empty.png" mode="aspectFit"></image>
				<text>暂无设备数据</text>
			</view>
		</scroll-view>
		
		<!-- 筛选弹窗 -->
		<uni-popup ref="filterPopup" type="bottom">
			<view class="filter-modal">
				<view class="filter-header">
					<text class="filter-title">筛选条件</text>
					<text class="filter-reset" @click="resetFilter">重置</text>
				</view>
				
				<view class="filter-content">
					<!-- 设备类型筛选 -->
					<view class="filter-section">
						<text class="filter-section-title">设备类型</text>
						<view class="filter-options">
							<view 
								v-for="(type, index) in deviceTypes" 
								:key="index"
								class="filter-option" 
								:class="{ selected: filterOptions.type === type.value }"
								@click="selectFilterOption('type', type.value)"
							>
								{{ type.name }}
							</view>
						</view>
					</view>
					
					<!-- 设备区域筛选 -->
					<view class="filter-section">
						<text class="filter-section-title">设备区域</text>
						<view class="filter-options">
							<view 
								v-for="(area, index) in deviceAreas" 
								:key="index"
								class="filter-option" 
								:class="{ selected: filterOptions.area === area.value }"
								@click="selectFilterOption('area', area.value)"
							>
								{{ area.name }}
							</view>
						</view>
					</view>
					
					<!-- 排序方式 -->
					<view class="filter-section">
						<text class="filter-section-title">排序方式</text>
						<view class="filter-options">
							<view 
								v-for="(sort, index) in sortOptions" 
								:key="index"
								class="filter-option" 
								:class="{ selected: filterOptions.sortBy === sort.value }"
								@click="selectFilterOption('sortBy', sort.value)"
							>
								{{ sort.name }}
							</view>
						</view>
					</view>
				</view>
				
				<view class="filter-footer">
					<button class="btn-cancel" @click="cancelFilter">取消</button>
					<button class="btn-apply" @click="applyFilter">确认</button>
				</view>
			</view>
		</uni-popup>
		
		<!-- 悬浮添加按钮 -->
		<view class="fab-button" @click="navigateToAdd">
			<text class="iconfont icon-add"></text>
		</view> 
	</view>
</template>

<script>
	import CustomTabBar from '@/components/CustomTabBar.vue';

	export default {
		components: {
			CustomTabBar
		},
		data() {
			return {
				searchKeyword: '',
				currentStatus: 'all',
				statusTabs: [
					{ name: '全部', value: 'all', count: 0 },
					{ name: '在线', value: 'online', count: 0 },
					{ name: '离线', value: 'offline', count: 0 },
					{ name: '故障', value: 'fault', count: 0 }
				],
				deviceList: [],
				page: 1,
				pageSize: 10,
				isLoading: false,
				noMoreData: false,
				filterOptions: {
					type: '',
					area: '',
					sortBy: 'installTime',
					sortOrder: 'desc'
				},
				deviceTypes: [
					{ name: '全部', value: '' },
					{ name: '泵类', value: 'pump' },
					{ name: '阀门', value: 'valve' },
					{ name: '传感器', value: 'sensor' },
					{ name: '控制器', value: 'controller' }
				],
				deviceAreas: [
					{ name: '全部', value: '' },
					{ name: '东区', value: 'east' },
					{ name: '西区', value: 'west' },
					{ name: '南区', value: 'south' },
					{ name: '北区', value: 'north' }
				],
				sortOptions: [
					{ name: '安装时间', value: 'installTime' },
					{ name: '维护周期', value: 'maintainPeriod' },
					{ name: '告警数量', value: 'alarmCount' }
				]
			}
		},
		onLoad() {
			this.loadDeviceList();
			this.loadDeviceStats();
		},
		methods: {
			// 加载设备列表
			loadDeviceList(append = false) {
				if (this.isLoading) return;
				
				this.isLoading = true;
				
				// 构造请求参数
				const params = {
					page: this.page,
					pageSize: this.pageSize,
					status: this.currentStatus === 'all' ? '' : this.currentStatus,
					keyword: this.searchKeyword,
					...this.filterOptions
				};
				
				// 模拟API调用
				setTimeout(() => {
					// 实际项目中应该调用API
					// uni.request({
					//     url: '/api/devices/list',
					//     method: 'POST',
					//     data: params,
					//     success: (res) => {
					//         // 处理返回的数据
					//     }
					// });
					
					// 模拟数据
					const mockData = this.getMockDeviceList();
					
					// 更新设备列表
					if (append) {
						this.deviceList = [...this.deviceList, ...mockData];
					} else {
						this.deviceList = mockData;
					}
					
					// 是否还有更多数据
					this.noMoreData = mockData.length < this.pageSize;
					this.isLoading = false;
				}, 500);
			},
			
			// 加载设备统计
			loadDeviceStats() {
				// 模拟API调用
				setTimeout(() => {
					// 实际项目中应该调用API
					// uni.request({
					//     url: '/api/devices/stats',
					//     method: 'GET',
					//     success: (res) => {
					//         // 处理返回的数据
					//     }
					// });
					
					// 模拟数据
					const stats = {
						total: 10,
						online: 5,
						offline: 3,
						fault: 2
					};
					
					// 更新选项卡数量
					this.statusTabs[0].count = stats.total;
					this.statusTabs[1].count = stats.online;
					this.statusTabs[2].count = stats.offline;
					this.statusTabs[3].count = stats.fault;
				}, 300);
			},
			
			// 搜索设备
			handleSearch() {
				this.page = 1;
				this.noMoreData = false;
				this.loadDeviceList();
			},
			
			// 清除搜索
			clearSearch() {
				this.searchKeyword = '';
				this.handleSearch();
			},
			
			// 切换状态选项卡
			switchStatus(status) {
				if (this.currentStatus === status) return;
				
				this.currentStatus = status;
				this.page = 1;
				this.noMoreData = false;
				this.loadDeviceList();
			},
			
			// 加载更多设备
			loadMoreDevices() {
				if (this.isLoading || this.noMoreData) return;
				
				this.page++;
				this.loadDeviceList(true);
			},
			
			// 显示筛选弹窗
			showFilterModal() {
				this.$refs.filterPopup.open();
			},
			
			// 选择筛选选项
			selectFilterOption(field, value) {
				this.filterOptions[field] = value;
			},
			
			// 重置筛选条件
			resetFilter() {
				this.filterOptions = {
					type: '',
					area: '',
					sortBy: 'installTime',
					sortOrder: 'desc'
				};
			},
			
			// 取消筛选
			cancelFilter() {
				this.$refs.filterPopup.close();
			},
			
			// 应用筛选
			applyFilter() {
				this.page = 1;
				this.noMoreData = false;
				this.loadDeviceList();
				this.$refs.filterPopup.close();
			},
			
			// 跳转到设备详情
			navigateToDetail(deviceId) {
				uni.navigateTo({
					url: `/pages/device/detail?id=${deviceId}`
				});
			},
			
			// 跳转到添加设备
			navigateToAdd() {
				uni.navigateTo({
					url: '/pages/device/add'
				});
			},
			
			// 格式化日期
			formatDate(dateString) {
				if (!dateString) return '未知';
				
				const date = new Date(dateString);
				return `${date.getMonth() + 1}月${date.getDate()}日`;
			},
			
			// 获取模拟设备列表
			getMockDeviceList() {
				return [
					{
						deviceId: "dev_001",
						name: "1号循环泵",
						type: "pump",
						model: "XGZ-2023",
						status: "online",
						location: {
							building: "3号楼",
							floor: "1层",
							room: "泵房",
							coordinates: {
								lat: 39.904200,
								lng: 116.407400
							}
						},
						lastMaintenance: "2023-12-01 10:15:00",
						nextMaintenance: "2023-12-16 10:15:00",
						alarmCount: 0
					},
					{
						deviceId: "dev_002",
						name: "2号阀门",
						type: "valve",
						model: "DFV-2022",
						status: "online",
						location: {
							building: "3号楼",
							floor: "1层",
							room: "阀门间",
							coordinates: {
								lat: 39.904300,
								lng: 116.407500
							}
						},
						lastMaintenance: "2023-12-02 22:30:00",
						nextMaintenance: "2023-12-16 22:30:00",
						alarmCount: 0
					},
					{
						deviceId: "dev_003",
						name: "3号控制器",
						type: "controller",
						model: "CTL-2023",
						status: "fault",
						location: {
							building: "4号楼",
							floor: "1层",
							room: "控制室",
							coordinates: {
								lat: 39.904400,
								lng: 116.407600
							}
						},
						lastMaintenance: "2023-12-03 14:20:00",
						nextMaintenance: "2023-12-17 14:20:00",
						alarmCount: 2
					},
					{
						deviceId: "dev_004",
						name: "温度传感器",
						type: "sensor",
						model: "TSR-100",
						status: "offline",
						location: {
							building: "4号楼",
							floor: "2层",
							room: "机房",
							coordinates: {
								lat: 39.904500,
								lng: 116.407700
							}
						},
						lastMaintenance: "2023-12-04 09:10:00",
						nextMaintenance: "2023-12-18 09:10:00",
						alarmCount: 0
					},
					{
						deviceId: "dev_005",
						name: "3号阀门",
						type: "valve",
						model: "DFV-2022",
						status: "online",
						location: {
							building: "5号楼",
							floor: "1层",
							room: "阀门间",
							coordinates: {
								lat: 39.904600,
								lng: 116.407800
							}
						},
						lastMaintenance: "2023-12-05 19:20:00",
						nextMaintenance: "2023-12-19 19:20:00",
						alarmCount: 0
					}
				];
			}
		}
	}
</script>

<style lang="scss">
	.device-container {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f5f5f5;
	}
	
	.search-filter-bar {
		display: flex;
		padding: 20rpx 30rpx;
		background-color: #fff;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
		
		.search-box {
			flex: 1;
			height: 70rpx;
			background-color: #f5f5f5;
			border-radius: 35rpx;
			display: flex;
			align-items: center;
			padding: 0 20rpx;
			margin-right: 20rpx;
			
			.iconfont {
				font-size: 36rpx;
				color: #999;
				margin-right: 10rpx;
			}
			
			input {
				flex: 1;
				height: 70rpx;
				font-size: 28rpx;
			}
		}
		
		.filter-btn {
			display: flex;
			align-items: center;
			padding: 0 20rpx;
			
			.iconfont {
				font-size: 36rpx;
				color: $uni-color-primary;
				margin-right: 6rpx;
			}
			
			text {
				font-size: 28rpx;
				color: $uni-color-primary;
			}
		}
	}
	
	.status-tabs {
		display: flex;
		background-color: #fff;
		margin-bottom: 20rpx;
		
		.status-tab {
			flex: 1;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			height: 80rpx;
			font-size: 28rpx;
			color: $uni-text-color;
			position: relative;
			
			&.active {
				color: $uni-color-primary;
				font-weight: bold;
				
				&::after {
					content: '';
					position: absolute;
					bottom: 0;
					left: 50%;
					transform: translateX(-50%);
					width: 40rpx;
					height: 4rpx;
					background-color: $uni-color-primary;
					border-radius: 2rpx;
				}
			}
			
			.tab-count {
				font-size: 24rpx;
				color: $uni-text-color-grey;
				margin-left: 4rpx;
			}
		}
	}
	
	.device-list {
		flex: 1;
		padding: 0 30rpx;
		
		.device-card {
			display: flex;
			position: relative;
			background-color: #fff;
			border-radius: 8rpx;
			margin-bottom: 20rpx;
			padding: 20rpx;
			box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
			
			.device-status {
				position: absolute;
				top: 20rpx;
				left: 0;
				width: 8rpx;
				height: 36rpx;
				border-radius: 0 4rpx 4rpx 0;
				
				&.online {
					background-color: $uni-color-success;
				}
				
				&.offline {
					background-color: $uni-text-color-grey;
				}
				
				&.fault {
					background-color: $uni-color-error;
				}
			}
			
			.device-info {
				flex: 1;
				padding-left: 16rpx;
				
				.device-name-type {
					display: flex;
					align-items: center;
					margin-bottom: 10rpx;
					
					.device-name {
						font-size: 32rpx;
						font-weight: bold;
						color: $uni-text-color;
						margin-right: 10rpx;
					}
					
					.device-type {
						font-size: 24rpx;
						color: #fff;
						background-color: $uni-color-primary;
						padding: 4rpx 12rpx;
						border-radius: 4rpx;
					}
				}
				
				.device-model {
					font-size: 26rpx;
					color: $uni-text-color-grey;
					margin-bottom: 10rpx;
				}
				
				.device-location {
					display: flex;
					align-items: center;
					font-size: 26rpx;
					color: $uni-text-color-grey;
					
					.iconfont {
						margin-right: 6rpx;
					}
				}
			}
			
			.device-metrics {
				display: flex;
				flex-direction: column;
				justify-content: center;
				margin-right: 20rpx;
				
				.metric-item {
					display: flex;
					align-items: center;
					margin-bottom: 6rpx;
					
					.metric-label {
						font-size: 24rpx;
						color: $uni-text-color-grey;
						margin-right: 10rpx;
					}
					
					.metric-value {
						font-size: 24rpx;
						color: $uni-text-color;
						
						&.alarm {
							color: $uni-color-error;
							font-weight: bold;
						}
					}
				}
			}
			
			.device-action {
				display: flex;
				align-items: center;
				
				.iconfont {
					font-size: 40rpx;
					color: $uni-color-primary;
				}
			}
		}
		
		.loading-more, .no-more {
			text-align: center;
			font-size: 26rpx;
			color: $uni-text-color-grey;
			padding: 20rpx 0;
		}
		
		.empty-list {
			display: flex;
			flex-direction: column;
			align-items: center;
			padding-top: 200rpx;
			
			image {
				width: 200rpx;
				height: 200rpx;
				margin-bottom: 20rpx;
			}
			
			text {
				font-size: 28rpx;
				color: $uni-text-color-grey;
			}
		}
	}
	
	.filter-modal {
		background-color: #fff;
		border-radius: 16rpx 16rpx 0 0;
		overflow: hidden;
		
		.filter-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 30rpx;
			border-bottom: 1rpx solid #eee;
			
			.filter-title {
				font-size: 32rpx;
				font-weight: bold;
				color: $uni-text-color;
			}
			
			.filter-reset {
				font-size: 28rpx;
				color: $uni-color-primary;
			}
		}
		
		.filter-content {
			padding: 20rpx 30rpx;
			max-height: 60vh;
			overflow-y: auto;
			
			.filter-section {
				margin-bottom: 30rpx;
				
				.filter-section-title {
					font-size: 28rpx;
					color: $uni-text-color;
					margin-bottom: 20rpx;
				}
				
				.filter-options {
					display: flex;
					flex-wrap: wrap;
					
					.filter-option {
						padding: 10rpx 30rpx;
						background-color: #f5f5f5;
						border-radius: 30rpx;
						font-size: 26rpx;
						color: $uni-text-color;
						margin-right: 20rpx;
						margin-bottom: 16rpx;
						
						&.selected {
							background-color: rgba(24, 144, 255, 0.1);
							color: $uni-color-primary;
						}
					}
				}
			}
		}
		
		.filter-footer {
			display: flex;
			padding: 20rpx;
			border-top: 1rpx solid #eee;
			
			button {
				flex: 1;
				height: 80rpx;
				line-height: 80rpx;
				font-size: 28rpx;
				border-radius: 40rpx;
				
				&::after {
					border: none;
				}
			}
			
			.btn-cancel {
				margin-right: 20rpx;
				background-color: #f5f5f5;
				color: $uni-text-color;
			}
			
			.btn-apply {
				background-color: $uni-color-primary;
				color: #fff;
			}
		}
	}
	
	.fab-button {
		position: fixed;
		right: 30rpx;
		bottom: 30rpx;
		width: 100rpx;
		height: 100rpx;
		background-color: $uni-color-primary;
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		box-shadow: 0 6rpx 16rpx rgba(24, 144, 255, 0.3);
		
		.iconfont {
			font-size: 48rpx;
			color: #fff;
		}
	}
</style> 