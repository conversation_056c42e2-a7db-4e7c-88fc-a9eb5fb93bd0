package com.heating.repository;

import com.heating.entity.order.TWorkOrderMaterial;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 工单耗材数据访问层
 */
@Repository
public interface WorkOrderMaterialRepository extends JpaRepository<TWorkOrderMaterial, Long> {
    
    /**
     * 根据工单ID查询工单耗材列表
     * 
     * @param workOrderId 工单ID
     * @return 工单耗材列表
     */
    List<TWorkOrderMaterial> findByWorkOrderId(Long workOrderId);
} 