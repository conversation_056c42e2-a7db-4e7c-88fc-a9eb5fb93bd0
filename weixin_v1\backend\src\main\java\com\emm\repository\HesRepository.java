package com.emm.repository;

import com.emm.model.Hes;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface HesRepository extends JpaRepository<Hes, Long> {

    @Query("SELECT new map(h.id as id, h.name as name) FROM Hes h")
    List<Map<String, Object>> findAllStations();

    @Query("SELECT new map(h.id as id, h.name as name) FROM Hes h WHERE h.useHeatUnitName = (SELECT u.name FROM UseHeatUnit u WHERE u.id = :communityId) ORDER BY h.name")
    List<Map<String, Object>> findStationsByCommunityId(@Param("communityId") Long communityId);
}