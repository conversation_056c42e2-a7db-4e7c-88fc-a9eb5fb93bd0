
.no-permission-item[data-v-9e1a7520] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0.5;
}
.no-permission-text[data-v-9e1a7520] {
  font-size: 0.75rem;
  color: #999;
  text-align: center;
}

/* 确保permission-check组件继承其父容器的布局特性 */
[data-v-9e1a7520] .permission-check {
  display: inherit;
  width: inherit;
  height: inherit;
  flex: inherit;
  position: inherit;
}

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  box-sizing: border-box;
}
.order-info-card {
  background-color: #fff;
  border-radius: 0.375rem;
  overflow: hidden;
  margin: 0.625rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
  padding: 0.9375rem;
}
.order-info-card .order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.625rem;
}
.order-info-card .order-header .order-number {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
}
.order-info-card .order-header .order-status {
  padding: 0.1875rem 0.5rem;
  border-radius: 0.1875rem;
  font-size: 0.75rem;
  text-align: center;
  min-width: 2.5rem;
}
.order-info-card .order-header .order-status.status-pending {
  background-color: rgba(250, 173, 20, 0.1);
  color: #faad14;
}
.order-info-card .order-header .order-status.status-processing {
  background-color: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}
.order-info-card .order-header .order-status.status-completed {
  background-color: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}
.order-info-card .order-header .order-status.status-overdue {
  background-color: rgba(245, 34, 45, 0.1);
  color: #f5222d;
}
.order-info-card .order-location {
  margin-bottom: 0.625rem;
}
.order-info-card .order-location .location-info {
  font-size: 0.875rem;
  color: #666;
}
.order-info-card .order-location .location-info .location-name {
  font-size: 1rem;
  color: #333;
  font-weight: bold;
}
.order-info-card .patrol-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.625rem;
}
.order-info-card .patrol-info .patrol-executor .patrol-label, .order-info-card .patrol-info .patrol-date .patrol-label {
  font-size: 0.75rem;
  color: #999;
  margin-right: 0.3125rem;
}
.order-info-card .patrol-info .patrol-executor .patrol-value, .order-info-card .patrol-info .patrol-date .patrol-value {
  font-size: 0.875rem;
  color: #333;
  font-weight: bold;
}
.detail-tabs {
  display: flex;
  background-color: #fff;
  margin: 0 0.625rem 0.625rem;
  border-radius: 0.375rem;
  overflow: hidden;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
  position: -webkit-sticky;
  position: sticky;
  /* 使选项卡固定在顶部 */
  top: 0;
  z-index: 10;
}
.detail-tabs .tab-item {
  flex: 1;
  text-align: center;
  height: 2.5rem;
  line-height: 2.5rem;
  font-size: 0.875rem;
  color: #666;
  position: relative;
}
.detail-tabs .tab-item.active {
  color: #1890ff;
  font-weight: bold;
}
.detail-tabs .tab-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40%;
  height: 0.125rem;
  background-color: #1890ff;
  border-radius: 0.0625rem;
}
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 40vh;
}
.loading-container .loading-spinner {
  width: 2.1875rem;
  height: 2.1875rem;
  border: 0.1875rem solid #f3f3f3;
  border-top: 0.1875rem solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 0.625rem;
}
.loading-container .loading-text {
  font-size: 0.875rem;
  color: #666;
}
@keyframes spin {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2.5rem 1.25rem;
  width: 100%;
  box-sizing: border-box;
}
.error-container .error-icon {
  width: 2.5rem;
  height: 2.5rem;
  line-height: 2.5rem;
  text-align: center;
  background-color: #fff2f0;
  border-radius: 50%;
  color: #f5222d;
  font-size: 1.5625rem;
  font-weight: bold;
  margin-bottom: 0.625rem;
}
.error-container .error-message {
  font-size: 0.875rem;
  color: #666;
  text-align: center;
  margin-bottom: 0.9375rem;
}
.error-container .retry-button {
  background-color: #1890ff;
  color: #fff;
  font-size: 0.875rem;
  padding: 0.5rem 1.25rem;
  border-radius: 0.25rem;
  border: none;
}
.content-container {
  flex: 1;
  overflow: visible;
  /* 改为visible，允许内容自然流动 */
  padding-bottom: 3.75rem;
  /* 为底部按钮留出空间 */
}
.tab-content {
  height: auto;
  min-height: 60vh;
  /* 确保内容区域有足够的高度 */
  padding: 0 0.625rem;
  box-sizing: border-box;
}
.scroll-view-content {
  height: auto;
  /* 移除固定高度限制，改为自适应内容高度 */
  max-height: none;
  /* 移除最大高度限制 */
  overflow: visible;
  /* 确保内容可见 */
}
.info-section {
  background-color: #fff;
  border-radius: 0.375rem;
  padding: 0.9375rem;
  margin-bottom: 0.625rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.info-section .section-title {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.625rem;
  padding-left: 0.625rem;
}
.info-section .section-content .info-item {
  display: flex;
  padding: 0.5rem 0;
  border-bottom: 0.0625rem solid #f5f5f5;
}
.info-section .section-content .info-item:last-child {
  border-bottom: none;
}
.info-section .section-content .info-item .info-label {
  width: 5rem;
  font-size: 0.875rem;
  color: #666;
}
.info-section .section-content .info-item .info-value {
  flex: 1;
  font-size: 0.875rem;
  color: #333;
}
.info-section .section-content .info-item .info-value.normal-count {
  color: #52c41a;
  font-weight: bold;
}
.info-section .section-content .info-item .info-value.abnormal-count {
  color: #f5222d;
  font-weight: bold;
}
.info-section .section-content .info-item .info-value.status-value {
  font-weight: bold;
}
.info-section .section-content .info-item .info-value.status-value.pending {
  color: #faad14;
}
.info-section .section-content .info-item .info-value.status-value.processing {
  color: #1890ff;
}
.info-section .section-content .info-item .info-value.status-value.completed {
  color: #52c41a;
}
.info-section .section-content .info-item .info-value.status-value.overdue {
  color: #f5222d;
}
.patrol-results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.625rem;
  padding: 0.625rem;
  background-color: #fff;
  border-radius: 0.375rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.patrol-results-header .results-title {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
}
.patrol-results-header .results-summary {
  display: flex;
  align-items: center;
}
.patrol-results-header .results-summary .results-count {
  font-size: 0.8125rem;
  color: #999;
  margin-right: 0.5rem;
}
.patrol-results-header .results-summary .abnormal-count {
  font-size: 0.8125rem;
  color: #f5222d;
  font-weight: bold;
}
.empty-results {
  padding: 0;
  text-align: center;
  background-color: #fff;
  border-radius: 0.375rem;
  margin-bottom: 0.625rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
  min-height: 15.625rem;
  /* 增加最小高度，确保有足够空间 */
  display: flex;
  align-items: center;
  justify-content: center;
}
.empty-results .empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.25rem 0;
}
.empty-results uni-image {
  width: 6.25rem;
  height: 6.25rem;
  margin-bottom: 0.9375rem;
}
.empty-results uni-text {
  font-size: 0.875rem;
  color: #999;
  display: block;
  width: 100%;
  text-align: center;
  line-height: 1.5;
}
.task-list .task-item {
  padding: 0.625rem;
  background-color: #fff;
  border-radius: 0.375rem;
  margin-bottom: 0.625rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.task-list .task-item .task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}
.task-list .task-item .task-header .task-left {
  display: flex;
  align-items: center;
}
.task-list .task-item .task-header .task-left .task-status {
  width: 0.625rem;
  height: 0.625rem;
  border-radius: 50%;
  margin-right: 0.3125rem;
}
.task-list .task-item .task-header .task-left .task-status.completed {
  background-color: #52c41a;
}
.task-list .task-item .task-header .task-left .task-status.abnormal {
  background-color: #f5222d;
}
.task-list .task-item .task-header .task-left .task-status.pending {
  background-color: #faad14;
}
.task-list .task-item .task-header .task-left .task-status.overdue {
  background-color: #f5222d;
}
.task-list .task-item .task-header .task-left .task-title {
  font-size: 0.9375rem;
  font-weight: bold;
  color: #333;
}
.task-list .task-item .task-header .task-status-text {
  padding: 0.125rem 0.5rem;
  border-radius: 0.625rem;
  font-size: 0.75rem;
}
.task-list .task-item .task-header .task-status-text.status-normal {
  background-color: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}
.task-list .task-item .task-header .task-status-text.status-abnormal {
  background-color: rgba(245, 34, 45, 0.1);
  color: #f5222d;
}
.task-list .task-item .task-header .task-status-text.status-pending {
  background-color: rgba(250, 173, 20, 0.1);
  color: #faad14;
}
.task-list .task-item .task-header .task-status-text.status-overdue {
  background-color: rgba(245, 34, 45, 0.1);
  color: #f5222d;
}
.task-list .task-item .task-details {
  background-color: #f8f8f8;
  border-radius: 0.25rem;
  padding: 0.5rem;
  margin-top: 0.5rem;
}
.task-list .task-item .task-details .detail-row {
  display: flex;
  margin-bottom: 0.5rem;
}
.task-list .task-item .task-details .detail-row .detail-label {
  width: 3.75rem;
  font-size: 0.8125rem;
  color: #666;
}
.task-list .task-item .task-details .detail-row .detail-value {
  flex: 1;
  font-size: 0.8125rem;
  color: #333;
  word-break: break-all;
}
.task-list .task-item .task-details .detail-row .detail-value.value-abnormal {
  color: #f5222d;
}
.task-list .task-item .task-details .task-images {
  margin-top: 0.625rem;
}
.task-list .task-item .task-details .task-images .images-title {
  font-size: 0.875rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.3125rem;
}
.task-list .task-item .task-details .task-images .image-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -0.3125rem;
}
.task-list .task-item .task-details .task-images .image-list uni-image {
  width: 33.33%;
  height: 6.25rem;
  padding: 0.3125rem;
  box-sizing: border-box;
  flex-shrink: 0;
  border-radius: 0.25rem;
}
.all-images {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -0.3125rem;
  padding: 0.625rem;
  background-color: #fff;
  border-radius: 0.375rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.all-images .image-card {
  width: 33.33%;
  padding: 0.3125rem;
  box-sizing: border-box;
  position: relative;
  height: 6.875rem;
}
.all-images .image-card uni-image {
  width: 100%;
  height: 5.625rem;
  object-fit: cover;
  border-radius: 0.25rem;
}
.all-images .image-card .image-item-name {
  font-size: 0.75rem;
  color: #666;
  margin-top: 0.3125rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.action-buttons {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 0.625rem;
  background-color: #fff;
  display: flex;
  justify-content: center;
  box-shadow: 0 -0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.action-buttons .action-btn {
  flex: 1;
  height: 2.5rem;
  line-height: 2.5rem;
  text-align: center;
  border-radius: 0.25rem;
  font-size: 0.9375rem;
  margin: 0 0.3125rem;
}
.action-buttons .action-btn.start {
  background-color: #1890ff;
  color: #fff;
}
.action-buttons .action-btn.continue {
  background-color: #52c41a;
  color: #fff;
}
.action-buttons .action-btn.complete {
  background-color: #faad14;
  color: #fff;
}
.section-title {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
  margin: 0.625rem 0;
  padding-left: 0.625rem;
  border-left: 0.1875rem solid #1890ff;
}