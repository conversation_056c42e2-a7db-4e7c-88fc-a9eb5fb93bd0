2025-08-21T14:15:20.803+08:00  INFO 2776 --- [main] com.heating.HeatingApplication           : Starting HeatingApplication using Java 21.0.7 with PID 2776 (E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend\target\classes started by A in E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend)
2025-08-21T14:15:20.816+08:00 DEBUG 2776 --- [main] com.heating.HeatingApplication           : Running with Spring Boot v3.2.3, Spring v6.1.4
2025-08-21T14:15:20.817+08:00  INFO 2776 --- [main] com.heating.HeatingApplication           : No active profile set, falling back to 1 default profile: "default"
2025-08-21T14:15:21.632+08:00  INFO 2776 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-21T14:15:21.948+08:00  INFO 2776 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 144 ms. Found 34 JPA repository interfaces.
2025-08-21T14:15:22.527+08:00  INFO 2776 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8889 (http)
2025-08-21T14:15:22.538+08:00  INFO 2776 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-21T14:15:22.538+08:00  INFO 2776 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-08-21T14:15:22.592+08:00  INFO 2776 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-21T14:15:22.592+08:00  INFO 2776 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1735 ms
2025-08-21T14:15:22.652+08:00 DEBUG 2776 --- [main] c.h.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-08-21T14:15:22.767+08:00  INFO 2776 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-21T14:15:22.836+08:00  INFO 2776 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-08-21T14:15:22.871+08:00  INFO 2776 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-21T14:15:22.970+08:00  INFO 2776 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Starting...
2025-08-21T14:15:23.716+08:00  INFO 2776 --- [main] com.zaxxer.hikari.pool.HikariPool        : MyHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@43ab0659
2025-08-21T14:15:23.718+08:00  INFO 2776 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Start completed.
2025-08-21T14:15:24.226+08:00  INFO 2776 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-21T14:15:25.867+08:00  INFO 2776 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-21T14:15:26.627+08:00  INFO 2776 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-21T14:15:26.854+08:00  INFO 2776 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-21T14:15:28.734+08:00  INFO 2776 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源跨域访问: /uploads/**
2025-08-21T14:15:28.838+08:00  INFO 2776 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源映射: /uploads/** -> file:/home/<USER>/work/web/uploads/
2025-08-21T14:15:28.880+08:00  INFO 2776 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@31716c31, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2310e8a4, org.springframework.security.web.context.SecurityContextHolderFilter@770bdd86, org.springframework.security.web.header.HeaderWriterFilter@22b0ddc9, org.springframework.web.filter.CorsFilter@23d9a633, org.springframework.security.web.authentication.logout.LogoutFilter@4f7b4b50, com.heating.filter.JwtAuthenticationFilter@492521c4, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2568cc21, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@144aaab1, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@21cfa25, org.springframework.security.web.access.ExceptionTranslationFilter@6a99a045, org.springframework.security.web.access.intercept.AuthorizationFilter@3bd3d820]
2025-08-21T14:15:29.173+08:00  INFO 2776 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8889 (http) with context path ''
2025-08-21T14:15:29.181+08:00  INFO 2776 --- [main] com.heating.HeatingApplication           : Started HeatingApplication in 8.791 seconds (process running for 9.625)
2025-08-21T14:15:58.905+08:00  INFO 2776 --- [http-nio-8889-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-21T14:15:58.905+08:00  INFO 2776 --- [http-nio-8889-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-21T14:15:58.906+08:00  INFO 2776 --- [http-nio-8889-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-21T14:15:58.921+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing OPTIONS /api/auth/login
2025-08-21T14:15:58.935+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-21T14:15:58.948+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/api/auth/login?continue to session
2025-08-21T14:15:58.948+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-08-21T14:15:58.956+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing OPTIONS /error
2025-08-21T14:15:58.956+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-21T14:15:58.957+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/error?continue to session
2025-08-21T14:15:58.957+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
