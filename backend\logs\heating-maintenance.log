2025-08-22T08:24:29.668+08:00  INFO 11032 --- [main] com.heating.HeatingApplication           : Starting HeatingApplication using Java 21.0.7 with PID 11032 (E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend\target\classes started by A in E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend)
2025-08-22T08:24:29.684+08:00 DEBUG 11032 --- [main] com.heating.HeatingApplication           : Running with Spring Boot v3.2.3, Spring v6.1.4
2025-08-22T08:24:29.685+08:00  INFO 11032 --- [main] com.heating.HeatingApplication           : No active profile set, falling back to 1 default profile: "default"
2025-08-22T08:24:31.451+08:00  INFO 11032 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-22T08:24:31.565+08:00  INFO 11032 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 107 ms. Found 34 JPA repository interfaces.
2025-08-22T08:24:32.097+08:00  INFO 11032 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8889 (http)
2025-08-22T08:24:32.107+08:00  INFO 11032 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-22T08:24:32.108+08:00  INFO 11032 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-08-22T08:24:32.157+08:00  INFO 11032 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-22T08:24:32.158+08:00  INFO 11032 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2438 ms
2025-08-22T08:24:32.216+08:00 DEBUG 11032 --- [main] c.h.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-08-22T08:24:32.337+08:00  INFO 11032 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-22T08:24:32.407+08:00  INFO 11032 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-08-22T08:24:32.439+08:00  INFO 11032 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-22T08:24:32.540+08:00  INFO 11032 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Starting...
2025-08-22T08:24:36.140+08:00  INFO 11032 --- [main] com.zaxxer.hikari.pool.HikariPool        : MyHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@389008d1
2025-08-22T08:24:36.156+08:00  INFO 11032 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Start completed.
2025-08-22T08:24:36.457+08:00  INFO 11032 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-22T08:24:37.608+08:00  INFO 11032 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-22T08:24:38.179+08:00  INFO 11032 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-22T08:24:38.391+08:00  INFO 11032 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-22T08:24:39.973+08:00  INFO 11032 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源跨域访问: /uploads/**
2025-08-22T08:24:40.057+08:00  INFO 11032 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源映射: /uploads/** -> file:/home/<USER>/work/web/uploads/
2025-08-22T08:24:40.099+08:00  INFO 11032 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6df78021, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4b47646a, org.springframework.security.web.context.SecurityContextHolderFilter@34ee9000, org.springframework.security.web.header.HeaderWriterFilter@6846d654, org.springframework.web.filter.CorsFilter@67b8f02e, org.springframework.security.web.authentication.logout.LogoutFilter@35d0499b, com.heating.filter.JwtAuthenticationFilter@2e6d76ba, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6546c39, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3bd3d820, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6fdc624, org.springframework.security.web.access.ExceptionTranslationFilter@214b9b5, org.springframework.security.web.access.intercept.AuthorizationFilter@59fd35ad]
2025-08-22T08:24:40.362+08:00  INFO 11032 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8889 (http) with context path ''
2025-08-22T08:24:40.369+08:00  INFO 11032 --- [main] com.heating.HeatingApplication           : Started HeatingApplication in 11.068 seconds (process running for 12.242)
2025-08-22T08:41:00.121+08:00  INFO 11032 --- [http-nio-8889-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-22T08:41:00.121+08:00  INFO 11032 --- [http-nio-8889-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-22T08:41:00.122+08:00  INFO 11032 --- [http-nio-8889-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-22T08:41:00.136+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing OPTIONS /api/auth/login
2025-08-22T08:41:00.151+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-22T08:41:00.164+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/api/auth/login?continue to session
2025-08-22T08:41:00.164+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-08-22T08:41:00.173+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing OPTIONS /error
2025-08-22T08:41:00.173+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-22T08:41:00.173+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/error?continue to session
2025-08-22T08:41:00.173+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-08-22T08:41:55.282+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing POST /api/auth/login
2025-08-22T08:41:55.283+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured POST /api/auth/login
2025-08-22T08:41:55.795+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-22T08:41:55.800+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/auth/user-info
2025-08-22T08:41:55.801+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T08:41:55.801+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T08:41:55.825+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/auth/user-info
2025-08-22T08:41:57.269+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T08:41:57.269+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T08:41:57.269+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T08:41:57.270+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-22T08:41:57.270+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-22T08:41:57.270+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T08:41:57.270+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T08:41:57.270+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T08:41:57.270+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T08:41:57.272+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-22T08:41:57.273+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T08:41:57.273+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T08:41:57.273+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T08:41:57.274+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T08:41:57.274+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T08:41:57.275+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-22T08:41:57.276+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-22T08:41:57.276+08:00  INFO 11032 --- [http-nio-8889-exec-10] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-22T08:41:57.276+08:00  INFO 11032 --- [http-nio-8889-exec-10] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-22T08:41:57.277+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-22T08:41:57.277+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T08:41:57.277+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T08:41:57.284+08:00  INFO 11032 --- [http-nio-8889-exec-8] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-22T08:41:57.284+08:00  INFO 11032 --- [http-nio-8889-exec-8] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-22T08:41:57.285+08:00  INFO 11032 --- [http-nio-8889-exec-8] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-22T08:41:57.285+08:00  INFO 11032 --- [http-nio-8889-exec-4] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-22T08:41:57.285+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T08:41:57.358+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-22T08:41:57.359+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T08:41:57.359+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T08:41:57.362+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-22T08:41:57.413+08:00  INFO 11032 --- [http-nio-8889-exec-4] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-22T08:41:57.415+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T08:41:57.415+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T08:41:57.415+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T08:41:57.417+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T08:41:57.418+08:00  INFO 11032 --- [http-nio-8889-exec-2] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-22T08:41:57.418+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T08:41:57.453+08:00  INFO 11032 --- [http-nio-8889-exec-2] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-22T08:41:57.512+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-22T08:41:57.512+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T08:41:57.512+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T08:41:57.515+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-22T08:41:57.545+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-22T08:41:57.546+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T08:41:57.546+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T08:41:57.557+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-22T08:41:57.558+08:00  INFO 11032 --- [http-nio-8889-exec-3] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-22T08:41:57.558+08:00  INFO 11032 --- [http-nio-8889-exec-3] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-22T08:41:58.588+08:00  INFO 11032 --- [http-nio-8889-exec-8] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-22T08:41:58.592+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T08:41:58.592+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T08:41:58.592+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T08:41:58.595+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T08:41:58.596+08:00  INFO 11032 --- [http-nio-8889-exec-6] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-22T08:41:58.596+08:00  INFO 11032 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-22T08:41:58.596+08:00  INFO 11032 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-22T08:41:59.880+08:00  INFO 11032 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
