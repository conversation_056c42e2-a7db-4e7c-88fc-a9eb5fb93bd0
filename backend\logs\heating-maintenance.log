2025-08-21T14:15:20.803+08:00  INFO 2776 --- [main] com.heating.HeatingApplication           : Starting HeatingApplication using Java 21.0.7 with PID 2776 (E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend\target\classes started by A in E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend)
2025-08-21T14:15:20.816+08:00 DEBUG 2776 --- [main] com.heating.HeatingApplication           : Running with Spring Boot v3.2.3, Spring v6.1.4
2025-08-21T14:15:20.817+08:00  INFO 2776 --- [main] com.heating.HeatingApplication           : No active profile set, falling back to 1 default profile: "default"
2025-08-21T14:15:21.632+08:00  INFO 2776 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-21T14:15:21.948+08:00  INFO 2776 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 144 ms. Found 34 JPA repository interfaces.
2025-08-21T14:15:22.527+08:00  INFO 2776 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8889 (http)
2025-08-21T14:15:22.538+08:00  INFO 2776 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-21T14:15:22.538+08:00  INFO 2776 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-08-21T14:15:22.592+08:00  INFO 2776 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-21T14:15:22.592+08:00  INFO 2776 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1735 ms
2025-08-21T14:15:22.652+08:00 DEBUG 2776 --- [main] c.h.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-08-21T14:15:22.767+08:00  INFO 2776 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-21T14:15:22.836+08:00  INFO 2776 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-08-21T14:15:22.871+08:00  INFO 2776 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-21T14:15:22.970+08:00  INFO 2776 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Starting...
2025-08-21T14:15:23.716+08:00  INFO 2776 --- [main] com.zaxxer.hikari.pool.HikariPool        : MyHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@43ab0659
2025-08-21T14:15:23.718+08:00  INFO 2776 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Start completed.
2025-08-21T14:15:24.226+08:00  INFO 2776 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-21T14:15:25.867+08:00  INFO 2776 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-21T14:15:26.627+08:00  INFO 2776 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-21T14:15:26.854+08:00  INFO 2776 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-21T14:15:28.734+08:00  INFO 2776 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源跨域访问: /uploads/**
2025-08-21T14:15:28.838+08:00  INFO 2776 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源映射: /uploads/** -> file:/home/<USER>/work/web/uploads/
2025-08-21T14:15:28.880+08:00  INFO 2776 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@31716c31, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2310e8a4, org.springframework.security.web.context.SecurityContextHolderFilter@770bdd86, org.springframework.security.web.header.HeaderWriterFilter@22b0ddc9, org.springframework.web.filter.CorsFilter@23d9a633, org.springframework.security.web.authentication.logout.LogoutFilter@4f7b4b50, com.heating.filter.JwtAuthenticationFilter@492521c4, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2568cc21, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@144aaab1, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@21cfa25, org.springframework.security.web.access.ExceptionTranslationFilter@6a99a045, org.springframework.security.web.access.intercept.AuthorizationFilter@3bd3d820]
2025-08-21T14:15:29.173+08:00  INFO 2776 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8889 (http) with context path ''
2025-08-21T14:15:29.181+08:00  INFO 2776 --- [main] com.heating.HeatingApplication           : Started HeatingApplication in 8.791 seconds (process running for 9.625)
2025-08-21T14:15:58.905+08:00  INFO 2776 --- [http-nio-8889-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-21T14:15:58.905+08:00  INFO 2776 --- [http-nio-8889-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-21T14:15:58.906+08:00  INFO 2776 --- [http-nio-8889-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-21T14:15:58.921+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing OPTIONS /api/auth/login
2025-08-21T14:15:58.935+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-21T14:15:58.948+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/api/auth/login?continue to session
2025-08-21T14:15:58.948+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-08-21T14:15:58.956+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing OPTIONS /error
2025-08-21T14:15:58.956+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-21T14:15:58.957+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/error?continue to session
2025-08-21T14:15:58.957+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-08-21T14:34:18.619+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing OPTIONS /api/auth/login
2025-08-21T14:34:18.620+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-21T14:34:18.621+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/api/auth/login?continue to session
2025-08-21T14:34:18.621+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-08-21T14:34:18.621+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing OPTIONS /error
2025-08-21T14:34:18.621+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-21T14:34:18.622+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/error?continue to session
2025-08-21T14:34:18.622+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-08-21T14:34:30.880+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing OPTIONS /api/auth/login
2025-08-21T14:34:30.881+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-21T14:34:30.882+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/api/auth/login?continue to session
2025-08-21T14:34:30.882+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-08-21T14:34:30.882+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing OPTIONS /error
2025-08-21T14:34:30.883+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-21T14:34:30.883+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/error?continue to session
2025-08-21T14:34:30.883+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-08-21T14:34:51.307+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing OPTIONS /api/auth/login
2025-08-21T14:34:51.310+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-21T14:34:51.310+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/api/auth/login?continue to session
2025-08-21T14:34:51.310+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-08-21T14:34:51.311+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing OPTIONS /error
2025-08-21T14:34:51.311+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-21T14:34:51.314+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/error?continue to session
2025-08-21T14:34:51.314+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-08-21T14:35:12.385+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing OPTIONS /api/auth/login
2025-08-21T14:35:12.386+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-21T14:35:12.386+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/api/auth/login?continue to session
2025-08-21T14:35:12.386+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-08-21T14:35:12.387+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing OPTIONS /error
2025-08-21T14:35:12.387+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-21T14:35:12.388+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/error?continue to session
2025-08-21T14:35:12.388+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-08-21T14:39:48.065+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing OPTIONS /api/auth/login
2025-08-21T14:39:48.066+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-21T14:39:48.066+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/api/auth/login?continue to session
2025-08-21T14:39:48.066+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-08-21T14:39:48.067+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing OPTIONS /error
2025-08-21T14:39:48.067+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-21T14:39:48.067+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/error?continue to session
2025-08-21T14:39:48.067+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-08-21T14:39:58.136+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing OPTIONS /api/auth/login
2025-08-21T14:39:58.136+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-21T14:39:58.137+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/api/auth/login?continue to session
2025-08-21T14:39:58.137+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-08-21T14:39:58.137+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing OPTIONS /error
2025-08-21T14:39:58.138+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-21T14:39:58.138+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/error?continue to session
2025-08-21T14:39:58.138+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-08-21T14:40:05.543+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing POST /api/auth/login
2025-08-21T14:40:05.544+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured POST /api/auth/login
2025-08-21T14:40:06.088+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-21T14:40:06.094+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/auth/user-info
2025-08-21T14:40:06.094+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:06.094+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:06.113+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/auth/user-info
2025-08-21T14:40:07.486+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-21T14:40:07.486+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:07.487+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:07.487+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-21T14:40:07.488+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:07.488+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:07.489+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-21T14:40:07.490+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:07.490+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:07.491+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-21T14:40:07.491+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-21T14:40:07.491+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:07.491+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:07.493+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:40:07.493+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-21T14:40:07.493+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:07.493+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:07.493+08:00  INFO 2776 --- [http-nio-8889-exec-4] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-21T14:40:07.493+08:00  INFO 2776 --- [http-nio-8889-exec-4] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-21T14:40:07.494+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-21T14:40:07.496+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:40:07.496+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-21T14:40:07.502+08:00  INFO 2776 --- [http-nio-8889-exec-5] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-21T14:40:07.502+08:00  INFO 2776 --- [http-nio-8889-exec-5] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-21T14:40:07.502+08:00  INFO 2776 --- [http-nio-8889-exec-2] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-21T14:40:07.502+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-21T14:40:07.502+08:00  INFO 2776 --- [http-nio-8889-exec-5] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-21T14:40:07.577+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-21T14:40:07.578+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:07.578+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:07.583+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-21T14:40:07.638+08:00  INFO 2776 --- [http-nio-8889-exec-2] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-21T14:40:07.641+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-21T14:40:07.642+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:07.642+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:07.644+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-21T14:40:07.645+08:00  INFO 2776 --- [http-nio-8889-exec-8] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-21T14:40:07.645+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-21T14:40:07.686+08:00  INFO 2776 --- [http-nio-8889-exec-8] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-21T14:40:07.744+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-21T14:40:07.744+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:07.744+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:07.747+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-21T14:40:07.781+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-21T14:40:07.782+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:07.782+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:07.785+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-21T14:40:07.785+08:00  INFO 2776 --- [http-nio-8889-exec-10] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-21T14:40:07.785+08:00  INFO 2776 --- [http-nio-8889-exec-10] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-21T14:40:08.349+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/stats?uid=6&heatUnitId=0
2025-08-21T14:40:08.349+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:08.349+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:08.353+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/stats?uid=6&heatUnitId=0
2025-08-21T14:40:08.354+08:00  INFO 2776 --- [http-nio-8889-exec-1] c.h.controller.WorkOrderController       : 获取工单统计数据: uid=6, heatUnitId=0
2025-08-21T14:40:08.362+08:00  INFO 2776 --- [http-nio-8889-exec-1] c.h.service.impl.WorkOrderServiceImpl    : 开始获取工单统计数据: userId=6, heatUnitId=0
2025-08-21T14:40:08.399+08:00  INFO 2776 --- [http-nio-8889-exec-1] c.h.service.impl.WorkOrderServiceImpl    : 用户角色: admin
2025-08-21T14:40:08.399+08:00  INFO 2776 --- [http-nio-8889-exec-1] c.h.service.impl.WorkOrderServiceImpl    : 管理员或主管角色统计逻辑
2025-08-21T14:40:08.512+08:00  INFO 2776 --- [http-nio-8889-exec-1] c.h.service.impl.WorkOrderServiceImpl    : 工单统计数据获取成功: 我的工单数=0, 待处理工单数=5, 待接单工单数=3
2025-08-21T14:40:08.901+08:00  INFO 2776 --- [http-nio-8889-exec-5] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-21T14:40:08.904+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:40:08.904+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:08.904+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:08.907+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:40:08.907+08:00  INFO 2776 --- [http-nio-8889-exec-6] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-21T14:40:08.907+08:00  INFO 2776 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-21T14:40:08.907+08:00  INFO 2776 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-21T14:40:10.077+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing OPTIONS /api/auth/login
2025-08-21T14:40:10.078+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-21T14:40:10.078+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/api/auth/login?continue to session
2025-08-21T14:40:10.078+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-08-21T14:40:10.078+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing OPTIONS /error
2025-08-21T14:40:10.078+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-21T14:40:10.079+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/error?continue to session
2025-08-21T14:40:10.079+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-08-21T14:40:10.289+08:00  INFO 2776 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-21T14:40:12.300+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing POST /api/auth/login
2025-08-21T14:40:12.300+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured POST /api/auth/login
2025-08-21T14:40:12.626+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-21T14:40:12.631+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/auth/user-info
2025-08-21T14:40:12.631+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:12.631+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:12.633+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/auth/user-info
2025-08-21T14:40:13.646+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-21T14:40:13.647+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:13.647+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:13.647+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-21T14:40:13.648+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:13.648+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:13.649+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-21T14:40:13.649+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-21T14:40:13.650+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:13.650+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:13.650+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-21T14:40:13.650+08:00  INFO 2776 --- [http-nio-8889-exec-3] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-21T14:40:13.650+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-21T14:40:13.651+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-21T14:40:13.651+08:00  INFO 2776 --- [http-nio-8889-exec-9] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-21T14:40:13.652+08:00  INFO 2776 --- [http-nio-8889-exec-9] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-21T14:40:13.652+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-21T14:40:13.652+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:13.652+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:13.654+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:40:13.654+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:13.654+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:13.654+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-21T14:40:13.656+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:40:13.657+08:00  INFO 2776 --- [http-nio-8889-exec-1] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-21T14:40:13.657+08:00  INFO 2776 --- [http-nio-8889-exec-1] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-21T14:40:13.657+08:00  INFO 2776 --- [http-nio-8889-exec-1] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-21T14:40:13.717+08:00  INFO 2776 --- [http-nio-8889-exec-3] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-21T14:40:13.719+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-21T14:40:13.719+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:13.719+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:13.721+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-21T14:40:13.721+08:00  INFO 2776 --- [http-nio-8889-exec-5] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-21T14:40:13.722+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-21T14:40:13.725+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-21T14:40:13.725+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:13.725+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:13.728+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-21T14:40:13.776+08:00  INFO 2776 --- [http-nio-8889-exec-5] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-21T14:40:13.900+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-21T14:40:13.901+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:13.901+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:13.906+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-21T14:40:13.924+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-21T14:40:13.925+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:13.925+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:13.928+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-21T14:40:13.928+08:00  INFO 2776 --- [http-nio-8889-exec-2] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-21T14:40:13.928+08:00  INFO 2776 --- [http-nio-8889-exec-2] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-21T14:40:15.061+08:00  INFO 2776 --- [http-nio-8889-exec-1] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-21T14:40:15.063+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:40:15.063+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:15.063+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:15.065+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:40:15.066+08:00  INFO 2776 --- [http-nio-8889-exec-8] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-21T14:40:15.066+08:00  INFO 2776 --- [http-nio-8889-exec-8] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-21T14:40:15.066+08:00  INFO 2776 --- [http-nio-8889-exec-8] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-21T14:40:15.779+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/stats?uid=6&heatUnitId=0
2025-08-21T14:40:15.780+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:15.780+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:15.782+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/stats?uid=6&heatUnitId=0
2025-08-21T14:40:15.782+08:00  INFO 2776 --- [http-nio-8889-exec-3] c.h.controller.WorkOrderController       : 获取工单统计数据: uid=6, heatUnitId=0
2025-08-21T14:40:15.782+08:00  INFO 2776 --- [http-nio-8889-exec-3] c.h.service.impl.WorkOrderServiceImpl    : 开始获取工单统计数据: userId=6, heatUnitId=0
2025-08-21T14:40:15.856+08:00  INFO 2776 --- [http-nio-8889-exec-3] c.h.service.impl.WorkOrderServiceImpl    : 用户角色: admin
2025-08-21T14:40:15.856+08:00  INFO 2776 --- [http-nio-8889-exec-3] c.h.service.impl.WorkOrderServiceImpl    : 管理员或主管角色统计逻辑
2025-08-21T14:40:15.960+08:00  INFO 2776 --- [http-nio-8889-exec-3] c.h.service.impl.WorkOrderServiceImpl    : 工单统计数据获取成功: 我的工单数=0, 待处理工单数=5, 待接单工单数=3
2025-08-21T14:40:16.479+08:00  INFO 2776 --- [http-nio-8889-exec-8] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-21T14:40:16.758+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-21T14:40:16.758+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:16.758+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:16.759+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-21T14:40:16.759+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:16.760+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:16.760+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-21T14:40:16.761+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:16.761+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:16.761+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-21T14:40:16.762+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:16.762+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:16.762+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:40:16.763+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:16.763+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:16.765+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-21T14:40:16.766+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:40:16.766+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-21T14:40:16.766+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-21T14:40:16.767+08:00  INFO 2776 --- [http-nio-8889-exec-9] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-21T14:40:16.767+08:00  INFO 2776 --- [http-nio-8889-exec-5] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-21T14:40:16.767+08:00  INFO 2776 --- [http-nio-8889-exec-5] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-21T14:40:16.767+08:00  INFO 2776 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-21T14:40:16.767+08:00  INFO 2776 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-21T14:40:16.768+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-21T14:40:16.769+08:00  INFO 2776 --- [http-nio-8889-exec-10] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-21T14:40:16.769+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-21T14:40:16.850+08:00  INFO 2776 --- [http-nio-8889-exec-10] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-21T14:40:18.129+08:00  INFO 2776 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-21T14:40:24.776+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?page=1&pageSize=10&uid=6&type=my&heatUnitId=0
2025-08-21T14:40:24.776+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:24.776+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:24.779+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?page=1&pageSize=10&uid=6&type=my&heatUnitId=0
2025-08-21T14:40:24.780+08:00  INFO 2776 --- [http-nio-8889-exec-6] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=null, page=1, pageSize=10, type=my, heatUnitId=0
2025-08-21T14:40:24.780+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-21T14:40:24.883+08:00  INFO 2776 --- [http-nio-8889-exec-6] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-21T14:40:26.468+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?page=1&pageSize=10&status=%E5%BE%85%E6%8E%A5%E5%8D%95&uid=6&type=pending&heatUnitId=0
2025-08-21T14:40:26.468+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:26.468+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:26.471+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?page=1&pageSize=10&status=%E5%BE%85%E6%8E%A5%E5%8D%95&uid=6&type=pending&heatUnitId=0
2025-08-21T14:40:26.472+08:00  INFO 2776 --- [http-nio-8889-exec-2] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=待接单, uid=6, limit=null, page=1, pageSize=10, type=pending, heatUnitId=0
2025-08-21T14:40:26.472+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-21T14:40:26.575+08:00  INFO 2776 --- [http-nio-8889-exec-2] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=3, 总页数=1
2025-08-21T14:40:27.304+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?page=1&pageSize=10&uid=6&type=my&heatUnitId=0
2025-08-21T14:40:27.305+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:27.305+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:27.308+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?page=1&pageSize=10&uid=6&type=my&heatUnitId=0
2025-08-21T14:40:27.309+08:00  INFO 2776 --- [http-nio-8889-exec-1] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=null, page=1, pageSize=10, type=my, heatUnitId=0
2025-08-21T14:40:27.309+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-21T14:40:27.419+08:00  INFO 2776 --- [http-nio-8889-exec-1] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-21T14:40:28.012+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?page=1&pageSize=10&status=%E5%BE%85%E6%8E%A5%E5%8D%95&uid=6&type=pending&heatUnitId=0
2025-08-21T14:40:28.012+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:28.012+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:28.015+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?page=1&pageSize=10&status=%E5%BE%85%E6%8E%A5%E5%8D%95&uid=6&type=pending&heatUnitId=0
2025-08-21T14:40:28.016+08:00  INFO 2776 --- [http-nio-8889-exec-3] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=待接单, uid=6, limit=null, page=1, pageSize=10, type=pending, heatUnitId=0
2025-08-21T14:40:28.016+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-21T14:40:28.130+08:00  INFO 2776 --- [http-nio-8889-exec-3] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=3, 总页数=1
2025-08-21T14:40:29.972+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?page=1&pageSize=10&uid=6&type=my&heatUnitId=0
2025-08-21T14:40:29.972+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:29.972+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:29.974+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?page=1&pageSize=10&uid=6&type=my&heatUnitId=0
2025-08-21T14:40:29.975+08:00  INFO 2776 --- [http-nio-8889-exec-8] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=null, page=1, pageSize=10, type=my, heatUnitId=0
2025-08-21T14:40:29.975+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-21T14:40:30.083+08:00  INFO 2776 --- [http-nio-8889-exec-8] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-21T14:40:30.535+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?page=1&pageSize=10&status=%E5%BE%85%E6%8E%A5%E5%8D%95&uid=6&type=pending&heatUnitId=0
2025-08-21T14:40:30.536+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:30.536+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:30.539+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?page=1&pageSize=10&status=%E5%BE%85%E6%8E%A5%E5%8D%95&uid=6&type=pending&heatUnitId=0
2025-08-21T14:40:30.540+08:00  INFO 2776 --- [http-nio-8889-exec-4] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=待接单, uid=6, limit=null, page=1, pageSize=10, type=pending, heatUnitId=0
2025-08-21T14:40:30.540+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-21T14:40:30.641+08:00  INFO 2776 --- [http-nio-8889-exec-4] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=3, 总页数=1
2025-08-21T14:40:34.304+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?page=1&pageSize=10&uid=6&type=my&heatUnitId=0
2025-08-21T14:40:34.304+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:34.304+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:34.306+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?page=1&pageSize=10&uid=6&type=my&heatUnitId=0
2025-08-21T14:40:34.307+08:00  INFO 2776 --- [http-nio-8889-exec-10] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=null, page=1, pageSize=10, type=my, heatUnitId=0
2025-08-21T14:40:34.307+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-21T14:40:34.424+08:00  INFO 2776 --- [http-nio-8889-exec-10] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-21T14:40:35.018+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-21T14:40:35.018+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:35.018+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:35.019+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-21T14:40:35.019+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:35.019+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:35.020+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-21T14:40:35.020+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-21T14:40:35.020+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:35.020+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:35.020+08:00  INFO 2776 --- [http-nio-8889-exec-7] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-21T14:40:35.020+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-21T14:40:35.021+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-21T14:40:35.022+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-21T14:40:35.023+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-21T14:40:35.023+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:35.023+08:00  INFO 2776 --- [http-nio-8889-exec-9] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-21T14:40:35.023+08:00  INFO 2776 --- [http-nio-8889-exec-9] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-21T14:40:35.023+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:35.022+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:40:35.023+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:35.023+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:35.025+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:40:35.025+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-21T14:40:35.025+08:00  INFO 2776 --- [http-nio-8889-exec-2] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-21T14:40:35.025+08:00  INFO 2776 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-21T14:40:35.025+08:00  INFO 2776 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-21T14:40:35.095+08:00  INFO 2776 --- [http-nio-8889-exec-7] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-21T14:40:36.381+08:00  INFO 2776 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-21T14:40:37.889+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/list?page=1&pageSize=5&heatUnitId=0
2025-08-21T14:40:37.890+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:37.890+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:37.892+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/list?page=1&pageSize=5&heatUnitId=0
2025-08-21T14:40:37.894+08:00  INFO 2776 --- [http-nio-8889-exec-1] com.heating.controller.FaultController   : 获取故障列表: status=null, date=null, heatUnitId=0, page=1, pageSize=5
2025-08-21T14:40:37.895+08:00  INFO 2776 --- [http-nio-8889-exec-1] c.heating.service.impl.FaultServiceImpl  : 根据用户项目权限筛选故障列表: heatUnitId=0
2025-08-21T14:40:37.895+08:00  INFO 2776 --- [http-nio-8889-exec-1] c.heating.service.impl.FaultServiceImpl  : 用户拥有全局权限，显示所有故障记录
2025-08-21T14:40:39.464+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/list?status=%E5%BE%85%E7%A1%AE%E8%AE%A4&page=1&pageSize=5&heatUnitId=0
2025-08-21T14:40:39.465+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:39.465+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:39.467+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/list?status=%E5%BE%85%E7%A1%AE%E8%AE%A4&page=1&pageSize=5&heatUnitId=0
2025-08-21T14:40:39.468+08:00  INFO 2776 --- [http-nio-8889-exec-3] com.heating.controller.FaultController   : 获取故障列表: status=待确认, date=null, heatUnitId=0, page=1, pageSize=5
2025-08-21T14:40:39.468+08:00  INFO 2776 --- [http-nio-8889-exec-3] c.heating.service.impl.FaultServiceImpl  : 根据用户项目权限筛选故障列表: heatUnitId=0
2025-08-21T14:40:39.468+08:00  INFO 2776 --- [http-nio-8889-exec-3] c.heating.service.impl.FaultServiceImpl  : 用户拥有全局权限，显示所有故障记录
2025-08-21T14:40:39.914+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/list?status=%E5%B7%B2%E7%A1%AE%E8%AE%A4&page=1&pageSize=5&heatUnitId=0
2025-08-21T14:40:39.914+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:39.914+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:39.917+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/list?status=%E5%B7%B2%E7%A1%AE%E8%AE%A4&page=1&pageSize=5&heatUnitId=0
2025-08-21T14:40:39.917+08:00  INFO 2776 --- [http-nio-8889-exec-8] com.heating.controller.FaultController   : 获取故障列表: status=已确认, date=null, heatUnitId=0, page=1, pageSize=5
2025-08-21T14:40:39.917+08:00  INFO 2776 --- [http-nio-8889-exec-8] c.heating.service.impl.FaultServiceImpl  : 根据用户项目权限筛选故障列表: heatUnitId=0
2025-08-21T14:40:39.917+08:00  INFO 2776 --- [http-nio-8889-exec-8] c.heating.service.impl.FaultServiceImpl  : 用户拥有全局权限，显示所有故障记录
2025-08-21T14:40:40.318+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/list?status=%E5%B7%B2%E9%80%80%E5%9B%9E&page=1&pageSize=5&heatUnitId=0
2025-08-21T14:40:40.319+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:40.319+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:40.321+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/list?status=%E5%B7%B2%E9%80%80%E5%9B%9E&page=1&pageSize=5&heatUnitId=0
2025-08-21T14:40:40.321+08:00  INFO 2776 --- [http-nio-8889-exec-4] com.heating.controller.FaultController   : 获取故障列表: status=已退回, date=null, heatUnitId=0, page=1, pageSize=5
2025-08-21T14:40:40.321+08:00  INFO 2776 --- [http-nio-8889-exec-4] c.heating.service.impl.FaultServiceImpl  : 根据用户项目权限筛选故障列表: heatUnitId=0
2025-08-21T14:40:40.321+08:00  INFO 2776 --- [http-nio-8889-exec-4] c.heating.service.impl.FaultServiceImpl  : 用户拥有全局权限，显示所有故障记录
2025-08-21T14:40:40.975+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/list?status=%E5%B7%B2%E7%A1%AE%E8%AE%A4&page=1&pageSize=5&heatUnitId=0
2025-08-21T14:40:40.976+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:40.976+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:40.978+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/list?status=%E5%B7%B2%E7%A1%AE%E8%AE%A4&page=1&pageSize=5&heatUnitId=0
2025-08-21T14:40:40.978+08:00  INFO 2776 --- [http-nio-8889-exec-10] com.heating.controller.FaultController   : 获取故障列表: status=已确认, date=null, heatUnitId=0, page=1, pageSize=5
2025-08-21T14:40:40.978+08:00  INFO 2776 --- [http-nio-8889-exec-10] c.heating.service.impl.FaultServiceImpl  : 根据用户项目权限筛选故障列表: heatUnitId=0
2025-08-21T14:40:40.978+08:00  INFO 2776 --- [http-nio-8889-exec-10] c.heating.service.impl.FaultServiceImpl  : 用户拥有全局权限，显示所有故障记录
2025-08-21T14:40:41.286+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/list?status=%E5%BE%85%E7%A1%AE%E8%AE%A4&page=1&pageSize=5&heatUnitId=0
2025-08-21T14:40:41.287+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:41.287+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:41.290+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/list?status=%E5%BE%85%E7%A1%AE%E8%AE%A4&page=1&pageSize=5&heatUnitId=0
2025-08-21T14:40:41.290+08:00  INFO 2776 --- [http-nio-8889-exec-6] com.heating.controller.FaultController   : 获取故障列表: status=待确认, date=null, heatUnitId=0, page=1, pageSize=5
2025-08-21T14:40:41.290+08:00  INFO 2776 --- [http-nio-8889-exec-6] c.heating.service.impl.FaultServiceImpl  : 根据用户项目权限筛选故障列表: heatUnitId=0
2025-08-21T14:40:41.290+08:00  INFO 2776 --- [http-nio-8889-exec-6] c.heating.service.impl.FaultServiceImpl  : 用户拥有全局权限，显示所有故障记录
2025-08-21T14:40:41.671+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/list?page=1&pageSize=5&heatUnitId=0
2025-08-21T14:40:41.672+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:41.672+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:41.674+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/list?page=1&pageSize=5&heatUnitId=0
2025-08-21T14:40:41.675+08:00  INFO 2776 --- [http-nio-8889-exec-7] com.heating.controller.FaultController   : 获取故障列表: status=null, date=null, heatUnitId=0, page=1, pageSize=5
2025-08-21T14:40:41.675+08:00  INFO 2776 --- [http-nio-8889-exec-7] c.heating.service.impl.FaultServiceImpl  : 根据用户项目权限筛选故障列表: heatUnitId=0
2025-08-21T14:40:41.675+08:00  INFO 2776 --- [http-nio-8889-exec-7] c.heating.service.impl.FaultServiceImpl  : 用户拥有全局权限，显示所有故障记录
2025-08-21T14:40:42.381+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-21T14:40:42.381+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:42.381+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:42.382+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-21T14:40:42.382+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:42.382+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:42.383+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-21T14:40:42.383+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-21T14:40:42.384+08:00  INFO 2776 --- [http-nio-8889-exec-5] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-21T14:40:42.384+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-21T14:40:42.384+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-21T14:40:42.384+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:42.384+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:42.384+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-21T14:40:42.384+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:42.384+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:42.385+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:40:42.385+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:40:42.385+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:40:42.386+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-21T14:40:42.386+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-21T14:40:42.386+08:00  INFO 2776 --- [http-nio-8889-exec-2] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-21T14:40:42.386+08:00  INFO 2776 --- [http-nio-8889-exec-2] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-21T14:40:42.387+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:40:42.387+08:00  INFO 2776 --- [http-nio-8889-exec-3] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-21T14:40:42.387+08:00  INFO 2776 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-21T14:40:42.388+08:00  INFO 2776 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-21T14:40:42.454+08:00  INFO 2776 --- [http-nio-8889-exec-5] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-21T14:40:43.742+08:00  INFO 2776 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-21T14:41:18.391+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing POST /api/patrols/records/list
2025-08-21T14:41:18.391+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:41:18.391+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:41:18.394+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured POST /api/patrols/records/list
2025-08-21T14:41:18.396+08:00  INFO 2776 --- [http-nio-8889-exec-4] c.heating.controller.PatrolController    : 获取巡检记录列表请求: role=admin, executorId=6, status=null
2025-08-21T14:41:18.396+08:00  INFO 2776 --- [http-nio-8889-exec-4] c.h.service.impl.PatrolServiceImpl       : 获取巡检记录列表: PatrolRecordListRequest(status=null, executorId=6, startDate=null, endDate=null, page=1, pageSize=5, role=admin)
2025-08-21T14:41:18.398+08:00  INFO 2776 --- [http-nio-8889-exec-4] c.h.service.impl.PatrolServiceImpl       : 管理员角色，获取所有巡检记录
2025-08-21T14:41:22.509+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/plans/21/items
2025-08-21T14:41:22.509+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:41:22.509+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:41:22.511+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/plans/21/items
2025-08-21T14:41:22.537+08:00  INFO 2776 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 从数据库获取巡检项目列表, planId: 21
2025-08-21T14:41:32.203+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing POST /api/patrols/records/list
2025-08-21T14:41:32.204+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:41:32.204+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:41:32.206+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured POST /api/patrols/records/list
2025-08-21T14:41:32.206+08:00  INFO 2776 --- [http-nio-8889-exec-6] c.heating.controller.PatrolController    : 获取巡检记录列表请求: role=admin, executorId=6, status=null
2025-08-21T14:41:32.206+08:00  INFO 2776 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 获取巡检记录列表: PatrolRecordListRequest(status=null, executorId=6, startDate=null, endDate=null, page=1, pageSize=5, role=admin)
2025-08-21T14:41:32.206+08:00  INFO 2776 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 管理员角色，获取所有巡检记录
2025-08-21T14:41:37.876+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-21T14:41:37.877+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:41:37.877+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:41:37.877+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-21T14:41:37.878+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:41:37.878+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:41:37.878+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-21T14:41:37.879+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-21T14:41:37.879+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:41:37.879+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:41:37.880+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:41:37.880+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:41:37.881+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:41:37.881+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:41:37.881+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:41:37.883+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-21T14:41:37.887+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-21T14:41:37.888+08:00  INFO 2776 --- [http-nio-8889-exec-5] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-21T14:41:37.888+08:00  INFO 2776 --- [http-nio-8889-exec-5] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-21T14:41:37.887+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:41:37.888+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-21T14:41:37.888+08:00  INFO 2776 --- [http-nio-8889-exec-2] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-21T14:41:37.888+08:00  INFO 2776 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-21T14:41:37.888+08:00  INFO 2776 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-21T14:41:37.889+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-21T14:41:37.890+08:00  INFO 2776 --- [http-nio-8889-exec-7] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-21T14:41:37.890+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-21T14:41:37.956+08:00  INFO 2776 --- [http-nio-8889-exec-7] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-21T14:41:39.294+08:00  INFO 2776 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-21T14:41:45.210+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/rules
2025-08-21T14:41:45.210+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:41:45.210+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:41:45.210+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-08-21T14:41:45.211+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:41:45.211+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:41:45.212+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:41:45.212+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:41:45.212+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:41:45.212+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/rules
2025-08-21T14:41:45.213+08:00  INFO 2776 --- [http-nio-8889-exec-3] c.h.service.impl.AttendanceServiceImpl   : Getting attendance rules from database
2025-08-21T14:41:45.213+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-08-21T14:41:45.214+08:00  INFO 2776 --- [http-nio-8889-exec-8] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-08-21T14:41:45.214+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:41:45.215+08:00  INFO 2776 --- [http-nio-8889-exec-4] c.h.service.impl.AttendanceServiceImpl   : Getting recent attendance for user ID: 6 for the last 7 days
2025-08-21T14:41:45.448+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/rules
2025-08-21T14:41:45.448+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:41:45.448+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:41:45.451+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/rules
2025-08-21T14:41:45.451+08:00  INFO 2776 --- [http-nio-8889-exec-10] c.h.service.impl.AttendanceServiceImpl   : Getting attendance rules from database
2025-08-21T14:41:45.489+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:41:45.490+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:41:45.490+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:41:45.491+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:41:45.493+08:00  INFO 2776 --- [http-nio-8889-exec-6] c.h.service.impl.AttendanceServiceImpl   : Getting recent attendance for user ID: 6 for the last 7 days
2025-08-21T14:41:45.519+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-08-21T14:41:45.519+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:41:45.519+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:41:45.521+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-08-21T14:41:45.522+08:00  INFO 2776 --- [http-nio-8889-exec-7] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-08-21T14:41:50.165+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing POST /api/attendance/clock
2025-08-21T14:41:50.166+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:41:50.166+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:41:50.167+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured POST /api/attendance/clock
2025-08-21T14:41:50.170+08:00  INFO 2776 --- [http-nio-8889-exec-9] c.h.controller.AttendanceController      : 用户打卡请求: userId=6, clockType=checkin
2025-08-21T14:41:50.400+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-08-21T14:41:50.401+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:41:50.401+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:41:50.404+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:41:50.404+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:41:50.404+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:41:50.407+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:41:50.407+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-08-21T14:41:50.408+08:00  INFO 2776 --- [http-nio-8889-exec-5] c.h.service.impl.AttendanceServiceImpl   : Getting recent attendance for user ID: 6 for the last 7 days
2025-08-21T14:41:50.408+08:00  INFO 2776 --- [http-nio-8889-exec-1] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-08-21T14:41:50.409+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing POST /api/person/trajectory/location?userId=6&employeeId=6&longitude=116.4&latitude=39.9
2025-08-21T14:41:50.409+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:41:50.409+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:41:50.412+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured POST /api/person/trajectory/location?userId=6&employeeId=6&longitude=116.4&latitude=39.9
2025-08-21T14:41:54.213+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-21T14:41:54.213+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:41:54.213+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:41:54.213+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-21T14:41:54.214+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:41:54.214+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:41:54.215+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-21T14:41:54.215+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:41:54.215+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:41:54.215+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-21T14:41:54.216+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-21T14:41:54.216+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-21T14:41:54.216+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:41:54.217+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:41:54.217+08:00  INFO 2776 --- [http-nio-8889-exec-3] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-21T14:41:54.217+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-21T14:41:54.217+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-21T14:41:54.218+08:00  INFO 2776 --- [http-nio-8889-exec-8] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-21T14:41:54.218+08:00  INFO 2776 --- [http-nio-8889-exec-8] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-21T14:41:54.218+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:41:54.219+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-21T14:41:54.219+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:41:54.219+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:41:54.221+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:41:54.221+08:00  INFO 2776 --- [http-nio-8889-exec-6] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-21T14:41:54.221+08:00  INFO 2776 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-21T14:41:54.221+08:00  INFO 2776 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-21T14:41:54.282+08:00  INFO 2776 --- [http-nio-8889-exec-3] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-21T14:41:55.067+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/rules
2025-08-21T14:41:55.067+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:41:55.067+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:41:55.069+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-08-21T14:41:55.069+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:41:55.069+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:41:55.069+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/rules
2025-08-21T14:41:55.070+08:00  INFO 2776 --- [http-nio-8889-exec-7] c.h.service.impl.AttendanceServiceImpl   : Getting attendance rules from database
2025-08-21T14:41:55.070+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:41:55.070+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-08-21T14:41:55.071+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:41:55.071+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:41:55.071+08:00  INFO 2776 --- [http-nio-8889-exec-9] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-08-21T14:41:55.072+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:41:55.072+08:00  INFO 2776 --- [http-nio-8889-exec-2] c.h.service.impl.AttendanceServiceImpl   : Getting recent attendance for user ID: 6 for the last 7 days
2025-08-21T14:41:55.314+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/rules
2025-08-21T14:41:55.314+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:41:55.314+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:41:55.316+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/rules
2025-08-21T14:41:55.316+08:00  INFO 2776 --- [http-nio-8889-exec-1] c.h.service.impl.AttendanceServiceImpl   : Getting attendance rules from database
2025-08-21T14:41:55.348+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:41:55.348+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:41:55.348+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:41:55.350+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:41:55.350+08:00  INFO 2776 --- [http-nio-8889-exec-5] c.h.service.impl.AttendanceServiceImpl   : Getting recent attendance for user ID: 6 for the last 7 days
2025-08-21T14:41:55.373+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-08-21T14:41:55.374+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:41:55.374+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:41:55.376+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-08-21T14:41:55.377+08:00  INFO 2776 --- [http-nio-8889-exec-3] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-08-21T14:41:55.676+08:00  INFO 2776 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-21T14:42:01.799+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-21T14:42:01.799+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:42:01.799+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:42:01.800+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-21T14:42:01.800+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:42:01.800+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:42:01.801+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-21T14:42:01.801+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:42:01.801+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:42:01.802+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-21T14:42:01.802+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-21T14:42:01.802+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-21T14:42:01.802+08:00  INFO 2776 --- [http-nio-8889-exec-10] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-21T14:42:01.802+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-21T14:42:01.802+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:42:01.803+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:42:01.803+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-21T14:42:01.803+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:42:01.804+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:42:01.804+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:42:01.804+08:00  INFO 2776 --- [http-nio-8889-exec-8] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-21T14:42:01.804+08:00  INFO 2776 --- [http-nio-8889-exec-8] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-21T14:42:01.804+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-21T14:42:01.805+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:42:01.806+08:00  INFO 2776 --- [http-nio-8889-exec-2] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-21T14:42:01.806+08:00  INFO 2776 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-21T14:42:01.806+08:00  INFO 2776 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-21T14:42:01.873+08:00  INFO 2776 --- [http-nio-8889-exec-10] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-21T14:42:03.315+08:00  INFO 2776 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-21T14:42:04.956+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/rules
2025-08-21T14:42:04.956+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:42:04.956+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:42:04.957+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-08-21T14:42:04.958+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:42:04.958+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:42:04.958+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/rules
2025-08-21T14:42:04.958+08:00  INFO 2776 --- [http-nio-8889-exec-9] c.h.service.impl.AttendanceServiceImpl   : Getting attendance rules from database
2025-08-21T14:42:04.959+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:42:04.959+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-08-21T14:42:04.959+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:42:04.959+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:42:04.960+08:00  INFO 2776 --- [http-nio-8889-exec-1] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-08-21T14:42:04.961+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:42:04.961+08:00  INFO 2776 --- [http-nio-8889-exec-5] c.h.service.impl.AttendanceServiceImpl   : Getting recent attendance for user ID: 6 for the last 7 days
2025-08-21T14:42:05.224+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-08-21T14:42:05.224+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:42:05.224+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:42:05.226+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-08-21T14:42:05.227+08:00  INFO 2776 --- [http-nio-8889-exec-3] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-08-21T14:42:05.237+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/rules
2025-08-21T14:42:05.237+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:42:05.237+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:42:05.240+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/rules
2025-08-21T14:42:05.241+08:00  INFO 2776 --- [http-nio-8889-exec-6] c.h.service.impl.AttendanceServiceImpl   : Getting attendance rules from database
2025-08-21T14:42:05.243+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:42:05.243+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:42:05.243+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:42:05.246+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:42:05.246+08:00  INFO 2776 --- [http-nio-8889-exec-7] c.h.service.impl.AttendanceServiceImpl   : Getting recent attendance for user ID: 6 for the last 7 days
2025-08-21T14:42:39.652+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing OPTIONS /api/auth/login
2025-08-21T14:42:39.652+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-21T14:42:39.652+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/api/auth/login?continue to session
2025-08-21T14:42:39.652+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-08-21T14:42:39.653+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing OPTIONS /error
2025-08-21T14:42:39.653+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-21T14:42:39.653+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/error?continue to session
2025-08-21T14:42:39.653+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-08-21T14:42:42.313+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing OPTIONS /api/auth/login
2025-08-21T14:42:42.313+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-21T14:42:42.314+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/api/auth/login?continue to session
2025-08-21T14:42:42.314+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-08-21T14:42:42.314+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing OPTIONS /error
2025-08-21T14:42:42.314+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-21T14:42:42.314+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/error?continue to session
2025-08-21T14:42:42.314+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-08-21T14:42:50.122+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing POST /api/auth/login
2025-08-21T14:42:50.122+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured POST /api/auth/login
2025-08-21T14:42:50.453+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-21T14:42:50.458+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/auth/user-info
2025-08-21T14:42:50.458+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:42:50.458+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:42:50.460+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/auth/user-info
2025-08-21T14:42:51.519+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-21T14:42:51.520+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:42:51.520+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:42:51.520+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-21T14:42:51.520+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:42:51.520+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:42:51.521+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-21T14:42:51.521+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:42:51.521+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:42:51.522+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-21T14:42:51.522+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-21T14:42:51.522+08:00  INFO 2776 --- [http-nio-8889-exec-1] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-21T14:42:51.522+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-21T14:42:51.523+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-21T14:42:51.523+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:42:51.523+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:42:51.531+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-21T14:42:51.532+08:00  INFO 2776 --- [http-nio-8889-exec-5] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-21T14:42:51.532+08:00  INFO 2776 --- [http-nio-8889-exec-5] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-21T14:42:51.533+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-21T14:42:51.534+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:42:51.534+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:42:51.534+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:42:51.536+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:42:51.537+08:00  INFO 2776 --- [http-nio-8889-exec-7] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-21T14:42:51.537+08:00  INFO 2776 --- [http-nio-8889-exec-7] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-21T14:42:51.537+08:00  INFO 2776 --- [http-nio-8889-exec-7] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-21T14:42:51.599+08:00  INFO 2776 --- [http-nio-8889-exec-1] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-21T14:42:51.601+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-21T14:42:51.602+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:42:51.602+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:42:51.604+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-21T14:42:51.604+08:00  INFO 2776 --- [http-nio-8889-exec-3] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-21T14:42:51.604+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-21T14:42:51.613+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-21T14:42:51.614+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:42:51.614+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:42:51.615+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-21T14:42:51.639+08:00  INFO 2776 --- [http-nio-8889-exec-3] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-21T14:42:51.768+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-21T14:42:51.768+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:42:51.768+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:42:51.770+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-21T14:42:51.811+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-21T14:42:51.811+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:42:51.811+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:42:51.813+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-21T14:42:51.814+08:00  INFO 2776 --- [http-nio-8889-exec-8] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-21T14:42:51.814+08:00  INFO 2776 --- [http-nio-8889-exec-8] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-21T14:42:52.909+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/rules
2025-08-21T14:42:52.909+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:42:52.909+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:42:52.910+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-08-21T14:42:52.910+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:42:52.910+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:42:52.911+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/rules
2025-08-21T14:42:52.911+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:42:52.912+08:00  INFO 2776 --- [http-nio-8889-exec-2] c.h.service.impl.AttendanceServiceImpl   : Getting attendance rules from database
2025-08-21T14:42:52.912+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:42:52.912+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:42:52.913+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-08-21T14:42:52.913+08:00  INFO 2776 --- [http-nio-8889-exec-1] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-08-21T14:42:52.914+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:42:52.914+08:00  INFO 2776 --- [http-nio-8889-exec-6] c.h.service.impl.AttendanceServiceImpl   : Getting recent attendance for user ID: 6 for the last 7 days
2025-08-21T14:42:52.952+08:00  INFO 2776 --- [http-nio-8889-exec-7] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-21T14:42:52.953+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:42:52.953+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:42:52.953+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:42:52.955+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:42:52.956+08:00  INFO 2776 --- [http-nio-8889-exec-3] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-21T14:42:52.956+08:00  INFO 2776 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-21T14:42:52.956+08:00  INFO 2776 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-21T14:42:53.149+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/rules
2025-08-21T14:42:53.149+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:42:53.149+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:42:53.151+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/rules
2025-08-21T14:42:53.152+08:00  INFO 2776 --- [http-nio-8889-exec-10] c.h.service.impl.AttendanceServiceImpl   : Getting attendance rules from database
2025-08-21T14:42:53.192+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-08-21T14:42:53.192+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:42:53.192+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:42:53.194+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-08-21T14:42:53.194+08:00  INFO 2776 --- [http-nio-8889-exec-9] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-08-21T14:42:53.195+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:42:53.196+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:42:53.196+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:42:53.198+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:42:53.200+08:00  INFO 2776 --- [http-nio-8889-exec-5] c.h.service.impl.AttendanceServiceImpl   : Getting recent attendance for user ID: 6 for the last 7 days
2025-08-21T14:42:54.290+08:00  INFO 2776 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-21T14:43:51.089+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/rules
2025-08-21T14:43:51.089+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:43:51.089+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:43:51.090+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/rules
2025-08-21T14:43:51.091+08:00  INFO 2776 --- [http-nio-8889-exec-4] c.h.service.impl.AttendanceServiceImpl   : Getting attendance rules from database
2025-08-21T14:43:53.602+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/rules
2025-08-21T14:43:53.602+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:43:53.602+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:43:53.604+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-08-21T14:43:53.604+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/rules
2025-08-21T14:43:53.604+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:43:53.604+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:43:53.605+08:00  INFO 2776 --- [http-nio-8889-exec-7] c.h.service.impl.AttendanceServiceImpl   : Getting attendance rules from database
2025-08-21T14:43:53.607+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:43:53.607+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:43:53.607+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:43:53.608+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-08-21T14:43:53.608+08:00  INFO 2776 --- [http-nio-8889-exec-2] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-08-21T14:43:53.609+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:43:53.610+08:00  INFO 2776 --- [http-nio-8889-exec-1] c.h.service.impl.AttendanceServiceImpl   : Getting recent attendance for user ID: 6 for the last 7 days
2025-08-21T14:43:57.789+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing POST /api/attendance/clock
2025-08-21T14:43:57.789+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:43:57.789+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:43:57.791+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured POST /api/attendance/clock
2025-08-21T14:43:57.791+08:00  INFO 2776 --- [http-nio-8889-exec-6] c.h.controller.AttendanceController      : 用户打卡请求: userId=6, clockType=checkout
2025-08-21T14:43:58.034+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:43:58.034+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-08-21T14:43:58.034+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:43:58.034+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:43:58.034+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:43:58.034+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:43:58.036+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing POST /api/person/trajectory/location?userId=6&employeeId=6&longitude=116.4&latitude=39.9
2025-08-21T14:43:58.036+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-08-21T14:43:58.038+08:00  INFO 2776 --- [http-nio-8889-exec-10] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-08-21T14:43:58.038+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:43:58.038+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:43:58.038+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:43:58.038+08:00  INFO 2776 --- [http-nio-8889-exec-9] c.h.service.impl.AttendanceServiceImpl   : Getting recent attendance for user ID: 6 for the last 7 days
2025-08-21T14:43:58.040+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured POST /api/person/trajectory/location?userId=6&employeeId=6&longitude=116.4&latitude=39.9
2025-08-21T14:44:10.365+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-21T14:44:10.365+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-21T14:44:10.366+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:44:10.366+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:44:10.366+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:44:10.366+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:44:10.367+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-21T14:44:10.367+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:44:10.368+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:44:10.368+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-21T14:44:10.369+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-21T14:44:10.369+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-21T14:44:10.369+08:00  INFO 2776 --- [http-nio-8889-exec-8] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-21T14:44:10.369+08:00  INFO 2776 --- [http-nio-8889-exec-8] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-21T14:44:10.369+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-21T14:44:10.369+08:00  INFO 2776 --- [http-nio-8889-exec-3] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-21T14:44:10.369+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-21T14:44:10.370+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:44:10.370+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:44:10.371+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:44:10.372+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:44:10.372+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:44:10.372+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-21T14:44:10.373+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:44:10.374+08:00  INFO 2776 --- [http-nio-8889-exec-1] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-21T14:44:10.374+08:00  INFO 2776 --- [http-nio-8889-exec-1] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-21T14:44:10.374+08:00  INFO 2776 --- [http-nio-8889-exec-1] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-21T14:44:10.447+08:00  INFO 2776 --- [http-nio-8889-exec-3] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-21T14:44:11.761+08:00  INFO 2776 --- [http-nio-8889-exec-1] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-21T14:44:11.789+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/rules
2025-08-21T14:44:11.789+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:44:11.789+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:44:11.791+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/rules
2025-08-21T14:44:11.792+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-08-21T14:44:11.792+08:00  INFO 2776 --- [http-nio-8889-exec-2] c.h.service.impl.AttendanceServiceImpl   : Getting attendance rules from database
2025-08-21T14:44:11.792+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:44:11.792+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:44:11.793+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-08-21T14:44:11.794+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:44:11.794+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:44:11.794+08:00  INFO 2776 --- [http-nio-8889-exec-6] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-08-21T14:44:11.794+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:44:11.796+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:44:11.796+08:00  INFO 2776 --- [http-nio-8889-exec-5] c.h.service.impl.AttendanceServiceImpl   : Getting recent attendance for user ID: 6 for the last 7 days
2025-08-21T14:44:12.015+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/rules
2025-08-21T14:44:12.015+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:44:12.015+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:44:12.019+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/rules
2025-08-21T14:44:12.019+08:00  INFO 2776 --- [http-nio-8889-exec-9] c.h.service.impl.AttendanceServiceImpl   : Getting attendance rules from database
2025-08-21T14:44:12.083+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:44:12.084+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:44:12.084+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:44:12.090+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:44:12.090+08:00  INFO 2776 --- [http-nio-8889-exec-10] c.h.service.impl.AttendanceServiceImpl   : Getting recent attendance for user ID: 6 for the last 7 days
2025-08-21T14:44:12.114+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-08-21T14:44:12.114+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:44:12.114+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:44:12.120+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-08-21T14:44:12.120+08:00  INFO 2776 --- [http-nio-8889-exec-6] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-08-21T14:44:48.385+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-21T14:44:48.386+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:44:48.386+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:44:48.386+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-21T14:44:48.386+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:44:48.386+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:44:48.387+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-21T14:44:48.387+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-21T14:44:48.388+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-21T14:44:48.388+08:00  INFO 2776 --- [http-nio-8889-exec-7] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-21T14:44:48.388+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:44:48.388+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:44:48.388+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-21T14:44:48.389+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-21T14:44:48.390+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:44:48.390+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:44:48.390+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-21T14:44:48.390+08:00  INFO 2776 --- [http-nio-8889-exec-8] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-21T14:44:48.390+08:00  INFO 2776 --- [http-nio-8889-exec-8] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-21T14:44:48.391+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:44:48.392+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:44:48.392+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:44:48.392+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-21T14:44:48.394+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:44:48.394+08:00  INFO 2776 --- [http-nio-8889-exec-2] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-21T14:44:48.394+08:00  INFO 2776 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-21T14:44:48.394+08:00  INFO 2776 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-21T14:44:48.473+08:00  INFO 2776 --- [http-nio-8889-exec-7] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-21T14:44:49.752+08:00  INFO 2776 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-21T14:45:26.631+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/rules
2025-08-21T14:45:26.632+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:45:26.632+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:45:26.633+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-08-21T14:45:26.633+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:45:26.633+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:45:26.633+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/rules
2025-08-21T14:45:26.633+08:00  INFO 2776 --- [http-nio-8889-exec-5] c.h.service.impl.AttendanceServiceImpl   : Getting attendance rules from database
2025-08-21T14:45:26.634+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:45:26.634+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-08-21T14:45:26.635+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:45:26.635+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:45:26.635+08:00  INFO 2776 --- [http-nio-8889-exec-3] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-08-21T14:45:26.636+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:45:28.391+08:00  INFO 2776 --- [http-nio-8889-exec-9] c.h.service.impl.AttendanceServiceImpl   : Getting recent attendance for user ID: 6 for the last 7 days
2025-08-21T14:45:28.591+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/rules
2025-08-21T14:45:28.591+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:45:28.591+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:45:28.593+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/rules
2025-08-21T14:45:28.593+08:00  INFO 2776 --- [http-nio-8889-exec-10] c.h.service.impl.AttendanceServiceImpl   : Getting attendance rules from database
2025-08-21T14:45:28.643+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-08-21T14:45:28.644+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:45:28.644+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:45:28.645+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-08-21T14:45:28.645+08:00  INFO 2776 --- [http-nio-8889-exec-6] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-08-21T14:45:32.286+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:45:32.286+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:45:32.286+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:45:32.288+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:45:34.220+08:00  INFO 2776 --- [http-nio-8889-exec-1] c.h.service.impl.AttendanceServiceImpl   : Getting recent attendance for user ID: 6 for the last 7 days
2025-08-21T14:48:45.506+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-21T14:48:45.506+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-21T14:48:45.506+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:48:45.506+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:48:45.506+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:48:45.506+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:48:45.507+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-21T14:48:45.507+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:48:45.507+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:48:45.508+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-21T14:48:45.508+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-21T14:48:45.508+08:00  INFO 2776 --- [http-nio-8889-exec-2] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-21T14:48:45.508+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-21T14:48:45.508+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-21T14:48:45.509+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:48:45.509+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-21T14:48:45.509+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:48:45.509+08:00  INFO 2776 --- [http-nio-8889-exec-3] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-21T14:48:45.509+08:00  INFO 2776 --- [http-nio-8889-exec-3] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-21T14:48:45.510+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:48:45.510+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:48:45.510+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:48:45.510+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-21T14:48:45.511+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:48:45.512+08:00  INFO 2776 --- [http-nio-8889-exec-10] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-21T14:48:45.512+08:00  INFO 2776 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-21T14:48:45.512+08:00  INFO 2776 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-21T14:48:45.576+08:00  INFO 2776 --- [http-nio-8889-exec-2] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-21T14:48:46.927+08:00  INFO 2776 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-21T14:48:46.966+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/stats?uid=6&heatUnitId=0
2025-08-21T14:48:46.966+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:48:46.966+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:48:46.968+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/stats?uid=6&heatUnitId=0
2025-08-21T14:48:46.969+08:00  INFO 2776 --- [http-nio-8889-exec-6] c.h.controller.WorkOrderController       : 获取工单统计数据: uid=6, heatUnitId=0
2025-08-21T14:48:46.969+08:00  INFO 2776 --- [http-nio-8889-exec-6] c.h.service.impl.WorkOrderServiceImpl    : 开始获取工单统计数据: userId=6, heatUnitId=0
2025-08-21T14:48:47.036+08:00  INFO 2776 --- [http-nio-8889-exec-6] c.h.service.impl.WorkOrderServiceImpl    : 用户角色: admin
2025-08-21T14:48:47.036+08:00  INFO 2776 --- [http-nio-8889-exec-6] c.h.service.impl.WorkOrderServiceImpl    : 管理员或主管角色统计逻辑
2025-08-21T14:48:47.150+08:00  INFO 2776 --- [http-nio-8889-exec-6] c.h.service.impl.WorkOrderServiceImpl    : 工单统计数据获取成功: 我的工单数=0, 待处理工单数=5, 待接单工单数=3
2025-08-21T14:48:48.543+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing OPTIONS /api/auth/login
2025-08-21T14:48:48.544+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-21T14:48:48.544+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/api/auth/login?continue to session
2025-08-21T14:48:48.544+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-08-21T14:48:48.544+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing OPTIONS /error
2025-08-21T14:48:48.545+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-21T14:48:48.545+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/error?continue to session
2025-08-21T14:48:48.545+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-08-21T14:48:49.276+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing POST /api/auth/login
2025-08-21T14:48:49.277+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured POST /api/auth/login
2025-08-21T14:48:49.596+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-21T14:48:49.601+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/auth/user-info
2025-08-21T14:48:49.601+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:48:49.601+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:48:49.603+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/auth/user-info
2025-08-21T14:48:50.616+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-21T14:48:50.616+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:48:50.616+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:48:50.617+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-21T14:48:50.617+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:48:50.617+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:48:50.617+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-21T14:48:50.618+08:00  INFO 2776 --- [http-nio-8889-exec-8] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-21T14:48:50.618+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-21T14:48:50.619+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-21T14:48:50.619+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-21T14:48:50.619+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:48:50.619+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:48:50.620+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-21T14:48:50.621+08:00  INFO 2776 --- [http-nio-8889-exec-2] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-21T14:48:50.621+08:00  INFO 2776 --- [http-nio-8889-exec-2] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-21T14:48:50.622+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-21T14:48:50.623+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:48:50.623+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:48:50.624+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-21T14:48:50.624+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:48:50.624+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:48:50.624+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:48:50.626+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:48:50.626+08:00  INFO 2776 --- [http-nio-8889-exec-3] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-21T14:48:50.626+08:00  INFO 2776 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-21T14:48:50.626+08:00  INFO 2776 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-21T14:48:50.691+08:00  INFO 2776 --- [http-nio-8889-exec-8] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-21T14:48:50.693+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-21T14:48:50.693+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:48:50.693+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:48:50.695+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-21T14:48:50.695+08:00  INFO 2776 --- [http-nio-8889-exec-10] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-21T14:48:50.695+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-21T14:48:50.707+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-21T14:48:50.707+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:48:50.707+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:48:50.709+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-21T14:48:50.729+08:00  INFO 2776 --- [http-nio-8889-exec-10] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-21T14:48:50.857+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-21T14:48:50.857+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:48:50.857+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:48:50.859+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-21T14:48:50.903+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-21T14:48:50.903+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:48:50.903+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:48:50.905+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-21T14:48:50.905+08:00  INFO 2776 --- [http-nio-8889-exec-7] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-21T14:48:50.905+08:00  INFO 2776 --- [http-nio-8889-exec-7] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-21T14:48:51.979+08:00  INFO 2776 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-21T14:48:51.980+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:48:51.980+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:48:51.980+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:48:51.982+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:48:51.982+08:00  INFO 2776 --- [http-nio-8889-exec-4] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-21T14:48:51.983+08:00  INFO 2776 --- [http-nio-8889-exec-4] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-21T14:48:51.983+08:00  INFO 2776 --- [http-nio-8889-exec-4] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-21T14:48:53.339+08:00  INFO 2776 --- [http-nio-8889-exec-4] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-21T14:49:10.677+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/rules
2025-08-21T14:49:10.677+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:49:10.678+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:49:10.678+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-08-21T14:49:10.679+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:49:10.679+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:49:10.679+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/rules
2025-08-21T14:49:10.679+08:00  INFO 2776 --- [http-nio-8889-exec-8] c.h.service.impl.AttendanceServiceImpl   : Getting attendance rules from database
2025-08-21T14:49:10.680+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:49:10.680+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-08-21T14:49:10.680+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:49:10.680+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:49:10.680+08:00  INFO 2776 --- [http-nio-8889-exec-5] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-08-21T14:49:10.682+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:49:10.682+08:00  INFO 2776 --- [http-nio-8889-exec-10] c.h.service.impl.AttendanceServiceImpl   : Getting recent attendance for user ID: 6 for the last 7 days
2025-08-21T14:49:10.914+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/rules
2025-08-21T14:49:10.915+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:49:10.915+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:49:10.918+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/rules
2025-08-21T14:49:10.919+08:00  INFO 2776 --- [http-nio-8889-exec-6] c.h.service.impl.AttendanceServiceImpl   : Getting attendance rules from database
2025-08-21T14:49:13.819+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-08-21T14:49:13.820+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:49:13.820+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:49:14.131+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-08-21T14:49:14.131+08:00  INFO 2776 --- [http-nio-8889-exec-9] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-08-21T14:49:25.273+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:49:25.273+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:49:25.273+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:49:25.273+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:49:25.273+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:49:25.273+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:49:25.274+08:00 DEBUG 2776 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:49:25.274+08:00 DEBUG 2776 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:49:25.275+08:00  INFO 2776 --- [http-nio-8889-exec-1] c.h.service.impl.AttendanceServiceImpl   : Getting recent attendance for user ID: 6 for the last 7 days
2025-08-21T14:49:25.275+08:00  INFO 2776 --- [http-nio-8889-exec-2] c.h.service.impl.AttendanceServiceImpl   : Getting recent attendance for user ID: 6 for the last 7 days
2025-08-21T14:49:28.564+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:49:28.565+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:49:28.565+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:49:28.567+08:00 DEBUG 2776 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/recent?days=7&user_id=6
2025-08-21T14:49:28.567+08:00  INFO 2776 --- [http-nio-8889-exec-7] c.h.service.impl.AttendanceServiceImpl   : Getting recent attendance for user ID: 6 for the last 7 days
2025-08-21T14:53:06.329+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-21T14:53:06.329+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:53:06.329+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:53:06.330+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:53:06.331+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:53:06.331+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:53:06.331+08:00 DEBUG 2776 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-21T14:53:06.332+08:00 DEBUG 2776 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:53:06.332+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-21T14:53:06.332+08:00  INFO 2776 --- [http-nio-8889-exec-4] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-21T14:53:06.332+08:00  INFO 2776 --- [http-nio-8889-exec-4] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-21T14:53:06.332+08:00  INFO 2776 --- [http-nio-8889-exec-4] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-21T14:53:06.333+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:53:06.333+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:53:06.334+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-21T14:53:06.334+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:53:06.334+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:53:06.335+08:00 DEBUG 2776 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-21T14:53:06.335+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-21T14:53:06.335+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:53:06.335+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:53:06.335+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-21T14:53:06.335+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:53:06.335+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:53:06.336+08:00 DEBUG 2776 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-21T14:53:06.336+08:00  INFO 2776 --- [http-nio-8889-exec-5] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-21T14:53:06.336+08:00  INFO 2776 --- [http-nio-8889-exec-5] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-21T14:53:06.336+08:00 DEBUG 2776 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-21T14:53:06.336+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:53:06.337+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:53:06.337+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:53:06.337+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-21T14:53:06.338+08:00  INFO 2776 --- [http-nio-8889-exec-9] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-21T14:53:06.338+08:00 DEBUG 2776 --- [http-nio-8889-exec-9] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-21T14:53:06.338+08:00 DEBUG 2776 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-21T14:53:06.338+08:00  INFO 2776 --- [http-nio-8889-exec-10] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-21T14:53:06.338+08:00  INFO 2776 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-21T14:53:06.338+08:00  INFO 2776 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-21T14:53:06.340+08:00 DEBUG 2776 --- [http-nio-8889-exec-12] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-21T14:53:06.341+08:00 DEBUG 2776 --- [http-nio-8889-exec-12] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:53:06.341+08:00 DEBUG 2776 --- [http-nio-8889-exec-12] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:53:06.341+08:00 DEBUG 2776 --- [http-nio-8889-exec-11] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-21T14:53:06.342+08:00 DEBUG 2776 --- [http-nio-8889-exec-11] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-21T14:53:06.342+08:00 DEBUG 2776 --- [http-nio-8889-exec-11] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-21T14:53:06.343+08:00 DEBUG 2776 --- [http-nio-8889-exec-12] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-21T14:53:06.343+08:00  INFO 2776 --- [http-nio-8889-exec-12] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-21T14:53:06.343+08:00  INFO 2776 --- [http-nio-8889-exec-12] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-21T14:53:06.344+08:00 DEBUG 2776 --- [http-nio-8889-exec-11] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-21T14:53:06.362+08:00  WARN 2776 --- [MyHikariPool housekeeper] com.zaxxer.hikari.pool.HikariPool        : MyHikariPool - Thread starvation or clock leap detected (housekeeper delta=3m41s55ms871µs600ns).
2025-08-21T14:53:06.447+08:00  INFO 2776 --- [http-nio-8889-exec-9] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-21T14:53:08.196+08:00  INFO 2776 --- [http-nio-8889-exec-4] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-21T14:53:08.283+08:00  INFO 2776 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-21T14:53:39.073+08:00  INFO 2776 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-21T14:53:39.074+08:00  INFO 2776 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown initiated...
