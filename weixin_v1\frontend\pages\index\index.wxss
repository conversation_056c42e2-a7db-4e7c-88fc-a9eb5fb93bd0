.container {
  padding: 30rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

/* 统计卡片样式 */
.stats-card {
  background: linear-gradient(135deg, #4CAF50, #2196F3);
  border-radius: 16rpx;
  padding: 30rpx;
  color: #fff;
  margin-bottom: 30rpx;
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.stats-header .title {
  font-size: 32rpx;
  font-weight: bold;
}

.stats-header .date {
  font-size: 24rpx;
  opacity: 0.9;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30rpx;
}

.stats-item {
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  padding: 20rpx;
}

.stats-item .value {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
}

.stats-item .label {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 快捷操作样式 */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.action-item {
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  text-align: center;
}

.icon-box {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 12rpx;
}

.icon-box.fault { background: #FFF3E0; }
.icon-box.temp { background: #E3F2FD; }
.icon-box.work { background: #E8F5E9; }

.wx-icon {
  font-size: 40rpx;
}

.action-text {
  font-size: 28rpx;
  color: #333;
}

/* 区块通用样式 */
.section {
  margin-bottom: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.more {
  font-size: 24rpx;
  color: #666;
}

/* 故障列表样式 */
.fault-list {
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
}

.fault-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.fault-item:last-child {
  border-bottom: none;
}

.fault-main {
  flex: 1;
  margin-right: 20rpx;
}

.fault-type {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.type-tag {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  margin-right: 12rpx;
}

.level-1 {
  background: #FFF3E0;
  color: #FF9800;
}

.level-2 {
  background: #FFEBEE;
  color: #F44336;
}

.level-3 {
  background: #E57373;
  color: #fff;
}

.time {
  font-size: 24rpx;
  color: #999;
}

.fault-desc {
  font-size: 28rpx;
  color: #333;
}

.status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  margin-left: 10rpx;
}

/* 故障状态样式 */
.pending {
  color: #ff9f00; 
}

.processing {  /* 已确认 */
  color: #409EFF;
}

.returned {  /* 已退回 */
  color: #F56C6C;
} 

/* 温度监测样式 */
.temp-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
}

.temp-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.station {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.update-time {
  font-size: 24rpx;
  color: #999;
}

.temp-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.temp-item {
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.temp-item .name {
  font-size: 28rpx;
  color: #333;
}

.temp-item .temp {
  font-size: 32rpx;
  font-weight: bold;
}

.temp.normal { color: #4CAF50; }
.temp.high { color: #F44336; }
.temp.low { color: #2196F3; } 