import App from './App.vue'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'

// 导入Vuex状态管理
import store from './store'

// 导入图标字体样式
import './static/styles/iconfont.scss'

// 导入全局样式
import './static/styles/global.scss'

// 导入API配置
import { BASE_URL } from './utils/api'

// // 导入并注册权限指令
// import permissionDirective from './utils/permission-directive'
// Vue.use(permissionDirective)

// 导入并全局注册权限检查组件
import PermissionCheck from './components/PermissionCheck.vue' 
Vue.component('PermissionCheck', PermissionCheck) 
 
// 页面拦截器，用于登录拦截
const whiteList = ['/pages/user/login', '/pages/user/agreement']; // 不需要登录拦截的页面

// 添加请求拦截器
uni.addInterceptor('request', {
  invoke(args) {
    // 不在这里处理请求头，由api.js模块处理
    
    // 开发环境打印请求信息
    if (process.env.NODE_ENV !== 'production') {
      console.log('Request:', args);
    }
    
    return args;
  },
  success(args) {
    // 开发环境打印响应信息
    if (process.env.NODE_ENV !== 'production') {
      console.log('Response:', args);
    }
    
    // 特殊错误码处理
    if (args.data?.code === 401) {
      // token 失效
      uni.showToast({
        title: '登录已过期，请重新登录',
        icon: 'none'
      });
      
      // 清除登录信息
      uni.removeStorageSync('token');
      uni.removeStorageSync('userInfo');
      uni.removeStorageSync('userId');
      uni.removeStorageSync('userRole');
      uni.removeStorageSync('userPermissions');
      
      // 跳转到登录页
      setTimeout(() => {
        uni.reLaunch({
          url: '/pages/user/login'
        });
      }, 1500);
    }
    
    return args;
  },
  fail(err) {
    console.error('Request failed:', err);
    return err;
  }
});

// 路由拦截器
const routerInterceptor = {
  invoke(params) {
    // 获取目标页面路径
    const url = params.url.split('?')[0];
    
    // 检查是否在白名单中
    if (whiteList.includes(url)) {
      return true;
    }
    
    // 检查用户是否已登录
    const token = uni.getStorageSync('token');
    
    if (!token) {
      // 未登录，跳转到登录页面
      uni.navigateTo({
        url: '/pages/user/login'
      });
      return false;
    }
    
    // 已登录，允许继续
    return true;
  }
};

// 添加页面跳转拦截器
uni.addInterceptor('navigateTo', routerInterceptor);
uni.addInterceptor('redirectTo', routerInterceptor);
uni.addInterceptor('reLaunch', routerInterceptor);
uni.addInterceptor('switchTab', routerInterceptor);

// 全局混入，添加BASE_URL到所有组件
Vue.mixin({
  data() {
    return {
      $baseUrl: BASE_URL
    }
  }
});

Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
  store,
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
// 在Vue3环境下使用不同的变量名避免命名冲突
import { BASE_URL as API_BASE_URL } from './utils/api'
import { createStore } from 'vuex'
import attendanceModule from './store/attendance'

export function createApp() {
  const app = createSSRApp(App)
  
  // 创建Vue3版本的store
  const store = createStore({
    modules: {
      attendance: attendanceModule
    }
  })
  
  // 注册store
  app.use(store)
  
  // 为Vue3应用提供全局属性
  app.config.globalProperties.$baseUrl = API_BASE_URL
  
  return {
    app
  }
}
// #endif