
.no-permission-item[data-v-9e1a7520] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0.5;
}
.no-permission-text[data-v-9e1a7520] {
  font-size: 0.75rem;
  color: #999;
  text-align: center;
}

/* 确保permission-check组件继承其父容器的布局特性 */
[data-v-9e1a7520] .permission-check {
  display: inherit;
  width: inherit;
  height: inherit;
  flex: inherit;
  position: inherit;
}

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.clock-in-container {
  padding: 0.9375rem;
  background-color: #f5f7fa;
  min-height: 100vh;
}
.status-header {
  margin-bottom: 1.25rem;
}
.status-header .date-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 0.625rem;
}
.status-header .date-info .date {
  font-size: 1rem;
  color: #333;
  margin-bottom: 0.3125rem;
}
.status-header .date-info .time {
  font-size: 1.875rem;
  font-weight: bold;
  color: #333;
}
.status-header .admin-settings {
  position: absolute;
  top: 0.625rem;
  right: 0.625rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.3125rem;
  border-radius: 0.3125rem;
}
.status-header .admin-settings .settings-icon {
  font-size: 1.25rem;
  margin-bottom: 0.15625rem;
}
.status-header .admin-settings .settings-text {
  font-size: 0.75rem;
  color: #333;
}
.clock-status {
  margin-bottom: 1.25rem;
}
.clock-status .status-card {
  background-color: #fff;
  border-radius: 0.5rem;
  padding: 0.9375rem;
  box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.05);
}
.clock-status .status-card .work-time {
  display: flex;
  align-items: center;
  margin-bottom: 0.9375rem;
}
.clock-status .status-card .work-time .time-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.clock-status .status-card .work-time .time-item .time-label {
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 0.3125rem;
}
.clock-status .status-card .work-time .time-item .time-value {
  font-size: 1.125rem;
  font-weight: bold;
  color: #333;
}
.clock-status .status-card .work-time .time-divider {
  width: 0.0625rem;
  height: 2.5rem;
  background-color: #eee;
}
.clock-status .status-card .clock-records {
  display: flex;
  border-top: 0.0625rem solid #f5f5f5;
  padding-top: 0.9375rem;
}
.clock-status .status-card .clock-records .record-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.clock-status .status-card .clock-records .record-item .record-label {
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 0.3125rem;
}
.clock-status .status-card .clock-records .record-item .record-value {
  font-size: 1rem;
  color: #52c41a;
  font-weight: bold;
}
.clock-status .status-card .clock-records .record-item .record-value.not-clocked {
  color: #999;
}
.clock-action {
  margin-bottom: 1.875rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.clock-action .clock-circle {
  width: 9.375rem;
  height: 9.375rem;
  border-radius: 50%;
  background: linear-gradient(135deg, #4483e5, #6a9eef);
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 0.3125rem 0.9375rem rgba(106, 158, 239, 0.3);
  margin-bottom: 0.9375rem;
  transition: all 0.3s ease;
}
.clock-action .clock-circle .circle-inner {
  width: 8.125rem;
  height: 8.125rem;
  border-radius: 50%;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
}
.clock-action .clock-circle .clock-text {
  font-size: 1.125rem;
  font-weight: bold;
  color: #4483e5;
}
.clock-action .action-desc {
  font-size: 0.875rem;
  color: #666;
  text-align: center;
}
.section-title {
  font-size: 1rem;
  font-weight: bold;
  margin-bottom: 0.625rem;
  position: relative;
  padding-left: 0.625rem;
}
.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0.25rem;
  width: 0.25rem;
  height: 1rem;
  background-color: #1890ff;
  border-radius: 0.125rem;
}
.recent-records .record-list {
  background-color: #fff;
  border-radius: 0.5rem;
  padding: 0.625rem;
  box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.05);
}
.recent-records .record-list .record-empty {
  padding: 1.25rem 0;
  text-align: center;
  color: #999;
  font-size: 0.875rem;
}
.recent-records .record-list .record-item {
  display: flex;
  padding: 0.625rem 0;
  border-bottom: 0.0625rem solid #f5f5f5;
}
.recent-records .record-list .record-item:last-child {
  border-bottom: none;
}
.recent-records .record-list .record-item .record-date {
  width: 5.625rem;
  display: flex;
  flex-direction: column;
}
.recent-records .record-list .record-item .record-date .date {
  font-size: 0.875rem;
  color: #333;
  margin-bottom: 0.1875rem;
}
.recent-records .record-list .record-item .record-date .week {
  font-size: 0.75rem;
  color: #999;
}
.recent-records .record-list .record-item .record-details {
  flex: 1;
}
.recent-records .record-list .record-item .record-details .detail-item {
  display: flex;
  margin-bottom: 0.3125rem;
}
.recent-records .record-list .record-item .record-details .detail-item:last-child {
  margin-bottom: 0;
}
.recent-records .record-list .record-item .record-details .detail-item .detail-label {
  width: 2.5rem;
  font-size: 0.875rem;
  color: #666;
}
.recent-records .record-list .record-item .record-details .detail-item .detail-value {
  flex: 1;
  font-size: 0.875rem;
  color: #52c41a;
}
.recent-records .record-list .record-item .record-details .detail-item .detail-value.abnormal {
  color: #faad14;
}
.recent-records .record-list .record-item .record-details .detail-item .detail-value .status-tag {
  display: inline-block;
  font-size: 0.6875rem;
  padding: 0.0625rem 0.3125rem;
  border-radius: 0.125rem;
  background-color: rgba(250, 173, 20, 0.1);
  color: #faad14;
  margin-left: 0.3125rem;
}
.face-verify-popup {
  width: 18.75rem;
  background-color: #fff;
  border-radius: 0.625rem;
  overflow: hidden;
}
.face-verify-popup .popup-title {
  font-size: 1rem;
  font-weight: bold;
  padding: 0.9375rem;
  text-align: center;
  border-bottom: 0.03125rem solid #eee;
}
.face-verify-popup .popup-content {
  padding: 0.9375rem;
}
.face-verify-popup .camera-container, .face-verify-popup .photo-preview {
  width: 12.5rem;
  height: 12.5rem;
  margin: 0 auto 0.9375rem;
  background-color: #f5f5f5;
  border-radius: 0.3125rem;
  overflow: hidden;
}
.face-verify-popup .popup-footer {
  display: flex;
  justify-content: space-between;
  padding-top: 0.625rem;
}
.face-verify-popup .popup-footer uni-button {
  flex: 1;
  margin: 0 0.625rem;
  font-size: 0.875rem;
  border-radius: 1.25rem;
}
.face-verify-popup .popup-footer uni-button.btn-cancel {
  background-color: #f5f5f5;
  color: #333;
}
.face-verify-popup .popup-footer uni-button.btn-confirm {
  background-color: #007AFF;
  color: #fff;
}
.supplement-popup {
  width: 18.75rem;
  background-color: #fff;
  border-radius: 0.625rem;
  overflow: hidden;
}
.supplement-popup .popup-title {
  font-size: 1rem;
  font-weight: bold;
  padding: 0.9375rem;
  text-align: center;
  border-bottom: 0.03125rem solid #eee;
}
.supplement-popup .popup-content {
  padding: 0.9375rem;
}
.supplement-popup .form-item {
  margin-bottom: 0.625rem;
}
.supplement-popup .form-item .form-label {
  font-size: 0.875rem;
  color: #333;
  margin-bottom: 0.3125rem;
}
.supplement-popup .form-item .form-input {
  background-color: #f5f5f5;
  border-radius: 0.3125rem;
  padding: 0.625rem;
  font-size: 0.875rem;
}
.supplement-popup .form-item .form-input uni-textarea {
  width: 100%;
  height: 4.6875rem;
}
.supplement-popup .popup-footer {
  display: flex;
  justify-content: space-between;
  padding-top: 0.625rem;
}
.supplement-popup .popup-footer uni-button {
  flex: 1;
  margin: 0 0.625rem;
  font-size: 0.875rem;
  border-radius: 1.25rem;
}
.supplement-popup .popup-footer uni-button.btn-cancel {
  background-color: #f5f5f5;
  color: #333;
}
.supplement-popup .popup-footer uni-button.btn-confirm {
  background-color: #007AFF;
  color: #fff;
}
.supplement-images {
  display: flex;
  flex-wrap: wrap;
  margin-top: 0.3125rem;
}
.supplement-images .image-item {
  position: relative;
  width: 4.6875rem;
  height: 4.6875rem;
  margin-right: 0.625rem;
  margin-bottom: 0.625rem;
  border-radius: 0.3125rem;
  overflow: hidden;
}
.supplement-images .image-item .supplement-image {
  width: 100%;
  height: 100%;
}
.supplement-images .image-item .image-delete {
  position: absolute;
  top: 0;
  right: 0;
  width: 1.25rem;
  height: 1.25rem;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  font-size: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
}
.supplement-images .image-add {
  width: 4.6875rem;
  height: 4.6875rem;
  background-color: #f5f5f5;
  border: 0.03125rem dashed #ccc;
  border-radius: 0.3125rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.supplement-images .image-add .image-add-icon {
  font-size: 1.5rem;
  color: #999;
  line-height: 1;
  margin-bottom: 0.3125rem;
}
.supplement-images .image-add .image-add-text {
  font-size: 0.75rem;
  color: #999;
}
.clock-btn {
  margin-top: 1.25rem;
  width: 90%;
  height: 2.8125rem;
  background: linear-gradient(135deg, #4B79A1, #283E51);
  color: white;
  font-size: 1rem;
  border-radius: 1.40625rem;
  box-shadow: 0 0.3125rem 0.625rem rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
}
.clock-btn.disabled {
  background: linear-gradient(135deg, #ccc, #999);
  box-shadow: none;
}
.date-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.date-picker .icon-calendar {
  font-size: 1rem;
  color: #999;
}