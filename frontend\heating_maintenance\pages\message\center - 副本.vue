<template>
  <BaseTabBar>
    <view class="message-center-container">
      <!-- 消息分类 -->
      <view class="message-tabs">
        <view
          class="message-tab"
          v-for="(tab, index) in messageTabs"
          :key="index"
          :class="{ active: currentTab === tab.value }"
          @click="switchTab(tab.value)"
        >
		  {{ tab.label }}
		  <text class="badge" v-if="tab.count > 0">{{ tab.count }}</text>
		</view>
      </view>

      <!-- 批量操作工具栏 -->
      <view class="action-toolbar">
        <template v-if="currentTab === 'all'">
          <view class="action-filter" @click="toggleTimeFilterPanel">
            <text>按时间</text>
            <text class="action-arrow">▼</text>
          </view>
          <view class="action-filter" @click="toggleFilterPanel">
            <text>按紧急程度</text>
            <text class="action-arrow">▼</text>
          </view>
        </template>
        <template v-else>
          <view class="action-btn" @click="markAllRead">全部已读</view>
          <view class="action-btn" @click="deleteSelected">删除</view>
          <view class="action-filter" @click="toggleFilterPanel">
            <text>按紧急程度</text>
            <text class="action-arrow">▼</text>
          </view>
        </template>
      </view>

      <!-- 筛选面板 -->
      <view class="filter-panel" v-if="showFilterPanel">
        <view
          class="filter-item"
          v-for="(item, index) in priorityOptions"
          :key="index"
          :class="{ active: selectedPriority === item.value }"
          @click="selectPriority(item.value)"
        >
          {{ item.label }}
        </view>
      </view>

      <!-- 时间筛选面板 -->
      <view class="filter-panel" v-if="showTimeFilterPanel">
        <view
          class="filter-item"
          v-for="(item, index) in timeFilterOptions"
          :key="index"
          :class="{ active: selectedTimeFilter === item.value }"
          @click="selectTimeFilter(item.value)"
        >
          {{ item.label }}
        </view>
      </view>

      <!-- 消息列表 -->
      <view class="message-list">
        <view
          class="message-card"
          v-for="message in filteredMessages"
          :key="message.id"
          :class="{ unread: !message.read, [message.priority]: true }"
          @click="viewMessageDetail(message)"
        >
          <view class="message-header">
            <view class="message-title">
              <text class="priority-dot" :class="message.priority"></text>
              <text>{{ message.title }}</text>
            </view>
            <view class="message-time">{{ message.time }}</view>
          </view>
          <view class="message-content">{{ message.content }}</view>
          <view class="message-footer">
            <view class="message-type">{{ getTypeText(message.type) }}</view>
            <view class="message-actions">
              <template v-if="message.type === 'alarm'">
                <view
                  class="action-item ignore-btn"
                  @click.stop="ignoreAlarm(message.id)"
                >
                  忽略
                </view>
              </template>
              <template v-else>
                <view class="action-item" @click.stop="viewMessageDetail(message)">
                  查看
                </view>
              </template>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-if="filteredMessages.length === 0">
        <image class="empty-icon" src="/static/icons/empty.png"></image>
        <text class="empty-text">暂无消息</text>
      </view>

      <!-- 加载更多 -->
      <view
        class="load-more"
        v-if="hasMore && filteredMessages.length > 0"
        @click="loadMore"
        >加载更多</view
      >
      <view class="no-more" v-if="!hasMore && filteredMessages.length > 0"
        >没有更多消息了</view
      >
    </view>
  </BaseTabBar>
</template>

<script>
import { alarmApi, heatUnitApi, faultApi, patrolApi, workOrderApi } from "@/utils/api.js";
import BaseTabBar from "@/components/BaseTabBar.vue";
import PermissionCheck from "@/components/PermissionCheck.vue"; // 导入权限检查组件
export default {
  components: {
    BaseTabBar,
	PermissionCheck
  },
  data() {
    return {
      currentTab: "all",
      showFilterPanel: false,
      selectedPriority: "all",

      // 新增时间筛选相关数据
      showTimeFilterPanel: false,
      selectedTimeFilter: "all",
      timeFilterOptions: [
        { label: "全部时间", value: "all" },
        { label: "今天", value: "today" },
        { label: "昨天", value: "yesterday" },
        { label: "本周", value: "thisWeek" },
        { label: "本月", value: "thisMonth" },
      ],

      messageTabs: [
        { label: "全部", value: "all", count: 0 ,permissions:""},
        { label: "巡检", value: "inspection", count: 0 ,permissions:"home:patrol-record"},
        { label: "工单", value: "workOrder", count: 0 ,permissions:"home:workorder-list"},
        { label: "告警", value: "alarm", count: 0 ,permissions:"message:alarm-list"},
        { label: "故障", value: "fault", count: 0 ,permissions:"home:fault-list"},
        { label: "系统", value: "system", count: 0 ,permissions:""},
      ],

      priorityOptions: [
        { label: "全部", value: "all" },
        { label: "紧急", value: "urgent" },
        { label: "重要", value: "important" },
        { label: "常规", value: "normal" },
      ],

      messages: [],

      page: 1,
      pageSize: 10,
      hasMore: false,
      isLoading: false,
    };
  },
  computed: {
    filteredMessages() {
      let result = [...this.messages];

      if (this.currentTab !== "all") {
        console.log(`[Debug] 筛选类型: ${this.currentTab}`);
        result = result.filter((msg) => {
          const match = msg.type === this.currentTab;
          return match;
        });
        console.log(`[Debug] 筛选【${this.currentTab}】后数量: ${result.length}`);
      }

      if (this.selectedPriority !== "all") {
        result = result.filter((msg) => msg.priority === this.selectedPriority);
      }

      // 添加时间筛选逻辑
      if (this.selectedTimeFilter !== "all") {
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);

        const startOfWeek = new Date(today);
        startOfWeek.setDate(today.getDate() - today.getDay()); // 从周日开始

        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

        result = result.filter((msg) => {
          const messageDate = new Date(
            msg.time.replace(/(\d{4}-\d{2}-\d{2}) (\d{2}:\d{2})/, "$1T$2:00")
          );

          switch (this.selectedTimeFilter) {
            case "today":
              return messageDate >= today;
            case "yesterday":
              return messageDate >= yesterday && messageDate < today;
            case "thisWeek":
              return messageDate >= startOfWeek;
            case "thisMonth":
              return messageDate >= startOfMonth;
            default:
              return true;
          }
        });
      }

      result.sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime());

      return result;
    },
  },
  onLoad() {
    uni.$on("newMessageReceived", this.handleNewMessages);
    this.loadInitialMessages();
  },
  onShow() {
    //清理缓存数据
    this.messages = [];
    // 通过 api.js 获取消息
    this.fetchAllMessages();
    this.updateAllTabCounts();
  },
  onUnload() {
    uni.$off("newMessageReceived", this.handleNewMessages);
  },
  methods: {
    handleNewMessages(payload) {
      console.log("消息中心：收到新消息事件", payload);
      const { type, data } = payload;

      if (data && Array.isArray(data) && data.length > 0) {
        const newMessages = data
          .map((msg) => this.transformMessageFormat(msg, type))
          .filter((msg) => msg !== null);

        const existingIds = new Set(this.messages.map((m) => m.id));
        const uniqueNewMessages = newMessages.filter((nm) => !existingIds.has(nm.id));

        if (uniqueNewMessages.length > 0) {
          // 添加注释
          this.messages.unshift(...uniqueNewMessages);
          this.updateTabCounts(type, uniqueNewMessages.length);
          uni.showToast({
            title: `收到 ${
              uniqueNewMessages.length
            } 条新的${this.mapBackendChineseTypeToFrontend(type)}消息`,
            icon: "none",
          });
        }
      }
    },

    transformMessageFormat(msg, backendChineseType) {
      if (!msg || !msg.id) return null;

      let frontendType = this.mapBackendChineseTypeToFrontend(backendChineseType);
      let title = `新${backendChineseType}消息`;
      let content = "详情请点击查看";
      let priority = "normal";
      let timeValue = null;
      let heatUnitName = "";
      let heatUnitId = -1;
      let alarmId = -1;
      let faultLevel = "一般";

      switch (backendChineseType) {
        case "巡检":
          title = msg.name || title;
          content = `请执行巡检任务: ${msg.name || "未指定"}`;
          priority = "normal";
          break;
        case "告警":
          title = msg.heatUnitName;
          content = msg.alarmDesc || content;
          timeValue = msg.alarmDt;
          priority = "urgent";
          heatUnitName = msg.heatUnitName || "";
          heatUnitId = msg.heatUnitId;
          alarmId = msg.id;
          faultLevel = msg.faultLevels || "一般";
          break;
        case "故障":
          title = msg.heatUnitName;
          content = msg.faultDesc || content;
          timeValue = msg.occurTime;
          priority = "important";
          heatUnitName = msg.heatUnitName || "";
          faultLevel = msg.faultLevel || "一般";
          break;
        case "工单":
          title = `新工单 #${msg.id}`;
          content = msg.fault_desc
            ? `来源[${msg.fault_source || "未知"}]：${msg.fault_desc}`
            : content;
          timeValue = msg.createdTime;
          priority = "important";
          heatUnitName = msg.heatUnitName || "";
          faultLevel = msg.faultLevel || "一般";
          break;
      }

      let timeString = "";
      if (typeof timeValue === "string") {
        timeString = timeValue;
      } else if (timeValue instanceof Date) {
        timeString = timeValue.toISOString();
      } else if (Array.isArray(timeValue) && timeValue.length >= 5) {
        try {
          const [year, month, day, hour, minute] = timeValue;
          const isoMonth = String(month).padStart(2, "0");
          const isoDay = String(day).padStart(2, "0");
          const isoHour = String(hour).padStart(2, "0");
          const isoMinute = String(minute).padStart(2, "0");
          const isoSeconds =
            timeValue.length > 5 ? String(timeValue[5]).padStart(2, "0") : "00";
          timeString = `${year}-${isoMonth}-${isoDay}T${isoHour}:${isoMinute}:${isoSeconds}`;
        } catch (e) {
          console.error("消息中心：处理时间数组时出错:", timeValue, e);
          timeString = new Date().toISOString();
        }
      } else if (timeValue) {
        timeString = String(timeValue);
      } else {
        timeString = new Date().toISOString();
        console.warn(`消息 [${frontendType}-${msg.id}] 缺少有效时间，使用当前时间。`);
      }

      return {
        id: `${frontendType}-${msg.id}`,
        title: title,
        content: content,
        time: timeString.replace("T", " ").substring(0, 16),
        type: frontendType,
        priority: priority,
        read: false,
        relatedId: msg.id,
        heatUnitName: heatUnitName,
        heatUnitId: heatUnitId,
        alarmId: alarmId,
        faultLevel: faultLevel,
      };
    },

    mapBackendChineseTypeToFrontend(backendType) {
      const map = {
        巡检: "inspection",
        告警: "alarm",
        故障: "fault",
        工单: "workOrder",
      };
      return map[backendType] || "system";
    },

    updateTabCounts(backendChineseType, countIncrement) {
      const frontendType = this.mapBackendChineseTypeToFrontend(backendChineseType);
      const tab = this.messageTabs.find((t) => t.value === frontendType);
      if (tab) {
        tab.count = (tab.count || 0) + countIncrement;
      }
      const allTab = this.messageTabs.find((t) => t.value === "all");
      if (allTab) {
        allTab.count = (allTab.count || 0) + countIncrement;
      }
    },

    updateAllTabCounts() {
      const counts = {
        all: 0,
        inspection: 0,
        workOrder: 0,
        alarm: 0,
        fault: 0,
        system: 0,
      };
      this.messages.forEach((msg) => {
        counts.all++;
        if (counts[msg.type] !== undefined) {
          counts[msg.type]++;
        } else {
          counts.system++;
        }
      });
      this.messageTabs.forEach((tab) => {
        tab.count = counts[tab.value] || 0;
      });
    },

    loadInitialMessages() {
      if (this.isLoading) return;
      this.isLoading = true;
      console.log("加载初始消息列表 - 第1页");

      this.messages = [];
      this.selectedPriority = "all";
      this.selectedTimeFilter = "all";
      this.updateAllTabCounts();
      this.isLoading = false;
    },

    loadMore() {
      if (this.isLoading || !this.hasMore) return;
      this.page++;
      this.isLoading = true;
      console.log(`加载更多消息 - 第${this.page}页`);

      setTimeout(() => {
        this.hasMore = false;
        this.isLoading = false;
      }, 500);
    },

    switchTab(tab) {
      this.currentTab = tab;
      console.log(`切换到标签: ${tab}`);

      // Reset filters
      this.showFilterPanel = false;
      this.showTimeFilterPanel = false;

      // Optionally reset filter values when switching tabs
      // this.selectedPriority = 'all';
      // this.selectedTimeFilter = 'all';
    },

    toggleFilterPanel() {
      this.showFilterPanel = !this.showFilterPanel;
      if (this.showFilterPanel) {
        this.showTimeFilterPanel = false;
      }
    },

    toggleTimeFilterPanel() {
      this.showTimeFilterPanel = !this.showTimeFilterPanel;
      if (this.showTimeFilterPanel) {
        this.showFilterPanel = false;
      }
    },

    selectPriority(priority) {
      this.selectedPriority = priority;
      this.showFilterPanel = false;
    },

    selectTimeFilter(timeFilter) {
      this.selectedTimeFilter = timeFilter;
      this.showTimeFilterPanel = false;
    },

    markAllRead() {
      this.messages.forEach((msg) => {
        msg.read = true;
      });

      uni.showToast({
        title: "已全部标为已读",
        icon: "success",
      });
    },

    deleteSelected() {
      uni.showModal({
        title: "确认操作",
        content: "确定要删除当前筛选出的所有消息吗？",
        success: (res) => {
          if (res.confirm) {
            const idsToDelete = this.filteredMessages.map((m) => m.id);
            this.messages = this.messages.filter((msg) => !idsToDelete.includes(msg.id));

            this.updateAllTabCounts();

            uni.showToast({
              title: "删除成功",
              icon: "success",
            });
          }
        },
      });
    },

    viewMessageDetail(message) {
      if (!message) return;

      if (!message.read && message.type !== "alarm") {
        message.read = true;
      }

      if (message.type === "alarm") {
        this.showAlarmConfirmation(message);
      } else {
        let targetUrl = "";
        const relatedId = message.relatedId || "";

        switch (message.type) {
          case "fault":
            targetUrl = `/pages/fault/detail?id=${relatedId}`;
            break;
          case "workOrder":
            targetUrl = `/pages/workorder/detail?id=${relatedId}`;
            break;
          case "inspection":
            targetUrl = `/pages/patrol/detail?id=${relatedId}`;
            break;
        }

        if (targetUrl && relatedId) {
          console.log(`导航到: ${targetUrl}`);
          uni.navigateTo({
            url: targetUrl,
            fail: (err) => {
              console.error(`导航失败: ${JSON.stringify(err)}`);
              uni.showToast({ title: "页面跳转失败", icon: "none" });
            },
          });
        } else {
          console.log("无明确跳转目标或缺少 relatedId，显示 Modal", message);
          uni.showModal({
            title: message.title,
            content: message.content,
            showCancel: false,
          });
        }
      }
    },

    showAlarmConfirmation(message) {
      uni.showModal({
        title: "告警确认",
        content: `检测到告警：\n${message.content}\n\n是否确认此告警并直接上报故障？`,
        confirmText: "确认上报",
        cancelText: "取消",
        success: async (res) => {
          if (res.confirm) {
            uni.showLoading({ title: "处理中..." });
            let heatUnitId = null;
            let reportUserId = uni.getStorageSync("userId") || null; // 尝试从缓存获取用户ID
            if (!reportUserId) {
              uni.hideLoading();
              uni.showToast({ title: "无法获取用户信息，请重新登录", icon: "none" });
              return;
            }

            try {
              // 第一步：准备并调用故障上报接口
              const reportData = {
                heat_unit_id: message.heatUnitId, // 可能为 null
                alarm_id: message.alarmId,
                fault_type: "设备故障", // 固定值
                fault_level: message.faultLevel || "一般", // 使用告警等级
                fault_desc: message.content, // 使用告警描述
                fault_source: "系统检测", // 固定值
                occur_time: message.time ? `${message.time}:00` : null, // 格式化时间，确保秒存在
                report_user_id: reportUserId,
                attachment: [], // 无附件
              };
              console.log("准备直接上报故障数据:", reportData);
              const reportRes = await faultApi.reportFault(reportData);
              uni.hideLoading();
              if (reportRes.code === 200) {
                uni.showToast({ title: "故障已成功上报", icon: "success" });

                // 第二步：确认告警状态
                const alarmStatusRes = await alarmApi.updateAlarmStatus(
                  message.relatedId,
                  1
                ); // 1: 已确认
                if (alarmStatusRes.code !== 200) {
                  throw new Error(alarmStatusRes.message || "确认告警状态失败");
                }
                console.log(`告警 ${message.relatedId} 状态已更新为已确认`);

                // 可选：上报成功后从消息列表移除该告警
                const index = this.messages.findIndex((m) => m.id === message.id);
                if (index > -1) {
                  this.messages.splice(index, 1);
                  this.updateAllTabCounts();
                }
              } else {
                throw new Error(reportRes.message || "故障上报失败");
              }
            } catch (error) {
              uni.hideLoading();
              console.error("确认告警并上报故障过程中出错:", error);
              uni.showToast({ title: error.message || "操作失败，请重试", icon: "none" });
            }
          } // 点击了确认
        },
      });
    },

    ignoreAlarm(id) {
      const message = this.messages.find((msg) => msg.id === id);
      if (!message || message.type !== "alarm") return;

      uni.showModal({
        title: "确认忽略",
        content: "确定要忽略这条告警信息吗？忽略后将不再提醒。",
        success: (res) => {
          if (res.confirm) {
            console.log(
              `准备忽略告警，前端消息ID: ${id}, 后端告警ID: ${message.relatedId}`
            );
            uni.showLoading({ title: "处理中..." });
            alarmApi
              .updateAlarmStatus(message.relatedId, 3)
              .then((apiRes) => {
                uni.hideLoading();
                if (apiRes.code === 200) {
                  uni.showToast({ title: "告警已忽略", icon: "success" });
                  const index = this.messages.findIndex((m) => m.id === id);
                  if (index > -1) {
                    this.messages.splice(index, 1);
                    this.updateAllTabCounts();
                  }
                } else {
                  uni.showToast({ title: apiRes.message || "忽略失败", icon: "none" });
                }
              })
              .catch((err) => {
                uni.hideLoading();
                console.error("忽略告警API调用失败:", err);
                uni.showToast({ title: "操作失败，请重试", icon: "none" });
              });
          }
        },
      });
    },

    getTypeText(type) {
      const typeMap = {
        alarm: "告警",
        workOrder: "工单",
        inspection: "巡检",
        fault: "故障",
        system: "系统",
      };
      return typeMap[type] || "其他";
    },

    async fetchAllMessages() {
      console.log("[MessageCenter] Fetching all messages onShow...");
      uni.showLoading({ title: "加载中..." });

      try {
        const messageFetchers = [
          { api: patrolApi.getPatrolPlansMessages, type: "巡检", backendTypeKey: "巡检" },
          { api: alarmApi.getAlarmList, type: "告警", backendTypeKey: "告警" },
          { api: faultApi.getFaultMessages, type: "故障", backendTypeKey: "故障" },
          {
            api: workOrderApi.getWorkOrderMessages,
            type: "工单",
            backendTypeKey: "工单",
          },
        ];

        let allNewMessages = [];

        for (const fetcher of messageFetchers) {
          try {
            console.log(`[MessageCenter] Fetching ${fetcher.type} messages...`);
            const res = await fetcher.api();
            console.log(
              `[MessageCenter] Fetched ${fetcher.type} messages response:`,
              res
            );
            if (res && res.code === 200 && res.data && Array.isArray(res.data)) {
              const transformed = res.data
                .map((msg) => this.transformMessageFormat(msg, fetcher.backendTypeKey))
                .filter((msg) => msg !== null);
              allNewMessages.push(...transformed);
              console.log(
                `[MessageCenter] Transformed ${fetcher.type} messages:`,
                transformed.length
              );
            } else {
              console.warn(
                `[MessageCenter] Failed to fetch or no data for ${fetcher.type} messages:`,
                res
              );
            }
          } catch (error) {
            console.error(
              `[MessageCenter] Error fetching ${fetcher.type} messages:`,
              error
            );
          }
        }

        if (allNewMessages.length > 0) {
          const existingIds = new Set(this.messages.map((m) => m.id));
          const uniqueNewMessages = allNewMessages.filter(
            (nm) => !existingIds.has(nm.id)
          );

          if (uniqueNewMessages.length > 0) {
            this.messages.unshift(...uniqueNewMessages);
            // Sort messages by time after adding new ones
            this.messages.sort(
              (a, b) => new Date(b.time).getTime() - new Date(a.time).getTime()
            );
            console.log(
              `[MessageCenter] Added ${uniqueNewMessages.length} new unique messages to the list.`
            );
            uni.showToast({
              title: `加载了 ${uniqueNewMessages.length} 条新消息`,
              icon: "none",
            });
          } else {
            console.log("[MessageCenter] No new unique messages to add.");
          }
        } else {
          console.log("[MessageCenter] No messages fetched from APIs.");
        }
      } catch (error) {
        console.error("[MessageCenter] Error in fetchAllMessages:", error);
        uni.showToast({ title: "加载消息失败", icon: "none" });
      } finally {
        this.updateAllTabCounts();
        this.isLoading = false;
        uni.hideLoading();
        console.log("[MessageCenter] fetchAllMessages completed.");
      }
    },
  },
};
</script>

<style lang="scss">
.message-center-container {
  padding: 20rpx;
  box-sizing: border-box;
}

.message-tabs {
  display: flex;
  background-color: #fff;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
  overflow-x: auto;
  white-space: nowrap;

  .message-tab {
    flex: 1;
    min-width: 120rpx;
    text-align: center;
    padding: 20rpx 0;
    font-size: 28rpx;
    position: relative;

    .badge {
      position: absolute;
      top: 10rpx;
      right: 10rpx;
      background-color: $uni-color-error;
      color: #fff;
      font-size: 20rpx;
      border-radius: 20rpx;
      padding: 2rpx 10rpx;
      min-width: 20rpx;
      height: 20rpx;
      line-height: 20rpx;
      text-align: center;
    }

    &.active {
      color: $uni-color-primary;
      font-weight: bold;

      &::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 40rpx;
        height: 4rpx;
        background-color: $uni-color-primary;
      }
    }
  }
}

.action-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 8rpx;
  padding: 16rpx 20rpx;

  .action-btn {
    font-size: 26rpx;
    color: $uni-color-primary;
    padding: 6rpx 16rpx;
  }

  .action-filter {
    display: flex;
    align-items: center;
    font-size: 26rpx;
    color: $uni-text-color;

    .action-arrow {
      margin-left: 10rpx;
      font-size: 24rpx;
    }
  }
}

.filter-panel {
  background-color: #fff;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;

  .filter-item {
    padding: 16rpx 0;
    font-size: 28rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);

    &:last-child {
      border-bottom: none;
    }

    &.active {
      color: $uni-color-primary;
    }
  }
}

.message-list {
  .message-card {
    background-color: #fff;
    border-radius: 8rpx;
    padding: 24rpx;
    margin-bottom: 20rpx;
    border-left: 6rpx solid transparent;

    &.unread {
      background-color: rgba(24, 144, 255, 0.05);
    }

    &.urgent {
      border-left-color: $uni-color-error;
    }

    &.important {
      border-left-color: $uni-color-warning;
    }

    &.normal {
      border-left-color: $uni-color-primary;
    }

    .message-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16rpx;

      .message-title {
        font-size: 32rpx;
        font-weight: bold;
        display: flex;
        align-items: center;

        .priority-dot {
          width: 16rpx;
          height: 16rpx;
          border-radius: 50%;
          margin-right: 12rpx;

          &.urgent {
            background-color: $uni-color-error;
          }

          &.important {
            background-color: $uni-color-warning;
          }

          &.normal {
            background-color: $uni-color-primary;
          }
        }
      }

      .message-time {
        font-size: 24rpx;
        color: $uni-text-color-grey;
      }
    }

    .message-content {
      font-size: 28rpx;
      line-height: 1.5;
      margin-bottom: 16rpx;
    }

    .message-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .message-type {
        font-size: 24rpx;
        color: $uni-text-color-grey;
        background-color: #f5f5f5;
        padding: 4rpx 16rpx;
        border-radius: 4rpx;
      }

      .message-actions {
        display: flex;

        .action-item {
          font-size: 24rpx;
          color: $uni-color-primary;
          margin-left: 20rpx;
        }
      }
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .empty-icon {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 30rpx;
  }

  .empty-text {
    font-size: 28rpx;
    color: $uni-text-color-grey;
  }
}

.load-more,
.no-more {
  text-align: center;
  padding: 20rpx 0;
  font-size: 26rpx;
  color: $uni-text-color-grey;
}

.message-actions .action-item.ignore-btn {
  color: $uni-color-warning;
}
.message-actions .action-item.delete-btn {
  color: $uni-color-error;
}
</style>
