
  ;(function(){
  let u=void 0,isReady=false,onReadyCallbacks=[],isServiceReady=false,onServiceReadyCallbacks=[];
  const __uniConfig = {"pages":[],"globalStyle":{"backgroundColor":"#F8F8F8","navigationBar":{"backgroundColor":"#1890ff","titleText":"智慧供暖运维系统","type":"default","titleColor":"#ffffff"},"isNVue":false},"nvue":{"compiler":"uni-app","styleCompiler":"uni-app","flex-direction":"column"},"renderer":"auto","appname":"洁明智慧供热","splashscreen":{"alwaysShowBeforeRender":true,"autoclose":true},"compilerVersion":"4.66","entryPagePath":"pages/user/login","entryPageQuery":"","realEntryPagePath":"","networkTimeout":{"request":60000,"connectSocket":60000,"uploadFile":60000,"downloadFile":60000},"tabBar":{"position":"bottom","color":"#999","selectedColor":"#1890ff","borderStyle":"white","blurEffect":"none","fontSize":"10px","iconWidth":"24px","spacing":"3px","height":"60px","list":[{"pagePath":"pages/home/<USER>","text":"首页","iconPath":"/static/tab/home.png","selectedIconPath":"/static/tab/home-active.png"},{"pagePath":"pages/hes/list","text":"换热站","iconPath":"/static/tab/hes.png","selectedIconPath":"/static/tab/hes-active.png"},{"pagePath":"pages/message/center","text":"消息","iconPath":"/static/tab/notification.png","selectedIconPath":"/static/tab/notification-active.png"},{"pagePath":"pages/user/info","text":"我的","iconPath":"/static/tab/profile.png","selectedIconPath":"/static/tab/profile-active.png"}],"backgroundColor":"#ffffff","selectedIndex":0,"shown":true},"fallbackLocale":"zh-Hans","locales":{},"darkmode":false,"themeConfig":{}};
  const __uniRoutes = [{"path":"pages/user/login","meta":{"isQuit":true,"isEntry":true,"navigationBar":{"titleText":"登录","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/user/agreement","meta":{"navigationBar":{"titleText":"用户协议","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/home/<USER>","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":0,"navigationBar":{"titleText":"智慧供暖运维系统","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/device/list","meta":{"navigationBar":{"titleText":"设备列表","type":"default"},"isNVue":false}},{"path":"pages/device/detail","meta":{"navigationBar":{"titleText":"设备详情","type":"default"},"isNVue":false}},{"path":"pages/hes/list","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":1,"navigationBar":{"titleText":"换热站列表","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/hes/detail","meta":{"navigationBar":{"titleText":"换热站详情","type":"default"},"isNVue":false}},{"path":"pages/hes/control","meta":{"navigationBar":{"titleText":"换热站控制","type":"default"},"isNVue":false}},{"path":"pages/valves/control","meta":{"navigationBar":{"titleText":"阀门控制","type":"default"},"isNVue":false}},{"path":"pages/valves/detail","meta":{"navigationBar":{"titleText":"阀门详情","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/payment/stats","meta":{"navigationBar":{"titleText":"缴费统计","type":"default"},"isNVue":false}},{"path":"pages/message/center","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":2,"navigationBar":{"titleText":"消息中心","type":"default"},"isNVue":false}},{"path":"pages/user/info","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":3,"navigationBar":{"titleText":"个人中心","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/user/profile","meta":{"navigationBar":{"titleText":"个人资料","type":"default"},"isNVue":false}},{"path":"pages/user/message-settings","meta":{"navigationBar":{"titleText":"消息设置","type":"default"},"isNVue":false}},{"path":"pages/user/change-password","meta":{"navigationBar":{"titleText":"密码修改","type":"default"},"isNVue":false}},{"path":"pages/user/account-binding","meta":{"navigationBar":{"titleText":"账号绑定","type":"default"},"isNVue":false}},{"path":"pages/user/faq","meta":{"navigationBar":{"titleText":"常见问题","type":"default"},"isNVue":false}},{"path":"pages/user/about","meta":{"navigationBar":{"titleText":"关于系统","type":"default"},"isNVue":false}},{"path":"pages/workorder/detail","meta":{"navigationBar":{"titleText":"工单详情","type":"default"},"isNVue":false}},{"path":"pages/workorder/list","meta":{"navigationBar":{"titleText":"工单列表","type":"default"},"isNVue":false}},{"path":"pages/workorder/complete","meta":{"navigationBar":{"titleText":"完成工单","type":"default"},"isNVue":false}},{"path":"pages/workorder/transfer","meta":{"navigationBar":{"titleText":"转派工单","type":"default"},"isNVue":false}},{"path":"pages/patrol/plans","meta":{"enablePullDownRefresh":true,"navigationBar":{"titleText":"巡检计划","type":"default"},"isNVue":false}},{"path":"pages/patrol/detail","meta":{"navigationBar":{"titleText":"巡检计划详情","type":"default"},"isNVue":false}},{"path":"pages/patrol/execute","meta":{"navigationBar":{"titleText":"巡检执行","type":"default"},"isNVue":false}},{"path":"pages/patrol/create","meta":{"navigationBar":{"titleText":"新建巡检计划","type":"default"},"isNVue":false}},{"path":"pages/patrol/records","meta":{"enablePullDownRefresh":true,"navigationBar":{"titleText":"巡检工单","type":"default"},"isNVue":false}},{"path":"pages/patrol/record_detail","meta":{"navigationBar":{"titleText":"巡检工单详情","type":"default"},"isNVue":false}},{"path":"pages/patrol/create_record","meta":{"navigationBar":{"titleText":"新建巡检工单","type":"default"},"isNVue":false}},{"path":"pages/fault/report","meta":{"navigationBar":{"titleText":"故障上报","type":"default"},"isNVue":false}},{"path":"pages/fault/createfault","meta":{"navigationBar":{"titleText":"维修工单创建","type":"default"},"isNVue":false}},{"path":"pages/fault/list","meta":{"enablePullDownRefresh":true,"navigationBar":{"titleText":"故障列表","type":"default"},"isNVue":false}},{"path":"pages/fault/detail","meta":{"navigationBar":{"titleText":"故障详情","type":"default"},"isNVue":false}},{"path":"components/multi-selector","meta":{"navigationBar":{"titleText":"选择","style":"custom","type":"default"},"isNVue":false}},{"path":"components/datetime-picker","meta":{"navigationBar":{"titleText":"选择日期时间","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/attendance/clock-in","meta":{"navigationBar":{"titleText":"人员打卡","type":"default"},"isNVue":false}},{"path":"pages/attendance/statistics","meta":{"navigationBar":{"titleText":"考勤统计","type":"default"},"isNVue":false}},{"path":"pages/attendance/all-records","meta":{"navigationBar":{"titleText":"考勤记录","type":"default"},"isNVue":false}},{"path":"pages/attendance/admin/rules","meta":{"navigationBar":{"titleText":"考勤规则设置","type":"default"},"isNVue":false}},{"path":"pages/messageTest/messageTest","meta":{"navigationBar":{"titleText":"消息服务测试","type":"default"},"isNVue":false}},{"path":"pages/common/video-player","meta":{"backgroundColor":"#000000","navigationBar":{"backgroundColor":"#000000","titleText":"视频播放","type":"default","titleColor":"#ffffff"},"isNVue":false}}].map(uniRoute=>(uniRoute.meta.route=uniRoute.path,__uniConfig.pages.push(uniRoute.path),uniRoute.path='/'+uniRoute.path,uniRoute));
  __uniConfig.styles=[];//styles
  __uniConfig.onReady=function(callback){if(__uniConfig.ready){callback()}else{onReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"ready",{get:function(){return isReady},set:function(val){isReady=val;if(!isReady){return}const callbacks=onReadyCallbacks.slice(0);onReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
  __uniConfig.onServiceReady=function(callback){if(__uniConfig.serviceReady){callback()}else{onServiceReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"serviceReady",{get:function(){return isServiceReady},set:function(val){isServiceReady=val;if(!isServiceReady){return}const callbacks=onServiceReadyCallbacks.slice(0);onServiceReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
  service.register("uni-app-config",{create(a,b,c){if(!__uniConfig.viewport){var d=b.weex.config.env.scale,e=b.weex.config.env.deviceWidth,f=Math.ceil(e/d);Object.assign(__uniConfig,{viewport:f,defaultFontSize:16})}return{instance:{__uniConfig:__uniConfig,__uniRoutes:__uniRoutes,global:u,window:u,document:u,frames:u,self:u,location:u,navigator:u,localStorage:u,history:u,Caches:u,screen:u,alert:u,confirm:u,prompt:u,fetch:u,XMLHttpRequest:u,WebSocket:u,webkit:u,print:u}}}}); 
  })();
  