/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-popup[data-v-7db519c7] {
  position: fixed;
  z-index: 99;
}
.uni-popup.top[data-v-7db519c7], .uni-popup.left[data-v-7db519c7], .uni-popup.right[data-v-7db519c7] {
  top: 0;
}
.uni-popup .uni-popup__wrapper[data-v-7db519c7] {
  display: block;
  position: relative;
  /* iphonex 等安全区设置，底部安全区适配 */
}
.uni-popup .uni-popup__wrapper.left[data-v-7db519c7], .uni-popup .uni-popup__wrapper.right[data-v-7db519c7] {
  padding-top: 0;
  flex: 1;
}
.fixforpc-z-index[data-v-7db519c7] {
  z-index: 999;
}
.fixforpc-top[data-v-7db519c7] {
  top: 0;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.transfer-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 0.625rem;
}
.form-card {
  background-color: #fff;
  border-radius: 0.375rem;
  margin-bottom: 0.625rem;
  padding: 0.9375rem;
}
.form-card .form-title {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.9375rem;
  padding-bottom: 0.625rem;
  border-bottom: 0.03125rem solid #eee;
}
.form-card .form-item {
  margin-bottom: 0.9375rem;
}
.form-card .form-item .form-label {
  font-size: 0.875rem;
  color: #333;
  margin-bottom: 0.5rem;
}
.form-card .form-item .form-label.required::before {
  content: "*";
  color: #f5222d;
  margin-right: 0.1875rem;
}
.form-card .form-item .form-textarea {
  width: 100%;
  height: 6.25rem;
  padding: 0.625rem;
  background-color: #f5f5f5;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  color: #333;
  box-sizing: border-box;
}
.form-card .form-item .picker-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 2.5rem;
  padding: 0 0.625rem;
  background-color: #f5f5f5;
  border-radius: 0.25rem;
  box-sizing: border-box;
}
.form-card .form-item .picker-box .picker-text {
  font-size: 0.875rem;
  color: #333;
}
.form-card .form-item .picker-box .picker-text.placeholder {
  color: #999;
}
.form-card .form-item .picker-box .iconfont {
  font-size: 0.875rem;
  color: #999;
}
.action-buttons {
  display: flex;
  padding: 0.625rem 0;
}
.action-buttons uni-button {
  flex: 1;
  height: 2.5rem;
  line-height: 2.5rem;
  text-align: center;
  border-radius: 1.25rem;
  font-size: 0.875rem;
  margin: 0 0.3125rem;
}
.action-buttons uni-button::after {
  border: none;
}
.action-buttons .btn-cancel {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}
.action-buttons .btn-submit {
  background-color: #1890ff;
  color: #fff;
}
.popup-container {
  background-color: #fff;
  border-radius: 0.75rem 0.75rem 0 0;
  overflow: hidden;
}
.popup-container .popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.9375rem;
  border-bottom: 0.03125rem solid #eee;
}
.popup-container .popup-header .title {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
}
.popup-container .popup-header .cancel, .popup-container .popup-header .confirm {
  font-size: 0.875rem;
}
.popup-container .popup-header .cancel {
  color: #999;
}
.popup-container .popup-header .confirm {
  color: #1890ff;
}
.popup-container .search-box {
  padding: 0.625rem 0.9375rem;
  border-bottom: 0.03125rem solid #eee;
}
.popup-container .search-box .search-input-wrap {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 1.125rem;
  padding: 0 0.625rem;
  height: 2.25rem;
}
.popup-container .search-box .search-input-wrap .iconfont {
  color: #999;
  font-size: 1rem;
  margin-right: 0.3125rem;
}
.popup-container .search-box .search-input-wrap .search-input {
  flex: 1;
  height: 2.25rem;
  font-size: 0.875rem;
}
.popup-container .search-box .search-input-wrap .icon-close {
  padding: 0.3125rem;
}
.popup-container .popup-body {
  max-height: 60vh;
  padding-bottom: 1.5625rem;
}
.popup-container .popup-body .user-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.9375rem;
  border-bottom: 0.03125rem solid #eee;
  position: relative;
}
.popup-container .popup-body .user-item.active {
  background-color: #e6f7ff;
  border-left: 0.25rem solid #1890ff;
}
.popup-container .popup-body .user-item .user-info {
  display: flex;
  flex-direction: column;
}
.popup-container .popup-body .user-item .user-info .user-name {
  font-size: 0.875rem;
  color: #333;
  margin-bottom: 0.25rem;
  font-weight: bold;
}
.popup-container .popup-body .user-item .user-info .user-phone {
  font-size: 0.75rem;
  color: #999;
}
.popup-container .popup-body .user-item .select-indicator {
  display: flex;
  align-items: center;
  color: #1890ff;
}
.popup-container .popup-body .user-item .select-indicator .select-text {
  font-size: 0.875rem;
  color: #1890ff;
  margin-right: 0.3125rem;
}
.popup-container .popup-body .user-item .select-indicator .iconfont {
  font-size: 1.25rem;
  color: #1890ff;
}
.popup-container .popup-body .empty-tip {
  text-align: center;
  color: #999;
  padding: 0.9375rem;
}