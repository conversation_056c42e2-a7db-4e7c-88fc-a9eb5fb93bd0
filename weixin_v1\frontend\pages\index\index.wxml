<view class="container">
  <!-- 顶部统计卡片 -->
  <view class="stats-card">
    <view class="stats-header">
      <text class="title">今日概况</text>
      <text class="date">{{currentDate}}</text>
    </view>
    <view class="stats-grid">
      <view class="stats-item">
        <text class="value">{{stats.faultCount || 0}}</text>
        <text class="label">故障数</text>
      </view>
      <view class="stats-item">
        <text class="value">{{stats.processingCount || 0}}</text>
        <text class="label">处理中</text>
      </view>
      <view class="stats-item">
        <text class="value">{{stats.completedCount || 0}}</text>
        <text class="label">已完成</text>
      </view>
      <view class="stats-item">
        <text class="value">{{stats.avgTemp || 0}}°C</text>
        <text class="label">平均温度</text>
      </view>
    </view>
  </view>
  
  <!-- 快捷操作区 -->
  <view class="quick-actions">
    <view class="action-item" bindtap="goToFaultReport">
      <view class="icon-box fault">
        <text class="wx-icon">⚠️</text>
      </view>
      <text class="action-text">故障上报</text>
    </view>
    <view class="action-item" bindtap="goToTempReport">
      <view class="icon-box temp">
        <text class="wx-icon">🌡️</text>
      </view>
      <text class="action-text">室温上报</text>
    </view>
    <view class="action-item" bindtap="goToWorkorders">
      <view class="icon-box work">
        <text class="wx-icon">📝</text>
      </view>
      <text class="action-text">我的工单</text>
    </view>
  </view>

  <!-- 最新故障列表 -->
  <view class="section">
    <view class="section-header">
      <text class="section-title">最新故障</text>
      <text class="more" bindtap="goToFaultList">查看更多 ›</text>
    </view>
    <view class="fault-list">
      <view class="fault-item" wx:for="{{recentFaults}}" wx:key="id" bindtap="goToFaultDetail" data-id="{{item.id}}">
        <view class="fault-main">
          <view class="fault-type">
            <text class="type-tag level-{{item.level}}">{{item.type}}</text>
            <text class="time">{{item.time}}</text>
          </view>
          <view class="fault-desc">{{item.description}}</view>
        </view>
        <text class="status {{item.status}}">{{item.statusText}}</text>
      </view>
    </view>
  </view> 
    
<!-- 温度监测区域 -->
    <view class="section">
        <view class="section-header">
          <text class="section-title">室温监测</text>
          <text class="more" bindtap="goToTempList">查看更多 ›</text>
        </view>
        <view class="temp-card">
          <view class="temp-header">
            <text class="station">室内平均温度</text>
            <text class="update-time">更新时间：{{tempUpdateTime}}</text>
          </view>
          <view class="temp-grid">
            <view class="temp-item" wx:for="{{stationTemps}}" wx:key="id">
              <text class="name">{{item.name}}</text>
              <text class="temp {{item.status}}">{{item.temperature}}°C</text>
            </view>
          </view>
        </view>
    </view>
</view> 