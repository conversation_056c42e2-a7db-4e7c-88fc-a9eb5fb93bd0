package com.heating.repository;

import com.heating.entity.TAlarmRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 告警记录仓库接口
 * 用于操作 t_alarm_record 表
 */
@Repository
public interface AlarmRecordRepository extends JpaRepository<TAlarmRecord, Long> {
    
    /**
     * 根据告警状态和处理状态查询告警记录
     * 
     * @param isAlarm 告警状态 (0-正常, 1-故障)
     * @param status 处理状态 (0-未确认, 1-已确认, 2-已完成, 3-已忽略)
     * @return 符合条件的告警记录列表
     */
    List<TAlarmRecord> findByIsAlarmAndStatus(Integer isAlarm, Integer status);
} 