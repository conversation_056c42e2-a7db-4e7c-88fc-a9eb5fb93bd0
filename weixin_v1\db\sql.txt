生成数据库：tb_wx_heat

表结构如下：

小区表：
`t_useheatunit` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `Name` varchar(32) DEFAULT NULL COMMENT '小区名称',
  `UnitType` varchar(16) DEFAULT NULL,
  `UseHeatNo` varchar(16) DEFAULT NULL,
  `Addr` varchar(64) DEFAULT NULL,
  `BuiltArea` double DEFAULT NULL,
  `BuiltYear` int(11) DEFAULT NULL,
  `FloorTotal` int(11) DEFAULT NULL,
  `HouseTotal` int(11) DEFAULT NULL,
  `UseHeatPic` varchar(100) DEFAULT NULL,
  `UnitManage` varchar(64) DEFAULT NULL,
  `UnitManageContact` varchar(32) DEFAULT NULL,
  `UnitManageTel` varchar(32) DEFAULT NULL,
  `HeatMeterTotal` int(11) DEFAULT NULL,
  `UsedHeatMeterTotal` int(11) DEFAULT NULL,
  `mm` varchar(128) DEFAULT NULL,
  `HeatingIndex` varchar(255) DEFAULT NULL
)

换热站表：
`t_hes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `UseHeatUnit_name` varchar(32) DEFAULT NULL COMMENT '小区名称',
  `Name` varchar(32) DEFAULT NULL COMMENT '换热站名称',
  `hescode` int(11) DEFAULT NULL COMMENT '换热站编号',
  `usedyear` int(11) DEFAULT NULL,
  `ordercode` int(11) DEFAULT NULL,
  `equipmentNum` int(11) DEFAULT NULL,
  `H` tinyint(4) DEFAULT NULL,
  `M` tinyint(4) DEFAULT NULL,
  `L` tinyint(4) DEFAULT NULL,
  `runMode` int(1) DEFAULT '0' COMMENT '运行模式 ，=0 手动，=1 自动 =2 停运',
  `wp_f_work` tinyint(4) DEFAULT NULL,
  `wp_s_work` tinyint(4) DEFAULT NULL,
  `sf` int(11) DEFAULT NULL,
  `isUsed` tinyint(4) DEFAULT NULL COMMENT '是否启用换热站',
  `connect_opc` tinyint(4) DEFAULT NULL,
  `heatingType` int(11) DEFAULT '2',
  `floorheating_area` float DEFAULT '0',
  `floorheating_hload` float DEFAULT '0',
  `floorheating_uhload` float DEFAULT NULL,
  `radiator_area` float DEFAULT NULL,
  `radiator_hload` float DEFAULT NULL,
  `radiator_uhload` float DEFAULT NULL,
  `open_connect_alarm` tinyint(4) DEFAULT '0' COMMENT '是否打开换热站连接警告',
  `isRun` tinyint(4) DEFAULT '0' COMMENT '是否正在运行（用连接状态判断） 掉线默认为不运行',
  `open_t_alarm` tinyint(4) DEFAULT '0',
  `alarm_t1` float DEFAULT NULL,
  `alarm_t2` float DEFAULT NULL,
  `alarmt_t1_state` tinyint(4) DEFAULT NULL,
  `alarmt_t2_state` tinyint(4) DEFAULT NULL,
  `disconnect_dt` varchar(32) DEFAULT NULL,
  `talarm_dt` varchar(32) DEFAULT NULL,
  `control_mode` int(11) DEFAULT '0' COMMENT '备用',
  `mm` varchar(128) DEFAULT NULL,
  `heat_rate` double DEFAULT '1' COMMENT '热能系数 ，修正热耗用 暂时废弃（解决西门子热表问题用）',
  `calc_mode` int(11) DEFAULT '3' COMMENT '算法控制： 1.室温控制 2.流量控制 3.二次供水温度控制',
  `target_t` int(11) DEFAULT NULL COMMENT '设定的目标基础温度，用此作为基础使用偏移算出实际的某一时候温度值',
  `sst_field_index` int(11) DEFAULT '1',
  `control_ip` varchar(20) DEFAULT NULL,
  `control_port` varchar(10) DEFAULT NULL,
  `Excontrol_ip` varchar(20) DEFAULT NULL,
  `Excontrol_port` varchar(10) DEFAULT NULL,
  `design_load` float DEFAULT NULL,
  `plate_heat_area` float DEFAULT NULL,
  `plate_htc` float DEFAULT NULL,
  `design_flow` float DEFAULT '0',
  `g_rate` decimal(4,2) DEFAULT '1.00' COMMENT '流量系数 默认为1',
  `orderid` int(11) DEFAULT NULL,
  `ordername` int(11) DEFAULT NULL,
  `isHeating` int(11) DEFAULT '1',
  `heatingindex` varchar(10) DEFAULT '27'
)

数据字典类型表：
`tb_dict` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(30) NOT NULL COMMENT '字典名称',
  `code` varchar(50) NOT NULL COMMENT '字典值',
  `sort` smallint(5) unsigned NOT NULL DEFAULT '125' COMMENT '显示顺序',
  `note` varchar(255) DEFAULT NULL COMMENT '字典备注',
  `create_user` int(10) unsigned DEFAULT '0' COMMENT '添加人',
  `create_time` datetime DEFAULT NULL COMMENT '添加时间',
  `update_user` int(10) unsigned DEFAULT '0' COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `mark` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '有效标识',
  PRIMARY KEY (`id`),
  KEY `name` (`name`) USING BTREE
)

数据字典子表：
`tb_dict_data` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(50) NOT NULL COMMENT '字典项名称',
  `code` varchar(50) NOT NULL COMMENT '字典项值',
  `dict_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '字典类型ID',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1在用 2停用',
  `note` varchar(300) DEFAULT NULL COMMENT '备注',
  `sort` smallint(5) unsigned NOT NULL DEFAULT '125' COMMENT '显示顺序',
  `create_user` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '添加人',
  `create_time` datetime DEFAULT NULL COMMENT '添加时间',
  `update_user` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `mark` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '有效标记'
)

角色表：
`tb_role` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(30) NOT NULL COMMENT '角色名称',
  `code` varchar(100) CHARACTER SET utf8 NOT NULL COMMENT '角色标签',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1正常 2禁用',
  `note` varchar(255) DEFAULT NULL COMMENT '备注',
  `sort` smallint(5) unsigned NOT NULL DEFAULT '125' COMMENT '排序',
  `create_user` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '添加人',
  `create_time` datetime DEFAULT NULL COMMENT '添加时间',
  `update_user` int(10) unsigned DEFAULT '0' COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `mark` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '有效标识(1正常 0删除)'
)

用户表：
`t_userapp` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) DEFAULT NULL COMMENT '用户名',
  `pwd` varchar(50) DEFAULT NULL COMMENT '密码',
  `phone` varchar(12) DEFAULT NULL COMMENT '手机号',
  `nlevel` int(11) DEFAULT NULL,
  `role_id` varchar(50) DEFAULT NULL COMMENT '角色id',
  `dt` varchar(25) DEFAULT NULL COMMENT '添加时间',
  `headpic` varchar(50) DEFAULT NULL COMMENT '头像',
  `userwhite_id` int(11) DEFAULT NULL COMMENT '白名单ID',
  `lon` varchar(30) DEFAULT NULL,
  `lat` varchar(30) DEFAULT NULL
)