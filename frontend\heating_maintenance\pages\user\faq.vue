<template>
	<view class="faq-container">
	
		
		<!-- 搜索框 -->
		<view class="search-box">
			<view class="search-input">
				<text class="search-icon iconfont icon-search"></text>
				<input type="text" v-model="searchKeyword" placeholder="搜索问题" @input="searchFaq" />
				<text class="clear-icon iconfont icon-close" v-if="searchKeyword" @click="clearSearch"></text>
			</view>
		</view>
		
		<!-- 内容区域 -->
		<view class="faq-content">
			<view class="no-result" v-if="filteredFaqs.length === 0 && searchKeyword">
				<image src="/static/images/empty.png" mode="aspectFit"></image>
				<text>暂无相关问题</text>
			</view>
			
			<!-- 搜索模式：直接显示结果列表 -->
			<block v-if="searchKeyword">
				<view class="faq-item" v-for="(item, index) in filteredFaqs" :key="index">
					<view class="faq-question" @click="toggleSearchAnswer(index)">
						<text class="question-marker">Q:</text>
						<text class="question-text">{{ item.question }}</text>
						<text class="question-arrow" :class="{ 'arrow-up': item.isOpen }">></text>
					</view>
					<view class="faq-answer" v-if="item.isOpen">
						<text class="answer-marker">A:</text>
						<text class="answer-text">{{ item.answer }}</text>
					</view>
				</view>
			</block>
			
			<!-- 非搜索模式：按分组显示所有问题 -->
			<block v-else>
				<view class="faq-section" v-for="(group, groupIndex) in allFaqs" :key="groupIndex">
					<view class="group-title">
						<text class="group-title-text">{{ group.groupTitle }}</text>
					</view>
					
					<view class="faq-item" v-for="(item, itemIndex) in group.items" :key="itemIndex">
						<view class="faq-question" @click="toggleAnswer(groupIndex, itemIndex)">
							<text class="question-marker">Q:</text>
							<text class="question-text">{{ item.question }}</text>
							<text class="question-arrow" :class="{ 'arrow-up': item.isOpen }">></text>
						</view>
						<view class="faq-answer" v-if="item.isOpen">
							<text class="answer-marker">A:</text>
							<text class="answer-text">{{ item.answer }}</text>
						</view>
					</view>
				</view>
			</block>
		</view>
		
		<!-- 底部联系客服区域 -->
		<view class="feedback-section">
			<text class="feedback-text">没有找到您需要的答案？</text>
			<view class="feedback-btn" @click="contactSupport">联系客服</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			searchKeyword: '',
			faqs: [
				{
					categoryId: 'account',
					groupTitle: '账号和登录',
					items: [
						{
							question: '如何修改密码？',
							answer: '您可以在个人中心 -> 安全设置 -> 密码修改中修改您的账号密码。系统会要求您输入当前密码和新密码进行验证。',
							isOpen: false
						},
						{
							question: '忘记密码怎么办？',
							answer: '如果您忘记了登录密码，请点击登录页面的"忘记密码"按钮，按照提示使用手机号码接收验证码进行密码重置。如果您没有绑定手机号，请联系系统管理员重置密码。',
							isOpen: false
						},
						{
							question: '如何更换手机号？',
							answer: '您可以在个人中心 -> 个人设置 -> 账号绑定中更换您的手机号。系统会向新手机号发送验证码进行验证。',
							isOpen: false
						}
					]
				},
				{
					categoryId: 'workorder',
					groupTitle: '工单处理',
					items: [
						{
							question: '如何接受工单？',
							answer: '工单列表中显示的"待接单"工单，点击进入工单详情后，点击底部的"接单"按钮即可接受工单。接受后，工单状态会变为"已接单"。',
							isOpen: false
						},
						{
							question: '如何完成工单？',
							answer: '在工单详情页面，点击"完成工单"按钮，填写维修内容、维修结果、使用的耗材及数量，选择维修时间并上传相关附件，点击提交即可完成工单。',
							isOpen: false
						},
						{
							question: '如何转派工单？',
							answer: '在工单详情页面，点击"转派工单"按钮，选择要转派的人员，填写转派原因，点击确认即可转派工单。',
							isOpen: false
						},
						{
							question: '如何查看历史工单？',
							answer: '在工单列表页面，可以通过顶部的状态筛选查看不同状态的工单。选择"已完成"标签即可查看历史完成的工单。',
							isOpen: false
						}
					]
				},
				{
					categoryId: 'patrol',
					groupTitle: '巡检管理',
					items: [
						{
							question: '如何开始巡检任务？',
							answer: '在巡检计划列表中，点击"开始巡检"按钮，系统会生成巡检任务单，按照巡检点顺序进行巡检。',
							isOpen: false
						},
						{
							question: '巡检过程中如何报告问题？',
							answer: '在巡检执行过程中，如发现问题，可以点击巡检项旁的"报告问题"按钮，填写问题描述并上传照片，系统会自动生成故障工单。',
							isOpen: false
						},
						{
							question: '如何查看历史巡检记录？',
							answer: '在巡检记录页面，可以查看所有已完成的巡检任务记录，包括巡检时间、巡检人员、巡检路线等信息。',
							isOpen: false
						}
					]
				},
				{
					categoryId: 'system',
					groupTitle: '系统使用',
					items: [
						{
							question: '如何更新APP？',
							answer: '当有新版本时，系统会自动提示更新。您也可以在"我的" -> "关于系统"中检查更新，如有新版本会显示"立即更新"按钮。',
							isOpen: false
						},
						{
							question: '系统支持哪些附件格式？',
							answer: '系统支持上传图片(JPG, PNG, GIF)和视频(MP4, MOV)格式的附件，单个附件大小不超过20MB。',
							isOpen: false
						},
						{
							question: '如何清理缓存？',
							answer: '在"我的" -> "设置" -> "清除缓存"中可以清理应用缓存数据，释放手机存储空间。注意：清除缓存不会删除您的账号信息和历史数据。',
							isOpen: false
						}
					]
				}
			],
			// 搜索结果的展开状态
			searchResults: []
		}
	},
	computed: {
		// 所有FAQ分组
		allFaqs() {
			return this.faqs;
		},
		
		// 搜索结果
		filteredFaqs() {
			if (this.searchKeyword) {
				// 搜索模式：搜索所有分类
				const keyword = this.searchKeyword.toLowerCase();
				const result = [];
				
				this.faqs.forEach(group => {
					group.items.forEach(item => {
						if (item.question.toLowerCase().includes(keyword) || 
							item.answer.toLowerCase().includes(keyword)) {
							// 创建一个新对象，避免修改原始数据
							result.push({
								...item,
								isOpen: false // 默认不展开
							});
						}
					});
				});
				
				// 更新搜索结果的展开状态
				this.searchResults = result;
				return result;
			}
			return [];
		}
	},
	methods: {
		// 切换问题答案展示（非搜索模式）
		toggleAnswer(groupIndex, itemIndex) {
			this.faqs[groupIndex].items[itemIndex].isOpen = !this.faqs[groupIndex].items[itemIndex].isOpen;
		},
		
		// 切换搜索结果中的问题答案展示
		toggleSearchAnswer(index) {
			if (this.searchKeyword && this.filteredFaqs[index]) {
				this.filteredFaqs[index].isOpen = !this.filteredFaqs[index].isOpen;
			}
		},
		
		// 重置所有问题的展开状态
		resetOpenState() {
			this.faqs.forEach(group => {
				group.items.forEach(item => {
					item.isOpen = false;
				});
			});
			// 同时重置搜索结果的展开状态
			if (this.searchResults) {
				this.searchResults.forEach(item => {
					item.isOpen = false;
				});
			}
		},
		
		// 搜索FAQ
		searchFaq() {
			// 重置展开状态
			this.resetOpenState();
		},
		
		// 清除搜索
		clearSearch() {
			this.searchKeyword = '';
			this.resetOpenState();
		},
		
		// 联系客服
		contactSupport() {
			uni.showModal({
				title: '联系客服',
				content: '客服热线：029-85396651\n工作时间：周一至周五 9:00-18:00',
				showCancel: false,
				confirmText: '我知道了'
			});
		}
	}
}
</script>

<style lang="scss">
.faq-container {
	background-color: #f5f7fa;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
}

.search-box {
	padding: 20rpx 30rpx;
	background-color: #fff;
	position: sticky;
	top: 0;
	z-index: 100;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	
	.search-input {
		position: relative;
		height: 80rpx;
		background-color: #f5f7fa;
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		padding: 0 30rpx;
		
		.search-icon {
			font-size: 32rpx;
			color: #999;
			margin-right: 10rpx;
		}
		
		input {
			flex: 1;
			height: 80rpx;
			font-size: 28rpx;
		}
		
		.clear-icon {
			font-size: 28rpx;
			color: #999;
			padding: 10rpx;
		}
	}
}

.faq-content {
	flex: 1;
	padding: 20rpx 0;
	
	.faq-section {
		margin-bottom: 20rpx;
		
		.group-title {
			position: relative;
			padding: 20rpx 30rpx;
			background-color: #f0f5ff;
			border-left: 8rpx solid #1890ff;
			
			.group-title-text {
				font-size: 30rpx;
				font-weight: bold;
				color: #333;
			}
		}
	}
	
	.no-result {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding-top: 100rpx;
		
		image {
			width: 200rpx;
			height: 200rpx;
			margin-bottom: 20rpx;
		}
		
		text {
			font-size: 28rpx;
			color: #999;
		}
	}
}

.faq-item {
	background-color: #fff;
	margin-bottom: 2rpx;
	
	.faq-question {
		padding: 30rpx;
		display: flex;
		align-items: flex-start;
		position: relative;
		
		.question-marker {
			color: $uni-color-primary;
			font-weight: bold;
			font-size: 30rpx;
			margin-right: 20rpx;
			flex-shrink: 0;
		}
		
		.question-text {
			flex: 1;
			font-size: 28rpx;
			color: #333;
			line-height: 1.6;
		}
		
		.question-arrow {
			font-size: 24rpx;
			color: #999;
			margin-left: 20rpx;
			transition: transform 0.3s;
			transform: rotate(90deg);
			
			&.arrow-up {
				transform: rotate(-90deg);
			}
		}
	}
	
	.faq-answer {
		padding: 0 30rpx 30rpx;
		display: flex;
		align-items: flex-start;
		background-color: #f9f9f9;
		
		.answer-marker {
			color: #f5222d;
			font-weight: bold;
			font-size: 30rpx;
			margin-right: 20rpx;
			flex-shrink: 0;
		}
		
		.answer-text {
			flex: 1;
			font-size: 28rpx;
			color: #666;
			line-height: 1.8;
		}
	}
}

.feedback-section {
	padding: 30rpx;
	background-color: #fff;
	border-top: 1rpx solid #eee;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
	width: 100%;
	box-sizing: border-box;
	margin-top: 20rpx;
	
	.feedback-text {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 20rpx;
	}
	
	.feedback-btn {
		padding: 15rpx 60rpx;
		background-color: $uni-color-primary;
		color: #fff;
		font-size: 28rpx;
		border-radius: 40rpx;
		
		&:active {
			opacity: 0.8;
		}
	}
}
</style> 