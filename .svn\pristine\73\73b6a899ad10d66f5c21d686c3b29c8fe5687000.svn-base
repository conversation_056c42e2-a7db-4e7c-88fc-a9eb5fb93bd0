package com.heating.controller;

import com.heating.dto.*;
import com.heating.dto.user.LoginRequest;
import com.heating.dto.user.RegisterRequest;
import com.heating.dto.user.UserInfoUpdateRequest;
import com.heating.dto.user.UserListRequest;
import com.heating.dto.user.UserListResponse;
import com.heating.dto.user.ChangePasswordRequest;
import com.heating.service.AuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import lombok.extern.slf4j.Slf4j;
import javax.validation.Valid;
import java.util.List;
import java.util.Map; 

@RestController
@RequestMapping("/api/auth")
@Slf4j
public class AuthController {

    @Autowired
    private AuthService authService;

    @PostMapping("/login")
    public ResponseEntity<Map<String, Object>> login(@Valid @RequestBody LoginRequest request) {
        if (request.username() == null || request.username().trim().isEmpty()) {
            return ResponseEntity.ok(Map.of(
                    "code", 400 ,
                    "message", "账号不能为空"
            ));
        }
        try {
            // 根据登录类型选择不同的登录方法
            Map<String, Object> result;
            if ("phone".equals(request.loginType())) {
                // 手机号登录
                result = authService.phoneLogin(request);
            } else {
                // 默认用户名登录
                result = authService.userLogin(request);
            }
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("登录失败", e);
            return ResponseEntity.ok(Map.of(
                    "code", 400 ,
                    "message", "登录失败: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/user-info")
    public ResponseEntity<Map<String, Object>> getUserInfo(@RequestAttribute Long userId) {
        try {
            Map<String, Object> result = authService.getUserInfo(userId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.ok(Map.of(
                    "code", 400 ,
                    "message", "获取用户信息失败"
            ));
        }
    }

    @PostMapping("/user-modify")
    public ResponseEntity<Map<String, Object>> updateUserInfo(
            @RequestAttribute Long userId,
            @Valid @RequestBody UserInfoUpdateRequest request) {
        try {
            boolean result = authService.updateUserInfo(userId, request);

            if(result){ 
                return ResponseEntity.ok(Map.of(
                        "code", 200 ,
                        "message", "用户信息更新成功"
                ));
            }else{
                return ResponseEntity.ok(Map.of(
                        "code", 400 ,
                        "message", "用户信息更新失败"
                ));
            }
        } catch (Exception e) {
            log.error("Update user info failed", e);
            return ResponseEntity.ok(Map.of(
                    "code", 400 ,
                    "message", "更新用户信息失败"
            ));
        }
    }

    // 用户注册
    @PostMapping("/register")
    public ResponseEntity<Map<String, Object>> register(@RequestBody RegisterRequest request) {
        try {
                boolean result = authService.userRegister(request);
                if(result){
                    return ResponseEntity.ok(Map.of(
                            "code", 200 ,
                            "message", "注册成功，请等待管理员审核"
                    ));
                }else{
                    return ResponseEntity.ok(Map.of(
                            "code", 400 ,
                            "message", "注册失败"
                    ));
                }
        } catch (Exception e) {
            log.error("Registration failed", e);
            return ResponseEntity.ok(Map.of(
                    "code", 400 ,
                    "message", "注册失败"
            ));
        }   
    }   

    /**
     * 获取用户列表
     * @param request 包含角色筛选条件的请求对象
     * @return 用户列表响应
     */
    @PostMapping("/user/list")
    public ResponseEntity<Map<String, Object>> getUserList(@Valid @RequestBody(required = false) UserListRequest request) {
        try {
            List<UserListResponse> userList = authService.getUserList(request);
            return ResponseEntity.ok(Map.of(
                    "code", 200,
                    "message", "获取用户列表成功",
                    "data", userList
            ));
        } catch (Exception e) {
            log.error("Failed to get user list", e);
            return ResponseEntity.ok(Map.of(
                    "code", 400,
                    "message", "获取用户列表失败"
            ));
        }
    }
    
    /**
     * 修改密码
     * @param userId 用户ID
     * @param request 包含旧密码和新密码的请求对象
     * @return 修改结果
     */
    @PostMapping("/change-password")
    public ResponseEntity<Map<String, Object>> changePassword(
            @RequestAttribute Long userId,
            @Valid @RequestBody ChangePasswordRequest request) {
        try {
            boolean result = authService.changePassword(userId, request.oldPassword(), request.newPassword());
            
            if (result) {
                return ResponseEntity.ok(Map.of(
                        "code", 200,
                        "message", "密码修改成功"
                ));
            } else {
                return ResponseEntity.ok(Map.of(
                        "code", 400,
                        "message", "密码修改失败，请检查当前密码是否正确"
                ));
            }
        } catch (Exception e) {
            log.error("Change password failed", e);
            return ResponseEntity.ok(Map.of(
                    "code", 400,
                    "message", "密码修改失败: " + e.getMessage()
            ));
        }
    }
} 