-- --------------------------------------------------------
-- 主机:                           43.139.65.175
-- 服务器版本:                        8.0.39-0ubuntu0.24.04.2 - (Ubuntu)
-- 服务器操作系统:                      Linux
-- HeidiSQL 版本:                  11.2.0.6213
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;


-- 导出 tb_gongre 的数据库结构
CREATE DATABASE IF NOT EXISTS `tb_gongre` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci */ /*!80016 DEFAULT ENCRYPTION='N' */;
USE `tb_gongre`;

-- 导出  存储过程 tb_gongre.InitializeStatistics 结构
DELIMITER //
CREATE PROCEDURE `InitializeStatistics`(
	IN `unitNo` VARCHAR(50),
	IN `reportDate` DATE
)
BEGIN
    DECLARE valves_num, indoort_num, heatmater_num INT;
    
    -- 获取采集周期并计算应上报次数
    SELECT 
        FLOOR(1440 / collect_cycle),
        FLOOR(1440 / indoor_cycle),
        FLOOR(1440 / heatmater_cycle)
    INTO 
        valves_num, indoort_num, heatmater_num
    FROM tb_other_param;

    -- 创建统计表名
    SET @tableName = CONCAT('t_house_statistics_', unitNo);

    -- 如果表不存在，则创建表
    SET @createTableSQL = CONCAT(
        'CREATE TABLE IF NOT EXISTS ', @tableName, ' (
            id INT AUTO_INCREMENT PRIMARY KEY,
            house_id INT,
            floor_id INT,
            floor_unit_id INT,
            room_no  VARCHAR(50),
            dt DATE,
            valves_no VARCHAR(50),
            indoor_t_no VARCHAR(50),
            heat_meter_no VARCHAR(50),
            valves_num INT DEFAULT 0,
            indoort_num INT DEFAULT 0,
            heatmater_num INT DEFAULT 0,
            valves_actuality_num INT DEFAULT 0,
            heatmater_actuality_num INT DEFAULT 0,
            indoort_actuality_num INT DEFAULT 0,
            UNIQUE KEY unique_house_dt (house_id, dt) -- 确保每户每天的数据唯一性
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci'
    );
    
    PREPARE stmt FROM @createTableSQL;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;

    -- 插入默认记录到统计表中，并设置预期上报次数
    SET @insertDefaultRecordsSQL = CONCAT(
        'INSERT INTO ', @tableName, ' (house_id,floor_id,floor_unit_id, room_no,dt, valves_no, indoor_t_no, heat_meter_no, valves_num, indoort_num, heatmater_num)
         SELECT h.id, h.heat_unit_floor_id,h.heat_unit_floor_unit_id,h.room_no,''', reportDate, ''', h.valves_no, h.indoor_t_no, h.heat_meter_no, ', valves_num, ', ', indoort_num, ', ', heatmater_num, '
         FROM t_house h
         JOIN t_heat_unit u ON h.heat_unit_id = u.id
         WHERE u.unit_no = ''', unitNo, ''' AND u.is_used = 1
         ON DUPLICATE KEY UPDATE 
             valves_num = VALUES(valves_num),
             indoort_num = VALUES(indoort_num),
             heatmater_num = VALUES(heatmater_num)'
    );
    
    PREPARE stmt FROM @insertDefaultRecordsSQL;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END//
DELIMITER ;

-- 导出  表 tb_gongre.t_alarm_config 结构
CREATE TABLE IF NOT EXISTS `t_alarm_config` (
  `id` bigint NOT NULL DEFAULT '0',
  `hes_unit_id` int DEFAULT NULL COMMENT '项目小区ID',
  `rule_type` int DEFAULT NULL COMMENT ' 1-换热站  2-锅炉  3-小区住户',
  `hescode` int DEFAULT NULL,
  `hesname` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `dev_type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '设备类型',
  `dev_id` int DEFAULT NULL COMMENT '设备ID（关联设备管理）',
  `run_rules_ids` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '运行规则ID',
  `alarm_rules_ids` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '告警规则IDs',
  `is_used` int DEFAULT '1' COMMENT '是否启用',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='告警配置表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_alarm_record 结构
CREATE TABLE IF NOT EXISTS `t_alarm_record` (
  `id` bigint NOT NULL DEFAULT '0',
  `heat_unit_id` bigint DEFAULT NULL COMMENT '热用户ID',
  `device_parent` varchar(50) DEFAULT NULL COMMENT '设备父节点： 换热站/住户',
  `sub_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '设备父节点id: 如 换热站id/住户id',
  `dev_type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '设备类型',
  `dev_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '室内设备编号(阀门、热表、面板编号)',
  `alarm_desc` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '告警描述',
  `alarm_field` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '告警字段',
  `alarm_value` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '告警值',
  `is_alarm` int DEFAULT '1' COMMENT '0-''正常'', 1-''故障''',
  `is_handle` int DEFAULT '0' COMMENT '0-''未处理'' 1''处理中'', 2-''已处理''',
  `status` int DEFAULT '0' COMMENT '0-未确认  1-已确认 2-已完成 3-已忽略',
  `rule_id` int DEFAULT NULL COMMENT '告警规则id',
  `alarm_type` int DEFAULT NULL COMMENT '1-''业务报警'', 2-''设备报警',
  `alarm_level` int DEFAULT '3' COMMENT '告警等级 1-严重告警 2-重要告警  3-一般告警 4-提示告警',
  `alarm_duration` int DEFAULT '0' COMMENT '告警时长',
  `alarm_dt` datetime DEFAULT NULL COMMENT '告警时间',
  `alarm_clear_dt` datetime DEFAULT NULL COMMENT '告警清除时间',
  `unit` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '单位',
  `remark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='告警记录表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_alarm_rules 结构
CREATE TABLE IF NOT EXISTS `t_alarm_rules` (
  `id` bigint NOT NULL DEFAULT '0',
  `name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `rule_type` varchar(50) DEFAULT NULL COMMENT '换热站 /小区住户',
  `dev_type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '设备类型(来源于字典)',
  `mpfield` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `field_alisa` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '测点字段',
  `alarm_cond` varchar(512) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '告警条件',
  `out_field` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '输出值',
  `alarm_type` int DEFAULT '1' COMMENT '告警类型  1-业务  2-设备',
  `alarm_level` int DEFAULT '3' COMMENT '报警等级   1-严重告警  2-重要告警  3-一般告警 4- 提示告警',
  `alarm_desc` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '告警描述',
  `is_create_tickets` int DEFAULT '0' COMMENT '是否下发工单',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='告警规则表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_all_hes_data 结构
CREATE TABLE IF NOT EXISTS `t_all_hes_data` (
  `id` bigint NOT NULL DEFAULT '0',
  `collect_dt` datetime DEFAULT NULL,
  `hescode` int DEFAULT NULL,
  `num` int DEFAULT NULL,
  `hesname` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `heat_type` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `control_mode` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `fw` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `fsf` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `fre1` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `fre2` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `h_valve` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `l_valve` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `fsp` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `fbp` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `fst` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `fbt` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `h_fre1` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `h_fre2` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `h_ssp` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `h_sbp` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `h_sst` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `h_sbt` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `l_fre1` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `l_fre2` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `l_ssp` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `l_sbp` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `l_sst` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `l_sbt` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `warning` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `mid_sst` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `mid_sbt` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `four_sst` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `four_sbt` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_approval_record 结构
CREATE TABLE IF NOT EXISTS `t_approval_record` (
  `id` bigint NOT NULL DEFAULT '0',
  `housepay_id` int DEFAULT NULL COMMENT '缴费记录ID',
  `heat_unit_id` int DEFAULT NULL,
  `heat_unit_floor_id` int DEFAULT NULL,
  `heat_unit_floor_unit_id` int DEFAULT NULL,
  `house_id` int(10) unsigned zerofill NOT NULL,
  `room_no` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `heat_year` int DEFAULT NULL,
  `approval_status` int NOT NULL DEFAULT '0' COMMENT '0-未审批 1-审批通过 2-审批未通过',
  `approval_date` datetime DEFAULT NULL,
  `approver` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `create_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_housepay` (`housepay_id`) USING BTREE,
  CONSTRAINT `t_approval_record_ibfk_1` FOREIGN KEY (`housepay_id`) REFERENCES `t_house_pay_info` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='缴费审批记录';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_attendance_record 结构
CREATE TABLE IF NOT EXISTS `t_attendance_record` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `clock_type` varchar(20) NOT NULL COMMENT '打卡类型:checkin-上班,checkout-下班',
  `clock_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '打卡时间',
  `latitude` double NOT NULL DEFAULT '0',
  `longitude` double NOT NULL DEFAULT '0',
  `face_photo` json NOT NULL COMMENT '人脸照片(base64数组)',
  `liveness_data` json DEFAULT NULL COMMENT '活体检测数据',
  `status` varchar(20) NOT NULL DEFAULT 'normal' COMMENT '状态:normal-正常,late-迟到,early-早退',
  `leave_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '请假类型:sick,personal,business',
  `leave_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '请假原因',
  `leave_proof` json DEFAULT NULL COMMENT '请假证明材料',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=43 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='考勤记录表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_attendance_rules 结构
CREATE TABLE IF NOT EXISTS `t_attendance_rules` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `allowed_distance` int NOT NULL COMMENT '允许的打卡距离(米)',
  `clock_in_time` time(6) NOT NULL COMMENT '上班打卡时间',
  `clock_out_time` time(6) NOT NULL COMMENT '下班打卡时间',
  `early_leave_threshold` int NOT NULL COMMENT '迟到阈值(分钟)',
  `effective_date` time(6) DEFAULT NULL COMMENT '生效日期',
  `is_active` bit(1) NOT NULL COMMENT '是否激活',
  `late_threshold` int NOT NULL COMMENT '迟到阈值(分钟)',
  `latitude` double DEFAULT NULL,
  `longitude` double DEFAULT NULL,
  `location_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '考勤地点名称',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='考勤规则';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_attendance_supplement 结构
CREATE TABLE IF NOT EXISTS `t_attendance_supplement` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `approved_by` bigint DEFAULT NULL COMMENT '审批人ID',
  `approved_time` datetime(6) DEFAULT NULL COMMENT '审批时间',
  `create_time` datetime(6) NOT NULL COMMENT '创建时间',
  `date` date NOT NULL COMMENT '申请补卡日期',
  `images` json DEFAULT NULL COMMENT '证明材料图片',
  `reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '补卡原因',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '审批备注',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '状态：pending/approved/rejected',
  `type` int NOT NULL COMMENT '1-上班打卡, 2-下班打卡',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='考勤补卡申请';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_boiler_param 结构
CREATE TABLE IF NOT EXISTS `t_boiler_param` (
  `id` bigint NOT NULL DEFAULT '0',
  `dt` datetime DEFAULT NULL,
  `min_g` decimal(18,3) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_camera 结构
CREATE TABLE IF NOT EXISTS `t_camera` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `heat_unit_id` bigint NOT NULL COMMENT '热用户ID',
  `hes_id` bigint DEFAULT NULL COMMENT '换热站ID',
  `camera_no` varchar(50) NOT NULL COMMENT '摄像头编号',
  `name` varchar(100) NOT NULL COMMENT '摄像头名称',
  `location` varchar(200) NOT NULL COMMENT '安装位置',
  `room_url` varchar(200) NOT NULL COMMENT '房间地址',
  `rtsp_url` varchar(200) NOT NULL COMMENT '视频流地址',
  `image_url` varchar(200) NOT NULL COMMENT '背景图',
  `status` varchar(20) NOT NULL DEFAULT 'online' COMMENT '状态:online-在线,offline-离线',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_center_calcrst 结构
CREATE TABLE IF NOT EXISTS `t_center_calcrst` (
  `id` bigint NOT NULL DEFAULT '0',
  `dt` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `hescode` int DEFAULT NULL,
  `G` decimal(18,3) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_complaints 结构
CREATE TABLE IF NOT EXISTS `t_complaints` (
  `id` bigint NOT NULL DEFAULT '0',
  `heat_unit_id` int NOT NULL DEFAULT '0' COMMENT '项目小区',
  `complainant_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '投诉人',
  `complainant_contact` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '投诉人的联系方式',
  `channel` int DEFAULT '1' COMMENT '渠道  1-''APP提交''，2-''电话'',',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '内容',
  `complainan_address` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `complaint_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '''温度不达标'',''漏水'', ''设备故障'', ''服务态度'', ''其他''',
  `complaint_date` datetime NOT NULL COMMENT '投诉时间',
  `status` int NOT NULL DEFAULT '0' COMMENT '0-待处理 1-''处理中'',2-''已解决''',
  `handler_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '处理人员',
  `resolution_method` int DEFAULT '0' COMMENT '解决方式 1-工单、2-客服回复 3-自动解决',
  `resolution` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '结果',
  `resolution_count` int DEFAULT NULL COMMENT '处理次数',
  `resolution_date` datetime DEFAULT NULL COMMENT '处理结束时间',
  `mm` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='投诉记录表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_complaint_actions 结构
CREATE TABLE IF NOT EXISTS `t_complaint_actions` (
  `id` bigint NOT NULL DEFAULT '0',
  `complaint_id` int DEFAULT NULL COMMENT '投诉ID',
  `action_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '处理描述',
  `action_dt` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='投诉处理记录表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_data_log 结构
CREATE TABLE IF NOT EXISTS `t_data_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `status` varchar(20) DEFAULT NULL,
  `data` text,
  `dt` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=615809 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_data_report_cycle 结构
CREATE TABLE IF NOT EXISTS `t_data_report_cycle` (
  `id` bigint NOT NULL DEFAULT '0' COMMENT '配置ID',
  `house_id` int NOT NULL COMMENT '住户ID',
  `room_no` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '住户房号',
  `heat_unit_id` int NOT NULL COMMENT '小区ID',
  `building_id` int NOT NULL COMMENT '楼宇ID',
  `unit_id` int NOT NULL COMMENT '单元ID',
  `device_type` tinyint(1) NOT NULL COMMENT '设备类型：1-阀门，2-室内面板，3-热表',
  `device_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '设备编号',
  `report_cycle` int NOT NULL DEFAULT '10' COMMENT '上报周期(分钟)',
  `heating_report_cycle` int NOT NULL DEFAULT '10' COMMENT '供暖上报周期(分钟)',
  `timeout_threshold` int NOT NULL DEFAULT '30' COMMENT '超时阈值(分钟)',
  `is_send` int(1) unsigned zerofill DEFAULT '0' COMMENT '是否发送工单',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_house_device` (`house_id`,`device_type`) USING BTREE,
  KEY `idx_device` (`device_type`,`device_code`) USING BTREE,
  KEY `idx_building` (`building_id`) USING BTREE,
  KEY `idx_unit` (`unit_id`) USING BTREE,
  KEY `idx_heatunit` (`heat_unit_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='数据上报周期配置表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_dept 结构
CREATE TABLE IF NOT EXISTS `t_dept` (
  `id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '主键ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '部门名称',
  `pid` int unsigned NOT NULL DEFAULT '0' COMMENT '上级ID',
  `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '部门编号',
  `fullname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '部门全称',
  `type` tinyint unsigned DEFAULT '0' COMMENT '部门类型：1公司 2子公司 3部门 4小组',
  `sort` smallint unsigned NOT NULL DEFAULT '125' COMMENT '排序',
  `note` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注说明',
  `create_user` int unsigned DEFAULT '0' COMMENT '添加人',
  `create_time` datetime DEFAULT NULL COMMENT '添加时间',
  `update_user` int unsigned DEFAULT '0' COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `mark` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '有效标识',
  PRIMARY KEY (`id`),
  KEY `index_pid` (`pid`) USING BTREE,
  KEY `index_name` (`name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='部门表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_device 结构
CREATE TABLE IF NOT EXISTS `t_device` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `no` varchar(50) DEFAULT NULL COMMENT '设备编号',
  `heat_unit_id` bigint DEFAULT NULL COMMENT '所属热用户ID',
  `device_parent` varchar(50) DEFAULT NULL COMMENT '设备父节点： 换热站/住户',
  `sub_id` bigint DEFAULT NULL COMMENT '设备父节点id: 如 换热站id/住户id',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '设备名称',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '设备类型(pump-水泵,valve-阀门等)',
  `model` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '设备型号',
  `status` enum('online','offline','fault') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'online' COMMENT '设备状态:online-在线,offline-离线,fault-故障',
  `building` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '所在建筑',
  `floor` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '所在楼层',
  `room` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '所在房间',
  `latitude` double DEFAULT NULL,
  `longitude` double DEFAULT NULL,
  `last_maintenance` datetime DEFAULT NULL COMMENT '上次维护时间',
  `next_maintenance` datetime DEFAULT NULL COMMENT '下次维护时间',
  `manufacturer` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '生产厂家',
  `period` int DEFAULT NULL COMMENT '维护周期',
  `alarm_count` int DEFAULT '0' COMMENT '告警次数',
  `staff` json DEFAULT NULL COMMENT '设备运维人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备信息表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_device_alarm 结构
CREATE TABLE IF NOT EXISTS `t_device_alarm` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `device_id` varchar(50) NOT NULL,
  `alarm_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '告警类型',
  `alarm_level` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '告警级别:info,warning,error',
  `alarm_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '告警内容',
  `param_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '告警参数名',
  `param_value` decimal(38,2) DEFAULT NULL,
  `threshold` decimal(10,2) DEFAULT NULL COMMENT '阈值',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'active' COMMENT '状态:active-活动,resolved-已解决',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `auto_plan` json DEFAULT NULL COMMENT '自动预案配置',
  `plan_execution_log` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '预案执行记录',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备告警表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_device_installation 结构
CREATE TABLE IF NOT EXISTS `t_device_installation` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `device_id` varchar(50) NOT NULL,
  `installer_id` bigint NOT NULL COMMENT '安装人员ID',
  `install_date` date NOT NULL COMMENT '安装日期',
  `location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '安装位置',
  `photos` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '安装现场照片(JSON数组)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备安装记录表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_device_maintenance 结构
CREATE TABLE IF NOT EXISTS `t_device_maintenance` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `device_id` bigint NOT NULL DEFAULT '0' COMMENT '设备ID',
  `maintenance_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '维护类型:routine-常规保养,repair-维修',
  `executor_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '维护内容',
  `parts_replaced` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '更换配件',
  `cost` decimal(10,2) DEFAULT NULL COMMENT '维护成本',
  `images` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '相关图片(JSON数组)',
  `status` enum('pending','processing','completed','cancelled') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `executor_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备维护记录表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_device_parameter 结构
CREATE TABLE IF NOT EXISTS `t_device_parameter` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `device_id` varchar(50) NOT NULL,
  `param_name` varchar(50) NOT NULL COMMENT '参数名称',
  `param_value` decimal(38,2) NOT NULL,
  `unit` varchar(20) DEFAULT NULL COMMENT '单位',
  `normal_min` decimal(38,2) DEFAULT NULL,
  `normal_max` decimal(38,2) DEFAULT NULL,
  `record_time` datetime NOT NULL COMMENT '记录时间',
  `data_quality` varchar(20) DEFAULT 'normal' COMMENT '数据质量:normal-正常,suspect-可疑,invalid-无效',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备参数记录表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_device_patrol_item 结构
CREATE TABLE IF NOT EXISTS `t_device_patrol_item` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `device_id` bigint DEFAULT NULL COMMENT '设备ID',
  `patrol_item_dict_id` bigint DEFAULT NULL COMMENT '巡检字典项目ID',
  `normal_range` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '巡检正常范围',
  PRIMARY KEY (`id`),
  KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备巡检项表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_device_realtime_data 结构
CREATE TABLE IF NOT EXISTS `t_device_realtime_data` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `device_id` bigint NOT NULL DEFAULT '0' COMMENT '设备ID',
  `temperature` decimal(10,2) DEFAULT NULL,
  `pressure` decimal(10,2) DEFAULT NULL,
  `flow` decimal(10,2) DEFAULT NULL,
  `collect_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '采集时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备实时数据表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_dict 结构
CREATE TABLE IF NOT EXISTS `t_dict` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典名称',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典值',
  `sort` smallint unsigned NOT NULL DEFAULT '125' COMMENT '显示顺序',
  `note` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '字典备注',
  `create_user` int unsigned DEFAULT '0' COMMENT '添加人',
  `create_time` datetime DEFAULT NULL COMMENT '添加时间',
  `update_user` int unsigned DEFAULT '0' COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `mark` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '有效标识',
  PRIMARY KEY (`id`),
  KEY `name` (`name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='字典类型表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_dict_data 结构
CREATE TABLE IF NOT EXISTS `t_dict_data` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典项名称',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典项值',
  `dict_id` int unsigned NOT NULL DEFAULT '0' COMMENT '字典类型ID',
  `status` int DEFAULT NULL,
  `note` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `sort` int DEFAULT NULL,
  `create_user` int unsigned NOT NULL DEFAULT '0' COMMENT '添加人',
  `create_time` datetime DEFAULT NULL COMMENT '添加时间',
  `update_user` int unsigned NOT NULL DEFAULT '0' COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `mark` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '有效标记',
  `create_user_id` int DEFAULT NULL,
  `update_user_id` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `title` (`name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=60 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='字典项管理表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_documents 结构
CREATE TABLE IF NOT EXISTS `t_documents` (
  `id` int NOT NULL AUTO_INCREMENT,
  `unit_id` int NOT NULL,
  `document_type` int NOT NULL DEFAULT '4' COMMENT '1-设计图纸 2-户型图 3-文本资料 4-其他资料',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `file_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `create_user` int unsigned DEFAULT '0' COMMENT '添加人',
  `create_time` datetime DEFAULT NULL COMMENT '添加时间',
  `update_user` int unsigned DEFAULT '0' COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `mark` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '有效标识',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='项目资料表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_employees 结构
CREATE TABLE IF NOT EXISTS `t_employees` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `sex` int DEFAULT '1' COMMENT '1-男 2-女',
  `birth_date` date DEFAULT NULL COMMENT '出生日期',
  `hire_date` date DEFAULT NULL COMMENT '入职日期',
  `position_id` int DEFAULT NULL COMMENT '职位ID',
  `dept_id` int DEFAULT NULL COMMENT ' 部门ID',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `address` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `is_work` int DEFAULT '0' COMMENT '是否上班0-否  1-是',
  `lon` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `lat` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `create_user` int unsigned DEFAULT '0' COMMENT '添加人',
  `create_time` datetime DEFAULT NULL COMMENT '添加时间',
  `update_user` int unsigned DEFAULT '0' COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `mark` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '有效标识',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='员工信息表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_fault 结构
CREATE TABLE IF NOT EXISTS `t_fault` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `fault_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '故障编号',
  `heat_unit_id` bigint DEFAULT NULL COMMENT '热用户ID',
  `alarm_id` bigint DEFAULT NULL COMMENT '告警记录ID',
  `fault_source` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '系统检测' COMMENT '故障来源：用户投诉/巡检上报/系统检测',
  `fault_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '故障类型',
  `fault_level` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '故障等级:提示/一般/重要/严重',
  `fault_desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '故障描述',
  `occur_time` datetime NOT NULL COMMENT '发生时间',
  `report_user_id` bigint NOT NULL DEFAULT '0' COMMENT '上报人员ID',
  `report_time` datetime NOT NULL COMMENT '上报时间',
  `fault_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '故障状态：待确认/已确认/已退回',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `manager_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='故障信息表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_fault_attachment 结构
CREATE TABLE IF NOT EXISTS `t_fault_attachment` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `fault_id` bigint DEFAULT NULL,
  `file_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '附件类型：image/video',
  `file_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件路径',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_fault` (`fault_id`)
) ENGINE=InnoDB AUTO_INCREMENT=38 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='故障附件表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_feedback 结构
CREATE TABLE IF NOT EXISTS `t_feedback` (
  `id` int NOT NULL AUTO_INCREMENT,
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '反馈内容',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '反馈的分类 (例如：建议、问题报告、功能请求等）。',
  `contact_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '联系信息',
  `status` int DEFAULT '1' COMMENT '1-未读 2-已读 3-处理中 4-已解决',
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '用户名称',
  `created_dt` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `updated_dt` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='意见反馈表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_flow_limit_config 结构
CREATE TABLE IF NOT EXISTS `t_flow_limit_config` (
  `id` bigint NOT NULL DEFAULT '0',
  `house_id` int DEFAULT NULL,
  `flow_limit_max` decimal(10,2) DEFAULT NULL COMMENT '流量上限',
  `flow_limit_min` decimal(10,2) DEFAULT NULL COMMENT '流量下限',
  `alarm_type` int DEFAULT NULL,
  `alarm_level` int DEFAULT NULL,
  `alarm_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `is_create_tickets` int DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='热表流量限制配置表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_grquexian 结构
CREATE TABLE IF NOT EXISTS `t_grquexian` (
  `id` int NOT NULL AUTO_INCREMENT,
  `quexian_address` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci,
  `jinji_level` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `quexian_content` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci,
  `jiejue_fangan` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci,
  `create_user` int DEFAULT NULL,
  `create_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `update_user` int DEFAULT NULL,
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `mark` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_heatmeter_data_10001 结构
CREATE TABLE IF NOT EXISTS `t_heatmeter_data_10001` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `collectDt` datetime NOT NULL,
  `heat_meter_no` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `F_S_T` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `F_B_T` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `F_S_H` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `F_S_C` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `power` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `velocity_flow` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `water_yield` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`,`collectDt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_heatmeter_data_10002 结构
CREATE TABLE IF NOT EXISTS `t_heatmeter_data_10002` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `collectDt` datetime NOT NULL,
  `heat_meter_no` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `F_S_T` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `F_B_T` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `F_S_H` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `F_S_C` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `power` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `velocity_flow` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `water_yield` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`,`collectDt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_heatmeter_data_10003 结构
CREATE TABLE IF NOT EXISTS `t_heatmeter_data_10003` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `collectDt` datetime NOT NULL,
  `heat_meter_no` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `F_S_T` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `F_B_T` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `F_S_H` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `F_S_C` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `power` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `velocity_flow` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `water_yield` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`,`collectDt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_heat_unit 结构
CREATE TABLE IF NOT EXISTS `t_heat_unit` (
  `id` bigint unsigned NOT NULL DEFAULT '0',
  `name` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '热用户名称',
  `unit_type` varchar(16) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '热用户类型',
  `unit_no` varchar(16) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '热用户编号',
  `province_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '省份',
  `city_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '市区',
  `district_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '区县',
  `addr` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '地址',
  `built_area` double DEFAULT NULL COMMENT '建筑面积',
  `built_year` int DEFAULT NULL COMMENT '建筑年限',
  `built_type` int DEFAULT '0' COMMENT '0-板楼 1-塔楼 2-板塔结合',
  `longitude` double DEFAULT NULL,
  `latitude` double DEFAULT NULL,
  `house_total` int DEFAULT NULL COMMENT '总户数',
  `is_used` int DEFAULT '0',
  `unit_manage` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '物业联系人',
  `unit_manage_tel` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '物业联系电话',
  `mark_desc` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '描述',
  `geometry` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci,
  `properties` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci,
  `memo` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '备注',
  `created_at` datetime(6) DEFAULT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `id` (`id`) USING BTREE,
  KEY `Name` (`name`) USING BTREE,
  KEY `UnitNo` (`unit_no`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_heat_unit_floor 结构
CREATE TABLE IF NOT EXISTS `t_heat_unit_floor` (
  `id` int NOT NULL AUTO_INCREMENT,
  `floor_name` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT '楼宇名称',
  `heat_unit_id` int DEFAULT NULL COMMENT '热用户id',
  `house_num` int DEFAULT NULL COMMENT '户数',
  `remarks` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT '备注',
  `sort` int DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `UseHeatUnitId` (`heat_unit_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci COMMENT='楼宇表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_heat_unit_floor_unit 结构
CREATE TABLE IF NOT EXISTS `t_heat_unit_floor_unit` (
  `id` int NOT NULL AUTO_INCREMENT,
  `floor_unit_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT '楼宇单元名称',
  `heat_unit_id` int DEFAULT NULL COMMENT '热用户id',
  `heat_unit_floor_id` int DEFAULT NULL COMMENT '楼宇id',
  `house_num` int DEFAULT NULL COMMENT '户数',
  `remarks` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT '备注',
  `sort` int DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `UseHeatUnitId` (`heat_unit_id`) USING BTREE,
  KEY `UseHeatUnitFloorId` (`heat_unit_floor_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=57 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci COMMENT='楼宇单元表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_hes 结构
CREATE TABLE IF NOT EXISTS `t_hes` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `heat_unit_id` int DEFAULT NULL COMMENT '小区ID',
  `heat_unit_name` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '小区名称',
  `name` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '监测站名称',
  `hescode` int DEFAULT NULL COMMENT '监测站唯一标识',
  `used_year` int DEFAULT NULL COMMENT '使用年限',
  `equipment_num` int DEFAULT NULL COMMENT '分区',
  `run_mode` int DEFAULT '0' COMMENT '运行模式 ，=0 手动，=1 自动 =2 停运',
  `is_used` tinyint DEFAULT NULL COMMENT '是否启用换热站',
  `heating_type` int DEFAULT NULL COMMENT '供热类型',
  `heating_area` float DEFAULT '0' COMMENT '供热面积',
  `is_run` tinyint DEFAULT '1' COMMENT '是否正在运行（用连接状态判断） 掉线默认为不运行',
  `disconnect_dt` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '失联时间',
  `heat_rate` double DEFAULT '1' COMMENT '热能系数 ，修正热耗用 暂时废弃（解决西门子热表问题用）',
  `calc_mode` int DEFAULT NULL COMMENT '算法控制： 1.室温控制 2.流量控制 3.二次供水温度控制',
  `target_t` int DEFAULT NULL COMMENT '设定的目标基础温度，用此作为基础使用偏移算出实际的某一时候温度值',
  `control_ip` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '控制IP',
  `control_port` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '控制端口',
  `design_load` float DEFAULT '0' COMMENT '设计负荷',
  `design_flow` float DEFAULT '0' COMMENT '设计流量',
  `g_rate` decimal(10,2) DEFAULT NULL,
  `is_heating` int DEFAULT '1' COMMENT '是否供热',
  `flow_rate` float DEFAULT NULL COMMENT '流量系数',
  `status` int unsigned DEFAULT '0' COMMENT '0-离线  1-在线  2-故障',
  `heating_index` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '27' COMMENT '热系数',
  `alarm_count` int unsigned DEFAULT '0' COMMENT '告警次数',
  `remark` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '备注',
  `hes_code` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb3 COMMENT='监测站基本信息';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_hes_calc 结构
CREATE TABLE IF NOT EXISTS `t_hes_calc` (
  `id` int NOT NULL,
  `calc_desc` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `calc_index` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_hes_calcparam3 结构
CREATE TABLE IF NOT EXISTS `t_hes_calcparam3` (
  `id` int NOT NULL,
  `hesname` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `hescode` int DEFAULT NULL,
  `sst` double DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_hes_calcparam4 结构
CREATE TABLE IF NOT EXISTS `t_hes_calcparam4` (
  `id` int NOT NULL,
  `dt` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `hescode` int DEFAULT NULL,
  `Q` decimal(18,3) DEFAULT NULL,
  `G` decimal(18,3) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_hes_control_record 结构
CREATE TABLE IF NOT EXISTS `t_hes_control_record` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `hes_id` bigint NOT NULL COMMENT '换热站ID',
  `operator_id` bigint NOT NULL COMMENT '操作人ID',
  `control_type` varchar(50) NOT NULL COMMENT '控制类型:temp-温度调节,pressure-压力调节,valve-阀门控制,pump-水泵控制',
  `control_param` varchar(50) NOT NULL COMMENT '控制参数',
  `old_value` decimal(10,2) DEFAULT NULL COMMENT '原值',
  `new_value` decimal(10,2) DEFAULT NULL COMMENT '新值',
  `control_time` datetime NOT NULL COMMENT '控制时间',
  `result` varchar(20) NOT NULL COMMENT '控制结果:success-成功,failed-失败',
  `remark` text COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='换热站控制记录表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_hes_data_1 结构
CREATE TABLE IF NOT EXISTS `t_hes_data_1` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `collectDt` datetime NOT NULL,
  `p0` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p1` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p2` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p3` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p4` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p5` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p6` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p7` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p8` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p9` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p10` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p11` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p12` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p13` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p14` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p15` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p16` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p17` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p18` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p19` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p20` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p21` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p22` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p23` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p28` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p29` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p30` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p31` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p32` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p33` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p34` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p35` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p36` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p37` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p38` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p39` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p40` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p41` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p42` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p43` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p46` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p47` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p48` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p49` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p50` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p51` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p52` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p53` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p54` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p55` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p56` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p57` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p58` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p59` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p88` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p90` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p91` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p92` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p93` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p94` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p95` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p96` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p97` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p98` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p99` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p100` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p101` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p102` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p103` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p104` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p105` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p106` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p107` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p109` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p110` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p111` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p112` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p113` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p114` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p115` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p116` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p117` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p118` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p119` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p120` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p121` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p122` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p123` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p124` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `p125` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`,`collectDt`)
) ENGINE=InnoDB AUTO_INCREMENT=490660 DEFAULT CHARSET=utf8mb3 COMMENT='换热站采集数据存储表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_hes_graph_item 结构
CREATE TABLE IF NOT EXISTS `t_hes_graph_item` (
  `id` decimal(18,0) NOT NULL,
  `hescode` int DEFAULT NULL,
  `isSel` tinyint DEFAULT NULL,
  `PointFieldDesc` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `PointField` varchar(16) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_hes_indoort 结构
CREATE TABLE IF NOT EXISTS `t_hes_indoort` (
  `id` int NOT NULL AUTO_INCREMENT,
  `UseHeatUnitId` int DEFAULT NULL,
  `hescode` int DEFAULT NULL,
  `FloorName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `FloorNum` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `HouseNum` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `install_T` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `install_dt` datetime DEFAULT NULL,
  `FloorPic` varchar(150) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `uid` int DEFAULT NULL,
  `create_dt` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb3;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_hes_modelconfig 结构
CREATE TABLE IF NOT EXISTS `t_hes_modelconfig` (
  `id` int NOT NULL,
  `name` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `hescode` int DEFAULT NULL,
  `sceneNum` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_hes_monitor_point_define 结构
CREATE TABLE IF NOT EXISTS `t_hes_monitor_point_define` (
  `id` int NOT NULL AUTO_INCREMENT,
  `isused` tinyint NOT NULL COMMENT '是否使用',
  `property` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT ' 属性  换热站/住户',
  `equipment_class` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '设备类型',
  `hescode` int DEFAULT NULL COMMENT '换热站编号',
  `hesname` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '换热站名称',
  `mpdesc` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '描述',
  `field_alisa` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '字段名',
  `mpminvalue` double DEFAULT NULL COMMENT '最小量程',
  `mpmaxvalue` double DEFAULT NULL COMMENT '最大量程',
  `iscontrol` tinyint NOT NULL COMMENT '是否控制',
  `issave` tinyint(1) DEFAULT NULL COMMENT '是否存储',
  `isshow` tinyint(1) DEFAULT NULL COMMENT '是否显示',
  `unit` varchar(16) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '单位',
  `mm` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=121 DEFAULT CHARSET=utf8mb3 COMMENT='测点配置表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_hes_mpdictionary 结构
CREATE TABLE IF NOT EXISTS `t_hes_mpdictionary` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `property` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '属性 换热站/小区住户',
  `equipment_class` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '所属设备',
  `num` int DEFAULT NULL COMMENT '分区',
  `field_alisa` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '别名',
  `mpfield` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `mpdesc` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `mpmaxvalue` double DEFAULT NULL,
  `mpminvalue` double DEFAULT NULL,
  `mpvaluetype` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '1-数值型  2-状态型',
  `is_save` bit(1) DEFAULT b'1',
  `is_show` bit(1) DEFAULT b'1',
  `is_control` bit(1) DEFAULT b'0',
  `unit` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `mm` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=123 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_hes_rfh 结构
CREATE TABLE IF NOT EXISTS `t_hes_rfh` (
  `id` int NOT NULL,
  `hesname` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `dt_h` varchar(13) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `value` varchar(18) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_hes_rule 结构
CREATE TABLE IF NOT EXISTS `t_hes_rule` (
  `id` int NOT NULL,
  `hescode` int DEFAULT NULL,
  `run_h` int DEFAULT NULL,
  `offset_t` int DEFAULT NULL,
  `control_t` int NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_hes_rules 结构
CREATE TABLE IF NOT EXISTS `t_hes_rules` (
  `id` int NOT NULL,
  `hesname` varchar(24) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `hescode` int DEFAULT NULL,
  `dt` datetime DEFAULT NULL,
  `tn1` varchar(8) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `tn2` varchar(8) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `mmo` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_hes_statistics_day 结构
CREATE TABLE IF NOT EXISTS `t_hes_statistics_day` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `hescode` int DEFAULT NULL,
  `dt` date DEFAULT NULL,
  `iscalc` int NOT NULL,
  `F_S_C_H` decimal(18,3) DEFAULT NULL,
  `F_S_C_F` decimal(18,3) DEFAULT NULL,
  `unit_h` decimal(18,3) DEFAULT NULL,
  `energy` decimal(18,3) DEFAULT NULL,
  `area` decimal(18,3) DEFAULT NULL,
  `avgT` decimal(18,2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=12106 DEFAULT CHARSET=utf8mb3;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_hes_statistics_hour 结构
CREATE TABLE IF NOT EXISTS `t_hes_statistics_hour` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `hescode` int DEFAULT NULL,
  `dt` date DEFAULT NULL,
  `area` decimal(18,3) DEFAULT NULL,
  `isCalc` int NOT NULL DEFAULT '0',
  `h0` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `h1` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `h2` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `h3` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `h4` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `h5` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `h6` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `h7` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `h8` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `h9` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `h10` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `h11` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `h12` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `h13` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `h14` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `h15` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `h16` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `h17` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `h18` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `h19` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `h20` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `h21` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `h22` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `h23` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=12106 DEFAULT CHARSET=utf8mb3;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_hes_year_harea 结构
CREATE TABLE IF NOT EXISTS `t_hes_year_harea` (
  `id` int NOT NULL AUTO_INCREMENT,
  `hesname` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `hescode` int DEFAULT NULL,
  `hyear` int DEFAULT NULL,
  `heara` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `freearea` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb3 COMMENT='供热面积表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_house 结构
CREATE TABLE IF NOT EXISTS `t_house` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `heat_unit_id` bigint DEFAULT NULL,
  `heat_unit_floor_id` bigint DEFAULT NULL,
  `heat_unit_floor_unit_id` bigint DEFAULT NULL,
  `heat_unit_no` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT '热用户编号',
  `floor_no` bigint DEFAULT NULL COMMENT '楼层号',
  `room_no` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT '房间号',
  `indoor_t_no` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT '室内面板编号',
  `heat_meter_no` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT '热表编号',
  `valves_no` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT '阀门编号',
  `built_area` double DEFAULT NULL COMMENT '建筑面积',
  `billable_area` double DEFAULT NULL COMMENT '计量面积',
  `toward` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT '房屋朝向',
  `house_master_tel` varchar(16) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT '住户联系电话',
  `house_master` varchar(16) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT '住户名称',
  `house_type` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT '''房屋类型：板式、塔式、板塔式',
  `house_pic` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT '户型图',
  `is_install_heatmeter` int DEFAULT '0' COMMENT '是否安装热表',
  `is_pay` int DEFAULT '0' COMMENT '当年是否缴费',
  `is_heating` int DEFAULT '0' COMMENT '是否供暖',
  `valve_status` int DEFAULT '0' COMMENT '阀门状态 0-关闭 1-开启',
  `control_mode` int DEFAULT '0' COMMENT '阀门运行模式 0-手动  1-自动',
  `rule_id` int DEFAULT '0' COMMENT '规则id(配置的调节算法规则)',
  `heating_type` int DEFAULT '1' COMMENT '供暖类型：地暖、暖气片',
  `valve_factory` int DEFAULT '1' COMMENT '阀门厂家   1-黑蚂蚁阀门',
  `channel_no` int DEFAULT NULL COMMENT '采集通道号',
  `meter_type` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT '仪表类型',
  `concentrator_id` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT '集中器Id',
  `add_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT '地址码',
  `dev_lot_no` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT '设备批次号',
  `dev_desc` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT '设备描述',
  `use_quality` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT '使用性质',
  `room_area` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT '房间区域',
  `house_properties` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT '房屋属性',
  `measurement_method` int DEFAULT '1' COMMENT '计量方式 1-面积 2-计量',
  `mm` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT '备注',
  `is_install_heat_meter` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `concentrator_id` (`concentrator_id`) USING BTREE,
  KEY `indoortno` (`indoor_t_no`) USING BTREE,
  KEY `unino` (`heat_unit_no`) USING BTREE,
  KEY `valvesno` (`valves_no`) USING BTREE,
  KEY `heatmetermno` (`heat_meter_no`) USING BTREE,
  KEY `useheatunit_id` (`heat_unit_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4197 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci COMMENT='住户信息表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_house_pay_info 结构
CREATE TABLE IF NOT EXISTS `t_house_pay_info` (
  `id` int NOT NULL AUTO_INCREMENT,
  `heat_unit_id` int DEFAULT NULL COMMENT '小区ID',
  `heat_unit_floor_id` int DEFAULT NULL,
  `heat_unit_floor_unit_id` int DEFAULT NULL,
  `house_id` int DEFAULT NULL COMMENT '住户ID',
  `room_no` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `heat_year` int DEFAULT NULL COMMENT '供暖季',
  `is_heat` tinyint(1) DEFAULT '1' COMMENT '是否供暖 0-不供热  1-供热',
  `is_special_user` tinyint(1) DEFAULT '0' COMMENT '是否特殊用户  0-否  1- 是',
  `is_approval` tinyint DEFAULT '0' COMMENT '是否审批 0-否 1-是',
  `pay_status` smallint DEFAULT '1' COMMENT '1-全缴 2-半缴费  3-空置',
  `pay_date` datetime DEFAULT NULL,
  `opt_user` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `opt_date` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `remark` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_house_statistics_10001 结构
CREATE TABLE IF NOT EXISTS `t_house_statistics_10001` (
  `id` bigint NOT NULL DEFAULT '0',
  `house_id` int DEFAULT NULL,
  `floor_id` int DEFAULT NULL,
  `floor_unit_id` int DEFAULT NULL,
  `room_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `dt` date DEFAULT NULL,
  `valves_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `indoor_t_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `heat_meter_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `valves_num` int DEFAULT '0',
  `indoort_num` int DEFAULT '0',
  `heatmater_num` int DEFAULT '0',
  `valves_actuality_num` int DEFAULT '0',
  `heatmater_actuality_num` int DEFAULT '0',
  `indoort_actuality_num` int DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_house_dt` (`house_id`,`dt`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_house_statistics_10002 结构
CREATE TABLE IF NOT EXISTS `t_house_statistics_10002` (
  `id` bigint NOT NULL DEFAULT '0',
  `house_id` int DEFAULT NULL,
  `floor_id` int DEFAULT NULL,
  `floor_unit_id` int DEFAULT NULL,
  `room_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `dt` date DEFAULT NULL,
  `valves_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `indoor_t_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `heat_meter_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `valves_num` int DEFAULT '0',
  `indoort_num` int DEFAULT '0',
  `heatmater_num` int DEFAULT '0',
  `valves_actuality_num` int DEFAULT '0',
  `heatmater_actuality_num` int DEFAULT '0',
  `indoort_actuality_num` int DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_house_dt` (`house_id`,`dt`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_house_statistics_10003 结构
CREATE TABLE IF NOT EXISTS `t_house_statistics_10003` (
  `id` bigint NOT NULL DEFAULT '0',
  `house_id` int DEFAULT NULL,
  `floor_id` int DEFAULT NULL,
  `floor_unit_id` int DEFAULT NULL,
  `room_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `dt` date DEFAULT NULL,
  `valves_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `indoor_t_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `heat_meter_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `valves_num` int DEFAULT '0',
  `indoort_num` int DEFAULT '0',
  `heatmater_num` int DEFAULT '0',
  `valves_actuality_num` int DEFAULT '0',
  `heatmater_actuality_num` int DEFAULT '0',
  `indoort_actuality_num` int DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_house_dt` (`house_id`,`dt`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_indoorT_data_001 结构
CREATE TABLE IF NOT EXISTS `t_indoorT_data_001` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `collectDt` datetime NOT NULL,
  `indoor_t_no` varchar(50) DEFAULT NULL,
  `t` varchar(50) DEFAULT NULL,
  `h` varchar(50) DEFAULT NULL,
  `set_t` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`,`collectDt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3
/*!50100 PARTITION BY RANGE (year(`collectDt`))
(PARTITION t_indoorT_data_001_2025 VALUES LESS THAN (2026) ENGINE = InnoDB,
 PARTITION t_indoorT_data_001_2026 VALUES LESS THAN (2027) ENGINE = InnoDB,
 PARTITION t_indoorT_data_001_2027 VALUES LESS THAN (2028) ENGINE = InnoDB,
 PARTITION t_indoorT_data_001_2028 VALUES LESS THAN (2029) ENGINE = InnoDB,
 PARTITION t_indoorT_data_001_2029 VALUES LESS THAN (2030) ENGINE = InnoDB,
 PARTITION t_indoorT_data_001_2030 VALUES LESS THAN (2031) ENGINE = InnoDB,
 PARTITION t_indoorT_data_001_2031 VALUES LESS THAN (2032) ENGINE = InnoDB,
 PARTITION t_indoorT_data_001_2032 VALUES LESS THAN (2033) ENGINE = InnoDB,
 PARTITION t_indoorT_data_001_2033 VALUES LESS THAN (2034) ENGINE = InnoDB,
 PARTITION t_indoorT_data_001_2034 VALUES LESS THAN (2035) ENGINE = InnoDB,
 PARTITION t_indoorT_data_001_2035 VALUES LESS THAN (2036) ENGINE = InnoDB) */;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_indoorT_data_10001 结构
CREATE TABLE IF NOT EXISTS `t_indoorT_data_10001` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `collectDt` datetime NOT NULL,
  `indoor_t_no` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `T` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `H` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `set_t` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`,`collectDt`)
) ENGINE=InnoDB AUTO_INCREMENT=190257 DEFAULT CHARSET=utf8mb3;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_indoorT_data_10002 结构
CREATE TABLE IF NOT EXISTS `t_indoorT_data_10002` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `collectDt` datetime NOT NULL,
  `indoor_t_no` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `T` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `H` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `set_t` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`,`collectDt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_indoorT_data_10003 结构
CREATE TABLE IF NOT EXISTS `t_indoorT_data_10003` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `collectDt` datetime NOT NULL,
  `indoor_t_no` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `T` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `H` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `set_t` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`,`collectDt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_knowledge_base 结构
CREATE TABLE IF NOT EXISTS `t_knowledge_base` (
  `id` int NOT NULL AUTO_INCREMENT,
  `fault_class` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '故障类型',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标题',
  `detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '解决方案',
  `keywords` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '关键词',
  `file_path` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_user` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `create_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `mark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='知识库';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_level 结构
CREATE TABLE IF NOT EXISTS `t_level` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '职级名称',
  `status` tinyint unsigned DEFAULT '1' COMMENT '状态：1正常 2停用',
  `sort` smallint unsigned DEFAULT '125' COMMENT '显示顺序',
  `create_user` int unsigned DEFAULT '0' COMMENT '添加人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` int unsigned DEFAULT '0' COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `mark` tinyint unsigned DEFAULT '1' COMMENT '有效标识',
  PRIMARY KEY (`id`),
  KEY `name` (`name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='职级表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_login_log 结构
CREATE TABLE IF NOT EXISTS `t_login_log` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '唯一性标识',
  `title` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '日志标题',
  `username` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '登录账号',
  `login_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '登录IP地址',
  `login_location` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '登录地区',
  `browser` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '浏览器类型',
  `os` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '操作系统',
  `status` tinyint unsigned DEFAULT '1' COMMENT '登录状态：0成功 1失败',
  `type` tinyint unsigned DEFAULT '0' COMMENT '操作类型：1登录系统 2注销系统 3刷新Token',
  `note` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '' COMMENT '登录备注',
  `create_user` int unsigned NOT NULL DEFAULT '0' COMMENT '添加人',
  `create_time` datetime DEFAULT NULL COMMENT '登录时间',
  `update_user` int unsigned NOT NULL DEFAULT '0' COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `mark` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '有效标记',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=500 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='登录日志表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_manager_heat_unit 结构
CREATE TABLE IF NOT EXISTS `t_manager_heat_unit` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `manager_id` bigint NOT NULL COMMENT '管理人员ID',
  `manager_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '管理人员名称',
  `heat_unit_id` bigint NOT NULL COMMENT '热用户ID',
  `heat_unit_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '热用户名称',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_manager_heat_unit` (`manager_id`,`heat_unit_id`),
  KEY `idx_manager_id` (`manager_id`),
  KEY `idx_heat_unit_id` (`heat_unit_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='管理人员与热用户关联表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_map_info 结构
CREATE TABLE IF NOT EXISTS `t_map_info` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `dev_name` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '探测站名称',
  `dev_type` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '设备类型',
  `lon` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `lat` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `route_name` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '线路名称',
  `direction` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `machine_room_no` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `kilometer_mark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '公里标',
  `install_site` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '安装地点',
  `mark_icon` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `mark_type` int DEFAULT '1',
  `mark_desc` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `geometry` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci,
  `properties` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci,
  `is_warning` int DEFAULT '0',
  `warn_val` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `styles` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci,
  `style_type` int DEFAULT '1',
  `line_width` int DEFAULT '3',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb3;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_materials_info 结构
CREATE TABLE IF NOT EXISTS `t_materials_info` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '材料名称',
  `specification` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '规格型号',
  `material_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '材质',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '材料类别',
  `usaged` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '用途',
  `mark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='材料信息表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_menu 结构
CREATE TABLE IF NOT EXISTS `t_menu` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `pid` int NOT NULL DEFAULT '0' COMMENT '父级ID',
  `title` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '菜单标题',
  `icon` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '图标',
  `path` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '菜单路径',
  `component` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '菜单组件',
  `target` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '打开方式：0组件 1内链 2外链',
  `permission` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '权限标识',
  `type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '类型：0菜单 1节点',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：0显示 1不显示',
  `hide` tinyint unsigned DEFAULT '0' COMMENT '是否可见：0显示 1隐藏',
  `syshide` tinyint(1) DEFAULT '0',
  `note` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `sort` smallint DEFAULT '125' COMMENT '显示顺序',
  `create_user` int NOT NULL DEFAULT '0' COMMENT '添加人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` int DEFAULT '0' COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `mark` tinyint(1) NOT NULL DEFAULT '1' COMMENT '有效标识',
  PRIMARY KEY (`id`),
  KEY `index_pid` (`pid`) USING BTREE,
  KEY `index_name` (`title`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2246 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='系统菜单表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_notice 结构
CREATE TABLE IF NOT EXISTS `t_notice` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '通知ID',
  `title` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '通知标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '通知内容',
  `source` tinyint(1) NOT NULL COMMENT '来源：1内部通知 2外部新闻',
  `is_top` tinyint unsigned NOT NULL DEFAULT '2' COMMENT '是否置顶：1是 2否',
  `browse` int unsigned NOT NULL DEFAULT '0' COMMENT '阅读量',
  `status` tinyint unsigned NOT NULL DEFAULT '2' COMMENT '状态：1已发布 2待发布',
  `create_user` int unsigned NOT NULL DEFAULT '0' COMMENT '添加人',
  `create_time` datetime DEFAULT NULL COMMENT '添加时间',
  `update_user` int unsigned NOT NULL DEFAULT '0' COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `mark` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '有效标识',
  PRIMARY KEY (`id`),
  KEY `index_title` (`title`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='通知公告表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_operation_log 结构
CREATE TABLE IF NOT EXISTS `t_operation_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `work_order_id` bigint DEFAULT NULL,
  `operation_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作类型',
  `operation_desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作描述',
  `operator_id` bigint DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_order` (`work_order_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=80 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='操作日志表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_oper_log 结构
CREATE TABLE IF NOT EXISTS `t_oper_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `title` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '日志标题',
  `log_type` int unsigned NOT NULL DEFAULT '0' COMMENT '操作类型（0其它 1新增 2修改 3删除）',
  `oper_method` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '' COMMENT '操作方法',
  `request_method` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '' COMMENT '请求方式',
  `oper_type` int unsigned NOT NULL DEFAULT '0' COMMENT '操作类型：0其他 1后台用户 2WAP用户',
  `oper_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '操作人员',
  `oper_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '请求URL',
  `oper_ip` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '' COMMENT '主机地址',
  `oper_location` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '' COMMENT '操作地点',
  `oper_param` varchar(2000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '' COMMENT '请求参数',
  `json_result` varchar(2000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '' COMMENT '返回参数',
  `status` int DEFAULT '0' COMMENT '操作状态（0正常 1异常）',
  `note` varchar(2000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '' COMMENT '备注',
  `create_user` int unsigned NOT NULL DEFAULT '0' COMMENT '添加人',
  `create_time` datetime DEFAULT NULL COMMENT '操作时间',
  `update_user` int unsigned NOT NULL DEFAULT '0' COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `mark` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '有效标识',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1181 DEFAULT CHARSET=utf8mb3 COMMENT='操作日志记录';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_other_param 结构
CREATE TABLE IF NOT EXISTS `t_other_param` (
  `id` int NOT NULL AUTO_INCREMENT,
  `price` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `rated_temp` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `collect_cycle` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '10' COMMENT '阀门热表采集周期，单位分钟',
  `indoor_cycle` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '60',
  `heatmater_cycle` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '30',
  `alarm_cycle` int DEFAULT NULL,
  `last_alarm_date` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_time` datetime DEFAULT NULL,
  `create_user` int DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `update_user` int DEFAULT NULL,
  `mark` bit(1) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb3 COMMENT='系统参数';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_outdoor_temperature 结构
CREATE TABLE IF NOT EXISTS `t_outdoor_temperature` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `temperature` decimal(38,2) DEFAULT NULL COMMENT '气温',
  `record_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录时间',
  `created_at` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_record_time` (`record_time`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='室外温度记录表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_patrol_category 结构
CREATE TABLE IF NOT EXISTS `t_patrol_category` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '类别ID',
  `category_code` varchar(32) NOT NULL COMMENT '类别编码',
  `category_name` varchar(100) NOT NULL COMMENT '类别名称',
  `parent_id` bigint DEFAULT '0' COMMENT '父类别ID',
  `description` varchar(500) DEFAULT NULL COMMENT '描述',
  `sort_order` int DEFAULT '0' COMMENT '排序序号',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_category_code` (`category_code`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='巡检项目类别表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_patrol_item 结构
CREATE TABLE IF NOT EXISTS `t_patrol_item` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `patrol_plan_id` bigint NOT NULL DEFAULT '0' COMMENT '巡检计划ID',
  `device_patrol_item_id` bigint NOT NULL DEFAULT '0' COMMENT '设备巡检项表ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='巡检项目表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_patrol_item_dictionary 结构
CREATE TABLE IF NOT EXISTS `t_patrol_item_dictionary` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `item_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '巡检项编码',
  `item_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '巡检项名称',
  `category_id` bigint DEFAULT NULL COMMENT '所属类别ID',
  `category_name` varchar(100) DEFAULT NULL COMMENT '所属类别名称',
  `param_type` varchar(20) DEFAULT NULL COMMENT '参数类型(text/number/boolean/selection)',
  `unit` varchar(20) DEFAULT NULL COMMENT '单位',
  `check_method` varchar(255) DEFAULT NULL COMMENT '检查方法',
  `importance` varchar(10) DEFAULT 'normal' COMMENT '重要性(critical/important/normal)',
  `description` varchar(500) DEFAULT NULL COMMENT '描述说明',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_item_code` (`item_code`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='巡检项目字典表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_patrol_plan 结构
CREATE TABLE IF NOT EXISTS `t_patrol_plan` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `plan_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '计划编号',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '计划名称',
  `patrol_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '巡检类型:换热站巡检，日常巡检，设备巡检，管道巡检，阀门巡检',
  `start_date` date NOT NULL COMMENT '开始日期',
  `end_date` date DEFAULT NULL COMMENT '结束日期',
  `executor_ids` json DEFAULT NULL COMMENT '指定的执行人员ID列表',
  `schedule_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '周期类型:daily,weekly,monthly,custom',
  `schedule_interval` int DEFAULT NULL COMMENT '巡检间隔',
  `schedule_week_days` json NOT NULL COMMENT '巡检周天',
  `schedule_month_days` json NOT NULL COMMENT '巡检月天',
  `device_ids` json NOT NULL COMMENT '被检测设备id列表',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'pending' COMMENT '状态:pending-待执行,processing-执行中,completed-已完成',
  `locations` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '巡检地点: 选择热用户',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='巡检计划表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_patrol_record 结构
CREATE TABLE IF NOT EXISTS `t_patrol_record` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `patrol_plan_id` int DEFAULT NULL,
  `executor_id` bigint NOT NULL COMMENT '执行人ID',
  `execution_date` date NOT NULL COMMENT '执行日期',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '完成时间',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'pending' COMMENT '状态:pending,processing,completed',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='巡检记录表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_patrol_result 结构
CREATE TABLE IF NOT EXISTS `t_patrol_result` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `patrol_record_id` bigint NOT NULL COMMENT '执行记录ID',
  `patrol_item_id` bigint NOT NULL COMMENT '巡检项目ID',
  `check_result` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '检查结果:normal-正常,abnormal-异常',
  `param_value` varchar(255) DEFAULT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '描述',
  `images` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '图片记录(JSON数组)',
  `latitude` double DEFAULT NULL,
  `longitude` double DEFAULT NULL,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=27 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='巡检结果表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_person_trajectory 结构
CREATE TABLE IF NOT EXISTS `t_person_trajectory` (
  `id` int NOT NULL AUTO_INCREMENT,
  `employee_id` int NOT NULL COMMENT '员工ID',
  `longitude` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '经度',
  `latitude` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '纬度',
  `status` int DEFAULT '0' COMMENT '0-正常  1-异常',
  `create_time` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `employees_id` (`employee_id`,`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='人员轨迹记录表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_point_rule 结构
CREATE TABLE IF NOT EXISTS `t_point_rule` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `rule_type` varchar(20) NOT NULL COMMENT '规则类型:report-上报,feedback-反馈',
  `point_value` int NOT NULL COMMENT '积分值',
  `max_value` int NOT NULL COMMENT '最大积分值',
  `description` varchar(200) DEFAULT NULL COMMENT '规则描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:0-禁用,1-启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='积分规则表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_position 结构
CREATE TABLE IF NOT EXISTS `t_position` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '岗位名称',
  `status` tinyint unsigned DEFAULT '1' COMMENT '状态：1正常 2停用',
  `sort` smallint unsigned DEFAULT '125' COMMENT '显示顺序',
  `create_user` int unsigned DEFAULT '0' COMMENT '添加人',
  `create_time` datetime DEFAULT NULL COMMENT '添加时间',
  `update_user` int unsigned DEFAULT '0' COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `mark` tinyint unsigned DEFAULT '1' COMMENT '有效标识',
  PRIMARY KEY (`id`),
  KEY `name` (`name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='岗位表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_repair_info 结构
CREATE TABLE IF NOT EXISTS `t_repair_info` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `mobile` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `address` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `create_user` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `create_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `update_user` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `mark` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `state` int DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb3;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_repair_record 结构
CREATE TABLE IF NOT EXISTS `t_repair_record` (
  `id` int NOT NULL AUTO_INCREMENT,
  `hescode` int DEFAULT NULL,
  `fault_class` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `fault_desc` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `repair_id` int DEFAULT NULL,
  `repair_state` int DEFAULT '1',
  `repair_content` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `repair_rst` varchar(150) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `create_user` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `create_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `update_user` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `mark` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb3 COMMENT='维修工单记录表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_role 结构
CREATE TABLE IF NOT EXISTS `t_role` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色名称',
  `code` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '角色标签',
  `status` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '状态：1正常 2禁用',
  `note` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `sort` smallint unsigned NOT NULL DEFAULT '125' COMMENT '排序',
  `create_user` int unsigned NOT NULL DEFAULT '0' COMMENT '添加人',
  `create_time` datetime DEFAULT NULL COMMENT '添加时间',
  `update_user` int unsigned DEFAULT '0' COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `mark` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '有效标识(1正常 0删除)',
  PRIMARY KEY (`id`),
  KEY `name` (`name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='系统角色表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_role_menu 结构
CREATE TABLE IF NOT EXISTS `t_role_menu` (
  `role_id` smallint unsigned NOT NULL DEFAULT '0' COMMENT '角色ID',
  `menu_id` smallint unsigned NOT NULL DEFAULT '0' COMMENT '菜单ID',
  KEY `role_id` (`menu_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='角色菜单关联表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_room_temperature 结构
CREATE TABLE IF NOT EXISTS `t_room_temperature` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `heat_unit_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '小区名称',
  `building_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '楼栋号',
  `unit_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '单元号',
  `room_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '户号',
  `indoor_temp` double DEFAULT NULL COMMENT '室内温度',
  `outdoor_temp` double DEFAULT NULL COMMENT '室外温度',
  `latitude` double DEFAULT NULL COMMENT '纬度',
  `longitude` double DEFAULT NULL COMMENT '经度',
  `images` json DEFAULT NULL COMMENT '现场照片',
  `videos` json DEFAULT NULL COMMENT '现场视频',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '备注说明',
  `report_time` datetime NOT NULL COMMENT '上报时间',
  `report_user_id` bigint DEFAULT NULL COMMENT '上报人ID',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_building` (`building_no`),
  KEY `idx_report_time` (`report_time`),
  KEY `idx_community` (`heat_unit_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=44 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='室温记录表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_runrules 结构
CREATE TABLE IF NOT EXISTS `t_runrules` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `week_rule` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `run_cond` varchar(512) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb3;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_system_param 结构
CREATE TABLE IF NOT EXISTS `t_system_param` (
  `id` int NOT NULL AUTO_INCREMENT,
  `system_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `system_logo` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `system_versions` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `copyright` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `company` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `linkman` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `mobile` varchar(11) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `internet_addr` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `company_addr` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `update_fre` int DEFAULT NULL,
  `intro` varchar(600) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `create_user` int DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `update_user` int DEFAULT NULL,
  `mark` bit(1) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb3 COMMENT='系统参数';

-- 数据导出被取消选择。

CREATE TABLE `t_sys_permission` (
	`id` BIGINT(19) NOT NULL AUTO_INCREMENT,
	`permission_code` VARCHAR(50) NULL DEFAULT NULL COMMENT '权限编码' COLLATE 'utf8mb4_0900_ai_ci',
	`permission_name` VARCHAR(50) NULL DEFAULT NULL COMMENT '权限名称' COLLATE 'utf8mb4_0900_ai_ci',
	`menu_name` VARCHAR(50) NULL DEFAULT NULL COMMENT '关联菜单名称' COLLATE 'utf8mb4_0900_ai_ci',
	`path` VARCHAR(50) NULL DEFAULT NULL COMMENT '页面名称' COLLATE 'utf8mb4_0900_ai_ci',
	`description` VARCHAR(200) NULL DEFAULT NULL COMMENT '权限描述' COLLATE 'utf8mb4_0900_ai_ci',
	`create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
	PRIMARY KEY (`id`) USING BTREE
)
COMMENT='系统权限表'
COLLATE='utf8mb4_0900_ai_ci'
ENGINE=InnoDB
AUTO_INCREMENT=30
; 

-- 导出  表 tb_gongre.t_sys_role 结构
CREATE TABLE IF NOT EXISTS `t_sys_role` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `role_code` varchar(20) NOT NULL COMMENT '角色编码:admin-系统管理员,inspector-巡检维修人员,resident-普通房户,monitor-换热站监控人员',
  `role_name` varchar(50) NOT NULL COMMENT '角色名称',
  `description` varchar(200) DEFAULT NULL COMMENT '角色描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:0-禁用,1-启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_role_code` (`role_code`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='系统角色表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_sys_role_permission 结构
CREATE TABLE IF NOT EXISTS `t_sys_role_permission` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `role_code` varchar(50) NOT NULL COMMENT '角色编码',
  `permission_code` varchar(50) NOT NULL COMMENT '权限编码',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:0-禁用,1-启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_perm` (`role_code`,`permission_code`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='角色权限关联表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_tousu 结构
CREATE TABLE IF NOT EXISTS `t_tousu` (
  `id` int NOT NULL AUTO_INCREMENT,
  `uid` int DEFAULT NULL,
  `regtime` int DEFAULT NULL,
  `tousu_address` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci,
  `tousu_content` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci,
  `tousu_fangan` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci,
  `create_user` int DEFAULT NULL,
  `create_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `mark` int DEFAULT NULL,
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `update_user` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb3;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_user 结构
CREATE TABLE IF NOT EXISTS `t_user` (
  `id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '主键ID',
  `realname` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '真实姓名',
  `nickname` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '昵称',
  `gender` tinyint unsigned NOT NULL DEFAULT '3' COMMENT '性别:1男 2女 3保密',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '登录用户名',
  `password` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '登录密码',
  `role_id` int DEFAULT NULL,
  `heat_unit_id` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '项目权限（所管辖的小区），0默认全部',
  `avatar` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '头像',
  `mobile` char(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '手机号码',
  `email` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '邮箱地址',
  `weixin` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '微信号',
  `birthday` date DEFAULT NULL COMMENT '出生日期',
  `dept_id` int unsigned NOT NULL DEFAULT '0' COMMENT '部门ID',
  `level_id` smallint unsigned NOT NULL DEFAULT '0' COMMENT '职级ID',
  `position_id` smallint unsigned NOT NULL DEFAULT '0' COMMENT '岗位ID',
  `gonghao` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工号',
  `province_code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '省份ID',
  `city_code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '市区ID',
  `district_code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '区县ID',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '详细地址',
  `city_name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '所属城市',
  `salt` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '盐加密',
  `intro` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '个人简介',
  `status` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '状态：1正常 2禁用',
  `note` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `sort` smallint unsigned NOT NULL DEFAULT '125' COMMENT '显示顺序',
  `login_num` smallint unsigned NOT NULL DEFAULT '0' COMMENT '登录次数',
  `login_ip` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最近登录IP',
  `login_time` datetime DEFAULT NULL COMMENT '最近登录时间',
  `create_user` int unsigned DEFAULT '0' COMMENT '添加人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` int unsigned DEFAULT '0' COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `mark` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '有效标识(1正常 0删除)',
  PRIMARY KEY (`id`),
  KEY `realname` (`realname`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='后台用户管理表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_userwhite 结构
CREATE TABLE IF NOT EXISTS `t_userwhite` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `phone` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `nlevel` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=38 DEFAULT CHARSET=utf8mb3;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_user_app 结构
CREATE TABLE IF NOT EXISTS `t_user_app` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(100) NOT NULL COMMENT '密码',
  `name` varchar(50) NOT NULL COMMENT '姓名',
  `role` varchar(20) NOT NULL COMMENT '角色:admin-管理员,maintenance-维修人员,inspector-巡检员',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:0-禁用,1-启用',
  `nick_name` varchar(50) DEFAULT NULL COMMENT '微信昵称',
  `is_audit` tinyint(1) DEFAULT '0' COMMENT '审核 0-未审核  1-已审核',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像地址',
  `gender` int DEFAULT NULL COMMENT '性别',
  `openid` varchar(100) DEFAULT NULL COMMENT '微信openid',
  `unionid` varchar(100) DEFAULT NULL COMMENT '微信unionid',
  `certifications` json DEFAULT NULL COMMENT '资质证书',
  `department` varchar(50) DEFAULT NULL COMMENT '部门',
  `skills` json DEFAULT NULL COMMENT '技能',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `certification_status` int DEFAULT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '邮件',
  `enabled` bit(1) DEFAULT NULL COMMENT '使能',
  `work_stats` json DEFAULT NULL COMMENT '工作统计',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_username` (`username`),
  UNIQUE KEY `openid` (`openid`),
  UNIQUE KEY `UK_ob8kqyqqgmefl0aco34akdtpe` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_user_certifications 结构
CREATE TABLE IF NOT EXISTS `t_user_certifications` (
  `user_id` bigint NOT NULL,
  `expire_date` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `number` varchar(255) DEFAULT NULL,
  KEY `FKn2bw638vse1c6o3w1uoti7sdk` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_user_notification_setting 结构
CREATE TABLE IF NOT EXISTS `t_user_notification_setting` (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `msg_types` json NOT NULL COMMENT '接收消息类型',
  `receive_sms` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否接收短信',
  `receive_push` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否接收推送',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户通知设置表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_user_point 结构
CREATE TABLE IF NOT EXISTS `t_user_point` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `total_points` int NOT NULL COMMENT '总积分',
  `point_history` json NOT NULL COMMENT '积分历史记录',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户积分表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_user_role 结构
CREATE TABLE IF NOT EXISTS `t_user_role` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int unsigned NOT NULL DEFAULT '0' COMMENT '人员ID',
  `role_id` int unsigned NOT NULL DEFAULT '0' COMMENT '角色ID',
  PRIMARY KEY (`id`),
  KEY `admin_id` (`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='人员角色表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_user_skills 结构
CREATE TABLE IF NOT EXISTS `t_user_skills` (
  `user_id` bigint NOT NULL,
  `skill` varchar(255) DEFAULT NULL,
  KEY `FKro13if9r7fwkr5115715127ai` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_values_control_log 结构
CREATE TABLE IF NOT EXISTS `t_values_control_log` (
  `id` int NOT NULL AUTO_INCREMENT,
  `house_id` int NOT NULL DEFAULT '0',
  `rules_id` int DEFAULT '0' COMMENT '规则id',
  `control_mode` int DEFAULT '0' COMMENT '控制模式  0-手动 1-自动',
  `status` int DEFAULT '0' COMMENT '操作关/开',
  `opt_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '操作的时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_valves_data_10001 结构
CREATE TABLE IF NOT EXISTS `t_valves_data_10001` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `collectDt` datetime NOT NULL,
  `valves_no` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `F_S_V` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `S_F_S_V` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `F_S_V_STATE` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `g_temperature` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `h_temperature` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`,`collectDt`),
  KEY `valvesno` (`valves_no`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5534312 DEFAULT CHARSET=utf8mb3;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_valves_data_10002 结构
CREATE TABLE IF NOT EXISTS `t_valves_data_10002` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `collectDt` datetime NOT NULL,
  `valves_no` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `F_S_V` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `S_F_S_V` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `F_S_V_STATE` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `g_temperature` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `h_temperature` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`,`collectDt`)
) ENGINE=InnoDB AUTO_INCREMENT=4276873 DEFAULT CHARSET=utf8mb3;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_valves_data_10003 结构
CREATE TABLE IF NOT EXISTS `t_valves_data_10003` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `collectDt` datetime NOT NULL,
  `valves_no` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `F_S_V` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `S_F_S_V` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `F_S_V_STATE` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `g_temperature` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `h_temperature` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`,`collectDt`)
) ENGINE=InnoDB AUTO_INCREMENT=1671772 DEFAULT CHARSET=utf8mb3;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_valves_rules_config 结构
CREATE TABLE IF NOT EXISTS `t_valves_rules_config` (
  `id` int NOT NULL AUTO_INCREMENT,
  `house_id` int DEFAULT NULL COMMENT '住户id ',
  `rules_id` int DEFAULT NULL COMMENT '规则id',
  PRIMARY KEY (`id`),
  UNIQUE KEY `house_id` (`house_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb3;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_valves_rules_info 结构
CREATE TABLE IF NOT EXISTS `t_valves_rules_info` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `model_id` int DEFAULT NULL COMMENT '规则模板id',
  `is_used` int DEFAULT NULL,
  `create_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_user` int DEFAULT NULL,
  `mm` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb3;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_valves_rules_model 结构
CREATE TABLE IF NOT EXISTS `t_valves_rules_model` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '规则id',
  `name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '规则名字',
  `fv_step` int DEFAULT NULL COMMENT '阀门的开度步长（每次调节开度）',
  `cycle` int DEFAULT NULL COMMENT '周期(多长时间调节一次)',
  `time_rules` varchar(125) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '24小时每小时的温度。使用|隔开',
  `is_used` int DEFAULT '0' COMMENT '是否启用',
  `create_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` int DEFAULT NULL COMMENT '创建用户',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb3;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_weather_setting 结构
CREATE TABLE IF NOT EXISTS `t_weather_setting` (
  `id` int NOT NULL,
  `dt` varchar(2) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `T` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `H` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `S` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `R` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_weather_station_data 结构
CREATE TABLE IF NOT EXISTS `t_weather_station_data` (
  `id` int NOT NULL AUTO_INCREMENT,
  `WS` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `T` double DEFAULT NULL,
  `F` double DEFAULT NULL,
  `H` double DEFAULT NULL,
  `R` double DEFAULT NULL,
  `S` double DEFAULT NULL,
  `CollectDT` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `index_T` (`T`) USING BTREE,
  KEY `index_CollectDT` (`CollectDT`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=23973 DEFAULT CHARSET=utf8mb3;

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_work_order 结构
CREATE TABLE IF NOT EXISTS `t_work_order` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `order_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '工单编号',
  `fault_id` bigint DEFAULT NULL COMMENT '故障ID',
  `repair_user_id` bigint DEFAULT NULL COMMENT '维修人员ID',
  `repair_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '维修内容',
  `repair_result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '维修结果',
  `repair_time` datetime DEFAULT NULL COMMENT '维修时间',
  `repair_materials_quantity` json DEFAULT NULL COMMENT '耗材数量',
  `order_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '工单状态：待接单/已接单/已完成',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '工单生成时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '工单更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='工单信息表';

-- 数据导出被取消选择。

-- 导出  表 tb_gongre.t_work_order_attachment 结构
CREATE TABLE IF NOT EXISTS `t_work_order_attachment` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `work_order_id` bigint DEFAULT NULL,
  `file_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '附件类型：image/video',
  `file_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件路径',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='工单附件表';

-- 数据导出被取消选择。

-- 导出  存储过程 tb_gongre.UpdateHeatmeterData 结构
DELIMITER //
CREATE PROCEDURE `UpdateHeatmeterData`(
	IN `unitNo` VARCHAR(50),
	IN `reportDate` DATE
)
BEGIN
    DECLARE heatmeterTable VARCHAR(255);
    DECLARE statisticsTable VARCHAR(255);
    
    -- 动态表名
    SET @heatmeterTable = CONCAT('t_heatmeter_data_', unitNo);
    SET @statisticsTable = CONCAT('t_house_statistics_', unitNo);

    -- 更新统计表中的实际上报次数
    SET @updateHeatmeterNumSQL = CONCAT(
        'UPDATE ', @statisticsTable, ' s
         JOIN (
             SELECT DATE(hd.collectDt) AS dt, hd.heat_meter_no, COUNT(*) AS heatmater_actuality_num
             FROM ', @heatmeterTable, ' hd
             WHERE DATE(hd.collectDt) = ''', reportDate, '''
             GROUP BY DATE(hd.collectDt), hd.heat_meter_no
         ) AS subq
         ON s.dt = subq.dt AND s.heat_meter_no = subq.heat_meter_no
         SET s.heatmater_actuality_num = subq.heatmater_actuality_num'
    );
    
    PREPARE stmt FROM @updateHeatmeterNumSQL;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END//
DELIMITER ;

-- 导出  存储过程 tb_gongre.UpdateIndoorTData 结构
DELIMITER //
CREATE PROCEDURE `UpdateIndoorTData`(
	IN `unitNo` VARCHAR(50),
	IN `reportDate` DATE
)
BEGIN
    DECLARE indoorTable VARCHAR(255);
    DECLARE statisticsTable VARCHAR(255);
    
    -- 动态表名
    SET @indoorTable = CONCAT('t_indoorT_data_', unitNo);
    SET @statisticsTable = CONCAT('t_house_statistics_', unitNo);

    -- 更新统计表中的实际上报次数
    SET @updateIndoorNumSQL = CONCAT(
        'UPDATE ', @statisticsTable, ' s
         JOIN (
             SELECT DATE(id.collectDt) AS dt, id.indoor_t_no, COUNT(*) AS indoort_actuality_num
             FROM ', @indoorTable, ' id
             WHERE DATE(id.collectDt) = ''', reportDate, '''
             GROUP BY DATE(id.collectDt), id.indoor_t_no
         ) AS subq
         ON s.dt = subq.dt AND s.indoor_t_no = subq.indoor_t_no
         SET s.indoort_actuality_num = subq.indoort_actuality_num'
    );
    
    PREPARE stmt FROM @updateIndoorNumSQL;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END//
DELIMITER ;

-- 导出  存储过程 tb_gongre.UpdateValvesData 结构
DELIMITER //
CREATE PROCEDURE `UpdateValvesData`(
	IN `unitNo` VARCHAR(50),
	IN `reportDate` DATE
)
BEGIN
    DECLARE valvesTable VARCHAR(255);
    DECLARE statisticsTable VARCHAR(255);
    
    -- 动态表名
    SET @valvesTable = CONCAT('t_valves_data_', unitNo);
    SET @statisticsTable = CONCAT('t_house_statistics_', unitNo);

    -- 更新统计表中的实际上报次数
    SET @updateValvesNumSQL = CONCAT(
        'UPDATE ', @statisticsTable, ' s
         JOIN (
             SELECT DATE(vd.collectDt) AS dt, vd.valves_no, COUNT(*) AS valves_actuality_num
             FROM ', @valvesTable, ' vd
             WHERE DATE(vd.collectDt) = ''', reportDate, '''
             GROUP BY DATE(vd.collectDt), vd.valves_no
         ) AS subq
         ON s.dt = subq.dt AND s.valves_no = subq.valves_no
         SET s.valves_actuality_num = subq.valves_actuality_num'
    );
    
    PREPARE stmt FROM @updateValvesNumSQL;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END//
DELIMITER ;

-- 导出  表 tb_gongre.wx_user_binding 结构
CREATE TABLE IF NOT EXISTS `wx_user_binding` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `open_id` varchar(50) NOT NULL COMMENT '微信openid',
  `union_id` varchar(50) DEFAULT NULL COMMENT '微信unionid',
  `user_id` bigint NOT NULL COMMENT '系统用户ID',
  `nick_name` varchar(50) DEFAULT NULL COMMENT '微信昵称',
  `avatar_url` varchar(200) DEFAULT NULL COMMENT '头像地址',
  `phone` varchar(20) DEFAULT NULL COMMENT '绑定手机号',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:0-解绑,1-绑定',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_openid` (`open_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='微信用户绑定表';

-- 数据导出被取消选择。

/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
