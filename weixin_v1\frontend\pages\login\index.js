Page({
  data: {
    username: '',
    password: '',
    isRemember: false
  },

  onLoad() {
    // 检查是否有保存的登录信息
    const savedUsername = wx.getStorageSync('savedUsername');
    const savedPassword = wx.getStorageSync('savedPassword');
    
    if (savedUsername && savedPassword) {
      this.setData({
        username: savedUsername,
        password: savedPassword,
        isRemember: true
      });
    }
  },

 
  onUsernameInput(e) {
    this.setData({
      username: e.detail.value
    });
  },

  onPasswordInput(e) {
    this.setData({
      password: e.detail.value
    });
  },

  toggleRemember() {
    this.setData({
      isRemember: !this.data.isRemember
    });
  },

  handleLogin() {
    const { username, password, isRemember } = this.data;

    if (!username.trim()) {
      wx.showToast({
        title: '请输入用户名',
        icon: 'none'
      });
      return;
    }

    if (!password) {
      wx.showToast({
        title: '请输入密码',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '登录中...'
    });

    // 调用登录接口
    wx.request({
      url: 'http://127.0.0.1:5000/api/login',
      method: 'POST',
      data: {
        username,
        password
      },
      success: (res) => {
 
        if (res.data.success) {

          // 保存登录状态和token
          wx.setStorageSync('token', res.data.token);
          wx.setStorageSync('userInfo', res.data.userInfo);
          wx.setStorageSync('userId',res.data.userInfo.id);
          if(res.data.userInfo.role_id === 1){
            wx.setStorageSync('isAdmin', true);
          }else{
            wx.setStorageSync('isAdmin', false);
          }
          // 如果选择记住密码，保存登录信息
          if (isRemember) {
            wx.setStorageSync('savedUsername', username);
            wx.setStorageSync('savedPassword', password);
          } else {
            wx.removeStorageSync('savedUsername');
            wx.removeStorageSync('savedPassword');
          }

          wx.showToast({
            title: '登录成功',
            icon: 'success',
            duration: 1500,
            success: () => {
              setTimeout(() => {
                wx.reLaunch({
                  url: '/pages/index/index'
                });
              }, 1500);
            }
          });
        } else {
          wx.showToast({
            title: res.data.message || '登录失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  onForgetPwd() {
    wx.showToast({
      title: '请联系管理员重置密码',
      icon: 'none'
    });
  },

  viewUserAgreement() {
    // TODO: 跳转到用户协议页面
  },

  viewPrivacyPolicy() {
    // TODO: 跳转到隐私政策页面
  }
}); 