Page({
  data: {
    detail: null,
    isEditing: false,
    detail_images:[]
  },

  onLoad(options) {
    const orderId = options.id;
    this.loadDetail(orderId);
  },

  loadDetail(orderId) {
    wx.showLoading({
      title: '加载中...'
    });

    wx.request({
      url: `http://localhost:5000/api/workorder/detail/${orderId}`,
      method: 'GET',
      success: (res) => {
        if (res.data.success) {
          console.log(res.data.data);
          this.setData({
            detail: res.data.data
          });
        } else {
          wx.showToast({
            title: res.data.message || '加载失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  previewImage_fault(e) {
    const { url } = e.currentTarget.dataset; 
    const { fault_attachments = [] } = this.data.detail.fault_attachments; 
    wx.previewImage({
      current: url, // 当前显示图片的链接
      urls: fault_attachments.map(item => item.file_path)  // 需要预览的图片链接列表
    });
  },

  previewImage_attachments(e) {
    const { url } = e.currentTarget.dataset; 
    const { attachments = [] } = this.data.detail.attachments; 
    wx.previewImage({
      current: url, // 当前显示图片的链接
      urls: attachments.map(item => item.file_path)  // 需要预览的图片链接列表
    });
  },


  handleEdit() {
    this.setData({
      isEditing: true
    });
  },

  handleInput(e) {
    const { field } = e.currentTarget.dataset;
    this.setData({
      [`detail.${field}`]: e.detail.value
    });
  },

  handleComplete() { 
    const { repair_content, repair_result, order_id } = this.data.detail;
    const repair_image = this.data.detail_images
    if (!repair_content || !repair_result) {
      wx.showToast({
        title: '请填写维修内容和结果',
        icon: 'none'
      });
      return;
    } 
    wx.showLoading({ title: '提交中...' });
    wx.request({
      url: `http://localhost:5000/api/workorder/complete/${order_id}`,
      method: 'POST',
      data: {
        repair_content,
        repair_result,
        repair_image
      },
      success: (res) => {
        if (res.data.success) {
          wx.showToast({
            title: '处理完成',
            icon: 'success',
            duration: 1500
          });
          
          // 延迟返回首页，确保用户能看到成功提示
          setTimeout(() => {
            // 返回首页
            wx.switchTab({
              url: 'http://localhost:5000/api/index/index'
            });
          }, 1500);
        } else {
          wx.showToast({
            title: res.data.message || '操作失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      },
      complete: () => {
        wx.hideLoading();
        this.setData({
          isEditing: false
        });
      }
    });
  },

  // 从相册选择图片
  chooseFromAlbum() {
    wx.chooseImage({
      count: 1, // 最多可选择的图片数量
      sizeType: ['compressed'], // 压缩图
      sourceType: ['album'],
      success: (res) => {
        this.setData({
          'detail_images': [...this.data.detail_images, ...res.tempFilePaths]
        });
        console.log('当前图片列表：', this.data.detail_images);
      }
    });
  },

  // 拍照上传
  takePhoto() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['camera'],
      success: (res) => {
        this.setData({
          'detail_images': [...this.data.detail_images, ...res.tempFilePaths]
        });
        console.log('当前图片列表：', this.data.detail_images);
      }
    });
  },

  // 删除图片
  deleteImage(e) {
    const index = e.currentTarget.dataset.index;
    const images = this.data.detail_images;
    images.splice(index, 1);
    this.setData({
      'detail_images': images
    });
    console.log('删除后的图片列表：', this.data.detail_images);
  },

  // 预览图片
  previewImage(e) {
    const current = e.currentTarget.dataset.url;
    wx.previewImage({
      current,
      urls: this.data.detail_images
    });
  },
}); 