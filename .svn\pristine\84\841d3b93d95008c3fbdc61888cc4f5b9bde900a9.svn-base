<template>
	<view class="fault-report-container">
		<!-- 表单内容 -->
		<view class="form-card">
			<view class="form-group"> 
				
				<view class="form-item">
					<text class="form-label required">热用户</text>
					<view class="form-picker-container">
						<picker class="form-picker" @change="handleUnitChange" :value="unitIndex" :range="heatUnits" range-key="name">
							<view class="picker-text" :class="{'placeholder': unitIndex === -1}">
								{{ unitIndex === -1 ? '请选择热用户' : heatUnits[unitIndex].name }}
							</view>
						</picker>
					</view>
				</view>

				<view class="form-item">
					<text class="form-label required">详细住户</text>
					<view class="address-inputs" :class="{'error': addressInputError}">
						<view class="address-input-item" :class="{'error': addressInputError}">
							<input
								class="form-input"
								type="number"
								v-model="buildingNo"
								placeholder="楼号"
								maxlength="2"
							/>
							<text class="address-input-label">号楼</text>
						</view>
						<text class="address-separator">-</text>
						<view class="address-input-item" :class="{'error': addressInputError}">
							<input
								class="form-input"
								type="number"
								v-model="unitNo"
								placeholder="单元"
								maxlength="2"
							/>
							<text class="address-input-label">单元</text>
						</view>
						<text class="address-separator">-</text>
						<view class="address-input-item room-input" :class="{'error': addressInputError}">
							<input
								class="form-input"
								type="number"
								v-model="roomNo"
								placeholder="房号"
								maxlength="4"
								@blur="formatAddressFields"
								@input="updateFormattedAddress"
							/>
							<text class="address-input-label">房号</text>
						</view>
					</view>
					<text class="address-hint">请输入楼号、单元号和房号 (可选)</text>
				
				</view>

				<view class="form-item">
					<text class="form-label required">故障类型</text>
					<view class="form-picker-container">
						<picker class="form-picker" @change="handleTypeChange" :value="typeIndex" :range="faultTypes">
							<view class="picker-text" :class="{'placeholder': typeIndex === -1}">
								{{ typeIndex === -1 ? '请选择故障类型' : faultTypes[typeIndex] }}
							</view>
						</picker>
					</view>
				</view>
				
				<view class="form-item">
					<text class="form-label required">故障等级</text>
					<view class="level-options">
						<view 
							class="level-option" 
							v-for="(level, index) in faultLevels" 
							:key="index"
							:class="{'active': faultLevel === level.value}"
							@click="selectLevel(level.value)"
						>
							<text>{{ level.label }}</text>
						</view>
					</view>
				</view>
				
				<view class="form-item">
					<text class="form-label required">故障描述</text>
					<textarea 
						class="form-textarea" 
						v-model="faultDesc" 
						placeholder="请详细描述故障情况，便于维修人员了解问题" 
						maxlength="200"
					></textarea>
					<text class="textarea-counter">{{ faultDesc.length }}/200</text>
				</view>
			</view>
		</view>
		
		<!-- 图片上传 -->
		<view class="form-card">
			<view class="form-header">
				<text class="form-title">图片附件</text>
				<text class="form-subtitle">上传故障现场图片（最多6张）</text>
			</view>
			
			<view class="upload-area">
				<view class="image-list">
					<view class="image-item" v-for="(image, index) in imageList" :key="index">
						<image :src="image" mode="aspectFill" @click="previewImage(index)"></image>
						<text class="delete-icon" @click="deleteImage(index)">×</text>
					</view>
					
					<view class="upload-item" v-if="imageList.length < 6" @click="chooseImage">
						<text class="iconfont icon-camera"></text>
						<text>上传图片</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 视频上传 -->
		<view class="form-card">
			<view class="form-header">
				<text class="form-title">视频附件</text>
				<text class="form-subtitle">上传故障现场视频（最多1个）</text>
			</view>
			
			<view class="upload-area">
				<view class="video-container" v-if="videoPath">
					<video 
						:src="getVideoUrl()" 
						controls
						object-fit="fill"
						initial-time="0"
						show-fullscreen-btn="true"
						show-play-btn="true"
						show-center-play-btn="true"
						enable-progress-gesture="true"
						auto-pause-if-navigate="true"
						auto-pause-if-open-native="true"
						codec="h264"
						@error="onVideoError"
						@loadedmetadata="onVideoLoad"
					></video>
					<text class="delete-icon" @click="deleteVideo">×</text>
					
					<!-- 如果视频加载有问题，提供在浏览器打开的选项 -->
					<view class="video-actions" v-if="serverVideoPath">
						<view class="video-action-btn" @click="openInBrowser">
							在浏览器中打开
						</view>
					</view>
				</view>
				
				<view class="upload-item" v-if="!videoPath" @click="chooseVideo">
					<text class="iconfont icon-video"></text>
					<text>上传视频</text>
				</view>
			</view>
		</view>
		
		<!-- 发生时间 -->
		<view class="form-card">
			<view class="form-group">
				<view class="form-item">
					<text class="form-label required">发生时间</text>
					<view class="form-picker-container">
						<picker 
							class="form-picker" 
							mode="date" 
							:value="faultDate" 
							:start="startDate" 
							:end="endDate" 
							@change="handleDateChange"
						>
							<view class="picker-text" :class="{'placeholder': !faultDate}">
								{{ faultDate || '请选择日期' }}
							</view>
						</picker>
					</view>
				</view>
				
				<view class="form-item">
					<text class="form-label"></text>
					<view class="form-picker-container">
						<picker 
							class="form-picker" 
							mode="time" 
							:value="faultTime" 
							@change="handleTimeChange"
						>
							<view class="picker-text" :class="{'placeholder': !faultTime}">
								{{ faultTime || '请选择时间' }}
							</view>
						</picker>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 底部提交按钮 -->
		<view class="submit-btn-container">
			<button class="submit-btn" @click="submitReport">确定创建</button>
		</view>
	</view>
</template>

<script>
	import { faultApi, heatUnitApi, dictApi } from '@/utils/api.js';
	import uploadUtils from '@/utils/upload.js';
	
	export default {
		data() {
			const now = new Date();
			const year = now.getFullYear();
			const month = (now.getMonth() + 1).toString().padStart(2, '0');
			const day = now.getDate().toString().padStart(2, '0');
			const hours = now.getHours().toString().padStart(2, '0');
			const minutes = now.getMinutes().toString().padStart(2, '0');
			
			return {
				// 故障类型
				faultTypes: [],
				typeIndex: -1,
				
				// 热力单元 
				heatUnits: [],
				unitIndex: -1,

				// 告警ID 默认为 -1
				alarmId:-1,
				
				// 故障来源
				faultSources: ['用户投诉', '巡检上报'],
				sourceIndex: 0,  // 默认设置为"用户投诉"(索引0)
				
				// 故障等级 提示/一般/重要/严重	
				faultLevels: [
					{ label: '提示', value: '提示' },
					{ label: '一般', value: '一般' },
					{ label: '重要', value: '重要' },
					{ label: '严重', value: '严重' }
				],
				faultLevel: '一般',
				
				// 故障描述
				faultDesc: '',
				
				// 附件
				imageList: [], // 本地临时路径
				serverImageList: [], // 服务器路径
				videoPath: '', // 本地临时路径
				serverVideoPath: '', // 服务器路径
				
				// 发生时间
				faultDate: '',
				faultTime: '',
				startDate: `${year - 1}-${month}-${day}`,
				endDate: `${year}-${month}-${day}`,
				
				// 设备ID（如果从设备详情页跳转过来）
				deviceId: '',
				
				// 加载状态
				isLoading: false,

				// 地址字段
				buildingNo: '',
				unitNo: '',
				roomNo: '',
				formattedAddress: '', // 格式化后的完整地址
				addressInputError: false, // 地址输入错误状态
				
				// 热力单位ID
				heatUnitId: null
			}
		},
		onShow(){
			this.loadFaultTypeOptions();
		},
		async onLoad(options) {
			// 从URL参数中获取热力单位ID（如果有）
			if (options.heatUnitId) {
				this.heatUnitId = options.heatUnitId;
				console.log('从参数接收到热力单位ID:', this.heatUnitId);
			}
			
			// 加载热力单元列表（故障类型通常是固定的）
			try {
				await this.loadHeatUnits();
				console.log('热用户列表加载完毕');
			} catch (loadErr) {
				console.error('onLoad 中加载热用户列表失败:', loadErr);
				// 即使加载失败，也继续处理其他逻辑，可能使用默认列表
			}

			// 设置日期选择器的默认范围和值
			const now = new Date();
			const year = now.getFullYear();
			const month = (now.getMonth() + 1).toString().padStart(2, '0');
			const day = now.getDate().toString().padStart(2, '0');
			const hours = now.getHours().toString().padStart(2, '0');
			const minutes = now.getMinutes().toString().padStart(2, '0');
			
			this.startDate = `${year - 1}-${month}-${day}`; // 开始日期设为一年前
			this.endDate = `${year}-${month}-${day}`;     // 结束日期设为今天
			// 默认发生时间设为当前时间
			if (!this.faultDate) this.faultDate = `${year}-${month}-${day}`;
			if (!this.faultTime) this.faultTime = `${hours}:${minutes}`;

			// 获取路由参数
			if (options.deviceId) {
				this.deviceId = options.deviceId;
				console.log('从设备详情页跳转, 设备ID:', this.deviceId);
			}

			// --- 处理从告警消息传递过来的数据 ---
			if (options.alarmData) { 

				console.log('接收到告警传递的数据（原始）:', options.alarmData);
				try {
					const alarmInfo = JSON.parse(decodeURIComponent(options.alarmData));
					console.log('解析后的告警数据:', alarmInfo);

					// 预填故障来源
					const sourceIdx = this.faultSources.findIndex(source => source === '系统检测');
					if (sourceIdx > -1) this.sourceIndex = sourceIdx;

					// 预填故障类型
					const typeIdx = this.faultTypes.findIndex(type => type === '设备故障');
					if (typeIdx > -1) this.typeIndex = typeIdx;

					// 预填故障描述
					if (alarmInfo.faultDesc) this.faultDesc = alarmInfo.faultDesc;

					// 预填发生时间
					if (alarmInfo.occurTime) {
						const dateTimeParts = alarmInfo.occurTime.split(' ');
						if (dateTimeParts.length === 2) {
							this.faultDate = dateTimeParts[0];
							this.faultTime = dateTimeParts[1];
						} else {
							console.warn('无法解析告警传递的 occurTime 格式:', alarmInfo.occurTime);
						}
					} 
					if (alarmInfo.faultLevel) {
						// 这里的 faultLevels 是 data 中定义的选项数组: [{label: '提示', value: '提示'}, ...]
						// 注意：确保 alarmInfo.faultLevel 的值（例如 '一般'）与 faultLevels 数组中某一项的 value 匹配
						const levelObj = this.faultLevels.find(level => level.value === alarmInfo.faultLevel);
						if (levelObj) {
							this.faultLevel = levelObj.value; // 设置 data 中的 faultLevel 为告警传递过来的值
							console.log(`已根据告警信息预选故障等级: ${this.faultLevel}`);
						} else {
							// 如果后端传来的值在前端选项里找不到，使用默认值
							console.warn(`告警传递的故障等级 '${alarmInfo.faultLevel}' 在选项中未找到，使用默认值 '一般'`);
							this.faultLevel = '一般'; // 确保 faultLevel 有一个有效值
						}
					} else {
						// 如果告警信息没提供等级，也设置一个默认值
						this.faultLevel = '一般';
					} 

					// 预填热用户 (优先使用热力单位ID)
					if (alarmInfo.heatUnitId) {
						this.heatUnitId = alarmInfo.heatUnitId;
					} else if (alarmInfo.heatUnitName) {
						this.selectUnitByName(alarmInfo.heatUnitName);
					}

				} catch (e) {
					console.error('解析告警传递数据失败:', e);
				}
			}  
		},
		methods: {
			// 加载热用户列表
			async loadHeatUnits() {
				this.isLoading = true;
				// 使用getList方法获取热力单位列表
				return heatUnitApi.getList() 
					.then(res => {
						console.log('热用户列表接口响应:', res);
						if (res.code === 200 && res.data) {
							this.heatUnits = res.data;
							
							// 如果有热力单位列表
							if (this.heatUnits.length > 0) {
								// 如果传入了heatUnitId参数，尝试预选对应的热力单位
								if (this.heatUnitId) {
									const index = this.heatUnits.findIndex(unit => unit.id === this.heatUnitId);
									if (index > -1) {
										this.unitIndex = index;
										console.log('根据传入的热力单位ID预选:', this.heatUnits[index].name);
									} else {
										// 如果没找到匹配的，默认选择第一个热力单位
										this.unitIndex = 0;
										console.log('未找到匹配的热力单位，默认选择第一个:', this.heatUnits[0].name);
									}
								} else {
									// 如果没有传入指定ID，默认选择第一个热力单位
									this.unitIndex = 0;
									console.log('默认选择第一个热力单位:', this.heatUnits[0].name);
								}
							}
						} else {
							this.heatUnits = [];
						}
					})
					.catch(err => {
						console.error('获取热用户列表失败:', err);
						this.heatUnits = [];
					})
					.finally(() => {
						this.isLoading = false;
					});
			},
			
			// 加载故障类型选项
			loadFaultTypeOptions() {
				dictApi.getDictDataByDictId(5)
					.then(res => {
						if (res.code === 200 && res.data) {
							console.log(res.data)
							this.faultTypes = res.data.map(item=>item.name);
							
						}
					})
					.catch(err => {
						console.error('获取巡检类型选项失败:', err);
					});
			},
			// 故障类型选择
			handleTypeChange(e) {
				this.typeIndex = e.detail.value;
			},
			
			// 热用户选择
			handleUnitChange(e) {
				this.unitIndex = e.detail.value;
			},
			
			// 故障来源选择
			handleSourceChange(e) {
				this.sourceIndex = e.detail.value;
			},
			
			// 故障等级选择
			selectLevel(level) {
				this.faultLevel = level;
			},
			
			// 日期选择
			handleDateChange(e) {
				this.faultDate = e.detail.value;
			},
			
			// 时间选择
			handleTimeChange(e) {
				this.faultTime = e.detail.value;
			},
			
			// 选择图片
			chooseImage() {
				uni.chooseImage({
					count: 6 - this.imageList.length,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						// 显示上传中提示
						uni.showLoading({
							title: '上传中...',
							mask: true
						});
						
						// 检查token是否存在，不存在则提示用户登录
						const token = uni.getStorageSync('token');
						if (!token) {
							uni.hideLoading();
							uni.showToast({
								title: '请先登录',
								icon: 'none'
							});
							return;
						}
						
						// 逐个上传图片
						const uploadPromises = res.tempFilePaths.map(path => {
							return uploadUtils.uploadImage(path)
								.then(serverPath => {
									// 保存本地路径和服务器路径
									this.imageList.push(path);
									this.serverImageList.push(serverPath);
									return serverPath;
								});
						});
						
						// 等待所有图片上传完成
						Promise.all(uploadPromises)
							.then(() => {
								uni.hideLoading();
								uni.showToast({
									title: '上传成功',
									icon: 'success'
								});
							})
							.catch(err => {
								uni.hideLoading();
								uni.showToast({
									title: err.message || '上传失败',
									icon: 'none'
								});
							});
					}
				});
			},
			
			// 预览图片
			previewImage(index) {
				// 将本地路径转换为可访问的URL
				const previewUrls = this.imageList.map((path, i) => {
					// 如果有对应的服务器路径，使用服务器路径构建完整URL
					if (this.serverImageList[i]) {
						return uploadUtils.getFileUrl(this.serverImageList[i]);
					}
					return path;
				});
				
				uni.previewImage({
					urls: previewUrls,
					current: previewUrls[index]
				});
			},
			
			// 删除图片
			deleteImage(index) {
				this.imageList.splice(index, 1);
				this.serverImageList.splice(index, 1);
			},
			
			// 选择视频
			chooseVideo() {
				uni.chooseVideo({
					sourceType: ['album', 'camera'],
					maxDuration: 60,
					camera: 'back',
					success: (res) => {
						// 显示上传中提示
						uni.showLoading({
							title: '上传中...',
							mask: true
						});
						
						// 检查token是否存在，不存在则提示用户登录
						const token = uni.getStorageSync('token');
						if (!token) {
							uni.hideLoading();
							uni.showToast({
								title: '请先登录',
								icon: 'none'
							});
							return;
						}
						
						// 上传视频到服务器
						uploadUtils.uploadVideo(res.tempFilePath)
							.then(serverPath => {
								// 保存本地路径和服务器路径
								this.videoPath = res.tempFilePath;
								this.serverVideoPath = serverPath;
								
								uni.hideLoading();
								uni.showToast({
									title: '上传成功',
									icon: 'success'
								});
							})
							.catch(err => {
								uni.hideLoading();
								uni.showToast({
									title: err.message || '上传失败',
									icon: 'none'
								});
							});
					}
				});
			},
			
			// 获取视频URL
			getVideoUrl() {
				// 特殊处理以@开头的URL
				if (this.serverVideoPath && this.serverVideoPath.startsWith('@http')) {
					const cleanUrl = this.serverVideoPath.substring(1); // 去除@符号
					console.log('视频路径以@http开头，清理后:', cleanUrl);
					return cleanUrl;
				}
				
				// 如果是服务器路径，使用工具方法获取完整URL
				if (this.serverVideoPath) {
					// 确保它真的是一个http URL
					if (this.serverVideoPath.startsWith('http')) {
						return this.serverVideoPath;
					}
					const url = uploadUtils.getFileUrl(this.serverVideoPath);
					console.log('处理后的视频URL:', url);
					return url;
				}
				
				// 否则返回本地临时路径
				console.log('使用本地视频路径:', this.videoPath);
				return this.videoPath;
			},
			
			// 在浏览器中打开视频
			openInBrowser() {
				let url = this.getVideoUrl();
				
				// 检查URL是否有效
				if (!url) {
					uni.showToast({
						title: '视频URL无效',
						icon: 'none'
					});
					return;
				}
				
				console.log('在浏览器中打开视频:', url);
				
				// 使用系统浏览器打开
				plus.runtime.openURL(url, (err) => {
					if (err) {
						console.error('打开浏览器失败:', err);
						uni.showToast({
							title: '打开浏览器失败',
							icon: 'none'
						});
					}
				});
			},
			
			// 视频加载错误处理
			onVideoError(e) {
				console.error('视频加载失败:', e.detail);
				console.error('视频路径:', this.videoPath);
				console.error('服务器视频路径:', this.serverVideoPath);
				console.error('处理后URL:', this.getVideoUrl());
				
				uni.showToast({
					title: '视频加载失败，请尝试其他方式查看',
					icon: 'none',
					duration: 2000
				});
			},
			
			// 视频加载成功
			onVideoLoad(e) {
				console.log('视频加载成功, 路径:', this.videoPath);
				console.log('视频元数据:', e.detail);
			},
			
			// 删除视频
			deleteVideo() {
				this.videoPath = '';
				this.serverVideoPath = '';
			},
			
			// 上传附件
			uploadAttachments() {
				return new Promise((resolve) => {
					const attachments = [];
					
					// 添加已上传的图片
					this.serverImageList.forEach((serverPath, index) => {
						attachments.push({
							file_type: 'image',
							file_path: serverPath
						});
					});
					
					// 添加已上传的视频
					if (this.serverVideoPath) {
						attachments.push({
							file_type: 'video',
							file_path: this.serverVideoPath
						});
					}
					
					// 直接返回已上传的附件列表
					resolve(attachments);
				});
			},
			
			// 表单验证
			validateForm() {
				if (this.typeIndex === -1) {
					uni.showToast({
						title: '请选择故障类型',
						icon: 'none'
					});
					return false;
				}
				
				if (this.unitIndex === -1) {
					uni.showToast({
						title: '请选择热用户',
						icon: 'none'
					});
					return false;
				}
				
				// 检查地址字段，如果填写了任何一个地址字段，则其他字段也必须填写
				if ((this.buildingNo || this.unitNo || this.roomNo) &&
					!(this.buildingNo && this.unitNo && this.roomNo)) {
					uni.showToast({
						title: '请完整填写楼号、单元号和房号',
						icon: 'none',
						duration: 3000
					});
					return false;
				}
				
				if (!this.faultLevel) {
					uni.showToast({
						title: '请选择故障等级',
						icon: 'none'
					});
					return false;
				}
				
				if (!this.faultDesc.trim()) {
					uni.showToast({
						title: '请填写故障描述',
						icon: 'none'
					});
					return false;
				}
				
				if (!this.faultDate || !this.faultTime) {
					uni.showToast({
						title: '请选择故障发生时间',
						icon: 'none'
					});
					return false;
				}
				
				return true;
			},
			
			// 提交故障上报
			async submitReport() {
				if (!this.validateForm()) return;
				
				// 显示加载提示
				uni.showLoading({
					title: '正在提交...'
				});
				
				try {
					// 上传附件
					const attachments = await this.uploadAttachments(); 
					
					// 获取用户ID
					const userId = uni.getStorageSync('userId') || 1; 
					
					// 确保热力单位ID存在
					if (this.unitIndex === -1 || !this.heatUnits[this.unitIndex]) {
						uni.hideLoading();
						uni.showToast({
							title: '请选择热用户',
							icon: 'none'
						});
						return;
					}
					
					// 在提交前更新一次格式化地址
					this.updateFormattedAddress();
					
					// 构建上报数据（按照API文档要求）
					const reportData = {
						heat_unit_id: this.heatUnits[this.unitIndex].id,
						alarm_id: this.alarmId,
						fault_type: this.faultTypes[this.typeIndex],
						fault_level: this.faultLevel,
						fault_desc: this.faultDesc,
						fault_source: this.faultSources[this.sourceIndex],
						occur_time: `${this.faultDate} ${this.faultTime}:00`,
						report_user_id: userId,
						address: this.formattedAddress,
						attachment: attachments,
						fault_status:"已确认"
					};
					
					console.log('提交故障问题并创建工单:', reportData);
					
					// 调用API提交故障上报
					faultApi.reportFault(reportData)
						.then(res => {
							if (res.code === 200) {
								// 隐藏加载提示
								uni.hideLoading();
								
								// 显示提交成功
								uni.showToast({
									title: '创建成功',
									icon: 'success'
								});
								setTimeout(() => {
									uni.navigateBack();
								}, 1500);
							
							} else {
								throw new Error(res.message || '创建失败');
							}
						})
						.catch(err => {
							uni.hideLoading();
							
							// 检查错误信息是否包含住户相关的错误
							const errorMsg = err.message || '网络异常，请稍后重试';
							const isHouseError = errorMsg.includes('未找到住户') || 
												errorMsg.includes('房间号格式') || 
												errorMsg.includes('楼号') || 
												errorMsg.includes('单元号') || 
												errorMsg.includes('房号');
							
							// 如果是住户相关错误，显示特殊提示并高亮输入框
							if (isHouseError) {
								// 高亮显示地址输入框
								this.highlightAddressInputs();
								
								// 显示详细错误提示
								uni.showModal({
									title: '住户信息错误',
									content: errorMsg,
									showCancel: false,
									confirmText: '我知道了'
								});
							} else {
								// 其他错误正常显示
								uni.showToast({
									title: errorMsg,
									icon: 'none',
									duration: 3000
								});
							}
						});
				} catch (error) {
					uni.hideLoading();
					uni.showToast({
						title: '提交过程中出错，请重试',
						icon: 'none'
					});
				}
			},
			
			// ----> 新增：尝试预选热用户 <---- -->
			selectUnitByName(unitName) {
				// 去除空格
				const unitNameTrim = unitName.trim(); 
				if (!this.heatUnits || !Array.isArray(this.heatUnits)) {
					console.error('selectUnitByName 调用时 heatUnits 列表无效');
					return;
				}
				const index = this.heatUnits.findIndex(unit => unit.name === unitNameTrim);
				if (index > -1) {
					this.unitIndex = index;
					console.log(`已根据名称 '${unitName}' 预选热用户，索引: ${index}`);
				} else {
					console.warn(`未在热用户列表中找到名称为 '${unitName}' 的项`);
					
					// 如果只有一个热力单位，自动选择它
					if (this.heatUnits.length === 1) {
						this.unitIndex = 0;
						console.log(`未找到名称匹配的热用户，但只有一个选项，已自动选择: ${this.heatUnits[0].name}`);
					} else {
						this.unitIndex = -1;
					}
				}
			},
			// ----> 新增结束 <---- -->
			
			// 处理地址输入框获取焦点
			onAddressFocus() {
				// 不再强制滚动页面
				// 原代码会导致页面突然向上滚动，影响用户体验
				// setTimeout(() => {
				// 	uni.pageScrollTo({
				// 		scrollTop: 200, // 大致位置，根据实际页面布局调整
				// 		duration: 300
				// 	});
				// }, 300);
			},
			formatAddressFields() {
				// 如果用户输入了楼号，确保它是两位数
				if (this.buildingNo) {
					// 确保它是数字
					this.buildingNo = this.buildingNo.toString().replace(/\D/g, '');
					// 限制长度
					if (this.buildingNo.length > 2) {
						this.buildingNo = this.buildingNo.substring(0, 2);
					}
					// 自动补零（只在UI上显示补零，实际值保持不变）
					if (this.buildingNo.length === 1) {
						console.log('楼号已格式化:', this.buildingNo);
					}
				}
				
				// 如果用户输入了单元号，确保它是两位数
				if (this.unitNo) {
					// 确保它是数字
					this.unitNo = this.unitNo.toString().replace(/\D/g, '');
					// 限制长度
					if (this.unitNo.length > 2) {
						this.unitNo = this.unitNo.substring(0, 2);
					}
					// 自动补零（只在UI上显示补零，实际值保持不变）
					if (this.unitNo.length === 1) {
						console.log('单元号已格式化:', this.unitNo);
					}
				}
				
				// 如果用户输入了房号，确保它是四位数
				if (this.roomNo) {
					// 确保它是数字
					this.roomNo = this.roomNo.toString().replace(/\D/g, '');
					// 限制长度
					if (this.roomNo.length > 4) {
						this.roomNo = this.roomNo.substring(0, 4);
					}
					// 自动补零（只在UI上显示补零，实际值保持不变）
					if (this.roomNo.length < 4) {
						console.log('房号已格式化:', this.roomNo);
					}
				}
				
				// 如果用户填写了任何一个字段，提示填写所有字段
				if ((this.buildingNo || this.unitNo || this.roomNo) && 
					!(this.buildingNo && this.unitNo && this.roomNo)) {
					// 如果不是所有字段都已填写，显示提示
					uni.showToast({
						title: '请完整填写楼号、单元号和房号',
						icon: 'none',
						duration: 2000
					});
				}
			},
			updateFormattedAddress() {
				// 只有当所有三个字段都有值时才更新格式化地址
				if (this.buildingNo && this.unitNo && this.roomNo) {
					// 格式化为 xx-xx-xxxx
					const paddedBuildingNo = this.buildingNo;
					const paddedUnitNo = this.unitNo;
					const paddedRoomNo = this.roomNo.padStart(4, '0');
					this.formattedAddress = `${paddedBuildingNo}-${paddedUnitNo}-${paddedRoomNo}`;
				} else {
					this.formattedAddress = '';
				}
			},
			highlightAddressInputs() {
				// 添加错误样式类
				this.addressInputError = true;
				
				// 3秒后自动移除错误样式
				setTimeout(() => {
					this.addressInputError = false;
				}, 3000);
			}
		}
	}
</script>

<style lang="scss">
	.fault-report-container {
		padding: 20rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
		padding-bottom: 120rpx;
	}
	
	.form-card {
		background-color: #fff;
		border-radius: 12rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
	}
	
	.form-header {
		margin-bottom: 20rpx;
		
		.form-title {
			font-size: 30rpx;
			font-weight: bold;
			color: $uni-text-color;
			margin-bottom: 8rpx;
			display: block;
		}
		
		.form-subtitle {
			font-size: 24rpx;
			color: $uni-text-color-grey;
		}
	}
	
	.form-group {
		.form-item {
			margin-bottom: 24rpx;
			
			&:last-child {
				margin-bottom: 0;
			}
			
			.form-label {
				font-size: 28rpx;
				color: $uni-text-color;
				margin-bottom: 12rpx;
				display: block;
				
				&.required::before {
					content: '*';
					color: $uni-color-error;
					margin-right: 4rpx;
				}
			}
			
			.form-input-container {
				position: relative;
				width: 100%;
				background-color: #f8f8f8;
				border-radius: 8rpx;
				border: 1rpx solid #e5e5e5;
				transition: background-color 0.2s;
				
				&:active {
					background-color: #f0f0f0;
				}
			}
			
			.form-picker-container {
				background-color: #f8f8f8;
				padding: 20rpx;
				border-radius: 8rpx;
				border: 1rpx solid #e5e5e5;
				position: relative;
				overflow: hidden;
				transition: background-color 0.2s;
				
				&:active {
					background-color: #f0f0f0;
				}
				
				&::after {
					content: '';
					position: absolute;
					right: 20rpx;
					top: 50%;
					transform: translateY(-50%);
					width: 0;
					height: 0;
					border-left: 12rpx solid transparent;
					border-right: 12rpx solid transparent;
					border-top: 12rpx solid #999;
					pointer-events: none;
				}
			}
			
			.form-picker {
				width: 100%;
				height: 100%;
				border: none;
				background: transparent;
				
				.picker-text {
					font-size: 28rpx;
					color: $uni-text-color;
					padding-right: 40rpx;
					
					&.placeholder {
						color: $uni-text-color-placeholder;
					}
				}
			}
			
			.form-value {
				font-size: 28rpx;
				color: $uni-text-color;
				padding: 20rpx;
				background-color: #f8f8f8;
				border-radius: 8rpx;
			}
			
			.level-options {
				display: flex;
				justify-content: space-between;
				
				.level-option {
					flex: 1;
					height: 80rpx;
					display: flex;
					justify-content: center;
					align-items: center;
					margin: 0 10rpx;
					background-color: #f8f8f8;
					border-radius: 8rpx;
					font-size: 28rpx;
					color: $uni-text-color;
					
					&:first-child {
						margin-left: 0;
					}
					
					&:last-child {
						margin-right: 0;
					}
					
					&.active {
						background-color: $uni-color-primary;
						color: #fff;
					}
				}
			}
			
			.form-textarea {
				width: 100%;
				height: 200rpx;
				padding: 20rpx;
				box-sizing: border-box;
				background-color: #f8f8f8;
				border-radius: 8rpx;
				font-size: 28rpx;
				position: relative;
			}
			
			.textarea-counter {
				position: absolute;
				right: 30rpx;
				margin-top: 8rpx;
				font-size: 24rpx;
				color: $uni-text-color-grey;
			}
		}
	}
	
	.upload-area {
		.image-list {
			display: flex;
			flex-wrap: wrap;
			
			.image-item, .upload-item {
				width: 200rpx;
				height: 200rpx;
				margin-right: 20rpx;
				margin-bottom: 20rpx;
				position: relative;
			}
			
			.image-item {
				image {
					width: 100%;
					height: 100%;
					border-radius: 8rpx;
				}
				
				.delete-icon {
					position: absolute;
					top: -16rpx;
					right: -16rpx;
					width: 40rpx;
					height: 40rpx;
					background-color: rgba(0, 0, 0, 0.5);
					color: #fff;
					border-radius: 50%;
					display: flex;
					justify-content: center;
					align-items: center;
					font-size: 32rpx;
				}
			}
			
			.upload-item {
				border: 2rpx dashed #ddd;
				border-radius: 8rpx;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				
				.iconfont {
					font-size: 60rpx;
					color: $uni-text-color-grey;
					margin-bottom: 16rpx;
				}
				
				text {
					font-size: 24rpx;
					color: $uni-text-color-grey;
				}
			}
		}
		
		.video-container {
			width: 100%;
			height: 400rpx;
			position: relative;
			
			video {
				width: 100%;
				height: 100%;
				border-radius: 8rpx;
			}
			
			.delete-icon {
				position: absolute;
				top: 16rpx;
				right: 16rpx;
				width: 40rpx;
				height: 40rpx;
				background-color: rgba(0, 0, 0, 0.5);
				color: #fff;
				border-radius: 50%;
				display: flex;
				justify-content: center;
				align-items: center;
				font-size: 32rpx;
			}
			
			.video-actions {
				margin-top: 20rpx;
				display: flex;
				justify-content: center;
				
				.video-action-btn {
					background-color: #f5f5f5;
					color: $uni-text-color;
					padding: 10rpx 30rpx;
					border-radius: 30rpx;
					font-size: 24rpx;
					box-shadow: 0 2rpx 5rpx rgba(0,0,0,0.1);
				}
			}
		}
	}
	
	.submit-btn-container {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		padding: 20rpx;
		background-color: #fff;
		box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);
		
		.submit-btn {
			width: 100%;
			height: 88rpx;
			line-height: 88rpx;
			background-color: $uni-color-primary;
			color: #fff;
			font-size: 32rpx;
			border-radius: 8rpx;
		}
	}
	
	.form-input {
		background-color: transparent;
		padding: 20rpx;
		width: 100%;
		height: 80rpx;
		box-sizing: border-box;
		font-size: 28rpx;
		color: $uni-text-color;
		border: none;
	}
	
	.form-input-container {
		position: relative;
		width: 100%;
		background-color: #f8f8f8;
		border-radius: 8rpx;
		border: 1rpx solid #e5e5e5;
		transition: background-color 0.2s;
		
		&:active {
			background-color: #f0f0f0;
		}
	}
	
	.address-inputs {
		display: flex;
		align-items: center;
		margin-bottom: 10rpx;
		
		&.error {
			border-color: #fa436a;
			background-color: #fff0f0;
			animation: shake 0.5s ease-in-out;
		}
	}
	
	.address-input-item {
		position: relative;
		background-color: #f8f8f8;
		border-radius: 8rpx;
		border: 1rpx solid #e5e5e5;
		flex: 1;
		padding-right: 60rpx; // 为标签留出空间
		
		&.error {
			border-color: #fa436a;
			background-color: #fff0f0;
			animation: shake 0.5s ease-in-out;
		}
		
		.form-input {
			height: 80rpx;
			padding: 0 20rpx;
			font-size: 28rpx;
		}
		
		.address-input-label {
			position: absolute;
			right: 20rpx;
			top: 50%;
			transform: translateY(-50%);
			font-size: 24rpx;
			color: #909399;
		}
	}
	
	.room-input {
		flex: 1.5; // 房号输入框稍宽一些
	}
	
	.address-separator {
		padding: 0 10rpx;
		color: #909399;
		font-size: 30rpx;
		font-weight: bold;
	}
	
	.address-hint {
		font-size: 24rpx;
		color: #909399;
		margin-top: 6rpx;
		margin-left: 10rpx;
	}
	
	@keyframes shake {
		0%, 100% { transform: translateX(0); }
		20%, 60% { transform: translateX(-5px); }
		40%, 80% { transform: translateX(5px); }
	}
</style> 