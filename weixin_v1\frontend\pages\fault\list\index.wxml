<view class="container">
  <!-- 筛选栏 -->
  <view class="filter-bar">
    <!-- 状态筛选按钮 -->
    <view class="status-filter">
      <view class="filter-btn {{currentStatus === '' ? 'active' : ''}}" 
            bindtap="filterByStatus" data-status="">全部</view>
      <view class="filter-btn {{currentStatus === '待确认' ? 'active' : ''}}" 
            bindtap="filterByStatus" data-status="待确认">待确认</view>
      <view class="filter-btn {{currentStatus === '已确认' ? 'active' : ''}}" 
            bindtap="filterByStatus" data-status="已确认">已确认</view>
      <view class="filter-btn {{currentStatus === '已退回' ? 'active' : ''}}" 
            bindtap="filterByStatus" data-status="已退回">已退回</view>
    </view>
    <!-- 日期筛选 -->
    <picker mode="date" bindchange="onDateChange">
      <view class="date-picker">
        <text>{{selectedDate || '选择日期'}}</text>
      </view>
    </picker>
  </view>

  <!-- 故障列表 -->
  <view class="fault-list">
    <view class="fault-item" 
          wx:for="{{faults}}" 
          wx:key="fault_id"
          bindtap="goToDetail"
          data-id="{{item.fault_id}}">
      <view class="fault-header">
        <text class="fault-no">故障编号：{{item.fault_id}}</text>
        <text class="status {{item.status}}">{{item.fault_status}}</text>
      </view>
      
      <view class="fault-info">
        <view class="info-item">
          <text class="label">换热站：</text>
          <text class="value">{{item.station_name}}</text>
        </view>
        <view class="info-item">
          <text class="label">故障类型：</text>
          <text class="value">{{item.fault_type}}</text>
        </view>
        <view class="info-item">
          <text class="label">故障等级：</text>
          <text class="level-{{item.fault_level}}">{{item.fault_level === 1 ? '一般' : item.fault_level === 2 ? '重要' : '紧急'}}
          </text>
        </view>
        <view class="info-item">
          <text class="label">发生时间：</text>
          <text class="value">{{item.occur_time}}</text>
        </view>
        <view class="info-item">
          <text class="label">上报人员：</text>
          <text class="value">{{item.report_user_name}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 无数据提示 -->
  <view class="empty-tip" wx:if="{{faults.length === 0}}">
    暂无故障记录
  </view>
</view> 