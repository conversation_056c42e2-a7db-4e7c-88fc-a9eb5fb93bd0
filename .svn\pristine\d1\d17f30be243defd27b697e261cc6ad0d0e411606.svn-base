/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.message-settings-container {
  background-color: #f5f7fa;
  min-height: 100vh;
  padding-bottom: 1.25rem;
}
.page-header {
  background-color: #fff;
  padding: 0.9375rem;
  border-bottom: 0.03125rem solid #eee;
}
.page-header .page-title {
  font-size: 1.125rem;
  font-weight: bold;
  color: #333;
}
.settings-card {
  margin: 0.9375rem;
  background-color: #fff;
  border-radius: 0.375rem;
  box-shadow: 0 0.1875rem 0.625rem rgba(0, 0, 0, 0.05);
  overflow: hidden;
}
.settings-card .settings-section {
  padding: 0 0.625rem;
}
.settings-card .settings-section .section-title {
  font-size: 0.9375rem;
  font-weight: bold;
  padding: 0.9375rem 0.3125rem 0.625rem;
  color: #333;
  position: relative;
  display: block;
  width: 100%;
  box-sizing: border-box;
  padding-left: 0.625rem;
}
.settings-card .settings-section .section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 0.1875rem;
  height: 0.9375rem;
  background-color: #1890ff;
  border-radius: 0.09375rem;
}
.settings-card .settings-section .setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0.3125rem;
  border-bottom: 0.03125rem solid rgba(0, 0, 0, 0.05);
}
.settings-card .settings-section .setting-item:last-child {
  border-bottom: none;
}
.settings-card .settings-section .setting-item .setting-info {
  flex: 1;
}
.settings-card .settings-section .setting-item .setting-info .setting-name {
  font-size: 0.875rem;
  color: #333;
  margin-bottom: 0.25rem;
  display: block;
}
.settings-card .settings-section .setting-item .setting-info .setting-desc {
  font-size: 0.75rem;
  color: #999;
  display: block;
}
.settings-card .settings-section .setting-item uni-switch {
  transform: scale(0.8);
  transform-origin: right center;
  margin-left: 0.625rem;
  /* 修复在某些机型上的显示问题 */
  flex-shrink: 0;
  min-width: 2.5rem;
}
.note-text {
  font-size: 0.75rem;
  color: #999;
  padding: 0 1.25rem;
  margin-top: 0.3125rem;
}
.submit-btn {
  margin: 1.875rem 0.9375rem;
  height: 2.8125rem;
  line-height: 2.8125rem;
  background-color: #1890ff;
  color: #fff;
  text-align: center;
  border-radius: 0.375rem;
  font-size: 1rem;
  box-shadow: 0 0.1875rem 0.625rem rgba(24, 144, 255, 0.3);
  font-weight: bold;
}
.submit-btn:active {
  opacity: 0.9;
  transform: translateY(0.0625rem);
}