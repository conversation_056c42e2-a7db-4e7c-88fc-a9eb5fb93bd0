/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uniui-cart-filled[data-v-946bce22]:before {
  content: "\e6d0";
}
.uniui-gift-filled[data-v-946bce22]:before {
  content: "\e6c4";
}
.uniui-color[data-v-946bce22]:before {
  content: "\e6cf";
}
.uniui-wallet[data-v-946bce22]:before {
  content: "\e6b1";
}
.uniui-settings-filled[data-v-946bce22]:before {
  content: "\e6ce";
}
.uniui-auth-filled[data-v-946bce22]:before {
  content: "\e6cc";
}
.uniui-shop-filled[data-v-946bce22]:before {
  content: "\e6cd";
}
.uniui-staff-filled[data-v-946bce22]:before {
  content: "\e6cb";
}
.uniui-vip-filled[data-v-946bce22]:before {
  content: "\e6c6";
}
.uniui-plus-filled[data-v-946bce22]:before {
  content: "\e6c7";
}
.uniui-folder-add-filled[data-v-946bce22]:before {
  content: "\e6c8";
}
.uniui-color-filled[data-v-946bce22]:before {
  content: "\e6c9";
}
.uniui-tune-filled[data-v-946bce22]:before {
  content: "\e6ca";
}
.uniui-calendar-filled[data-v-946bce22]:before {
  content: "\e6c0";
}
.uniui-notification-filled[data-v-946bce22]:before {
  content: "\e6c1";
}
.uniui-wallet-filled[data-v-946bce22]:before {
  content: "\e6c2";
}
.uniui-medal-filled[data-v-946bce22]:before {
  content: "\e6c3";
}
.uniui-fire-filled[data-v-946bce22]:before {
  content: "\e6c5";
}
.uniui-refreshempty[data-v-946bce22]:before {
  content: "\e6bf";
}
.uniui-location-filled[data-v-946bce22]:before {
  content: "\e6af";
}
.uniui-person-filled[data-v-946bce22]:before {
  content: "\e69d";
}
.uniui-personadd-filled[data-v-946bce22]:before {
  content: "\e698";
}
.uniui-arrowthinleft[data-v-946bce22]:before {
  content: "\e6d2";
}
.uniui-arrowthinup[data-v-946bce22]:before {
  content: "\e6d3";
}
.uniui-arrowthindown[data-v-946bce22]:before {
  content: "\e6d4";
}
.uniui-back[data-v-946bce22]:before {
  content: "\e6b9";
}
.uniui-forward[data-v-946bce22]:before {
  content: "\e6ba";
}
.uniui-arrow-right[data-v-946bce22]:before {
  content: "\e6bb";
}
.uniui-arrow-left[data-v-946bce22]:before {
  content: "\e6bc";
}
.uniui-arrow-up[data-v-946bce22]:before {
  content: "\e6bd";
}
.uniui-arrow-down[data-v-946bce22]:before {
  content: "\e6be";
}
.uniui-arrowthinright[data-v-946bce22]:before {
  content: "\e6d1";
}
.uniui-down[data-v-946bce22]:before {
  content: "\e6b8";
}
.uniui-bottom[data-v-946bce22]:before {
  content: "\e6b8";
}
.uniui-arrowright[data-v-946bce22]:before {
  content: "\e6d5";
}
.uniui-right[data-v-946bce22]:before {
  content: "\e6b5";
}
.uniui-up[data-v-946bce22]:before {
  content: "\e6b6";
}
.uniui-top[data-v-946bce22]:before {
  content: "\e6b6";
}
.uniui-left[data-v-946bce22]:before {
  content: "\e6b7";
}
.uniui-arrowup[data-v-946bce22]:before {
  content: "\e6d6";
}
.uniui-eye[data-v-946bce22]:before {
  content: "\e651";
}
.uniui-eye-filled[data-v-946bce22]:before {
  content: "\e66a";
}
.uniui-eye-slash[data-v-946bce22]:before {
  content: "\e6b3";
}
.uniui-eye-slash-filled[data-v-946bce22]:before {
  content: "\e6b4";
}
.uniui-info-filled[data-v-946bce22]:before {
  content: "\e649";
}
.uniui-reload[data-v-946bce22]:before {
  content: "\e6b2";
}
.uniui-micoff-filled[data-v-946bce22]:before {
  content: "\e6b0";
}
.uniui-map-pin-ellipse[data-v-946bce22]:before {
  content: "\e6ac";
}
.uniui-map-pin[data-v-946bce22]:before {
  content: "\e6ad";
}
.uniui-location[data-v-946bce22]:before {
  content: "\e6ae";
}
.uniui-starhalf[data-v-946bce22]:before {
  content: "\e683";
}
.uniui-star[data-v-946bce22]:before {
  content: "\e688";
}
.uniui-star-filled[data-v-946bce22]:before {
  content: "\e68f";
}
.uniui-calendar[data-v-946bce22]:before {
  content: "\e6a0";
}
.uniui-fire[data-v-946bce22]:before {
  content: "\e6a1";
}
.uniui-medal[data-v-946bce22]:before {
  content: "\e6a2";
}
.uniui-font[data-v-946bce22]:before {
  content: "\e6a3";
}
.uniui-gift[data-v-946bce22]:before {
  content: "\e6a4";
}
.uniui-link[data-v-946bce22]:before {
  content: "\e6a5";
}
.uniui-notification[data-v-946bce22]:before {
  content: "\e6a6";
}
.uniui-staff[data-v-946bce22]:before {
  content: "\e6a7";
}
.uniui-vip[data-v-946bce22]:before {
  content: "\e6a8";
}
.uniui-folder-add[data-v-946bce22]:before {
  content: "\e6a9";
}
.uniui-tune[data-v-946bce22]:before {
  content: "\e6aa";
}
.uniui-auth[data-v-946bce22]:before {
  content: "\e6ab";
}
.uniui-person[data-v-946bce22]:before {
  content: "\e699";
}
.uniui-email-filled[data-v-946bce22]:before {
  content: "\e69a";
}
.uniui-phone-filled[data-v-946bce22]:before {
  content: "\e69b";
}
.uniui-phone[data-v-946bce22]:before {
  content: "\e69c";
}
.uniui-email[data-v-946bce22]:before {
  content: "\e69e";
}
.uniui-personadd[data-v-946bce22]:before {
  content: "\e69f";
}
.uniui-chatboxes-filled[data-v-946bce22]:before {
  content: "\e692";
}
.uniui-contact[data-v-946bce22]:before {
  content: "\e693";
}
.uniui-chatbubble-filled[data-v-946bce22]:before {
  content: "\e694";
}
.uniui-contact-filled[data-v-946bce22]:before {
  content: "\e695";
}
.uniui-chatboxes[data-v-946bce22]:before {
  content: "\e696";
}
.uniui-chatbubble[data-v-946bce22]:before {
  content: "\e697";
}
.uniui-upload-filled[data-v-946bce22]:before {
  content: "\e68e";
}
.uniui-upload[data-v-946bce22]:before {
  content: "\e690";
}
.uniui-weixin[data-v-946bce22]:before {
  content: "\e691";
}
.uniui-compose[data-v-946bce22]:before {
  content: "\e67f";
}
.uniui-qq[data-v-946bce22]:before {
  content: "\e680";
}
.uniui-download-filled[data-v-946bce22]:before {
  content: "\e681";
}
.uniui-pyq[data-v-946bce22]:before {
  content: "\e682";
}
.uniui-sound[data-v-946bce22]:before {
  content: "\e684";
}
.uniui-trash-filled[data-v-946bce22]:before {
  content: "\e685";
}
.uniui-sound-filled[data-v-946bce22]:before {
  content: "\e686";
}
.uniui-trash[data-v-946bce22]:before {
  content: "\e687";
}
.uniui-videocam-filled[data-v-946bce22]:before {
  content: "\e689";
}
.uniui-spinner-cycle[data-v-946bce22]:before {
  content: "\e68a";
}
.uniui-weibo[data-v-946bce22]:before {
  content: "\e68b";
}
.uniui-videocam[data-v-946bce22]:before {
  content: "\e68c";
}
.uniui-download[data-v-946bce22]:before {
  content: "\e68d";
}
.uniui-help[data-v-946bce22]:before {
  content: "\e679";
}
.uniui-navigate-filled[data-v-946bce22]:before {
  content: "\e67a";
}
.uniui-plusempty[data-v-946bce22]:before {
  content: "\e67b";
}
.uniui-smallcircle[data-v-946bce22]:before {
  content: "\e67c";
}
.uniui-minus-filled[data-v-946bce22]:before {
  content: "\e67d";
}
.uniui-micoff[data-v-946bce22]:before {
  content: "\e67e";
}
.uniui-closeempty[data-v-946bce22]:before {
  content: "\e66c";
}
.uniui-clear[data-v-946bce22]:before {
  content: "\e66d";
}
.uniui-navigate[data-v-946bce22]:before {
  content: "\e66e";
}
.uniui-minus[data-v-946bce22]:before {
  content: "\e66f";
}
.uniui-image[data-v-946bce22]:before {
  content: "\e670";
}
.uniui-mic[data-v-946bce22]:before {
  content: "\e671";
}
.uniui-paperplane[data-v-946bce22]:before {
  content: "\e672";
}
.uniui-close[data-v-946bce22]:before {
  content: "\e673";
}
.uniui-help-filled[data-v-946bce22]:before {
  content: "\e674";
}
.uniui-paperplane-filled[data-v-946bce22]:before {
  content: "\e675";
}
.uniui-plus[data-v-946bce22]:before {
  content: "\e676";
}
.uniui-mic-filled[data-v-946bce22]:before {
  content: "\e677";
}
.uniui-image-filled[data-v-946bce22]:before {
  content: "\e678";
}
.uniui-locked-filled[data-v-946bce22]:before {
  content: "\e668";
}
.uniui-info[data-v-946bce22]:before {
  content: "\e669";
}
.uniui-locked[data-v-946bce22]:before {
  content: "\e66b";
}
.uniui-camera-filled[data-v-946bce22]:before {
  content: "\e658";
}
.uniui-chat-filled[data-v-946bce22]:before {
  content: "\e659";
}
.uniui-camera[data-v-946bce22]:before {
  content: "\e65a";
}
.uniui-circle[data-v-946bce22]:before {
  content: "\e65b";
}
.uniui-checkmarkempty[data-v-946bce22]:before {
  content: "\e65c";
}
.uniui-chat[data-v-946bce22]:before {
  content: "\e65d";
}
.uniui-circle-filled[data-v-946bce22]:before {
  content: "\e65e";
}
.uniui-flag[data-v-946bce22]:before {
  content: "\e65f";
}
.uniui-flag-filled[data-v-946bce22]:before {
  content: "\e660";
}
.uniui-gear-filled[data-v-946bce22]:before {
  content: "\e661";
}
.uniui-home[data-v-946bce22]:before {
  content: "\e662";
}
.uniui-home-filled[data-v-946bce22]:before {
  content: "\e663";
}
.uniui-gear[data-v-946bce22]:before {
  content: "\e664";
}
.uniui-smallcircle-filled[data-v-946bce22]:before {
  content: "\e665";
}
.uniui-map-filled[data-v-946bce22]:before {
  content: "\e666";
}
.uniui-map[data-v-946bce22]:before {
  content: "\e667";
}
.uniui-refresh-filled[data-v-946bce22]:before {
  content: "\e656";
}
.uniui-refresh[data-v-946bce22]:before {
  content: "\e657";
}
.uniui-cloud-upload[data-v-946bce22]:before {
  content: "\e645";
}
.uniui-cloud-download-filled[data-v-946bce22]:before {
  content: "\e646";
}
.uniui-cloud-download[data-v-946bce22]:before {
  content: "\e647";
}
.uniui-cloud-upload-filled[data-v-946bce22]:before {
  content: "\e648";
}
.uniui-redo[data-v-946bce22]:before {
  content: "\e64a";
}
.uniui-images-filled[data-v-946bce22]:before {
  content: "\e64b";
}
.uniui-undo-filled[data-v-946bce22]:before {
  content: "\e64c";
}
.uniui-more[data-v-946bce22]:before {
  content: "\e64d";
}
.uniui-more-filled[data-v-946bce22]:before {
  content: "\e64e";
}
.uniui-undo[data-v-946bce22]:before {
  content: "\e64f";
}
.uniui-images[data-v-946bce22]:before {
  content: "\e650";
}
.uniui-paperclip[data-v-946bce22]:before {
  content: "\e652";
}
.uniui-settings[data-v-946bce22]:before {
  content: "\e653";
}
.uniui-search[data-v-946bce22]:before {
  content: "\e654";
}
.uniui-redo-filled[data-v-946bce22]:before {
  content: "\e655";
}
.uniui-list[data-v-946bce22]:before {
  content: "\e644";
}
.uniui-mail-open-filled[data-v-946bce22]:before {
  content: "\e63a";
}
.uniui-hand-down-filled[data-v-946bce22]:before {
  content: "\e63c";
}
.uniui-hand-down[data-v-946bce22]:before {
  content: "\e63d";
}
.uniui-hand-up-filled[data-v-946bce22]:before {
  content: "\e63e";
}
.uniui-hand-up[data-v-946bce22]:before {
  content: "\e63f";
}
.uniui-heart-filled[data-v-946bce22]:before {
  content: "\e641";
}
.uniui-mail-open[data-v-946bce22]:before {
  content: "\e643";
}
.uniui-heart[data-v-946bce22]:before {
  content: "\e639";
}
.uniui-loop[data-v-946bce22]:before {
  content: "\e633";
}
.uniui-pulldown[data-v-946bce22]:before {
  content: "\e632";
}
.uniui-scan[data-v-946bce22]:before {
  content: "\e62a";
}
.uniui-bars[data-v-946bce22]:before {
  content: "\e627";
}
.uniui-checkbox[data-v-946bce22]:before {
  content: "\e62b";
}
.uniui-checkbox-filled[data-v-946bce22]:before {
  content: "\e62c";
}
.uniui-shop[data-v-946bce22]:before {
  content: "\e62f";
}
.uniui-headphones[data-v-946bce22]:before {
  content: "\e630";
}
.uniui-cart[data-v-946bce22]:before {
  content: "\e631";
}
@font-face {
  font-family: uniicons;
  src: url("../../assets/uniicons.32e978a5.ttf");
}
.uni-icons[data-v-946bce22] {
  font-family: uniicons;
  text-decoration: none;
  text-align: center;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-popup[data-v-7db519c7] {
  position: fixed;
  z-index: 99;
}
.uni-popup.top[data-v-7db519c7], .uni-popup.left[data-v-7db519c7], .uni-popup.right[data-v-7db519c7] {
  top: 0;
}
.uni-popup .uni-popup__wrapper[data-v-7db519c7] {
  display: block;
  position: relative;
  /* iphonex 等安全区设置，底部安全区适配 */
}
.uni-popup .uni-popup__wrapper.left[data-v-7db519c7], .uni-popup .uni-popup__wrapper.right[data-v-7db519c7] {
  padding-top: 0;
  flex: 1;
}
.fixforpc-z-index[data-v-7db519c7] {
  z-index: 999;
}
.fixforpc-top[data-v-7db519c7] {
  top: 0;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.all-records-container {
  padding: 0.625rem;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.filter-bar {
  display: flex;
  justify-content: space-between;
  padding: 0.625rem 0;
}
.filter-bar .date-range-selector, .filter-bar .staff-selector, .filter-bar .status-selector {
  flex: 1;
  background-color: #fff;
  padding: 0.46875rem 0.625rem;
  border-radius: 0.3125rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 0.8125rem;
  margin-right: 0.3125rem;
  box-shadow: 0 0.0625rem 0.15625rem rgba(0, 0, 0, 0.05);
}
.filter-bar .date-range-selector:last-child, .filter-bar .staff-selector:last-child, .filter-bar .status-selector:last-child {
  margin-right: 0;
}
.filter-bar .date-range-selector uni-text, .filter-bar .staff-selector uni-text, .filter-bar .status-selector uni-text {
  width: 80%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.search-bar {
  background-color: #fff;
  border-radius: 0.3125rem;
  padding: 0.46875rem 0.625rem;
  display: flex;
  align-items: center;
  margin-bottom: 0.625rem;
  box-shadow: 0 0.0625rem 0.15625rem rgba(0, 0, 0, 0.05);
}
.search-bar uni-input {
  flex: 1;
  height: 1.875rem;
  margin: 0 0.3125rem;
  font-size: 0.875rem;
}
.search-bar .clear-btn {
  font-size: 0.8125rem;
  color: #999;
}
.statistics-info {
  background-color: #fff;
  border-radius: 0.3125rem;
  padding: 0.625rem;
  margin-bottom: 0.625rem;
  display: flex;
  flex-direction: column;
  box-shadow: 0 0.0625rem 0.15625rem rgba(0, 0, 0, 0.05);
}
.statistics-info uni-text {
  font-size: 0.875rem;
  color: #333;
  margin-bottom: 0.3125rem;
}
.statistics-info .status-count {
  display: flex;
  flex-wrap: wrap;
}
.statistics-info .status-count .status-item {
  margin-right: 0.625rem;
}
.statistics-info .status-count .status-item uni-text {
  font-size: 0.75rem;
}
.statistics-info .status-count .status-item uni-text.normal {
  color: #4cd964;
}
.statistics-info .status-count .status-item uni-text.late {
  color: #f0ad4e;
}
.statistics-info .status-count .status-item uni-text.early {
  color: #5bc0de;
}
.statistics-info .status-count .status-item uni-text.absent {
  color: #dd524d;
}
.records-list {
  background-color: #fff;
  border-radius: 0.3125rem;
  padding: 0.625rem;
  box-shadow: 0 0.0625rem 0.15625rem rgba(0, 0, 0, 0.05);
}
.records-list .record-group {
  margin-bottom: 0.9375rem;
}
.records-list .record-group .group-header {
  display: flex;
  align-items: center;
  padding: 0.46875rem 0.3125rem;
  border-bottom: 0.03125rem solid #f5f5f5;
  background-color: #f9f9f9;
  border-radius: 0.25rem 0.25rem 0 0;
}
.records-list .record-group .group-header .date {
  font-size: 0.875rem;
  font-weight: bold;
  color: #333;
}
.records-list .record-group .group-header .week {
  font-size: 0.75rem;
  color: #666;
  margin-left: 0.3125rem;
  background-color: #eaeaea;
  padding: 0.0625rem 0.3125rem;
  border-radius: 0.125rem;
}
.records-list .record-group .record-item {
  display: flex;
  flex-direction: column;
  padding: 0.625rem 0.3125rem;
  border-bottom: 0.03125rem solid #f5f5f5;
}
.records-list .record-group .record-item:last-child {
  border-bottom: none;
}
.records-list .record-group .record-item .staff-info {
  display: flex;
  align-items: center;
  margin-bottom: 0.625rem;
}
.records-list .record-group .record-item .staff-info .avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  border: 0.0625rem solid #eaeaea;
  box-shadow: 0 0.0625rem 0.1875rem rgba(0, 0, 0, 0.1);
}
.records-list .record-group .record-item .staff-info .staff-details {
  margin-left: 0.625rem;
}
.records-list .record-group .record-item .staff-info .staff-details .staff-name {
  font-size: 0.9375rem;
  font-weight: bold;
  color: #333;
}
.records-list .record-group .record-item .staff-info .staff-details .staff-dept {
  font-size: 0.75rem;
  color: #666;
  margin-top: 0.15625rem;
  background-color: #f5f5f5;
  display: inline-block;
  padding: 0.0625rem 0.3125rem;
  border-radius: 0.125rem;
}
.records-list .record-group .record-item .record-details {
  padding-left: 3.125rem;
}
.records-list .record-group .record-item .record-details .time-record {
  display: flex;
  flex-wrap: wrap;
}
.records-list .record-group .record-item .record-details .time-record .time-item {
  display: flex;
  align-items: center;
  margin-right: 0.9375rem;
  margin-bottom: 0.46875rem;
}
.records-list .record-group .record-item .record-details .time-record .time-item .time-label {
  font-size: 0.8125rem;
  color: #666;
  margin-right: 0.3125rem;
  background-color: #f9f9f9;
  padding: 0.125rem 0.3125rem;
  border-radius: 0.125rem;
}
.records-list .record-group .record-item .record-details .time-record .time-item .time-value {
  font-size: 0.875rem;
  color: #333;
  font-weight: 500;
}
.records-list .record-group .record-item .record-details .time-record .time-item .time-value.abnormal {
  color: #f0ad4e;
}
.records-list .record-group .record-item .record-details .time-record .time-item .status-tag {
  font-size: 0.6875rem;
  padding: 0.125rem 0.375rem;
  border-radius: 0.625rem;
  margin-left: 0.3125rem;
}
.records-list .record-group .record-item .record-details .time-record .time-item .status-tag.late {
  background-color: #fef0e5;
  color: #f0ad4e;
  border: 0.03125rem solid #f0ad4e;
}
.records-list .record-group .record-item .record-details .time-record .time-item .status-tag.early {
  background-color: #e5f5fa;
  color: #5bc0de;
  border: 0.03125rem solid #5bc0de;
}
.records-list .record-group .record-item .record-details .location-info {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-top: 0.15625rem;
}
.records-list .record-group .record-item .record-details .location-info .location-label {
  font-size: 0.8125rem;
  color: #666;
  margin-right: 0.3125rem;
  background-color: #f9f9f9;
  padding: 0.125rem 0.3125rem;
  border-radius: 0.125rem;
}
.records-list .record-group .record-item .record-details .location-info .location-value {
  font-size: 0.8125rem;
  color: #666;
  flex: 1;
  word-break: break-all;
}
.records-list .load-more, .records-list .no-more {
  text-align: center;
  padding: 0.9375rem 0;
}
.records-list .load-more uni-text, .records-list .no-more uni-text {
  font-size: 0.8125rem;
  color: #999;
}
.records-list .empty-records {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.875rem 0;
}
.records-list .empty-records uni-image {
  width: 6.25rem;
  height: 6.25rem;
  margin-bottom: 0.625rem;
}
.records-list .empty-records uni-text {
  font-size: 0.875rem;
  color: #999;
}
.date-popup, .staff-popup, .status-popup {
  background-color: #fff;
  border-top-left-radius: 0.625rem;
  border-top-right-radius: 0.625rem;
}
.date-popup .popup-header, .staff-popup .popup-header, .status-popup .popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.9375rem;
  border-bottom: 0.03125rem solid #f5f5f5;
}
.date-popup .popup-header uni-text, .staff-popup .popup-header uni-text, .status-popup .popup-header uni-text {
  font-size: 1rem;
  font-weight: bold;
}
.date-popup .popup-header .close-btn, .staff-popup .popup-header .close-btn, .status-popup .popup-header .close-btn {
  font-size: 0.875rem;
  color: #007AFF;
}
.date-popup .date-options {
  padding: 0 0.9375rem 0.9375rem;
}
.date-popup .date-options .date-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.625rem 0;
  border-bottom: 0.03125rem solid #f5f5f5;
}
.date-popup .date-options .date-option uni-text {
  font-size: 0.9375rem;
  color: #333;
}
.date-popup .date-options .date-option.active uni-text {
  color: #007AFF;
}
.date-popup .date-options .custom-date-range {
  padding: 0.625rem 0;
}
.date-popup .date-options .custom-date-range uni-text {
  font-size: 0.9375rem;
  color: #333;
  display: block;
  margin-bottom: 0.46875rem;
}
.date-popup .date-options .custom-date-range .date-inputs {
  display: flex;
  align-items: center;
}
.date-popup .date-options .custom-date-range .date-inputs .date-picker {
  flex: 1;
}
.date-popup .date-options .custom-date-range .date-inputs .date-input {
  background-color: #f5f5f5;
  padding: 0.46875rem 0.625rem;
  border-radius: 0.3125rem;
}
.date-popup .date-options .custom-date-range .date-inputs .date-input uni-text {
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 0;
}
.date-popup .date-options .custom-date-range .date-inputs .date-separator {
  margin: 0 0.625rem;
  font-size: 0.875rem;
  color: #999;
}
.date-popup .date-options .custom-date-range .custom-date-actions {
  margin-top: 0.625rem;
  display: flex;
  justify-content: flex-end;
}
.date-popup .date-options .custom-date-range .custom-date-actions .btn-apply {
  background-color: #007AFF;
  color: #fff;
  font-size: 0.875rem;
  padding: 0.3125rem 0.9375rem;
  border-radius: 0.25rem;
}
.date-popup .date-options .custom-date-range .custom-date-actions .btn-apply:disabled {
  background-color: #cccccc;
}
.staff-popup .search-box {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  margin: 0.625rem 0.9375rem;
  padding: 0.3125rem 0.625rem;
  border-radius: 0.3125rem;
}
.staff-popup .search-box uni-input {
  flex: 1;
  height: 1.875rem;
  margin-left: 0.3125rem;
  font-size: 0.875rem;
}
.staff-popup .staff-list {
  max-height: 18.75rem;
  padding: 0 0.46875rem;
}
.staff-popup .staff-list .staff-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.625rem 0.46875rem;
  border-bottom: 0.03125rem solid #f5f5f5;
  margin-bottom: 0.15625rem;
  border-radius: 0.25rem;
}
.staff-popup .staff-list .staff-item:active {
  background-color: #f9f9f9;
}
.staff-popup .staff-list .staff-item .staff-info {
  display: flex;
  flex-direction: column;
}
.staff-popup .staff-list .staff-item .staff-info .staff-name {
  font-size: 0.9375rem;
  color: #333;
  font-weight: 500;
}
.staff-popup .staff-list .staff-item .staff-info .staff-dept {
  font-size: 0.75rem;
  color: #666;
  margin-top: 0.1875rem;
  background-color: #f5f5f5;
  display: inline-block;
  padding: 0.0625rem 0.3125rem;
  border-radius: 0.125rem;
  max-width: 12.5rem;
}
.status-popup .status-list {
  padding-bottom: 0.9375rem;
}
.status-popup .status-list .status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.625rem 0.9375rem;
  border-bottom: 0.03125rem solid #f5f5f5;
}
.status-popup .status-list .status-item uni-text {
  font-size: 0.9375rem;
  color: #333;
}