package com.emm.dto;

import java.util.List;
import com.fasterxml.jackson.annotation.JsonProperty;

// 请求体类
public class CompleteOrderRequest {
    @JsonProperty("repair_content")
    private String repairContent;

    @JsonProperty("repair_result")
    private String repairResult;

    @JsonProperty("repair_image")
    private List<String> repairImage;

    // Getters and Setters
    public String getRepairContent() {
        return repairContent;
    }

    public void setRepairContent(String repairContent) {
        this.repairContent = repairContent;
    }

    public String getRepairResult() {
        return repairResult;
    }

    public void setRepairResult(String repairResult) {
        this.repairResult = repairResult;
    }

    public List<String> getRepairImage() {
        return repairImage;
    }

    public void setRepairImage(List<String> repairImage) {
        this.repairImage = repairImage;
    }
}