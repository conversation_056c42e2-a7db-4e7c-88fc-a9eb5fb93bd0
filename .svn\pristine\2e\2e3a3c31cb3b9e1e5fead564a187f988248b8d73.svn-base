package com.heating.entity.patrol;

import com.heating.converter.JsonConverter;
import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Entity
@Table(name = "t_patrol_result")
public class TPatrolResult {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "patrol_record_id")
    private Long patrolRecordId;

    @Column(name = "patrol_item_id")
    private Long patrolItemId;

    @Column(name = "check_result")
    private String checkResult;

    @Column(name = "param_value")
    private String paramValue;

    private String description;

    @Convert(converter = JsonConverter.class)
    @Column(columnDefinition = "json")
    private List<String> images;

    @Column(name = "latitude")
    private Double latitude;

    @Column(name = "longitude")
    private Double longitude;

    @Column(name = "create_time")
    private LocalDateTime createTime;

    @Column(name = "update_time")
    private LocalDateTime updateTime;   
} 