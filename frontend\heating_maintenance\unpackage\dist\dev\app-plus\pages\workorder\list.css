/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.workorder-list-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 搜索栏 */
.search-bar {
  padding: 0.625rem 0.9375rem 0 0.9375rem;
  background-color: #fff;
  position: -webkit-sticky;
  position: sticky;
  height: 2.65625rem;
  top: 0;
  z-index: 999;
}
.search-bar .search-input {
  display: flex;
  align-items: center;
  background-color: #f5f7fa;
  border-radius: 1.125rem;
  padding: 0.4375rem 0.9375rem;
}
.search-bar .search-input .search-icon-left {
  font-family: "iconfont";
  font-size: 1rem;
  color: #999;
  margin-right: 0.5rem;
}
.search-bar .search-input uni-input {
  flex: 1;
  height: 1.875rem;
  font-size: 0.875rem;
  color: #333;
}
.search-bar .search-input .clear-icon {
  font-family: "iconfont";
  font-size: 1rem;
  color: #999;
  padding: 0.3125rem;
}
.search-bar .search-input .search-icon-right {
  font-family: "iconfont";
  font-size: 1rem;
  color: #007aff;
  padding: 0.3125rem;
  margin-left: 0.3125rem;
}

/* 筛选部分 */
.filter-section-seach {
  background-color: #ffffff;
  display: flex;
  justify-content: space-between;
  padding: 0.625rem 0.9375rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
  position: -webkit-sticky;
  position: sticky;
  height: 2.34375rem;
  top: 3.125rem;
  z-index: 999;
}
.filter-section-seach .date-filter,
.filter-section-seach .status-filter {
  flex: 1;
  height: 2.1875rem;
}
.filter-section-seach .date-filter .date-picker,
.filter-section-seach .date-filter .status-picker,
.filter-section-seach .status-filter .date-picker,
.filter-section-seach .status-filter .status-picker {
  display: flex;
  align-items: center;
  height: 2.1875rem;
  border: 1px solid #e5e5e5;
  border-radius: 0.1875rem;
  padding: 0 0.625rem;
  font-size: 0.875rem;
  color: #333;
}
.filter-section-seach .date-filter .date-picker .date-text,
.filter-section-seach .date-filter .date-picker .status-text,
.filter-section-seach .date-filter .status-picker .date-text,
.filter-section-seach .date-filter .status-picker .status-text,
.filter-section-seach .status-filter .date-picker .date-text,
.filter-section-seach .status-filter .date-picker .status-text,
.filter-section-seach .status-filter .status-picker .date-text,
.filter-section-seach .status-filter .status-picker .status-text {
  flex: 1;
}
.filter-section-seach .date-filter .date-picker .iconfont,
.filter-section-seach .date-filter .status-picker .iconfont,
.filter-section-seach .status-filter .date-picker .iconfont,
.filter-section-seach .status-filter .status-picker .iconfont {
  margin-left: 0.3125rem;
  color: #999;
}
.filter-section-seach .date-filter {
  margin-right: 0.625rem;
}
.filter-section-seach .status-filter {
  margin-right: 0.625rem;
}

/* 筛选条件区域 */
.filter-condition {
  background-color: #fff;
  margin-bottom: 0.625rem;
  padding: 0 0.9375rem 0.625rem;
}
.filter-condition .condition-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.625rem 0;
  border-bottom: 1px solid #f5f5f5;
  margin-bottom: 0.625rem;
}
.filter-condition .condition-header .condition-title {
  font-size: 0.9375rem;
  font-weight: 600;
  color: #333;
}
.filter-condition .condition-header .close-icon {
  font-size: 1.25rem;
  color: #999;
  padding: 0 0.3125rem;
}
.filter-condition .filter-section {
  margin-bottom: 0.625rem;
}
.filter-condition .filter-section .section-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.625rem;
}
.filter-condition .filter-section .section-header .blue-bar {
  width: 0.1875rem;
  height: 0.9375rem;
  background-color: #007aff;
  margin-right: 0.46875rem;
  border-radius: 0.09375rem;
}
.filter-condition .filter-section .section-header .section-title {
  font-size: 0.875rem;
  color: #333;
  font-weight: 500;
}
.filter-condition .filter-section .filter-options {
  display: flex;
  flex-wrap: wrap;
}
.filter-condition .filter-section .filter-options .filter-option {
  padding: 0.3125rem 0.75rem;
  margin-right: 0.625rem;
  margin-bottom: 0.46875rem;
  font-size: 0.8125rem;
  color: #666;
  background-color: #f5f7fa;
  border-radius: 0.9375rem;
}
.filter-condition .filter-section .filter-options .filter-option.active {
  color: #fff;
  background-color: #007aff;
}
.filter-condition .filter-section .date-range .date-picker {
  height: 2.1875rem;
  background-color: #f5f7fa;
  border-radius: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.filter-condition .filter-section .date-range .date-picker .date-text {
  font-size: 0.8125rem;
  color: #666;
}
.filter-condition .filter-actions {
  display: flex;
  margin-top: 0.9375rem;
}
.filter-condition .filter-actions uni-button {
  flex: 1;
  height: 2.5rem;
  line-height: 2.5rem;
  text-align: center;
  font-size: 0.875rem;
  border-radius: 1.25rem;
}
.filter-condition .filter-actions uni-button.filter-reset {
  color: #666;
  background-color: #f5f5f5;
  margin-right: 0.625rem;
}
.filter-condition .filter-actions uni-button.filter-confirm {
  color: #fff;
  background-color: #007aff;
}

/* 工单列表 */
.workorder-list .workorder-item {
  margin: 0.625rem;
  padding: 0.9375rem;
  background-color: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.workorder-list .workorder-item .workorder-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.625rem;
}
.workorder-list .workorder-item .workorder-header .workorder-code {
  font-size: 0.875rem;
  color: #666;
}
.workorder-list .workorder-item .workorder-header .status-tag {
  padding: 0.125rem 0.5rem;
  border-radius: 0.625rem;
  font-size: 0.75rem;
}
.workorder-list .workorder-item .workorder-header .status-tag.pending {
  background-color: #e6f7ff;
  color: #1890ff;
}
.workorder-list .workorder-item .workorder-header .status-tag.processing {
  background-color: #fff7e6;
  color: #fa8c16;
}
.workorder-list .workorder-item .workorder-header .status-tag.completed {
  background-color: #f6ffed;
  color: #52c41a;
}
.workorder-list .workorder-item .workorder-header .status-tag.transferred {
  background-color: #f2f4f8;
  color: #6777ef;
}
.workorder-list .workorder-item .workorder-header .status-tag.cancelled {
  background-color: #f5f5f5;
  color: #999;
}
.workorder-list .workorder-item .workorder-info {
  margin-bottom: 0.625rem;
}
.workorder-list .workorder-item .workorder-info .info-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.3125rem;
}
.workorder-list .workorder-item .workorder-info .info-item .info-label {
  font-size: 0.8125rem;
  color: #999;
  width: 4.6875rem;
}
.workorder-list .workorder-item .workorder-info .info-item .location {
  font-size: 0.9375rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.25rem;
}
.workorder-list .workorder-item .workorder-info .info-item .fault-type {
  font-size: 0.875rem;
  color: #333;
  margin-bottom: 0.25rem;
}
.workorder-list .workorder-item .workorder-info .info-item .fault-level {
  font-size: 0.875rem;
  color: #666;
  line-height: 1.5;
}
.workorder-list .workorder-item .workorder-info .info-item .level-tag {
  padding: 0.125rem 0.375rem;
  border-radius: 0.125rem;
  font-size: 0.75rem;
}
.workorder-list .workorder-item .workorder-info .info-item .level-tag.notice {
  background-color: #e6f7ff;
  color: #1890ff;
}
.workorder-list .workorder-item .workorder-info .info-item .level-tag.normal {
  background-color: #e6f7ff;
  color: #1890ff;
}
.workorder-list .workorder-item .workorder-info .info-item .level-tag.important {
  background-color: #fff7e6;
  color: #fa8c16;
}
.workorder-list .workorder-item .workorder-info .info-item .level-tag.critical {
  background-color: #fff1f0;
  color: #f5222d;
}
.workorder-list .workorder-item .workorder-info .info-item .level-tag.default {
  background-color: #f5f5f5;
  color: #999;
}
.workorder-list .workorder-item .workorder-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.workorder-list .workorder-item .workorder-footer .workorder-meta {
  display: flex;
  flex-direction: column;
}
.workorder-list .workorder-item .workorder-footer .workorder-meta .workorder-time {
  font-size: 0.75rem;
  color: #999;
  margin-bottom: 0.25rem;
}
.workorder-list .workorder-item .workorder-footer .workorder-actions {
  display: flex;
  align-items: center;
}
.workorder-list .workorder-item .workorder-footer .workorder-actions .action-button {
  padding: 0.3125rem 0.75rem;
  margin-left: 0.625rem;
  font-size: 0.75rem;
  color: #666;
  background-color: #f5f7fa;
  border-radius: 0.9375rem;
}
.workorder-list .workorder-item .workorder-footer .workorder-actions .action-button.primary {
  color: #fff;
  background-color: #007aff;
}

/* 加载状态 */
.loading-more,
.no-more {
  text-align: center;
  padding: 0.9375rem;
  font-size: 0.8125rem;
  color: #999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3.125rem 0;
}
.empty-state .empty-image {
  width: 7.5rem;
  height: 7.5rem;
  margin-bottom: 0.9375rem;
}
.empty-state .empty-text {
  font-size: 0.875rem;
  color: #999;
  margin-bottom: 1.25rem;
}
.empty-state .refresh-button {
  font-size: 0.875rem;
  color: #007aff;
  background-color: #fff;
  border: 0.0625rem solid #007aff;
  border-radius: 1.25rem;
  padding: 0.3125rem 1.875rem;
}

/* 重置按钮 */
.refresh-button {
  font-size: 0.875rem;
  background-color: #fff;
  border-radius: 0.15625rem;
  align-items: center;
  height: 2.25rem;
  border-radius: 0.1875rem;
  padding: 0 0.625rem;
  font-size: 0.875rem;
  position: relative;
}