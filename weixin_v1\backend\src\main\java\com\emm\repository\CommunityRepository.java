package com.emm.repository;

import com.emm.model.Community;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface CommunityRepository extends JpaRepository<Community, Long> {

    @Query(value = "SELECT id, name FROM t_useheatunit", nativeQuery = true)
    List<Map<String, Object>> findAllCommunities();
}