package com.emm.controller;

import com.emm.service.HesService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api")
public class HesController {

    private static final Logger logger = LoggerFactory.getLogger(HesController.class);

    @Autowired
    private HesService hesService;

    @GetMapping("/stations")
    public ResponseEntity<?> getStations() {
        logger.info("Accessing GET /api/stations");
        try {
            List<Map<String, Object>> stations = hesService.getAllStations();
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", stations);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error in GET /api/stations: {}", e.getMessage());
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/hes/station")
    public ResponseEntity<?> getStation(@RequestParam Long communityId) {
        logger.info("Accessing GET /api/hes/station with communityId: {}", communityId);
        try {
            List<Map<String, Object>> stations = hesService.getStationsByCommunityId(communityId);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", stations);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error in GET /api/hes/station: {}", e.getMessage());
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}