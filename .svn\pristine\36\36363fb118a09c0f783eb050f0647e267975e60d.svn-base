package com.heating.controller;

import com.heating.dto.permission.PermissionResponse;
import com.heating.dto.permission.RolePermissionResponse;
import com.heating.service.SysPermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * 系统级别操作控制器
 */
@RestController
@RequestMapping("/api/system")
@Slf4j
public class SystemController {

    @Autowired
    private SysPermissionService sysPermissionService;

    /**
     * 获取系统权限列表
     * 
     * @return 权限列表响应
     */
    @GetMapping("/permission")
    public ResponseEntity<Map<String, Object>> getSystemPermissions() {
        try {
            List<PermissionResponse> permissions = sysPermissionService.getSystemPermissions();
            return ResponseEntity.ok(Map.of(
                    "code", 200,
                    "message", "获取权限列表成功",
                    "data", permissions
            ));
        } catch (Exception e) {
            log.error("Failed to get system permissions", e);
            return ResponseEntity.ok(Map.of(
                    "code", 500,
                    "message", "获取权限列表失败: 系统错误"
            ));
        }
    }
    
    /**
     * 获取角色权限编码列表
     * 
     * @param roleCode 角色编码
     * @return 角色权限列表响应
     */
    @GetMapping("/role/permission/{roleCode}")
    public ResponseEntity<Map<String, Object>> getRolePermissions(@PathVariable String roleCode) {
        try {
            List<RolePermissionResponse> permissions = sysPermissionService.getRolePermissions(roleCode);
            return ResponseEntity.ok(Map.of(
                    "code", 200,
                    "message", "获取权限列表成功",
                    "data", permissions
            ));
        } catch (Exception e) {
            log.error("Failed to get role permissions for role: {}", roleCode, e);
            return ResponseEntity.ok(Map.of(
                    "code", 500,
                    "message", "获取权限列表失败: 系统错误"
            ));
        }
    }
} 