package com.emm.repository;

import com.emm.model.UseHeatUnit;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface UseHeatUnitRepository extends JpaRepository<UseHeatUnit, Long> {

    @Query("SELECT new map(u.id as id, u.name as name) FROM UseHeatUnit u ORDER BY u.name")
    List<Map<String, Object>> findAllCommunities();

    @Query("SELECT new map(u.id as id, u.name as name) FROM UseHeatUnit u ORDER BY u.name")
    List<Map<Integer, String>> findUseheatunits(); 

}