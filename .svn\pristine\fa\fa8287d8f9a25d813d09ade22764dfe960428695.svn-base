<template>
	<view class="clock-in-container">
		<!-- 顶部状态栏 -->
		<view class="status-header">
			<view class="date-info">
				<text class="date">{{ currentDate }}</text>
				<text class="time">{{ currentTime }}</text>
			</view>
			<!-- 管理员设置按钮 -->
			<PermissionCheck permission="attendance:clock-rules">
				<view class="admin-settings" @click="goToAdminSettings">
					<text class="settings-icon">⚙️</text>
					<text class="settings-text">规则设置</text>
				</view>
			 </PermissionCheck>
		</view>
		
		<!-- 打卡状态展示 -->
		<view class="clock-status">
			<view class="status-card">
				<view class="work-time">
					<view class="time-item">
						<text class="time-label">上班时间</text>
						<text class="time-value">{{ attendanceRules.clockInTime || '08:30' }}</text>
					</view>
					<view class="time-divider"></view>
					<view class="time-item">
						<text class="time-label">下班时间</text>
						<text class="time-value">{{ attendanceRules.clockOutTime || '17:30' }}</text>
					</view>
				</view>
				
				<view class="clock-records">
					<view class="record-item">
						<text class="record-label">上班打卡</text>
						<text class="record-value" :class="{'not-clocked': !clockInTime}">
							{{ clockInTime || '未打卡' }}
						</text>
					</view>
					<view class="record-item">
						<text class="record-label">下班打卡</text>
						<text class="record-value" :class="{'not-clocked': !clockOutTime}">
							{{ clockOutTime || '未打卡' }}
						</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 打卡按钮区域 -->
		<view class="clock-action">
			<!-- 移除位置错误提示和授权按钮 -->
			
			<view class="clock-circle" @click="handleClockIn">
				<view class="circle-inner">
					<text class="clock-text">{{ getClockButtonText() }}</text>
				</view>
			</view>
			
			<view class="action-desc">
				<text>{{ getActionDescription() }}</text>
			</view>
		</view>
		
		<!-- 打卡记录 -->
		<view class="recent-records">
			<view class="section-title">最近打卡记录</view>
			<view class="record-list">
				<view class="record-empty" v-if="recentRecords.length === 0">
					<text>暂无打卡记录</text>
				</view>
				<view class="record-item" v-for="(record, index) in recentRecords" :key="index">
					<view class="record-date">
						<text class="date">{{ record.date }}</text>
						<text class="week">{{ record.week }}</text>
					</view>
					<view class="record-details">
						<view class="detail-item">
							<text class="detail-label">上班</text>
							<text class="detail-value" :class="{'abnormal': record.clockInStatus !== 'normal'}">
								{{ record.clockInTime }}
								<text class="status-tag" v-if="record.clockInStatus === 'late'">迟到</text>
							</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">下班</text>
							<text class="detail-value" :class="{'abnormal': record.clockOutStatus !== 'normal'}">
								{{ record.clockOutTime }}
								<text class="status-tag" v-if="record.clockOutStatus === 'early'">早退</text>
							</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 补卡申请弹窗 -->
		<!-- <uni-popup ref="supplementPopup" type="center">
			<view class="supplement-popup">
				<view class="popup-title">补卡申请</view>
				<view class="popup-content">
					<view class="form-item">
						<view class="form-label">补卡日期</view>
						<view class="form-input date-picker">
							<picker mode="date" :value="supplementDate" @change="supplementDate = $event.detail.value">
								<view>{{ supplementDate }}</view>
							</picker>
							<text class="icon-calendar">📅</text>
						</view>
					</view>
					
					<view class="form-item">
						<view class="form-label">补卡类型</view>
						<view class="form-input">
							<picker :range="supplementTypes" :value="supplementTypeIndex" @change="supplementTypeIndex = $event.detail.value">
								<view>{{ supplementTypes[supplementTypeIndex] }}</view>
							</picker>
						</view>
					</view>
					
					<view class="form-item">
						<view class="form-label">申请原因</view>
						<view class="form-input">
							<textarea v-model="supplementReason" placeholder="请输入补卡原因" maxlength="200" />
						</view>
					</view>
					
					<view class="form-item">
						<view class="form-label">证明材料</view>
						<view class="supplement-images">
							<view class="image-item" v-for="(image, index) in supplementImages" :key="index">
								<image class="supplement-image" :src="image" mode="aspectFill"></image>
								<view class="image-delete" @tap="deleteSupplementImage(index)">×</view>
							</view>
							<view class="image-add" @tap="chooseSupplementImage" v-if="supplementImages.length < 3">
								<view class="image-add-icon">+</view>
								<view class="image-add-text">上传</view>
							</view>
						</view>
					</view>
					
					<view class="popup-footer">
						<button class="btn-cancel" @click="$refs.supplementPopup.close()" :disabled="loading">取消</button>
						<button class="btn-confirm" @click="submitSupplementApplication" :disabled="loading">提交</button>
					</view>
				</view>
			</view>
		</uni-popup> -->
	</view>
</template>

<script>
	import { attendanceApi } from '@/utils/api.js';
	import PermissionCheck from "@/components/PermissionCheck.vue"; // 导入权限检查组件
	
	export default {
		components: {
		  PermissionCheck, // 本地注册组件
		},
		data() {
			return {
				// 当前时间相关
				timer: null,
				currentDate: '',
				currentTime: '',
				
				// 位置相关
				latitude: 0, // 默认纬度，使用默认值
				longitude: 0, // 默认经度，使用默认值
				clockStatus: 'normal', // 默认为normal，始终允许打卡
				locationError: '', // 存储定位错误信息
				useNativeGeolocation: true, // 默认使用原生定位
				locationPermissionDenied: false, // 位置权限是否被拒绝
				hasShownLocationError: false, // 是否已经显示过位置错误提醒
				
				// 今日打卡记录
				clockInTime: '',
				clockOutTime: '',
				
				// 补卡相关
				supplementDate: this.formatDate(new Date()),
				supplementTypes: ['上班打卡', '下班打卡'],
				supplementTypeIndex: 0,
				supplementReason: '',
				supplementImages: [],
				
				// 最近打卡记录
				recentRecords: [],
				
				// 考勤规则
				attendanceRules: {
					clockInTime: '08:30',
					clockOutTime: '17:30',
					allowedDistance: 500, // 允许打卡的距离范围(米)
					lateThreshold: 15, // 迟到阈值(分钟)
					earlyLeaveThreshold: 15, // 早退阈值(分钟)
					locationUploadInterval: 1 // 位置上传时间间隔(分钟)
				},
				
				// 加载状态
				loading: false,
				
				// 是否是管理员
				isAdmin: false,
				
				// 定位轨迹上传定时器
				locationUploadTimer: null,
				isUploadingLocation: false, // 是否正在上传位置
				isGettingLocation: false // 是否正在获取位置
			}
		},
		computed: {
			// 移除canClockIn计算属性，始终允许打卡
			canClockIn() {
				return true; // 始终允许打卡
			},
		},
		onLoad() {
			// 初始化时间显示
			this.updateDateTime();
			
			// 设置定时器更新时间
			this.timer = setInterval(() => {
				this.updateDateTime();
			}, 1000);
			
			// 初始化错误提醒标志位
			this.hasShownLocationError = false;
			this.isGettingLocation = false;
			
			// 获取考勤规则（先获取规则）
			this.getAttendanceRules();
			
			// 获取今日打卡记录
			this.getTodayClockRecord();
			
			// 获取最近打卡记录
			this.getRecentRecords();
			
			// 在后台尝试获取位置权限和位置信息，但不阻塞页面加载
			setTimeout(() => {
				this.checkLocationPermission().then(hasPermission => {
					if (hasPermission) {
						// 已有权限，尝试获取位置
						this.tryGetLocation();
					} else {
						// 没有权限，静默请求授权
						this.requestLocationPermission();
					}
				});
			}, 500);
		},
		onUnload() {
			// 清除定时器
			if (this.timer) {
				clearInterval(this.timer);
				this.timer = null;
			}
		},
		onShow()
		{
			// 获取考勤规则（先获取规则）
			this.getAttendanceRules();
			
			// 获取今日打卡记录
			this.getTodayClockRecord();
			
			// 获取最近打卡记录
			this.getRecentRecords();
		},
		methods: {
			// 启动定位上传
			startLocationUpload() {
				this.$store.dispatch('attendance/startLocationUpload');
				console.log('启动全局位置上传');
			},
			
			// 停止定位上传
			stopLocationUpload() {
				this.$store.dispatch('attendance/stopLocationUpload');
				console.log('停止全局位置上传');
			},
			
			// 更新日期时间
			updateDateTime() {
				const now = new Date();
				
				// 格式化日期: 2023年01月01日 星期一
				const year = now.getFullYear();
				const month = String(now.getMonth() + 1).padStart(2, '0');
				const day = String(now.getDate()).padStart(2, '0');
				const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
				const weekDay = weekDays[now.getDay()];
				
				this.currentDate = `${year}年${month}月${day}日 ${weekDay}`;
				
				// 格式化时间: 08:00:00
				const hours = String(now.getHours()).padStart(2, '0');
				const minutes = String(now.getMinutes()).padStart(2, '0');
				const seconds = String(now.getSeconds()).padStart(2, '0');
				
				this.currentTime = `${hours}:${minutes}:${seconds}`;
			},
			
			// 获取位置信息
			getLocation() {
				// 如果已经在获取位置中，不要重复请求
				if (this.isGettingLocation) {
					console.log('正在获取位置中，跳过重复请求');
					return;
				}
				
				this.isGettingLocation = true;
				this.clockStatus = 'normal'; // 确保按钮不会变灰
				
				// 设置超时，确保即使获取位置失败也能继续
				const locationTimeout = setTimeout(() => {
					console.log('位置获取超时，停止尝试');
					this.locationError = '位置获取超时';
					this.clockStatus = 'normal';
					this.isGettingLocation = false;
					// 超时时尝试直接请求权限
					this.requestLocationPermission();
				}, 15000); // 15秒超时
				
				// 优先使用原生定位
				if (this.useNativeGeolocation) {
					// 尝试使用原生定位
					this.getNativeLocation().then(() => {
						clearTimeout(locationTimeout);
						this.isGettingLocation = false;
					}).catch(err => {
						console.error('原生定位失败，尝试使用uni定位:', err);
						this.useNativeGeolocation = false;
						// 如果原生定位失败，回退到uni定位
						this.getUniLocation().then(() => {
							clearTimeout(locationTimeout);
							this.isGettingLocation = false;
						}).catch(err => {
							clearTimeout(locationTimeout);
							this.handleLocationError(err);
							this.isGettingLocation = false;
						});
					});
				} else {
					// 直接使用uni定位
					this.getUniLocation().then(() => {
						clearTimeout(locationTimeout);
						this.isGettingLocation = false;
					}).catch(err => {
						clearTimeout(locationTimeout);
						this.handleLocationError(err);
						this.isGettingLocation = false;
					});
				}
			},
			
			// 使用原生定位API获取位置
			getNativeLocation() {
				return new Promise((resolve, reject) => {
					// #ifdef APP-PLUS
					try {
						plus.geolocation.getCurrentPosition(
							(position) => {
								console.log('原生定位成功:', position);
								
								// 提取位置信息
								const coords = position.coords;
								this.latitude = coords.latitude;
								this.longitude = coords.longitude;
								this.locationError = ''; // 清除错误信息
								this.locationPermissionDenied = false;
								this.clockStatus = 'normal';
								this.hasShownLocationError = false; // 重置提醒标志
								
								resolve(position);
							},
							(err) => {
								console.error('原生定位失败:', err);
								reject({
									errMsg: err.message || '获取位置失败',
									detail: err
								});
							},
							{
								enableHighAccuracy: true, // 高精度定位
								timeout: 15000, // 超时时间
								maximumAge: 0, // 不使用缓存
								provider: 'system', // 使用系统定位
								geocode: false // 不获取地理编码信息
							}
						);
					} catch (e) {
						console.error('调用原生定位API异常:', e);
						reject({
							errMsg: '调用定位服务失败',
							detail: e.message
						});
					}
				
					// 非APP环境下，使用uni定位API
					this.getUniLocation().then(resolve).catch(reject);
					// #endif
				});
			},
			
			// 使用uni.getLocation获取位置
			getUniLocation() {
				return new Promise((resolve, reject) => {
					uni.getLocation({
						type: 'gcj02',
						isHighAccuracy: true,
						highAccuracyExpireTime: 5000,
						success: (res) => {
							console.log('uni位置获取成功:', res);
							this.latitude = res.latitude;
							this.longitude = res.longitude;
							this.locationError = ''; // 清除错误信息
							this.locationPermissionDenied = false;
							this.clockStatus = 'normal';
							this.hasShownLocationError = false; // 重置提醒标志
							
							resolve(res);
						},
						fail: (err) => {
							console.error('uni获取位置失败:', err);
							reject(err);
						},
						complete: () => {
							// 完成位置获取，无论成功失败
							console.log('位置获取完成');
						}
					});
				});
			},
			
			// 检查位置权限
			checkLocationPermission() {
				return new Promise((resolve) => {
					// #ifdef APP-PLUS
					if (plus.os.name.toLowerCase() === 'android') {
						const mainActivity = plus.android.runtimeMainActivity();
						const PackageManager = plus.android.importClass("android.content.pm.PackageManager");
						const ContextCompat = plus.android.importClass("androidx.core.content.ContextCompat");
						const Manifest = plus.android.importClass("android.Manifest");
						
						// 检查是否有精确位置权限
						const hasFineLocationPermission = ContextCompat.checkSelfPermission(mainActivity, Manifest.permission.ACCESS_FINE_LOCATION) === PackageManager.PERMISSION_GRANTED;
						// 检查是否有粗略位置权限
						const hasCoarseLocationPermission = ContextCompat.checkSelfPermission(mainActivity, Manifest.permission.ACCESS_COARSE_LOCATION) === PackageManager.PERMISSION_GRANTED;
						
						// 如果有任一位置权限，判断为有权限
						resolve(hasFineLocationPermission || hasCoarseLocationPermission);
					} else if (plus.os.name.toLowerCase() === 'ios') {
						// iOS平台
						const cllocationMgr = plus.ios.import("CLLocationManager");
						const status = cllocationMgr.authorizationStatus();
						
						// kCLAuthorizationStatusAuthorizedAlways: 4
						// kCLAuthorizationStatusAuthorizedWhenInUse: 3
						const hasPermission = (status === 4 || status === 3);
						plus.ios.deleteObject(cllocationMgr);
						resolve(hasPermission);
					} else {
						// 其他平台，假设没有权限
						resolve(false);
					}
					// #endif
					
					// #ifdef H5
					// H5环境
					if (navigator.geolocation) {
						// 尝试使用权限API查询权限状态
						if (navigator.permissions && navigator.permissions.query) {
							navigator.permissions.query({name: 'geolocation'}).then(permissionStatus => {
								if (permissionStatus.state === 'granted') {
									resolve(true);
								} else {
									resolve(false);
								}
							}).catch(() => {
								// 如果无法查询权限，假设没有权限
								resolve(false);
							});
						} else {
							// 不支持权限API，假设没有权限
							resolve(false);
						}
					} else {
						// 浏览器不支持地理位置API
						resolve(false);
					}
					// #endif
					
					// #ifdef MP-WEIXIN || MP-ALIPAY
					// 微信小程序或支付宝小程序
					uni.getSetting({
						success: (res) => {
							resolve(res.authSetting['scope.userLocation'] === true);
						},
						fail: () => {
							resolve(false);
						}
					});
					// #endif
					
					// 其他平台，假设没有权限
					// #ifndef APP-PLUS || H5 || MP-WEIXIN || MP-ALIPAY
					resolve(false);
					// #endif
				});
			},
			
			// 处理位置错误
			handleLocationError(err) {
				console.log('位置获取失败:', err);
				this.clockStatus = 'outside';
				this.locationError = '无法获取位置，请授权位置权限';
				
				// 检查位置权限
				this.checkLocationPermission().then(hasPermission => {
					if (!hasPermission) {
						// 没有权限，请求授权
						this.requestLocationPermission();
					} else {
						// 有权限但获取位置失败，可能是GPS未开启
						// #ifdef APP-PLUS
						if (plus.os.name.toLowerCase() === 'android') {
							// 检查GPS是否开启
							const mainActivity = plus.android.runtimeMainActivity();
							const locationManager = plus.android.importClass("android.location.LocationManager");
							const locationService = mainActivity.getSystemService(locationManager);
							const isGPSEnabled = locationService.isProviderEnabled(locationManager.GPS_PROVIDER);
							const isNetworkEnabled = locationService.isProviderEnabled(locationManager.NETWORK_PROVIDER);
							
							if (!isGPSEnabled && !isNetworkEnabled) {
								// GPS未开启，提示开启GPS
								uni.showModal({
									title: '定位服务未开启',
									content: '请开启GPS定位服务',
									confirmText: '去开启',
									success: (res) => {
										if (res.confirm) {
											// 跳转到位置设置页面
											const Intent = plus.android.importClass('android.content.Intent');
											const Settings = plus.android.importClass('android.provider.Settings');
											const intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
											mainActivity.startActivity(intent);
										}
									}
								});
							}
						}
						// #endif
					}
				});
				
				this.isGettingLocation = false;
			},
			
			// 请求位置权限
			requestLocationPermission() {
				// #ifdef APP-PLUS
				if (plus.os.name.toLowerCase() === 'android') {
					// 静默申请权限，不显示任何提示
					const Manifest = plus.android.importClass("android.Manifest");
					plus.android.requestPermissions(
						[Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION],
						(resultObj) => {
							if (resultObj.granted.indexOf(Manifest.permission.ACCESS_FINE_LOCATION) > -1 ||
								resultObj.granted.indexOf(Manifest.permission.ACCESS_COARSE_LOCATION) > -1) {
								// 权限获取成功，尝试获取位置
								this.tryGetLocation();
							}
							// 如果权限被拒绝，不做任何提示，使用默认位置
						}
					);
				} else if (plus.os.name.toLowerCase() === 'ios') {
					// iOS平台，直接请求权限
					const cllocationMgr = plus.ios.import("CLLocationManager");
					const mgr = new cllocationMgr();
					mgr.requestWhenInUseAuthorization(); // 请求使用期间访问位置
					plus.ios.deleteObject(mgr);
					plus.ios.deleteObject(cllocationMgr);
					
					// 延迟后尝试获取位置
					setTimeout(() => {
						this.tryGetLocation();
					}, 1000);
				}
				// #endif
				
				// #ifdef H5
				// H5环境
				if (navigator.geolocation) {
					navigator.geolocation.getCurrentPosition(
						(position) => {
							// 成功获取位置，更新数据
							this.latitude = position.coords.latitude;
							this.longitude = position.coords.longitude;
							this.locationError = '';
							this.hasShownLocationError = false;
							this.clockStatus = 'normal';
						},
						() => {
							// 如果用户拒绝，不做任何提示，使用默认位置
							console.log('H5环境获取位置失败，使用默认位置');
						}
					);
				}
				// #endif
				
				// #ifdef MP-WEIXIN || MP-ALIPAY
				// 微信小程序或支付宝小程序
				uni.authorize({
					scope: 'scope.userLocation',
					success: () => {
						// 授权成功，获取位置
						this.tryGetLocation();
					},
					fail: () => {
						// 授权失败，不做任何提示，使用默认位置
						console.log('小程序环境获取位置授权失败，使用默认位置');
					}
				});
				// #endif
			},
			
			// 打开系统位置设置
			openLocationSettings() {
				// #ifdef APP-PLUS
				if (plus.os.name.toLowerCase() === 'android') {
					const Intent = plus.android.importClass('android.content.Intent');
					const Settings = plus.android.importClass('android.provider.Settings');
					const mainActivity = plus.android.runtimeMainActivity();
					const intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
					mainActivity.startActivity(intent);
				} else if (plus.os.name.toLowerCase() === 'ios') {
					const UIApplication = plus.ios.import("UIApplication");
					const application = UIApplication.sharedApplication();
					const NSURL = plus.ios.import("NSURL");
					const setting_url = NSURL.URLWithString("app-settings:");
					application.openURL(setting_url);
					plus.ios.deleteObject(setting_url);
					plus.ios.deleteObject(application);
					plus.ios.deleteObject(UIApplication);
				}
				// #endif
			},
			
			// 检查是否在考勤范围内
			checkAttendanceArea(latitude, longitude) {
				// 如果后端API不可用，使用本地判断
				const checkLocal = () => {
					// 假设公司位置
					const companyLocation = {
						latitude: 34.341576, // 根据实际情况修改
						longitude: 108.940174 // 根据实际情况修改
					};
					
					// 计算距离
					const distance = this.calculateDistance(
						latitude, longitude,
						companyLocation.latitude, companyLocation.longitude
					);
					
					// 判断是否在范围内（假设500米内可打卡）
					const inArea = distance <= this.attendanceRules.allowedDistance;
					this.clockStatus = inArea ? 'normal' : 'outside';
					
					if (!inArea) {
						uni.showToast({
							title: `您距离考勤点${Math.round(distance)}米，不在打卡范围内`,
							icon: 'none',
							duration: 3000
						});
					}
				};
				
				// 调用后端API检查
				attendanceApi.checkAttendanceArea({
					latitude,
					longitude
				}).then(res => {
					if (res.code === 200) {
						this.clockStatus = res.data.inArea ? 'normal' : 'outside';
						
						// 如果不在范围内，显示距离信息
						if (!res.data.inArea && res.data.distance) {
							uni.showToast({
								title: `您距离考勤点${res.data.distance}米，不在打卡范围内`,
								icon: 'none',
								duration: 3000
							});
						}
					} else {
						// 如果API返回错误，使用本地判断
						checkLocal();
					}
				}).catch(err => {
					console.error('考勤范围校验异常:', err);
					// API调用失败，使用本地判断
					checkLocal();
				});
			},
			
			// 计算两点之间的距离（米）
			calculateDistance(lat1, lon1, lat2, lon2) {
				const R = 6371000; // 地球半径，单位米
				const dLat = this.deg2rad(lat2 - lat1);
				const dLon = this.deg2rad(lon2 - lon1);
				const a = 
					Math.sin(dLat/2) * Math.sin(dLat/2) +
					Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) * 
					Math.sin(dLon/2) * Math.sin(dLon/2);
				const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
				const distance = R * c;
				return distance;
			},
			
			// 角度转弧度
			deg2rad(deg) {
				return deg * (Math.PI/180);
			},
			
			// 获取考勤规则
			getAttendanceRules() {
				attendanceApi.getClockRules()
					.then(res => {
						if (res.code === 200 && res.data) {
							console.log('获取考勤规则成功:', res.data);
							this.attendanceRules = {
								...this.attendanceRules,
								...res.data
							};
						}
					})
					.catch(err => {
						console.error('获取考勤规则失败:', err);
					});
			},
			
			// 获取今日打卡记录
			getTodayClockRecord() {
				attendanceApi.getTodayRecord()
					.then(res => {
						console.log('获取今日打卡记录:', res);
						if (res.code === 200 && res.data) {
							this.clockInTime = res.data.clockInTime || '';
							this.clockOutTime = res.data.clockOutTime || '';
							
							// 如果已有上班打卡记录，显示打卡状态
							if (this.clockInTime && res.data.clockInStatus) {
								const statusText = res.data.clockInStatus === 'late' ? '（迟到）' : '';
								this.clockInTime = this.clockInTime + statusText;
							}
							
							// 如果已有下班打卡记录，显示打卡状态
							if (this.clockOutTime && res.data.clockOutStatus) {
								const statusText = res.data.clockOutStatus === 'early' ? '（早退）' : '';
								this.clockOutTime = this.clockOutTime + statusText;
							}
						}
					})
					.catch(err => {
						console.error('获取今日打卡记录失败:', err);
					});
			},
			
			// 获取最近打卡记录
			getRecentRecords() {
				attendanceApi.getRecentRecords(7)  // 获取最近7天的记录
					.then(res => {
						if (res.code === 200 && res.data && Array.isArray(res.data.records)) {
							this.recentRecords = res.data.records.map(item => {
								return {
									date: item.date,
									week: item.week,
									clockInTime: item.clockInTime || '未打卡',
									clockOutTime: item.clockOutTime || '未打卡',
									clockInStatus: item.clockInStatus || 'normal',
									clockOutStatus: item.clockOutStatus || 'normal'
								};
							});
						} else {
							// 处理返回数据格式不正确的情况
							console.warn('获取最近打卡记录返回数据格式异常:', res);
							this.recentRecords = [];
						}
					})
					.catch(err => {
						console.error('获取最近打卡记录失败:', err);
						this.recentRecords = []; // 确保在错误时也将数组清空
						uni.showToast({
							title: '获取打卡记录失败',
							icon: 'none'
						});
					});
			},
			
			// 处理打卡
			handleClockIn() {
				// 判断是上班打卡还是下班打卡
				const isClockIn = !this.clockInTime;
				const isClockOut = !!this.clockInTime && !this.clockOutTime;
				
				if (!isClockIn && !isClockOut) {
					uni.showToast({
						title: '今日打卡已完成',
						icon: 'info'
					});
					return;
				}
				
				// 先尝试获取最新位置，但不阻止打卡流程
				uni.showLoading({
					title: '打卡中...'
				});
				
				// 尝试获取位置，但不管成功与否都继续打卡
				this.tryGetLocation().finally(() => {
					uni.hideLoading();
					this.submitAttendance();
				});
			},
			
			// 尝试获取位置，但不影响打卡流程
			tryGetLocation() {
				return new Promise((resolve) => {
					// 如果已经在获取位置中，直接返回
					if (this.isGettingLocation) {
						console.log('正在获取位置中，跳过重复请求');
						resolve();
						return;
					}
					
					this.isGettingLocation = true;
					
					// 设置超时，确保不会因为位置获取而阻塞
					const locationTimeout = setTimeout(() => {
						console.log('位置获取超时，使用默认位置');
						this.isGettingLocation = false;
						resolve();
					}, 3000); // 缩短到3秒超时
					
					// 尝试使用原生定位
					if (this.useNativeGeolocation) {
						this.getNativeLocation().then(() => {
							clearTimeout(locationTimeout);
							this.isGettingLocation = false;
							resolve();
						}).catch(() => {
							// 如果原生定位失败，尝试uni定位
							this.getUniLocation().then(() => {
								clearTimeout(locationTimeout);
								this.isGettingLocation = false;
								resolve();
							}).catch(() => {
								// 如果都失败了，使用默认位置
								clearTimeout(locationTimeout);
								this.isGettingLocation = false;
								resolve();
							});
						});
					} else {
						// 直接使用uni定位
						this.getUniLocation().then(() => {
							clearTimeout(locationTimeout);
							this.isGettingLocation = false;
							resolve();
						}).catch(() => {
							// 如果失败了，使用默认位置
							clearTimeout(locationTimeout);
							this.isGettingLocation = false;
							resolve();
						});
					}
					
					// 同时尝试请求位置权限，但不阻塞流程
					this.checkLocationPermission().then(hasPermission => {
						if (!hasPermission) {
							this.requestLocationPermission();
						}
					});
				});
			},
			
			// 显示补卡申请弹窗
			showSupplementModal() {
				// 重置表单数据
				this.resetSupplementForm();
				this.$refs.supplementPopup.open();
			},
			
			// 重置补卡表单
			resetSupplementForm() {
				this.supplementDate = this.formatDate(new Date());
				this.supplementTypeIndex = 0;
				this.supplementReason = '';
				this.supplementImages = [];
			},
			
			// 选择补卡证明图片
			chooseSupplementImage() {
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						// 限制最多3张图片
						if (this.supplementImages.length < 3) {
							this.supplementImages.push(res.tempFilePaths[0]);
						} else {
							uni.showToast({
								title: '最多上传3张图片',
								icon: 'none'
							});
						}
					}
				});
			},
			
			// 删除补卡证明图片
			deleteSupplementImage(index) {
				this.supplementImages.splice(index, 1);
			},
			
			// 提交补卡申请
			submitSupplementApplication() {
				// 表单验证
				if (!this.supplementDate) {
					uni.showToast({
						title: '请选择补卡日期',
						icon: 'none'
					});
					return;
				}
				
				if (!this.supplementReason || this.supplementReason.trim() === '') {
					uni.showToast({
						title: '请输入补卡原因',
						icon: 'none'
					});
					return;
				}
				
				// 显示加载状态
				this.loading = true;
				
				// 准备要提交的数据
				const formData = {
					userId: this.getUserId(),
					date: this.supplementDate,
					type: this.supplementTypeIndex + 1, // 1-上班打卡, 2-下班打卡
					reason: this.supplementReason,
					images: []
				};
				
				// 先上传图片，再提交表单
				if (this.supplementImages.length > 0) {
					this.uploadSupplementImages().then(imageUrls => {
						formData.images = imageUrls;
						this.sendSupplementRequest(formData);
					}).catch(err => {
						this.loading = false;
						uni.showToast({
							title: '图片上传失败',
							icon: 'none'
						});
					});
				} else {
					this.sendSupplementRequest(formData);
				}
			},
			
			// 上传补卡证明图片
			uploadSupplementImages() {
				return new Promise((resolve, reject) => {
					const uploadTasks = this.supplementImages.map(imagePath => {
						return new Promise((uploadResolve, uploadReject) => {
							uni.uploadFile({
								url: this.$api.baseUrl + '/attendance/upload',
								filePath: imagePath,
								name: 'file',
								header: {
									token: uni.getStorageSync('token')
								},
								success: (res) => {
									if (res.statusCode === 200) {
										const data = JSON.parse(res.data);
										if (data.code === 0 && data.data) {
											uploadResolve(data.data.url);
										} else {
											uploadReject(new Error(data.msg || '上传失败'));
										}
									} else {
										uploadReject(new Error('上传失败'));
									}
								},
								fail: (err) => {
									uploadReject(err);
								}
							});
						});
					});
					
					Promise.all(uploadTasks).then(resolve).catch(reject);
				});
			},
			
			// 发送补卡申请请求
			sendSupplementRequest(formData) {
				attendanceApi.submitSupplementApplication(formData).then(res => {
					this.loading = false;
					if (res.code === 0) {
						uni.showToast({
							title: res.msg || '补卡申请已提交',
							icon: 'success'
						});
						this.$refs.supplementPopup.close();
						this.resetSupplementForm();
						
						// 刷新打卡记录
						this.getTodayClockRecord();
						this.getRecentRecords();
					} else {
						uni.showToast({
							title: res.msg || '补卡申请失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					this.loading = false;
					console.error('补卡申请失败:', err);
					uni.showToast({
						title: '补卡申请失败',
						icon: 'none'
					});
				});
			},
			
			// 获取打卡按钮文本
			getClockButtonText() {
				if (!this.clockInTime) {
					return '上班打卡';
				}
				
				if (!this.clockOutTime) {
					return '下班打卡';
				}
				
				return '今日打卡已完成';
			},
			
			// 获取操作描述
			getActionDescription() {
				const now = new Date();
				const currentHour = now.getHours();
				const currentMinute = now.getMinutes();
				
				if (!this.clockInTime) {
					// 上班打卡判断
					const clockInTimeArr = (this.attendanceRules.clockInTime || '08:30').split(':');
					const clockInHour = parseInt(clockInTimeArr[0]);
					const clockInMinute = parseInt(clockInTimeArr[1]);
					
					if (currentHour > clockInHour || (currentHour === clockInHour && currentMinute > clockInMinute)) {
						return `已超过上班时间${this.attendanceRules.clockInTime}，打卡将记为迟到`;
					} else {
						return `上班时间${this.attendanceRules.clockInTime}，请按时打卡`;
					}
				}
				
				if (!this.clockOutTime) {
					// 下班打卡判断
					const clockOutTimeArr = (this.attendanceRules.clockOutTime || '17:30').split(':');
					const clockOutHour = parseInt(clockOutTimeArr[0]);
					const clockOutMinute = parseInt(clockOutTimeArr[1]);
					
					if (currentHour < clockOutHour || (currentHour === clockOutHour && currentMinute < clockOutMinute)) {
						return `下班时间${this.attendanceRules.clockOutTime}，提前打卡将记为早退`;
					} else {
						return `您已完成上班打卡，下班时间${this.attendanceRules.clockOutTime}`;
					}
				}
				return '您已完成今日打卡';
			},
			
			// 格式化日期为YYYY-MM-DD
			formatDate(date) {
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				return `${year}-${month}-${day}`;
			},
			
			// 获取用户ID
			getUserId() {
				const userId = uni.getStorageSync('userId');
				if (!userId) {
					console.warn('未找到用户ID，使用默认值1');
					return 1; // 如果没有找到用户ID，返回默认值1
				}
				return userId || 1; // 如果转换失败，返回默认值1
			},
			
			// 切换定位方式
			toggleLocationMethod() {
				this.useNativeGeolocation = !this.useNativeGeolocation;
				uni.showToast({
					title: `已切换为${this.useNativeGeolocation ? '原生' : '统一'}定位API`,
					icon: 'none',
					duration: 2000
				});
				
				// 重新获取位置
				setTimeout(() => {
					this.getLocation();
				}, 500);
			},
			
			// 提交考勤打卡
			submitAttendance() {
				const now = new Date();
				const clockType = !this.clockInTime ? 'checkin' : 'checkout';
				
				// 判断打卡状态
				let status = 'normal';
				
				if (clockType === 'checkin') {
					// 上班打卡：判断是否迟到
					const clockInTimeArr = this.attendanceRules.clockInTime.split(':');
					const clockInHour = parseInt(clockInTimeArr[0]);
					const clockInMinute = parseInt(clockInTimeArr[1]);
					const clockInTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), clockInHour, clockInMinute);
					
					if (now > clockInTime) {
						status = 'late';
					}
				} else if (clockType === 'checkout') {
					// 下班打卡：判断是否早退
					const clockOutTimeArr = this.attendanceRules.clockOutTime.split(':');
					const clockOutHour = parseInt(clockOutTimeArr[0]);
					const clockOutMinute = parseInt(clockOutTimeArr[1]);
					const clockOutTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), clockOutHour, clockOutMinute);
					
					if (now < clockOutTime) {
						status = 'early';
					}
				}
				
				this.loading = true;
				
				// 显示加载提示
				uni.showLoading({
					title: '打卡中...'
				});
				
				// 保存当前经纬度，用于打卡成功后上传
				const currentLocation = {
					latitude: this.latitude,
					longitude: this.longitude
				};
				
				attendanceApi.submitClock({
					clock_type: clockType,
					latitude: this.latitude,
					longitude: this.longitude,
					status: status, // 打卡状态
					user_id: this.getUserId() // 添加用户ID
				}).then(res => {
					uni.hideLoading();
					if (res.code === 200) {
						uni.showToast({
							title: clockType === 'checkin' ? '上班打卡成功' : '下班打卡成功',
							icon: 'success'
						});
						
						// 更新打卡记录
						this.getTodayClockRecord();
						this.getRecentRecords();
						
						// 尝试上传当前位置记录，但不阻塞流程
						this.uploadTrajectoryRecord(currentLocation.latitude, currentLocation.longitude)
							.catch(err => {
								console.error('打卡后上传位置失败:', err);
							});
						
						// 如果是上班打卡，则启动位置上传
						if (clockType === 'checkin') {
							this.startLocationUpload();
							console.log('启动全局位置上传');
						} else {
							// 如果是下班打卡，则停止位置上传
							this.stopLocationUpload();
							console.log('停止全局位置上传');
						}
					} else {
						uni.showToast({
							title: res.message || '打卡失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					uni.hideLoading();
					console.error('打卡提交失败:', err);
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					});
				}).finally(() => {
					this.loading = false;
				});
			},
			
			// 切换管理员设置
			goToAdminSettings() {
				console.log('正在跳转到考勤规则设置页面');
				// 导航到考勤规则设置页面
				uni.navigateTo({
					url: '/pages/attendance/admin/rules',
					fail: (err) => {
						console.error('页面跳转失败:', err);
						uni.showToast({
							title: '页面跳转失败',
							icon: 'none'
						});
					}
				});
			},
			
			// 上传位置轨迹记录
			uploadTrajectoryRecord(latitude, longitude) {
				// 检查经纬度是否有效
				if (!longitude || !latitude || longitude === 0 || latitude === 0) {
					console.error('经纬度无效，无法上传位置轨迹:', {
						longitude: longitude,
						latitude: latitude
					});
					return Promise.reject({errMsg: '位置参数无效'});
				}
				
				// 获取用户ID
				const userId = this.getUserId();
				
				console.log('上传位置轨迹:', {
					userId: userId,
					longitude: longitude,
					latitude: latitude
				});
				
				// 调用API上传位置数据
				return attendanceApi.uploadPersonTrajectory({
					userId: userId, // 传递userId参数
					employeeId: userId, // 同时传递employeeId参数，值与userId相同
					longitude: longitude,
					latitude: latitude
				}).then(res => {
					if (res.code === 200) {
						console.log('位置轨迹上传成功:', res.data);
						return res.data;
					} else {
						console.error('位置轨迹上传失败:', res.message || '未知错误', res);
						return Promise.reject(res);
					}
				}).catch(err => {
					console.error('位置轨迹上传请求异常:', err);
					return Promise.reject(err);
				});
			},
			
			// 重新获取位置
			retryGetLocation() {
				// 直接请求位置权限
				this.requestLocationPermission();
			},
		}
	}
</script>

<style lang="scss">
	.clock-in-container {
		padding: 30rpx;
		background-color: #f5f7fa;
		min-height: 100vh;
	}
	
	.status-header {
		margin-bottom: 40rpx;
		
		.date-info {
			display: flex;
			flex-direction: column;
			align-items: center;
			margin-bottom: 20rpx;
			
			.date {
				font-size: 32rpx;
				color: #333;
				margin-bottom: 10rpx;
			}
			
			.time {
				font-size: 60rpx;
				font-weight: bold;
				color: #333;
			}
		}
		
		.admin-settings {
			position: absolute;
			top: 20rpx;
			right: 20rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			// background-color: rgba(255, 255, 255, 0.8);
			padding: 10rpx;
			border-radius: 10rpx;
			
			.settings-icon {
				font-size: 40rpx;
				margin-bottom: 5rpx;
			}
			
			.settings-text {
				font-size: 24rpx;
				color: #333;
			}
		}
	}
	
	.clock-status {
		margin-bottom: 40rpx;
		
		.status-card {
			background-color: #fff;
			border-radius: 16rpx;
			padding: 30rpx;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
			
			.work-time {
				display: flex;
				align-items: center;
				margin-bottom: 30rpx;
				
				.time-item {
					flex: 1;
					display: flex;
					flex-direction: column;
					align-items: center;
					
					.time-label {
						font-size: 28rpx;
						color: #666;
						margin-bottom: 10rpx;
					}
					
					.time-value {
						font-size: 36rpx;
						font-weight: bold;
						color: #333;
					}
				}
				
				.time-divider {
					width: 2rpx;
					height: 80rpx;
					background-color: #eee;
				}
			}
			
			.clock-records {
				display: flex;
				border-top: 2rpx solid #f5f5f5;
				padding-top: 30rpx;
				
				.record-item {
					flex: 1;
					display: flex;
					flex-direction: column;
					align-items: center;
					
					.record-label {
						font-size: 28rpx;
						color: #666;
						margin-bottom: 10rpx;
					}
					
					.record-value {
						font-size: 32rpx;
						color: $uni-color-success;
						font-weight: bold;
						
						&.not-clocked {
							color: #999;
						}
					}
				}
			}
		}
	}
	
	.clock-action {
		margin-bottom: 60rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		
		.clock-circle {
			width: 300rpx;
			height: 300rpx;
			border-radius: 50%;
			background: linear-gradient(135deg, #4483e5, #6a9eef);
			display: flex;
			justify-content: center;
			align-items: center;
			box-shadow: 0 10rpx 30rpx rgba(106, 158, 239, 0.3);
			margin-bottom: 30rpx;
			transition: all 0.3s ease;
			
			.circle-inner {
				width: 260rpx;
				height: 260rpx;
				border-radius: 50%;
				background-color: #fff;
				display: flex;
				justify-content: center;
				align-items: center;
			}
			
			.clock-text {
				font-size: 36rpx;
				font-weight: bold;
				color: #4483e5;
			}
		}
		
		.action-desc {
			font-size: 28rpx;
			color: #666;
			text-align: center;
		}
	}
	
	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 20rpx;
		position: relative;
		padding-left: 20rpx;
		
		&::before {
			content: '';
			position: absolute;
			left: 0;
			top: 8rpx;
			width: 8rpx;
			height: 32rpx;
			background-color: $uni-color-primary;
			border-radius: 4rpx;
		}
	}
	
	.recent-records {
		.record-list {
			background-color: #fff;
			border-radius: 16rpx;
			padding: 20rpx;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
			
			.record-empty {
				padding: 40rpx 0;
				text-align: center;
				color: #999;
				font-size: 28rpx;
			}
			
			.record-item {
				display: flex;
				padding: 20rpx 0;
				border-bottom: 2rpx solid #f5f5f5;
				
				&:last-child {
					border-bottom: none;
				}
				
				.record-date {
					width: 180rpx;
					display: flex;
					flex-direction: column;
					
					.date {
						font-size: 28rpx;
						color: #333;
						margin-bottom: 6rpx;
					}
					
					.week {
						font-size: 24rpx;
						color: #999;
					}
				}
				
				.record-details {
					flex: 1;
					
					.detail-item {
						display: flex;
						margin-bottom: 10rpx;
						
						&:last-child {
							margin-bottom: 0;
						}
						
						.detail-label {
							width: 80rpx;
							font-size: 28rpx;
							color: #666;
						}
						
						.detail-value {
							flex: 1;
							font-size: 28rpx;
							color: $uni-color-success;
							
							&.abnormal {
								color: $uni-color-warning;
							}
							
							.status-tag {
								display: inline-block;
								font-size: 22rpx;
								padding: 2rpx 10rpx;
								border-radius: 4rpx;
								background-color: rgba(250, 173, 20, 0.1);
								color: $uni-color-warning;
								margin-left: 10rpx;
							}
						}
					}
				}
			}
		}
	}
	
	.face-verify-popup {
		width: 600rpx;
		background-color: #fff;
		border-radius: 20rpx;
		overflow: hidden;
		
		.popup-title {
			font-size: 32rpx;
			font-weight: bold;
			padding: 30rpx;
			text-align: center;
			border-bottom: 1rpx solid #eee;
		}
		
		.popup-content {
			padding: 30rpx;
		}
		
		.camera-container, .photo-preview {
			width: 400rpx;
			height: 400rpx;
			margin: 0 auto 30rpx;
			background-color: #f5f5f5;
			border-radius: 10rpx;
			overflow: hidden;
		}
		
		.popup-footer {
			display: flex;
			justify-content: space-between;
			padding-top: 20rpx;
			
			button {
				flex: 1;
				margin: 0 20rpx;
				font-size: 28rpx;
				border-radius: 40rpx;
				
				&.btn-cancel {
					background-color: #f5f5f5;
					color: #333;
				}
				
				&.btn-confirm {
					background-color: #007AFF;
					color: #fff;
				}
			}
		}
	}
	
	.supplement-popup {
		width: 600rpx;
		background-color: #fff;
		border-radius: 20rpx;
		overflow: hidden;
		
		.popup-title {
			font-size: 32rpx;
			font-weight: bold;
			padding: 30rpx;
			text-align: center;
			border-bottom: 1rpx solid #eee;
		}
		
		.popup-content {
			padding: 30rpx;
		}
		
		.form-item {
			margin-bottom: 20rpx;
			
			.form-label {
				font-size: 28rpx;
				color: #333;
				margin-bottom: 10rpx;
			}
			
			.form-input {
				background-color: #f5f5f5;
				border-radius: 10rpx;
				padding: 20rpx;
				font-size: 28rpx;
				
				textarea {
					width: 100%;
					height: 150rpx;
				}
			}
		}
		
		.popup-footer {
			display: flex;
			justify-content: space-between;
			padding-top: 20rpx;
			
			button {
				flex: 1;
				margin: 0 20rpx;
				font-size: 28rpx;
				border-radius: 40rpx;
				
				&.btn-cancel {
					background-color: #f5f5f5;
					color: #333;
				}
				
				&.btn-confirm {
					background-color: #007AFF;
					color: #fff;
				}
			}
		}
	}
	
	.supplement-images {
		display: flex;
		flex-wrap: wrap;
		margin-top: 10rpx;
		
		.image-item {
			position: relative;
			width: 150rpx;
			height: 150rpx;
			margin-right: 20rpx;
			margin-bottom: 20rpx;
			border-radius: 10rpx;
			overflow: hidden;
			
			.supplement-image {
				width: 100%;
				height: 100%;
			}
			
			.image-delete {
				position: absolute;
				top: 0;
				right: 0;
				width: 40rpx;
				height: 40rpx;
				background-color: rgba(0, 0, 0, 0.5);
				color: #fff;
				font-size: 32rpx;
				display: flex;
				justify-content: center;
				align-items: center;
			}
		}
		
		.image-add {
			width: 150rpx;
			height: 150rpx;
			background-color: #f5f5f5;
			border: 1rpx dashed #ccc;
			border-radius: 10rpx;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			
			.image-add-icon {
				font-size: 48rpx;
				color: #999;
				line-height: 1;
				margin-bottom: 10rpx;
			}
			
			.image-add-text {
				font-size: 24rpx;
				color: #999;
			}
		}
	}
	
	.clock-btn {
		margin-top: 40rpx;
		width: 90%;
		height: 90rpx;
		background: linear-gradient(135deg, #4B79A1, #283E51);
		color: white;
		font-size: 32rpx;
		border-radius: 45rpx;
		box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.1);
		display: flex;
		justify-content: center;
		align-items: center;
		
		&.disabled {
			background: linear-gradient(135deg, #ccc, #999);
			box-shadow: none;
		}
	}
	
	.date-picker {
		display: flex;
		align-items: center;
		justify-content: space-between;
		
		.icon-calendar {
			font-size: 32rpx;
			color: #999;
		}
	}
</style> 