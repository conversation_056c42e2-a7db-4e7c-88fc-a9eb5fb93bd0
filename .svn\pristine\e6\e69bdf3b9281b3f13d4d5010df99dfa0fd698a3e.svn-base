package com.heating.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.heating.entity.hes.THes;

import java.util.List;
import java.util.Map;

@Repository
public interface HesRepository extends JpaRepository<THes, Long> {

    @Query("SELECT new map(h.id as id, h.name as name) FROM THes h")
    List<Map<String, Object>> findAllStations();

    @Query("SELECT new map(h.id as id, h.name as name) FROM THes h WHERE h.heatUnitName = (SELECT u.name FROM THeatUnit u WHERE u.id = :heatUnitId) ORDER BY h.name")
    List<Map<String, Object>> findStationsByHeatUnitId(@Param("heatUnitId") Long heatUnitId);
    
    /**
     * 统计指定状态的换热站数量
     * @param isRun 状态码（1表示在线）
     * @return 符合条件的换热站数量
     */
    @Query("SELECT COUNT(h) FROM THes h WHERE h.isRun = :isRun")
    long countByStatus(@Param("isRun") int isRun);
}