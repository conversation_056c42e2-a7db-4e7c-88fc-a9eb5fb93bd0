-- 插入小区表测试数据
INSERT INTO `t_useheatunit` (`Name`, `UnitType`, `UseHeatNo`, `Addr`, `BuiltArea`, `BuiltYear`, `FloorTotal`, `HouseTotal`, `UnitManage`, `UnitManageContact`, `UnitManageTel`) VALUES
('金色家园', '住宅', 'JD001', '市中心路123号', 12000.5, 2010, 18, 108, '张经理', '张三', '13800138001'),
('翠湖花园', '住宅', 'JD002', '湖滨路456号', 15000.8, 2012, 20, 160, '李经理', '李四', '13800138002'),
('阳光小区', '住宅', 'JD003', '阳光大道789号', 18000.3, 2015, 25, 200, '王经理', '王五', '13800138003'),
('和平广场', '商业', 'JD004', '和平路234号', 20000.0, 2008, 15, 80, '赵经理', '赵六', '13800138004'),
('幸福家园', '住宅', 'JD005', '幸福街567号', 13500.6, 2013, 16, 96, '钱经理', '钱七', '13800138005'),
('康乐小区', '住宅', 'JD006', '康乐路890号', 16800.2, 2011, 22, 176, '孙经理', '孙八', '13800138006'),
('富贵园', '住宅', 'JD007', '富贵街345号', 14200.7, 2014, 19, 114, '周经理', '周九', '13800138007'),
('吉祥花园', '住宅', 'JD008', '吉祥路678号', 17500.4, 2016, 24, 192, '吴经理', '吴十', '13800138008'),
('商贸中心', '商业', 'JD009', '商贸路901号', 25000.0, 2009, 30, 150, '郑经理', '郑十一', '13800138009'),
('龙腾苑', '住宅', 'JD010', '龙腾大道234号', 19800.9, 2017, 28, 224, '冯经理', '冯十二', '13800138010'),
('华府名园', '住宅', 'JD011', '华府街567号', 16300.5, 2018, 21, 168, '陈经理', '陈十三', '13800138011'),
('盛世华庭', '住宅', 'JD012', '盛世路890号', 21000.3, 2019, 26, 208, '楚经理', '楚十四', '13800138012'),
('御景园', '住宅', 'JD013', '御景街123号', 18500.7, 2015, 23, 184, '魏经理', '魏十五', '13800138013'),
('星河湾', '住宅', 'JD014', '星河路456号', 22000.4, 2016, 27, 216, '蒋经理', '蒋十六', '13800138014'),
('财富中心', '商业', 'JD015', '财富大道789号', 28000.0, 2010, 35, 175, '沈经理', '沈十七', '13800138015'),
('安居苑', '住宅', 'JD016', '安居街012号', 15800.6, 2012, 20, 160, '韩经理', '韩十八', '13800138016'),
('祥和园', '住宅', 'JD017', '祥和路345号', 17200.8, 2013, 22, 176, '杨经理', '杨十九', '13800138017'),
('朝阳花园', '住宅', 'JD018', '朝阳街678号', 20500.2, 2014, 25, 200, '朱经理', '朱二十', '13800138018'),
('和谐家园', '住宅', 'JD019', '和谐路901号', 19300.5, 2015, 24, 192, '秦经理', '秦二一', '13800138019'),
('福星苑', '住宅', 'JD020', '福星街234号', 16800.7, 2016, 21, 168, '尤经理', '尤二二', '13800138020');

-- 插入换热站表测试数据
INSERT INTO `t_hes` (`UseHeatUnit_name`, `Name`, `hescode`, `usedyear`, `runMode`, `isUsed`, `connect_opc`, `design_load`, `design_flow`, `isHeating`) VALUES
('金色家园', '金色换热站1', 1001, 2010, 1, 1, 1, 500.5, 100.5, 1),
('金色家园', '金色换热站2', 1002, 2010, 1, 1, 1, 520.5, 102.5, 1),
('翠湖花园', '翠湖换热站1', 1003, 2012, 1, 1, 1, 600.0, 120.0, 1),
('翠湖花园', '翠湖换热站2', 1004, 2012, 1, 1, 1, 580.5, 116.0, 1),
('阳光小区', '阳光换热站1', 1005, 2015, 1, 1, 1, 700.0, 140.0, 1),
('和平广场', '和平换热站1', 1006, 2008, 1, 1, 1, 800.0, 160.0, 1),
('幸福家园', '幸福换热站1', 1007, 2013, 1, 1, 1, 550.0, 110.0, 1),
('康乐小区', '康乐换热站1', 1008, 2011, 1, 1, 1, 650.0, 130.0, 1),
('富贵园', '富贵换热站1', 1009, 2014, 1, 1, 1, 570.0, 114.0, 1),
('吉祥花园', '吉祥换热站1', 1010, 2016, 1, 1, 1, 680.0, 136.0, 1),
('商贸中心', '商贸换热站1', 1011, 2009, 1, 1, 1, 900.0, 180.0, 1),
('龙腾苑', '龙腾换热站1', 1012, 2017, 1, 1, 1, 750.0, 150.0, 1),
('华府名园', '华府换热站1', 1013, 2018, 1, 1, 1, 620.0, 124.0, 1),
('盛世华庭', '盛世换热站1', 1014, 2019, 1, 1, 1, 800.0, 160.0, 1),
('御景园', '御景换热站1', 1015, 2015, 1, 1, 1, 700.0, 140.0, 1),
('星河湾', '星河换热站1', 1016, 2016, 1, 1, 1, 820.0, 164.0, 1),
('财富中心', '财富换热站1', 1017, 2010, 1, 1, 1, 950.0, 190.0, 1),
('安居苑', '安居换热站1', 1018, 2012, 1, 1, 1, 600.0, 120.0, 1),
('祥和园', '祥和换热站1', 1019, 2013, 1, 1, 1, 650.0, 130.0, 1),
('朝阳花园', '朝阳换热站1', 1020, 2014, 1, 1, 1, 750.0, 150.0, 1);

-- 插入数据字典类型表测试数据
INSERT INTO `tb_dict` (`name`, `code`, `sort`, `note`, `create_time`, `mark`) VALUES
('用户类型', 'user_type', 1, '用户类型字典', NOW(), 1),
('状态标识', 'status', 2, '状态标识字典', NOW(), 1),
('性别', 'gender', 3, '性别字典', NOW(), 1),
('职位', 'position', 4, '职位字典', NOW(), 1),
('部门', 'department', 5, '部门字典', NOW(), 1),
('学历', 'education', 6, '学历字典', NOW(), 1),
('权限级别', 'auth_level', 7, '权限级别字典', NOW(), 1),
('设备类型', 'device_type', 8, '设备类型字典', NOW(), 1),
('维修状态', 'repair_status', 9, '维修状态字典', NOW(), 1),
('支付方式', 'payment_method', 10, '支付方式字典', NOW(), 1),
('工单类型', 'work_order_type', 11, '工单类型字典', NOW(), 1),
('优先级', 'priority', 12, '优先级字典', NOW(), 1),
('区域', 'region', 13, '区域字典', NOW(), 1),
('建筑类型', 'building_type', 14, '建筑类型字典', NOW(), 1),
('计量单位', 'unit', 15, '计量单位字典', NOW(), 1),
('评价等级', 'rating', 16, '评价等级字典', NOW(), 1),
('运行模式', 'operation_mode', 17, '运行模式字典', NOW(), 1),
('告警级别', 'alarm_level', 18, '告警级别字典', NOW(), 1),
('设备状态', 'device_status', 19, '设备状态字典', NOW(), 1),
('系统模块', 'system_module', 20, '系统模块字典', NOW(), 1);

-- 插入数据字典子表测试数据
INSERT INTO `tb_dict_data` (`name`, `code`, `dict_id`, `status`, `sort`, `create_time`, `mark`) VALUES
('管理员', 'admin', 1, 1, 1, NOW(), 1),
('普通用户', 'user', 1, 1, 2, NOW(), 1),
('启用', 'enable', 2, 1, 1, NOW(), 1),
('禁用', 'disable', 2, 1, 2, NOW(), 1),
('男', 'male', 3, 1, 1, NOW(), 1),
('女', 'female', 3, 1, 2, NOW(), 1),
('经理', 'manager', 4, 1, 1, NOW(), 1),
('主管', 'supervisor', 4, 1, 2, NOW(), 1),
('技术部', 'tech', 5, 1, 1, NOW(), 1),
('运营部', 'operation', 5, 1, 2, NOW(), 1),
('本科', 'bachelor', 6, 1, 1, NOW(), 1),
('硕士', 'master', 6, 1, 2, NOW(), 1),
('高级权限', 'high', 7, 1, 1, NOW(), 1),
('中级权限', 'medium', 7, 1, 2, NOW(), 1),
('换热站', 'hes', 8, 1, 1, NOW(), 1),
('管网', 'pipeline', 8, 1, 2, NOW(), 1),
('待维修', 'pending', 9, 1, 1, NOW(), 1),
('维修中', 'repairing', 9, 1, 2, NOW(), 1),
('现金', 'cash', 10, 1, 1, NOW(), 1),
('在线支付', 'online', 10, 1, 2, NOW(), 1),
('紧急', 'urgent', 11, 1, 1, NOW(), 1);

-- 插入角色表测试数据
INSERT INTO `tb_role` (`name`, `code`, `status`, `note`, `sort`, `create_time`, `mark`) VALUES
('超级管理员', 'super_admin', 1, '系统超级管理员', 1, NOW(), 1),
('系统管理员', 'admin', 1, '系统管理员', 2, NOW(), 1),
('运营经理', 'operation_manager', 1, '运营部门经理', 3, NOW(), 1),
('技术主管', 'tech_supervisor', 1, '技术部门主管', 4, NOW(), 1),
('维修工程师', 'maintenance_engineer', 1, '维修工程师', 5, NOW(), 1),
('客服专员', 'customer_service', 1, '客服专员', 6, NOW(), 1),
('财务主管', 'finance_supervisor', 1, '财务部门主管', 7, NOW(), 1),
('人事专员', 'hr_specialist', 1, '人事专员', 8, NOW(), 1),
('质检员', 'quality_inspector', 1, '质量检查员', 9, NOW(), 1),
('数据分析师', 'data_analyst', 1, '数据分析师', 10, NOW(), 1),
('安全主管', 'security_supervisor', 1, '安全主管', 11, NOW(), 1),
('库存管理员', 'inventory_manager', 1, '库存管理员', 12, NOW(), 1),
('培训讲师', 'trainer', 1, '培训讲师', 13, NOW(), 1),
('项目经理', 'project_manager', 1, '项目经理', 14, NOW(), 1),
('区域主管', 'area_supervisor', 1, '区域主管', 15, NOW(), 1),
('设备管理员', 'equipment_manager', 1, '设备管理员', 16, NOW(), 1),
('调度员', 'dispatcher', 1, '调度员', 17, NOW(), 1),
('巡检员', 'inspector', 1, '巡检员', 18, NOW(), 1),
('值班主管', 'duty_supervisor', 1, '值班主管', 19, NOW(), 1),
('系统操作员', 'system_operator', 1, '系统操作员', 20, NOW(), 1);

-- 插入用户表测试数据
INSERT INTO `t_userapp` (`name`, `pwd`, `phone`, `nlevel`, `role_id`, `dt`, `headpic`) VALUES
('admin', 'e10adc3949ba59abbe56e057f20f883e', '13800138001', 1, '1', NOW(), 'default.jpg'),
('manager', 'e10adc3949ba59abbe56e057f20f883e', '13800138002', 2, '2', NOW(), 'default.jpg'),
('zhangsan', 'e10adc3949ba59abbe56e057f20f883e', '13800138003', 3, '3', NOW(), 'default.jpg'),
('lisi', 'e10adc3949ba59abbe56e057f20f883e', '13800138004', 3, '4', NOW(), 'default.jpg'),
('wangwu', 'e10adc3949ba59abbe56e057f20f883e', '13800138005', 3, '5', NOW(), 'default.jpg'),
('zhaoliu', 'e10adc3949ba59abbe56e057f20f883e', '13800138006', 3, '6', NOW(), 'default.jpg'),
('sunqi', 'e10adc3949ba59abbe56e057f20f883e', '13800138007', 3, '7', NOW(), 'default.jpg'),
('zhouba', 'e10adc3949ba59abbe56e057f20f883e', '13800138008', 3, '8', NOW(), 'default.jpg'),
('wujiu', 'e10adc3949ba59abbe56e057f20f883e', '13800138009', 3, '9', NOW(), 'default.jpg'),
('zhengshi', 'e10adc3949ba59abbe56e057f20f883e', '13800138010', 3, '10', NOW(), 'default.jpg'),
('operator1', 'e10adc3949ba59abbe56e057f20f883e', '13800138011', 4, '11', NOW(), 'default.jpg'),
('operator2', 'e10adc3949ba59abbe56e057f20f883e', '13800138012', 4, '12', NOW(), 'default.jpg'),
('operator3', 'e10adc3949ba59abbe56e057f20f883e', '13800138013', 4, '13', NOW(), 'default.jpg'),
('operator4', 'e10adc3949ba59abbe56e057f20f883e', '13800138014', 4, '14', NOW(), 'default.jpg'),
('operator5', 'e10adc3949ba59abbe56e057f20f883e', '13800138015', 4, '15', NOW(), 'default.jpg'),
('user1', 'e10adc3949ba59abbe56e057f20f883e', '13800138016', 5, '16', NOW(), 'default.jpg'),
('user2', 'e10adc3949ba59abbe56e057f20f883e', '13800138017', 5, '17', NOW(), 'default.jpg'),
('user3', 'e10adc3949ba59abbe56e057f20f883e', '13800138018', 5, '18', NOW(), 'default.jpg'),
('user4', 'e10adc3949ba59abbe56e057f20f883e', '13800138019', 5, '19', NOW(), 'default.jpg'),
('user5', 'e10adc3949ba59abbe56e057f20f883e', '13800138020', 5, '20', NOW(), 'default.jpg'); 