<template>
	<view class="patrol-execute-container">
		<!-- 加载状态 -->
		<view class="loading-container" v-if="isLoading">
			<view class="loading-spinner"></view>
			<text class="loading-text">正在加载巡检任务...</text>
		</view>
		
		<view v-else>
			<!-- 顶部进度条 -->
			<view class="progress-bar">
				<view class="progress-inner" :style="{ width: progressPercent + '%' }"></view>
			</view>
			<view class="progress-info">
				<text>{{ currentTaskIndex + 1 }}/巡检项目总数：{{ planTasks.length }}</text>
				<text>{{ progressPercent }}%</text>
			</view>
			
			<!-- 当前任务内容 -->
			<view class="task-card">
				<view class="task-header">
					<text class="task-title">{{ currentTask.itemName }}</text>
				</view>
				
				<view class="task-details">
					<view class="detail-row">
						<text class="detail-label">巡检对象</text>
						<text class="detail-value">{{ currentTask.deviceName }}</text>
					</view>
					<view class="detail-row">
						<text class="detail-label">巡检类型</text>
						<text class="detail-value">{{ currentTask.categoryName }}</text>
					</view>
					<view class="detail-row">
						<text class="detail-label">巡检内容</text>
						<text class="detail-value content-description">{{ currentTask.description }}</text>
					</view>
					<view class="detail-row">
						<text class="detail-label">检测方法</text>
						<text class="detail-value">{{ currentTask.checkMethod }}</text>
					</view>
					<view class="detail-row" v-if="currentTask.unit">
						<text class="detail-label">单位</text>
						<text class="detail-value">{{ currentTask.unit }}</text>
					</view>
					<view class="detail-row">
						<text class="detail-label">重要性</text>
						<text class="detail-value importance-tag" :class="getImportanceClass(currentTask.importance)">
							{{ getImportanceLabel(currentTask.importance) }}
						</text>
					</view>
				</view>
			</view>
			
			<!-- 巡检结果录入 -->
			<view class="input-card">
				<view class="card-header">
					<text>实际值</text>
					<text v-if="currentTask.paramType === 'numeric'" class="unit-text">{{currentTask.unit}}</text>
				</view>
				<view class="card-content">
					<input v-if="currentTask.paramType === 'numeric' || currentTask.paramType === 'selection' " 
						type="text" 
						class="form-input" 
						v-model="taskResult.actualValue" 
						placeholder="请输入数值" />
<!-- 					<picker v-else-if="currentTask.paramType === 'selection'" 
						:value="taskResult.actualValue" 
						:range="currentTask.options || []" 
						@change="onSelectionChange">
						<view class="picker-value">
							{{taskResult.actualValue || '请选择'}}
						</view>
					</picker> -->
			        <input v-else class="form-input" v-model="taskResult.actualValue" placeholder="请输入实际值" />
				</view>
				<view class="card-header" style="margin-top: 30rpx;">
					<text>检查结果</text>
					<text v-if="currentTask.paramType === 'numeric'" class="unit-text">{{currentTask.unit}}</text>
				</view>
				<view class="card-content">
					<view class="radio-group">
						<view class="radio-item" :class="{'radio-selected': taskResult.actualValue === '正常'}" @click="onBooleanChange({detail:{value:'正常'}})">
							<view class="radio-dot"></view>
							<text>正常</text>
						</view>
						<view class="radio-item" :class="{'radio-selected': taskResult.actualValue === '异常'}" @click="onBooleanChange({detail:{value:'异常'}})">
							<view class="radio-dot"></view>
							<text>异常</text>
						</view>
					</view>
				</view>
			</view>
		
			<view class="input-card">
				<view class="card-header">
					<text>备注</text>
				</view>
				<view class="card-content">
					<textarea class="form-textarea" v-model="taskResult.remark" placeholder="请输入备注信息（选填）"></textarea>
				</view>
			</view>
			
			<view class="input-card">
				<view class="card-header">
					<text>现场照片</text>
				</view>
				<view class="card-content">
					<view class="image-list">
						<view class="image-item" v-for="(image, index) in taskResult.images" :key="index">
							<image :src="image.url" mode="aspectFill" @click="previewImage(image.url)"></image>
							<view class="delete-icon" @click="deleteImage(index)">×</view>
						</view>
						<view class="image-upload" @click="uploadImage" v-if="taskResult.images.length < 3">
							<text class="upload-icon">+</text>
							<text class="upload-text">上传照片</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 底部操作按钮 -->
			<view class="action-buttons">
				<view class="action-btn prev" @click="prevTask" v-if="currentTaskIndex > 0">
					<text>上一项</text>
				</view>
				<view class="action-btn skip" @click="skipTask" v-if="currentTaskIndex < planTasks.length - 1">
					<text>跳过</text>
				</view>
				<view class="action-btn submit" @click="submitTask">
					<text>{{ currentTaskIndex < planTasks.length - 1 ? '下一项' : '完成' }}</text>
				</view>
			</view>
		</view>
		
		<!-- 确认跳过弹窗 -->
		<uni-popup ref="skipPopup" type="center">
			<view class="popup-content">
				<view class="popup-title">确认跳过</view>
				<view class="popup-message">确定要跳过当前巡检项吗？</view>
				<view class="popup-buttons">
					<view class="popup-button cancel" @click="cancelSkip">取消</view>
					<view class="popup-button confirm" @click="confirmSkip">确认</view>
				</view>
			</view>
		</uni-popup>
		
		<!-- 确认完成弹窗 -->
		<uni-popup ref="completePopup" type="center">
			<view class="popup-content">
				<view class="popup-title">巡检完成</view>
				<view class="popup-message">所有巡检项已完成，是否提交？</view>
				<view class="popup-buttons">
					<view class="popup-button cancel" @click="cancelComplete">继续检查</view>
					<view class="popup-button confirm" @click="confirmComplete">确认提交</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>

	import { patrolApi } from '@/utils/api.js';
	import uploadUtils from '@/utils/upload.js';

	export default {
		data() {
			return {
				planId: '',
				planTasks: [],
				currentTaskIndex: 0,
				taskResult: {
					actualValue: '',
					isAbnormal: false,
					remark: '',
					images: []
				},
				isLoading: false,
				isSkipping: false,
				isCompleting: false,
				completedResults: [] // 存储所有已完成的巡检结果
			}
		},
		computed: {
			currentTask() {
				return this.planTasks[this.currentTaskIndex] || {};
			},
			progressPercent() {
				if (!this.planTasks.length) return 0;
				const completedTasks = this.planTasks.filter(task => task.status === 'completed').length;
				return Math.floor((completedTasks / this.planTasks.length) * 100);
			}
		},
		onLoad(options) {
			this.planId = options.planId;
			this.id = options.id;
			console.log("记录id=",options.id)
			console.log("计划id=",options.planId)
			this.loadPlanTasks();
		},
		methods: {
			// 格式化日期时间为 YYYY-MM-DD HH:MM:SS
			formatDateTime(dateStr) {
				const date = new Date(dateStr);
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				const seconds = String(date.getSeconds()).padStart(2, '0');
				
				return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
			},
			
			// 加载巡检任务列表
			loadPlanTasks() {
				this.isLoading = true; 
				// 显示加载中提示
				uni.showLoading({
					title: '加载中...'
				});
				
				patrolApi.getItemList({
					planId: this.planId
				}).then(res => {
					if (res.data && Array.isArray(res.data)) {
						// 处理API返回的数据，添加状态字段
						this.planTasks = res.data.map(item => ({
							...item,
							status: 'pending', // 初始状态都设为待处理
							completedTime: null
						}));
						this.findNextPendingTask();
					} else {
						uni.showToast({
							title: '获取数据格式错误',
							icon: 'none'
						});
					}
					this.isLoading = false;
					uni.hideLoading();
				}).catch(err => {
					console.warn('获取巡检任务列表失败', err);
					this.isLoading = false;
					uni.hideLoading();
					// 显示错误提示
					uni.showToast({
						title: '加载任务失败，请重试',
						icon: 'none'
					});
				}); 
			},
			
			// 查找下一个待处理任务
			findNextPendingTask() {
				const pendingTaskIndex = this.planTasks.findIndex(task => task.status === 'pending');
				if (pendingTaskIndex !== -1) {
					this.currentTaskIndex = pendingTaskIndex;
					this.resetTaskResult();
				}
			},
			
			// 重置当前任务的录入结果
			resetTaskResult() {
				this.taskResult = {
					actualValue: '',
					isAbnormal: false,
					remark: '',
					images: []
				};
			},
			
			// 选择图片
			uploadImage() {
				uni.chooseImage({
					count: 3 - this.taskResult.images.length,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						// 显示上传中提示
						uni.showLoading({
							title: '上传中...',
							mask: true
						});
						
						// 检查token是否存在，不存在则提示用户登录
						const token = uni.getStorageSync('token');
						if (!token) {
							uni.hideLoading();
							uni.showToast({
								title: '请先登录',
								icon: 'none'
							});
							return;
						}
						
						// 逐个上传图片
						const uploadPromises = res.tempFilePaths.map(path => {
							return uploadUtils.uploadImage(path)
								.then(serverPath => {
									// 保存本地路径和服务器路径
									this.taskResult.images.push({
										url: path,
										serverUrl: serverPath
									});
									return serverPath;
								});
						});
						
						// 等待所有图片上传完成
						Promise.all(uploadPromises)
							.then(() => {
								uni.hideLoading();
								uni.showToast({
									title: '上传成功',
									icon: 'success'
								});
							})
							.catch(err => {
								uni.hideLoading();
								uni.showToast({
									title: err.message || '上传失败',
									icon: 'none'
								});
							});
					}
				});
			},
			
			// 删除图片
			deleteImage(index) {
				this.taskResult.images.splice(index, 1);
			},
			
			// 预览图片
			previewImage(url) {
				const previewUrls = this.taskResult.images.map(img => img.url);
				
				uni.previewImage({
					urls: previewUrls,
					current: url
				});
			},
			
			// 上一个任务
			prevTask() {
				if (this.currentTaskIndex > 0) {
					this.currentTaskIndex--;
					this.resetTaskResult();
					
					// 如果任务已完成，填充已有数据
					const task = this.planTasks[this.currentTaskIndex];
					if (task.status === 'completed') {
						this.taskResult.actualValue = task.actualValue || '';
						this.taskResult.isAbnormal = !!task.isAbnormal;
						this.taskResult.remark = task.remark || '';
						this.taskResult.images = task.images || [];
					}
				}
			},
			
			// 跳过任务
			skipTask() {
				this.isSkipping = true;
				this.$refs.skipPopup.open();
			},
			
			// 取消跳过
			cancelSkip() {
				this.$refs.skipPopup.close();
				this.isSkipping = false;
			},
			
			// 确认跳过
			confirmSkip() {
				this.$refs.skipPopup.close();
				this.isSkipping = false;
				
				// 显示加载中
				uni.showLoading({
					title: '保存中...'
				});
				
				// 创建跳过的任务结果
				const skippedResult = {
					itemId: this.currentTask.id,
					actualValue: '',
					isAbnormal: false,
					remark: '已跳过',
					images: [],
					skipped: true
				};
				
				// 更新当前任务状态
				this.planTasks[this.currentTaskIndex] = {
					...this.planTasks[this.currentTaskIndex],
					status: 'skipped',
					skippedTime: this.formatDateTime(new Date().toISOString())
				};
				
				// 保存到完成结果数组，标记为跳过
				const existingIndex = this.completedResults.findIndex(item => item.itemId === skippedResult.itemId);
				if (existingIndex !== -1) {
					this.completedResults[existingIndex] = skippedResult;
				} else {
					this.completedResults.push(skippedResult);
				}
				
				uni.hideLoading();
				
				// 提示
				uni.showToast({
					title: '已跳过此项',
					icon: 'none'
				});
				
				// 前进到下一个任务
				if (this.currentTaskIndex < this.planTasks.length - 1) {
					this.currentTaskIndex++;
					this.resetTaskResult();
				} else {
					// 如果是最后一个任务，询问是否完成整个巡检
					this.isCompleting = true;
					this.$refs.completePopup.open();
				}
			},
			
			// 提交任务
			submitTask() {
				// 验证输入
				if (!this.taskResult.actualValue) {
					uni.showToast({
						title: '请输入实际值',
						icon: 'none'
					});
					return;
				}
				
				// 显示提交中
				// uni.showLoading({
				// 	title: '保存中...'
				// });
				
				// 处理图片数据，提取服务器路径
				const processedImages = this.taskResult.images.map(img => ({
					url: img.url,
					serverUrl: img.serverUrl
				}));
				
				// 保存当前任务结果
				const currentResult = {
					itemId: this.currentTask.id,
					actualValue: this.taskResult.actualValue,
					isAbnormal: this.taskResult.isAbnormal || this.taskResult.actualValue === '异常',
					remark: this.taskResult.remark || '',
					images: processedImages || []
				};
				
				// 更新当前任务状态
				this.planTasks[this.currentTaskIndex] = {
					...this.planTasks[this.currentTaskIndex],
					actualValue: this.taskResult.actualValue,
					isAbnormal: this.taskResult.isAbnormal || this.taskResult.actualValue === '异常',
					remark: this.taskResult.remark,
					images: processedImages,
					status: 'completed',
					completedTime: this.formatDateTime(new Date().toISOString())
				};
				
				// 保存到完成结果数组中
				// 检查是否已存在，如果存在则更新，否则添加
				const existingIndex = this.completedResults.findIndex(item => item.itemId === currentResult.itemId);
				if (existingIndex !== -1) {
					this.completedResults[existingIndex] = currentResult;
				} else {
					this.completedResults.push(currentResult);
				}
				
				uni.hideLoading();
				// uni.showToast({
				// 	title: '保存成功',
				// 	icon: 'success'
				// });
				
				if (this.currentTaskIndex < this.planTasks.length - 1) {
					// 如果还有下一个任务，前进到下一个
					this.currentTaskIndex++;
					this.resetTaskResult();
				} else {
					// 如果是最后一个任务，询问是否完成整个巡检
					this.isCompleting = true;
					this.$refs.completePopup.open();
				}
			},
			
			// 取消完成
			cancelComplete() {
				this.isCompleting = false;
				this.$refs.completePopup.close();
			},
			
			// 确认完成
			confirmComplete() {
				this.$refs.completePopup.close();
				
				// 显示加载中
				uni.showLoading({
					title: '提交中...'
				});
				
				// 检查是否有未完成的任务
				const pendingTasks = this.planTasks.filter(task => task.status === 'pending');
				if (pendingTasks.length > 0) {
					uni.hideLoading();
					uni.showModal({
						title: '提示',
						content: `还有${pendingTasks.length}个巡检项未完成，是否继续提交？`,
						success: (res) => {
							if (res.confirm) {
								this.submitAllResults();
							}
						}
					});
					return;
				}
				
				this.submitAllResults();
			},
			
			// 提交所有巡检结果
			submitAllResults() {
				// 显示加载中
				uni.showLoading({
					title: '提交中...'
				});
				
				// 获取开始时间（第一个完成的任务时间或当前时间）
				const startTimeISO = this.planTasks[0]?.completedTime || new Date().toISOString();
				const endTimeISO = new Date().toISOString();
				
				// 构建提交数据
				const submitData = {
					patrol_plan_id: this.planId,
					id:this.id,
					executor_id: uni.getStorageSync('userId') || 1, // 从缓存获取当前登录用户ID，如果没有则默认为1
					start_time: this.formatDateTime(startTimeISO), // 格式化为 YYYY-MM-DD HH:MM:SS
					end_time: this.formatDateTime(endTimeISO), // 格式化为 YYYY-MM-DD HH:MM:SS
					status: "completed",
					remark: "巡检完成",
					patrol_results: this.completedResults.map(item => {
						return {
							patrol_item_id: item.itemId,
							check_result: item.isAbnormal ? 'abnormal' : 'normal',
							param_value: item.actualValue,
							description: item.remark || '',
							images: item.images.map(img => img.serverUrl || img.url), // 优先使用服务器路径
							latitude: 0, // 此处可以添加定位功能获取实际经纬度
							longitude: 0
						};
					})
				};
				// uni.hideLoading();
				// setTimeout(() => {
				// 	uni.navigateBack({
				// 	    delta: 2
				// 	});王Zeui-/
							// }, 1500);
				// 调用API提交所有结果
				patrolApi.submitPatrolRecord(submitData)
					.then(res => {
						uni.hideLoading();
						uni.showToast({
							title: '巡检已完成',
							icon: 'success'
						});
						// 跳转到巡检工单记录页面
						setTimeout(() => {
							uni.navigateBack({
							    delta: 2
							});
						}, 1500);
					})
					.catch(err => {
						console.error('提交巡检记录失败:', err);
						uni.hideLoading();
						uni.showToast({
							title: '提交失败，请重试',
							icon: 'none'
						});
					});
			},
			
			// 获取重要性类
			getImportanceClass(importance) {
				switch (importance) {
					case 'normal':
						return 'normal-importance';
					case 'important':
						return 'important-importance';
					case 'critical':
						return 'critical-importance';
					default:
						return 'normal-importance';
				}
			},
			
			// 获取重要性标签
			getImportanceLabel(importance) {
				switch (importance) {
					case 'normal':
						return '普通';
					case 'important':
						return '重要';
					case 'critical':
						return '关键';
					default:
						return '普通';
				}
			},
			
			// 选择类型输入
			onSelectionChange(e) {
				this.taskResult.actualValue = e.detail.value;
			},
			
			// 布尔类型输入
			onBooleanChange(e) {
				this.taskResult.actualValue = e.detail.value;
			},
		}
	}
</script>

<style lang="scss">
	.patrol-execute-container {
		padding: 20rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
		padding-bottom: 120rpx;
	}
	
	/* 加载状态样式 */
	.loading-container {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		height: 100vh;
		background-color: #f5f5f5;
	}
	
	.loading-spinner {
		width: 80rpx;
		height: 80rpx;
		border: 6rpx solid rgba(0, 122, 255, 0.1);
		border-top-color: #007aff;
		border-radius: 50%;
		animation: spin 1s infinite linear;
		margin-bottom: 30rpx;
	}
	
	.loading-text {
		font-size: 30rpx;
		color: #666;
	}
	
	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}
		100% {
			transform: rotate(360deg);
		}
	}
	
	.progress-bar {
		height: 10rpx;
		background-color: #eee;
		border-radius: 5rpx;
		overflow: hidden;
		margin-bottom: 10rpx;
		
		.progress-inner {
			height: 100%;
			background-color: $uni-color-primary;
		}
	}
	
	.progress-info {
		display: flex;
		justify-content: space-between;
		font-size: 24rpx;
		color: $uni-text-color-grey;
		margin-bottom: 20rpx;
	}
	
	.task-card {
		background-color: #fff;
		border-radius: 12rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
		
		.task-header {
			margin-bottom: 20rpx;
			
			.task-title {
				font-size: 32rpx;
				font-weight: bold;
				color: $uni-text-color;
			}
		}
		
		.task-details {
			.detail-row {
				display: flex;
				margin-bottom: 16rpx;
				
				&:last-child {
					margin-bottom: 0;
				}
				
				.detail-label {
					width: 120rpx;
					font-size: 28rpx;
					color: $uni-text-color-grey;
				}
				
				.detail-value {
					flex: 1;
					font-size: 28rpx;
					color: $uni-text-color;
				}
				
				.importance-tag {
					display: inline-block;
					padding: 4rpx 16rpx;
					border-radius: 8rpx;
					font-size: 24rpx;
					max-width: fit-content;
					
					&.normal-importance {
						background-color: #e6f7ff;
						color: #1890ff;
						border: 1rpx solid #91d5ff;
					}
					
					&.important-importance {
						background-color: #fff7e6;
						color: #fa8c16;
						border: 1rpx solid #ffd591;
					}
					
					&.critical-importance {
						background-color: #fff1f0;
						color: #f5222d;
						border: 1rpx solid #ffa39e;
					}
				}
			}
		}
	}
	
	.input-card {
		background-color: #fff;
		border-radius: 12rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
		
		.card-header {
			font-size: 32rpx;
			font-weight: bold;
			color: $uni-text-color;
			margin-bottom: 20rpx;
		}
		
		.card-content {
			.form-input {
				width: 100%;
				height: 80rpx;
				padding: 0 20rpx;
				border: 1rpx solid #ddd;
				border-radius: 8rpx;
				font-size: 28rpx;
				box-sizing: border-box;
			}
			
			.picker-value {
				width: 100%;
				height: 80rpx;
				line-height: 80rpx;
				text-align: center;
			}
			
			.radio-group {
				display: flex;
				
				.radio-item {
					display: flex;
					align-items: center;
					margin-right: 60rpx;
					
					.radio-dot {
						width: 32rpx;
						height: 32rpx;
						border-radius: 50%;
						border: 1rpx solid #ddd;
						margin-right: 10rpx;
						display: flex;
						justify-content: center;
						align-items: center;
						
						&::after {
							content: '';
							width: 16rpx;
							height: 16rpx;
							border-radius: 50%;
							background-color: $uni-color-primary;
							opacity: 0;
						}
					}
					
					text {
						font-size: 28rpx;
						color: $uni-text-color;
					}
					
					&.radio-selected {
						.radio-dot {
							border-color: $uni-color-primary;
							
							&::after {
								opacity: 1;
							}
						}
						
						text {
							color: $uni-color-primary;
						}
					}
				}
			}
			
			.form-textarea {
				width: 100%;
				height: 160rpx;
				padding: 20rpx;
				border: 1rpx solid #ddd;
				border-radius: 8rpx;
				font-size: 28rpx;
				box-sizing: border-box;
			}
			
			.image-list {
				display: flex;
				flex-wrap: wrap;
				
				.image-item {
					width: 160rpx;
					height: 160rpx;
					margin-right: 20rpx;
					margin-bottom: 20rpx;
					position: relative;
					border-radius: 8rpx;
					overflow: hidden;
					
					image {
						width: 100%;
						height: 100%;
						object-fit: cover;
					}
					
					.delete-icon {
						position: absolute;
						top: 0;
						right: 0;
						width: 40rpx;
						height: 40rpx;
						background-color: rgba(0,0,0,0.5);
						color: #fff;
						font-size: 28rpx;
						display: flex;
						justify-content: center;
						align-items: center;
						border-bottom-left-radius: 8rpx;
					}
				}
				
				.image-upload {
					width: 160rpx;
					height: 160rpx;
					border: 1rpx dashed #ddd;
					border-radius: 8rpx;
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;
					margin-bottom: 20rpx;
					
					.upload-icon {
						font-size: 48rpx;
						color: #999;
						margin-bottom: 8rpx;
					}
					
					.upload-text {
						font-size: 24rpx;
						color: #999;
					}
				}
			}
		}
	}
	
	.action-buttons {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		padding: 20rpx;
		background-color: #fff;
		display: flex;
		box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);
		
		.action-btn {
			flex: 1;
			height: 80rpx;
			line-height: 80rpx;
			text-align: center;
			border-radius: 8rpx;
			font-size: 30rpx;
			margin: 0 10rpx;
			
			&.prev {
				background-color: #f5f5f5;
				color: $uni-text-color;
			}
			
			&.skip {
				background-color: #f5f5f5;
				color: $uni-color-warning;
			}
			
			&.submit {
				background-color: $uni-color-primary;
				color: #fff;
			}
		}
	}
	
	.popup-content {
		width: 560rpx;
		background-color: #fff;
		border-radius: 12rpx;
		overflow: hidden;
		
		.popup-title {
			text-align: center;
			font-size: 32rpx;
			font-weight: bold;
			padding: 30rpx 0;
		}
		
		.popup-message {
			padding: 0 30rpx 30rpx;
			text-align: center;
			font-size: 28rpx;
			color: $uni-text-color;
		}
		
		.popup-buttons {
			display: flex;
			border-top: 1rpx solid #eee;
			
			.popup-button {
				flex: 1;
				text-align: center;
				height: 90rpx;
				line-height: 90rpx;
				font-size: 30rpx;
				
				&.cancel {
					color: $uni-text-color;
					border-right: 1rpx solid #eee;
				}
				
				&.confirm {
					color: $uni-color-primary;
				}
			}
		}
	}
	
	.content-description {
		word-break: break-word;
		white-space: normal;
	}
	
	textarea {
		width: 100%;
		height: 160rpx;
		padding: 20rpx;
		border: 1rpx solid #ddd;
		border-radius: 8rpx;
		font-size: 28rpx;
		box-sizing: border-box;
	}
	
	.unit-text {
		font-size: 24rpx;
		color: #999;
		margin-left: 10rpx;
	}
</style> 