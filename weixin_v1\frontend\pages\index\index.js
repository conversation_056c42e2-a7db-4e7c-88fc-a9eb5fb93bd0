Page({
  data: {
    currentDate: '',
    stats: {},
    recentFaults: [],
    tempUpdateTime: '',
    stationTemps: []
  },

  onLoad() {
    this.loadPageData();
  },

  loadPageData() {
    // 获取今日概况
    wx.request({
      url: 'http://localhost:5000/api/stats',
      method: 'GET',
      success: (res) => {
        console.log('Stats response:', res.data);  // 调试日志
        if (res.data.success) {
          this.setData({
            stats: res.data.data.stats || {},
            currentDate: res.data.data.currentDate || ''
          });
        } else {
          console.error('Failed to load stats:', res.data.message);
          wx.showToast({
            title: '加载失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('Request failed:', err);
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });

    // 获取最新故障
    wx.request({
      url: 'http://localhost:5000/api/recent-faults',
      method: 'GET',
      success: (res) => {
        console.log('Faults response:', res.data);  // 调试日志
        if (res.data.success) {
          this.setData({
            recentFaults: res.data.data || []
          });
        }
      }
    });

    // 获取小区室内平均温度
    wx.request({
      url: 'http://localhost:5000/api/station-temps',
      method: 'GET',
      success: (res) => { 
        if (res.data.success) {
          this.setData({
            stationTemps: res.data.data.stations || [],
            tempUpdateTime: res.data.data.updateTime || ''
          });
        }
      }
    });
  },

  goToFaultReport() {
    wx.navigateTo({
      url: '/pages/fault/report/index'
    });
  },

  goToTempReport() {
    wx.navigateTo({
      url: '/pages/temperature/report/index'
    });
  },

  goToWorkorders() {
    wx.navigateTo({
      url: '/pages/workorder/list/index'
    });
  },

  goToFaultList() {
    wx.navigateTo({
      url: '/pages/fault/list/index'
    });
  },

  goToFaultDetail(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/fault/detail/index?id=${id}`
    });
  },

  goToTempList() {
    wx.navigateTo({
      url: '/pages/temperature/list/index'
    });
  }
}); 