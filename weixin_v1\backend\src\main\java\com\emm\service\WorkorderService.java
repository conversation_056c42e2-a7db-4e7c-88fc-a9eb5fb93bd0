package com.emm.service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
 
import com.emm.model.OperationLog;
import com.emm.model.WorkOrder;
import com.emm.model.WorkOrderAttachment;
import com.emm.repository.WorkOrderAttachmentRepository;
import com.emm.repository.WorkOrderRepository;
import com.emm.repository.OperationLogRepository;
import com.emm.dto.CompleteOrderRequest;
import java.util.Date;

@Service
public class WorkorderService {
    private static final Logger logger = LoggerFactory.getLogger(WorkorderService.class);
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

    @Autowired
    private WorkOrderRepository workOrderRepository;
    
    @Autowired
    private WorkOrderAttachmentRepository attachmentRepository;
    
    @Autowired
    private OperationLogRepository operationLogRepository;
 
    /**
     * 完成工单
     */
    @Transactional(rollbackFor = Exception.class)
    public void completeOrder(String orderId, CompleteOrderRequest request) {
        logger.info("开始处理工单完成请求: orderId={}", orderId);
        try {
            // 获取工单
            WorkOrder workOrder = findWorkOrderById(orderId);
            if (workOrder == null) {
                logger.warn("工单完成失败：工单不存在 orderId={}", orderId);
                return;
            }
            
            // 更新工单
            updateWorkOrder(workOrder, request);
            
            // 保存维修附件
            saveWorkOrderAttachments(orderId, request.getRepairImage());

            // 记录操作日志
            saveOperationLog(workOrder);
            
            logger.info("工单处理完成: orderId={}", orderId);
        } catch (Exception e) {
            logger.error("工单处理失败: orderId={}, error={}", orderId, e.getMessage()); 
        }
    }

    /**
     * 获取工单列表
     */
    public List<Map<String, Object>> getWorkorderList(Date date, String status, Long userId) { 
        try {
            Date queryDate = null;
            if (date != null) {
                logger.info("查询日期: {}", date);
                queryDate = date; 

            } 
            String dbStatus = null;
            if (status != null && !status.isEmpty()) {
                dbStatus = convertStatus(status);
            } 
            List<Map<String, Object>> workOrders = workOrderRepository.findWorkOrderList(queryDate, dbStatus, userId);
            // 打印日志
            logger.info("查询到{}条工单记录", workOrders.size());
            if (workOrders != null) {
                processWorkOrderList(workOrders); 
            } 
            return workOrders;
        } catch (Exception e) {
            logger.error("查询工单列表失败: error={}", e.getMessage());
            return null;
        }
    }

    /**
     * 获取工单详情
     */
    public Map<String, Object> getWorkorderDetail(String orderId) {
        logger.info("开始查询工单详情: orderId={}", orderId);
        try {
            // 1、获取工单信息
            Map<String, Object> workOrder = workOrderRepository.findWorkOrderDetail(orderId).orElse(null); 
            
            if (workOrder == null) {
                
                logger.warn("工单详情不存在: orderId={}", orderId);
                return null;
            } 
            enrichWorkOrderDetail(workOrder);

            logger.info("工单基本信息查询成功: orderId={}, status={}", orderId, workOrder.get("order_status"));

            // 2、获取故障附件信息
            List<Map<String, Object>> faultAttachments = workOrderRepository.findFaultAttachments(orderId);
            workOrder.put("fault_attachments", faultAttachments);
            logger.debug("查询到故障附件数量: orderId={}, count={}", orderId, faultAttachments.size());

            // 3、获取工单附件信息
            List<Map<String, Object>> workOrderAttachments = workOrderRepository.findWorkOrderAttachments(orderId);
            workOrder.put("attachments", workOrderAttachments);
            logger.debug("查询到工单附件数量: orderId={}, count={}", orderId, workOrderAttachments.size());

            // 4、获取操作日志
            List<Map<String, Object>> operationLogs = workOrderRepository.findOperationLogs(orderId);
            workOrder.put("logs", operationLogs);
            logger.debug("查询到操作日志数量: orderId={}, count={}", orderId, operationLogs.size());

            // 5、转换状态和等级显示
            workOrder.put("status", convertStatusToDisplay((String) workOrder.get("order_status")));
            workOrder.put("status_text", workOrder.get("order_status"));
            workOrder.put("fault_level_text", convertLevelToText(workOrder.get("fault_level")));
            
            logger.info("工单详情查询完成: orderId={}", orderId);
            return workOrder;
        } catch (Exception e) {
            logger.error("查询工单详情失败: orderId={}, error={}", orderId, e.getMessage(), e);
            return null;
        }
    } 

    // 私有辅助方法
    private WorkOrder findWorkOrderById(String orderId) {
        return workOrderRepository.findById(orderId)
            .orElseGet(() -> {
                logger.warn("工单不存在: orderId={}", orderId);
                return null;
            });
    }

    private void updateWorkOrder(WorkOrder workOrder, CompleteOrderRequest request) {
        // 打印请求内容
        logger.info("工单完成请求内容: orderId={}, repairContent={}, repairResult={}, repairImage={}",
            workOrder.getOrderId(),
            request.getRepairContent(),
            request.getRepairResult(),
            request.getRepairImage()
        );

        workOrder.setRepairContent(request.getRepairContent());
        workOrder.setRepairResult(request.getRepairResult());
        workOrder.setRepairTime(LocalDateTime.now());
        workOrder.setOrderStatus("已完成");
        workOrderRepository.save(workOrder);
    }

    private void saveWorkOrderAttachments(String orderId, List<String> images) {
        if (images != null && !images.isEmpty()) {
            images.forEach(image -> {
                WorkOrderAttachment attachment = new WorkOrderAttachment();
                attachment.setOrderId(orderId);
                attachment.setFileType("image");
                attachment.setFilePath(image);
                attachment.setCreatedAt(LocalDateTime.now());
                attachmentRepository.save(attachment);
            });
        }
    }

    private void saveOperationLog(WorkOrder workOrder) {
        OperationLog log = new OperationLog();
        log.setFaultId(workOrder.getFaultId());
        log.setOrderId(workOrder.getOrderId());
        log.setOperationType("完成维修");
        log.setOperationDesc("维修人员提交维修结果");
        log.setOperatorId(workOrder.getRepairUserId());
        log.setCreatedAt(LocalDateTime.now());
        operationLogRepository.save(log);
    }

    private void processWorkOrderList(List<Map<String, Object>> workOrders) {
        workOrders.forEach(order -> {
            order.put("status", convertStatusToDisplay((String) order.get("order_status")));
            order.put("status_text", order.get("order_status"));
            order.put("fault_level_text", convertLevelToText(order.get("fault_level"))); 
            // formatDateTime(order, "created_time");
        });
    }

    private void enrichWorkOrderDetail(Map<String, Object> workOrder) {
        // 格式化时间
        formatDateTime(workOrder, "repair_time");
        formatDateTime(workOrder, "created_time");
        formatDateTime(workOrder, "updated_time");

        // 获取附件和日志
        String faultId = (String) workOrder.get("fault_id");
        String orderId = (String) workOrder.get("order_id");
        
        workOrder.put("fault_attachments", workOrderRepository.findFaultAttachments(faultId));
        workOrder.put("attachments", workOrderRepository.findWorkOrderAttachments(orderId));
        workOrder.put("logs", workOrderRepository.findOperationLogs(orderId));

        // 转换状态和等级显示
        workOrder.put("status", convertStatusToDisplay((String) workOrder.get("order_status")));
        workOrder.put("status_text", workOrder.get("order_status"));
        workOrder.put("fault_level_text", convertLevelToText( workOrder.get("fault_level")));
    }

    private void formatDateTime(Map<String, Object> map, String key) {
        LocalDateTime dateTime = (LocalDateTime) map.get(key);
        if (dateTime != null) {
            map.put(key, dateTime.format(DATE_TIME_FORMATTER));
        }
    }

    private LocalDate parseDate(String date) {
        return date != null ? LocalDate.parse(date) : null;
    }

    // 状态转换方法
    private String convertStatus(String frontendStatus) {
        if (frontendStatus == null)
            return null;
        switch (frontendStatus) {
            case "pending": return "待接单";
            case "processing": return "维修中";
            case "completed": return "已完成";
            case "returned": return "已退回";
            default: return null;
        }
    }

    private String convertStatusToDisplay(String dbStatus) {
        if (dbStatus == null) return "pending";
        switch (dbStatus) {
            case "待接单": return "pending";
            case "维修中": return "processing";
            case "已完成": return "completed";
            case "已退回": return "returned";
            default: return "pending";
        }
    }

    private String convertLevelToText(Object level) {
        if (level == null) return "未知";
        
        // 处理字符串类型
        if (level instanceof String) {
            try {
                return convertLevelToText(Integer.parseInt((String) level));
            } catch (NumberFormatException e) {
                logger.warn("故障等级格式无效: {}", level);
                return "未知";
            }
        }
        
        // 处理整数类型
        if (level instanceof Integer) {
            int levelInt = (Integer) level;
            switch (levelInt) {
                case 1: return "一般";
                case 2: return "重要";
                case 3: return "紧急";
                default: return String.valueOf(levelInt);
            }
        }
        
        return "未知";
    }

    public void acceptOrder(String orderId, String action, Long repairUserId, Long operatorId) {
        WorkOrder workOrder = findWorkOrderById(orderId);
        workOrder.setOrderStatus("维修中");
        workOrder.setRepairUserId(repairUserId); 
        workOrder.setUpdatedAt(LocalDateTime.now());
        workOrderRepository.save(workOrder);

        OperationLog log = new OperationLog();
        log.setFaultId(workOrder.getFaultId());
        log.setOrderId(orderId);
        log.setOperationType("工单接单");
        log.setOperationDesc("维修人员接单");
        log.setOperatorId(operatorId);
        log.setCreatedAt(LocalDateTime.now());
        operationLogRepository.save(log);
    }
} 