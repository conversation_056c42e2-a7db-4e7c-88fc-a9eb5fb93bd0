package com.heating.entity.attendance;

import com.heating.converter.JsonConverter;
import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Entity
//补卡申请
@Table(name = "t_attendance_supplement")
public class TAttendanceSupplement {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id", nullable = false)
    private Long userId; // 用户ID

    @Column(name = "date", nullable = false)
    private LocalDate date; // 申请补卡日期

    @Column(name = "type", nullable = false)
    private Integer type; // 1-上班打卡, 2-下班打卡

    @Column(name = "reason", nullable = false, length = 500)
    private String reason; // 补卡原因

    @Convert(converter = JsonConverter.class)
    @Column(name = "images", columnDefinition = "json")
    private List<String> images; // 证明材料图片

    @Column(name = "status", nullable = false, length = 20)
    private String status = "pending"; // pending/approved/rejected

    @Column(name = "remark", length = 500)
    private String remark; // 审批备注

    @Column(name = "approved_time")
    private LocalDateTime approvedTime; // 审批时间

    @Column(name = "approved_by")
    private Long approvedBy; // 审批人ID

    @Column(name = "create_time", nullable = false)
    private LocalDateTime createTime = LocalDateTime.now(); // 创建时间

    @Column(name = "update_time")
    private LocalDateTime updateTime; // 更新时间
} 