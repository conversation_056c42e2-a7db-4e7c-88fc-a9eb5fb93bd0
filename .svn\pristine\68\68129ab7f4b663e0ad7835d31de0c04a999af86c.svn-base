package com.heating.controller;

import com.heating.dto.PersonTrajectoryDTO;
import com.heating.entity.PersonTrajectory;
import com.heating.service.PersonTrajectoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 人员轨迹控制器
 */
@RestController
@RequestMapping("/api/person/trajectory")
public class PersonTrajectoryController {

    @Autowired
    private PersonTrajectoryService personTrajectoryService;

    /**
     * 添加轨迹记录
     * @param dto 轨迹记录DTO
     * @return 添加结果
     */
    @PostMapping
    public ResponseEntity<?> addTrajectory(@RequestBody @Valid PersonTrajectoryDTO dto) {
        PersonTrajectory trajectory = personTrajectoryService.addTrajectory(dto);
        return new ResponseEntity<>(trajectory, HttpStatus.CREATED);
    }

    /**
     * 批量添加轨迹记录
     * @param dtoList 轨迹记录DTO列表
     * @return 添加结果
     */
    @PostMapping("/batch")
    public ResponseEntity<?> addTrajectoryBatch(@RequestBody List<PersonTrajectoryDTO> dtoList) {
        List<PersonTrajectory> trajectories = personTrajectoryService.addTrajectoryBatch(dtoList);
        return new ResponseEntity<>(trajectories, HttpStatus.CREATED);
    }

    /**
     * 根据用户ID查询轨迹记录
     * @param userId 用户ID
     * @return 轨迹记录列表
     */
    @GetMapping("/{userId}")
    public ResponseEntity<?> getTrajectoryByUserId(@PathVariable Integer userId) {
        List<PersonTrajectory> trajectories = personTrajectoryService.getTrajectoryByUserId(userId);
        return new ResponseEntity<>(trajectories, HttpStatus.OK);
    }

    /**
     * 根据用户ID和时间范围查询轨迹记录
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 轨迹记录列表
     */
    @GetMapping("/{userId}/time-range")
    public ResponseEntity<?> getTrajectoryByUserIdAndTimeRange(
            @PathVariable Integer userId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        List<PersonTrajectory> trajectories = personTrajectoryService.getTrajectoryByUserIdAndTimeRange(userId, startTime, endTime);
        return new ResponseEntity<>(trajectories, HttpStatus.OK);
    }
    
    /**
     * 添加定位记录接口（单独封装用于移动端调用）
     * @param userId 用户ID
     * @param longitude 经度
     * @param latitude 纬度
     * @return 添加结果
     */
    @PostMapping("/location")
    public ResponseEntity<?> recordLocation(
            @RequestParam Integer userId,
            @RequestParam String longitude,
            @RequestParam String latitude) {
        
        PersonTrajectoryDTO dto = new PersonTrajectoryDTO();
        dto.setUserId(userId);
        dto.setEmployeeId(userId);
        dto.setLongitude(longitude);
        dto.setLatitude(latitude);
        dto.setStatus(0); // 正常状态
        
        PersonTrajectory trajectory = personTrajectoryService.addTrajectory(dto);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("success", true);
        response.put("message", "位置记录成功");
        response.put("data", trajectory);
        
        return new ResponseEntity<>(response, HttpStatus.CREATED);
    }
} 