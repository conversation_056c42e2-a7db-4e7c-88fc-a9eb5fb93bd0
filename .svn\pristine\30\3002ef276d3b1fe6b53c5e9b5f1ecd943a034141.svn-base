<template>
	<view class="valve-detail-page">
		<!-- 顶部栏 -->
		<view class="header">
			<view class="back-button" @click="navigateBack">
				<text class="iconfont icon-arrow-left"></text>
			</view>
			<text class="page-title">阀门详情</text>
		</view>
		
		<!-- 加载中 -->
		<view class="loading-container" v-if="loading">
			<uni-load-more :status="'loading'" :content-text="loadingText"></uni-load-more>
		</view>
		
		<!-- 阀门详情 -->
		<view class="valve-content" v-else-if="valveDetail">
			<!-- 状态指示部分 -->
			<view class="status-card">
				<view class="status-icon" :class="valveDetail.status">
					<text class="iconfont" :class="getStatusIcon(valveDetail.status)"></text>
				</view>
				<view class="status-info">
					<view class="status-header">
						<text class="valve-name">{{ valveDetail.name }}</text>
						<view class="status-tag" :class="'status-' + valveDetail.status">{{ getStatusText(valveDetail.status) }}</view>
					</view>
					<view class="status-detail">
						<view class="detail-item">
							<text class="detail-label">当前开度</text>
							<text class="detail-value">{{ valveDetail.openDegree }}%</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">上次操作</text>
							<text class="detail-value">{{ valveDetail.lastOperationTime }} {{ valveDetail.operationType }}</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">操作人员</text>
							<text class="detail-value">{{ valveDetail.operator }}</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 控制面板部分 -->
			<view class="control-panel">
				<view class="panel-title">控制面板</view>
				<view class="opening-control">
					<text class="control-label">开度调节：{{ targetOpenDegree }}%</text>
					<slider class="opening-slider" :value="targetOpenDegree" :min="0" :max="100" :step="1" @change="handleOpenDegreeChange" show-value />
				</view>
				<view class="control-buttons">
					<button class="control-btn set-btn" @click="setOpenDegree">设置开度</button>
					<button class="control-btn open-btn" @click="fullOpen">一键开启</button>
					<button class="control-btn close-btn" @click="fullClose">一键关闭</button>
				</view>
			</view>
			
			<!-- 操作记录部分 -->
			<view class="operation-records">
				<view class="records-title">操作记录</view>
				<view class="timeline">
					<view class="timeline-item" v-for="(record, index) in valveDetail.operationRecords" :key="index">
						<view class="timeline-dot"></view>
						<view class="timeline-content">
							<text class="timeline-time">{{ record.time }}</text>
							<text class="timeline-text">{{ record.content }}</text>
							<text class="timeline-operator">操作人：{{ record.operator }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 确认弹窗 -->
		<uni-popup ref="confirmPopup" type="dialog">
			<uni-popup-dialog 
				:title="confirmDialog.title" 
				:content="confirmDialog.content" 
				:cancelText="'取消'" 
				:confirmText="'确认'"
				@confirm="handleConfirmControl"
				@close="handleCancelControl">
			</uni-popup-dialog>
		</uni-popup>
		
		<!-- 结果提示 -->
		<uni-popup ref="resultPopup" type="message">
			<uni-popup-message 
				:type="resultMessage.type" 
				:message="resultMessage.message" 
				:duration="2000">
			</uni-popup-message>
		</uni-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				loading: true,
				loadingText: {
					contentdown: '加载中...',
					contentrefresh: '加载中...',
					contentnomore: '没有更多数据'
				},
				valveId: null,
				valveDetail: null,
				targetOpenDegree: 0,
				
				// 确认对话框
				confirmDialog: {
					title: '操作确认',
					content: '',
					action: ''
				},
				
				// 结果消息
				resultMessage: {
					type: 'success',
					message: ''
				}
			}
		},
		onLoad(option) {
			if (option.id) {
				this.valveId = option.id;
				this.loadValveDetail();
			} else {
				uni.showToast({
					title: '参数错误',
					icon: 'none'
				});
				setTimeout(() => {
					this.navigateBack();
				}, 1500);
			}
		},
		methods: {
			// 加载阀门详情
			loadValveDetail() {
				this.loading = true;
				
				// 模拟API调用
				setTimeout(() => {
					// 模拟数据
					this.valveDetail = {
						id: this.valveId,
						name: '东区小区 1号楼 1单元 101 入户阀门',
						status: 'open',
						openDegree: 80,
						lastOperationTime: '2025-04-03 14:35:20',
						operator: '系统',
						operationType: '自动开启(缴费成功)',
						operationRecords: [
							{
								time: '2025-04-03 14:35:20',
								content: '自动开启(缴费成功)',
								operator: '系统'
							},
							{
								time: '2025-02-28 09:10:15',
								content: '手动关闭(欠费)',
								operator: '李四'
							},
							{
								time: '2025-02-15 08:30:45',
								content: '开度调整至50%',
								operator: '张三'
							}
						]
					};
					
					this.targetOpenDegree = this.valveDetail.openDegree;
					this.loading = false;
				}, 1000);
			},
			
			// 处理开度变化
			handleOpenDegreeChange(e) {
				this.targetOpenDegree = e.detail.value;
			},
			
			// 设置开度
			setOpenDegree() {
				if (this.targetOpenDegree === this.valveDetail.openDegree) {
					uni.showToast({
						title: '开度未变化',
						icon: 'none'
					});
					return;
				}
				
				this.confirmDialog = {
					title: '开度调整确认',
					content: `确定要将${this.valveDetail.name}的开度调整为${this.targetOpenDegree}%吗？`,
					action: 'setOpenDegree'
				};
				
				this.$refs.confirmPopup.open();
			},
			
			// 一键开启
			fullOpen() {
				if (this.valveDetail.status === 'open' && this.valveDetail.openDegree === 100) {
					uni.showToast({
						title: '阀门已经完全开启',
						icon: 'none'
					});
					return;
				}
				
				this.confirmDialog = {
					title: '操作确认',
					content: `确定要完全开启${this.valveDetail.name}吗？`,
					action: 'fullOpen'
				};
				
				this.$refs.confirmPopup.open();
			},
			
			// 一键关闭
			fullClose() {
				if (this.valveDetail.status === 'closed' && this.valveDetail.openDegree === 0) {
					uni.showToast({
						title: '阀门已经完全关闭',
						icon: 'none'
					});
					return;
				}
				
				this.confirmDialog = {
					title: '操作确认',
					content: `确定要完全关闭${this.valveDetail.name}吗？`,
					action: 'fullClose'
				};
				
				this.$refs.confirmPopup.open();
			},
			
			// 确认控制操作
			handleConfirmControl() {
				const action = this.confirmDialog.action;
				
				// 准备请求参数
				const params = {
					valve_id: this.valveId
				};
				
				// 根据不同操作设置不同参数
				if (action === 'setOpenDegree') {
					params.open_degree = this.targetOpenDegree;
				} else if (action === 'fullOpen') {
					params.open_degree = 100;
				} else if (action === 'fullClose') {
					params.open_degree = 0;
				}
				
				// 模拟API调用
				uni.showLoading({
					title: '操作中...'
				});
				
				// 模拟API调用
				setTimeout(() => {
					// 更新阀门状态
					if (action === 'setOpenDegree') {
						this.valveDetail.openDegree = this.targetOpenDegree;
						this.valveDetail.status = this.targetOpenDegree > 0 ? 'open' : 'closed';
						this.addOperationRecord(`开度调整至${this.targetOpenDegree}%`);
					} else if (action === 'fullOpen') {
						this.valveDetail.openDegree = 100;
						this.valveDetail.status = 'open';
						this.targetOpenDegree = 100;
						this.addOperationRecord('完全开启阀门');
					} else if (action === 'fullClose') {
						this.valveDetail.openDegree = 0;
						this.valveDetail.status = 'closed';
						this.targetOpenDegree = 0;
						this.addOperationRecord('完全关闭阀门');
					}
					
					uni.hideLoading();
					
					// 显示成功消息
					this.resultMessage = {
						type: 'success',
						message: '操作成功'
					};
					this.$refs.resultPopup.open();
				}, 1500);
			},
			
			// 取消控制操作
			handleCancelControl() {
				// 不做任何操作，仅关闭弹窗
			},
			
			// 添加操作记录
			addOperationRecord(content) {
				const now = new Date();
				const timeStr = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
				
				// 添加到记录列表开头
				this.valveDetail.operationRecords.unshift({
					time: timeStr,
					content: content,
					operator: '当前用户' // 实际应用中应该从用户信息中获取
				});
				
				// 更新最后操作信息
				this.valveDetail.lastOperationTime = timeStr;
				this.valveDetail.operator = '当前用户';
				this.valveDetail.operationType = content;
			},
			
			// 获取状态图标
			getStatusIcon(status) {
				const iconMap = {
					'open': 'icon-check',
					'closed': 'icon-clear',
					'error': 'icon-alarm'
				};
				return iconMap[status] || 'icon-info';
			},
			
			// 获取状态文本
			getStatusText(status) {
				const statusMap = {
					'open': '已开启',
					'closed': '已关闭',
					'error': '异常'
				};
				return statusMap[status] || status;
			},
			
			// 返回上一页
			navigateBack() {
				uni.navigateBack();
			}
		}
	}
</script>

<style lang="scss">
.valve-detail-page {
	min-height: 100vh;
	background-color: #f5f7fa;
	padding-bottom: 30rpx;
}

.header {
	display: flex;
	align-items: center;
	height: 88rpx;
	background-color: #fff;
	padding: 0 30rpx;
	position: relative;
	box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
	
	.back-button {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.page-title {
		flex: 1;
		text-align: center;
		font-size: 34rpx;
		font-weight: 500;
		padding-right: 60rpx;
	}
}

.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 0;
}

.valve-content {
	padding: 20rpx 30rpx;
}

.status-card {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	display: flex;
	
	.status-icon {
		width: 120rpx;
		height: 120rpx;
		border-radius: 60rpx;
		margin-right: 24rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		
		&.open {
			background-color: rgba(46, 204, 113, 0.1);
			
			.iconfont {
				color: #2ecc71;
			}
		}
		
		&.closed {
			background-color: rgba(149, 165, 166, 0.1);
			
			.iconfont {
				color: #95a5a6;
			}
		}
		
		&.error {
			background-color: rgba(231, 76, 60, 0.1);
			
			.iconfont {
				color: #e74c3c;
			}
		}
		
		.iconfont {
			font-size: 60rpx;
		}
	}
	
	.status-info {
		flex: 1;
		
		.status-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 16rpx;
			
			.valve-name {
				font-size: 32rpx;
				font-weight: 600;
				color: #333;
				flex: 1;
				margin-right: 20rpx;
			}
		}
		
		.status-detail {
			.detail-item {
				margin-bottom: 12rpx;
				
				.detail-label {
					font-size: 26rpx;
					color: #666;
					margin-right: 10rpx;
				}
				
				.detail-value {
					font-size: 26rpx;
					color: #333;
					font-weight: 500;
				}
			}
		}
	}
}

.status-tag {
	padding: 6rpx 16rpx;
	border-radius: 30rpx;
	font-size: 24rpx;
	color: #fff;
	
	&.status-open {
		background-color: #2ecc71;
	}
	
	&.status-closed {
		background-color: #95a5a6;
	}
	
	&.status-error {
		background-color: #e74c3c;
	}
}

.control-panel {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	
	.panel-title {
		font-size: 30rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 20rpx;
		position: relative;
		padding-left: 20rpx;
		
		&::before {
			content: '';
			position: absolute;
			left: 0;
			top: 8rpx;
			width: 6rpx;
			height: 28rpx;
			background-color: #1989fa;
			border-radius: 3rpx;
		}
	}
	
	.opening-control {
		margin-bottom: 30rpx;
		
		.control-label {
			font-size: 28rpx;
			color: #333;
			margin-bottom: 10rpx;
			display: block;
		}
		
		.opening-slider {
			margin: 20rpx 0;
		}
	}
	
	.control-buttons {
		display: flex;
		justify-content: space-between;
		
		.control-btn {
			width: 30%;
			height: 80rpx;
			line-height: 80rpx;
			text-align: center;
			border-radius: 8rpx;
			font-size: 28rpx;
			
			&.set-btn {
				background-color: #1989fa;
				color: #fff;
			}
			
			&.open-btn {
				background-color: #2ecc71;
				color: #fff;
			}
			
			&.close-btn {
				background-color: #e74c3c;
				color: #fff;
			}
		}
	}
}

.operation-records {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	
	.records-title {
		font-size: 30rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 20rpx;
		position: relative;
		padding-left: 20rpx;
		
		&::before {
			content: '';
			position: absolute;
			left: 0;
			top: 8rpx;
			width: 6rpx;
			height: 28rpx;
			background-color: #1989fa;
			border-radius: 3rpx;
		}
	}
	
	.timeline {
		position: relative;
		padding-left: 30rpx;
		
		&::before {
			content: '';
			position: absolute;
			left: 10rpx;
			top: 0;
			bottom: 0;
			width: 2rpx;
			background-color: #e0e0e0;
		}
		
		.timeline-item {
			position: relative;
			padding-bottom: 30rpx;
			
			&:last-child {
				padding-bottom: 0;
			}
			
			.timeline-dot {
				position: absolute;
				left: -30rpx;
				top: 10rpx;
				width: 20rpx;
				height: 20rpx;
				border-radius: 50%;
				background-color: #1989fa;
				z-index: 1;
			}
			
			.timeline-content {
				.timeline-time {
					font-size: 24rpx;
					color: #999;
					margin-bottom: 8rpx;
					display: block;
				}
				
				.timeline-text {
					font-size: 28rpx;
					color: #333;
					margin-bottom: 8rpx;
					display: block;
				}
				
				.timeline-operator {
					font-size: 24rpx;
					color: #666;
					display: block;
				}
			}
		}
	}
}
</style> 