package com.emm.controller;

import com.emm.dto.CompleteOrderRequest;
import com.emm.dto.AcceptOrderRequest;
import com.emm.service.WorkorderService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/workorder")
public class WorkorderController {
    private static final Logger logger = LoggerFactory.getLogger(WorkorderController.class);

    @Autowired
    private WorkorderService workorderService;

    @GetMapping("/list")
    public ResponseEntity<?> getWorkorderList(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) Long uid) {
        logger.info("获取工单列表: date={}, status={}, uid={}", date, status, uid);
        try {
            logger.debug("开始查询工单列表...");
            List<Map<String, Object>> workOrders = workorderService.getWorkorderList(date, status, uid);
            logger.debug("查询完成，获取到 {} 条工单记录", workOrders.size());
            
            // 构建响应数据
            logger.debug("开始构建响应数据...");
            Map<String, Object> response_workOrders = new HashMap<>();
            response_workOrders.put("list", workOrders);
            response_workOrders.put("total", workOrders.size());

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", response_workOrders);
            logger.debug("响应数据构建完成，准备返回");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error in GET /api/workorder/list: {}", e.getMessage());
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/detail/{orderId}")
    public ResponseEntity<?> getWorkorderDetail(@PathVariable String orderId) {
        logger.info("获取工单详情: orderId={}", orderId);
        try {
            Map<String, Object> workOrder = workorderService.getWorkorderDetail(orderId);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", workOrder);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error in GET /api/workorder/detail/:{}", orderId, e.getMessage());
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @PostMapping("/complete/{orderId}")
    public ResponseEntity<?> completeWorkorder(
            @PathVariable String orderId,
            @RequestBody CompleteOrderRequest request) {
        logger.info("完成工单: orderId={}", orderId);
        logger.info("提交的数据: request={}", request);
        try {
            workorderService.completeOrder(orderId, request);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "工单完成处理成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error in POST /api/workorder/complete/:{}", orderId, e.getMessage());
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @PostMapping("/accept")
    public ResponseEntity<?> acceptOrder(@RequestBody AcceptOrderRequest request) {
        logger.info("工单接单/转单: orderId={}, action={}", request.getOrderId(), request.getAction());
        try {
            workorderService.acceptOrder(
                request.getOrderId(),
                request.getAction(),
                request.getRepairUserId(),
                request.getOperatorId()
            );
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Error in POST /api/workorder/accept: {}", e.getMessage());
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
} 