package com.heating.repository;

import com.heating.entity.system.DictData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 字典数据仓库接口
 */
@Repository
public interface DictDataRepository extends JpaRepository<DictData, Long> {

    /**
     * 根据字典ID查询字典数据列表
     * @param dictId 字典ID
     * @return 字典数据列表
     */
    List<DictData> findByDictId(Integer dictId);
} 