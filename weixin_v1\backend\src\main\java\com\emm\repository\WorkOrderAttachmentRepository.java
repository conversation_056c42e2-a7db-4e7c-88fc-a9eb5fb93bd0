package com.emm.repository;

import com.emm.model.WorkOrderAttachment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface WorkOrderAttachmentRepository extends JpaRepository<WorkOrderAttachment, String> {
    
    @Query(
        "SELECT new map(" +
            "wa.fileType as file_type, " +
            "wa.filePath as file_path" +
        ") " +
        "FROM WorkOrderAttachment wa " +
        "WHERE wa.orderId = :orderId " +
        "ORDER BY wa.createdAt DESC"
    )
    List<Map<String, Object>> findByOrderId(@Param("orderId") String orderId);
} 