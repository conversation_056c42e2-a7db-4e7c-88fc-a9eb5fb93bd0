package com.heating.repository;

import com.heating.entity.attendance.TAttendanceSupplement;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface AttendanceSupplementRepository extends JpaRepository<TAttendanceSupplement, Long> {
    /**
     * 根据用户ID查询补卡申请
     * @param userId 用户ID
     * @return 补卡申请列表
     */
    List<TAttendanceSupplement> findByUserIdOrderByCreateTimeDesc(Long userId);

    /**
     * 根据状态查询补卡申请
     * @param status 状态
     * @return 补卡申请列表
     */
    List<TAttendanceSupplement> findByStatusOrderByCreateTimeDesc(String status);

    /**
     * 查询用户指定日期和类型的补卡申请
     * @param userId 用户ID
     * @param date 日期
     * @param type 打卡类型
     * @return 补卡申请
     */
    Optional<TAttendanceSupplement> findByUserIdAndDateAndType(Long userId, LocalDate date, Integer type);

    /**
     * 根据ID查询补卡申请
     * @param id 补卡申请ID
     * @return 补卡申请
     */
    @Query("SELECT s FROM TAttendanceSupplement s WHERE s.id = :id")
    Optional<TAttendanceSupplement> findSupplementById(@Param("id") Long id);
} 