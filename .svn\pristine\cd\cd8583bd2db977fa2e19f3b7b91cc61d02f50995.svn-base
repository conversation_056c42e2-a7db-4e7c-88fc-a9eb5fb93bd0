/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.patrol-detail-container {
  padding: 0.625rem;
  background-color: #f5f5f5;
}
.detail-card {
  background-color: #fff;
  border-radius: 0.375rem;
  padding: 0.9375rem;
  margin-bottom: 0.625rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.detail-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.625rem;
}
.detail-card .card-header .card-title {
  font-size: 1rem;
  font-weight: bold;
  position: relative;
  padding-left: 0.625rem;
}
.detail-card .card-header .card-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0.25rem;
  width: 0.1875rem;
  height: 0.875rem;
  background-color: #1890ff;
  border-radius: 0.09375rem;
}
.detail-card .card-header .plan-status {
  padding: 0.1875rem 0.5rem;
  border-radius: 0.125rem;
  font-size: 0.75rem;
}
.detail-card .card-header .plan-status.pending {
  background-color: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}
.detail-card .card-header .plan-status.processing {
  background-color: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}
.detail-card .card-header .plan-status.completed {
  background-color: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}
.detail-card .card-header .plan-status.overdue {
  background-color: rgba(245, 34, 45, 0.1);
  color: #f5222d;
}
.detail-card .card-header .plan-status.canceled {
  background-color: rgba(102, 102, 102, 0.1);
  color: #666;
}
.info-group {
  margin-bottom: 0.625rem;
}
.info-group .info-item {
  display: flex;
  margin-bottom: 0.5rem;
}
.info-group .info-item:last-child {
  margin-bottom: 0;
}
.info-group .info-item .info-label {
  width: 5rem;
  font-size: 0.875rem;
  color: #666;
}
.info-group .info-item .info-value {
  flex: 1;
  font-size: 0.875rem;
  color: #333;
}
.info-group .info-item .info-value.progress {
  margin-bottom: 0.25rem;
}
.info-group .info-item .progress-bar {
  flex: 1;
  height: 0.3125rem;
  background-color: #eee;
  border-radius: 0.15625rem;
  overflow: hidden;
  margin-top: 0.3125rem;
}
.info-group .info-item .progress-bar .progress-inner {
  height: 100%;
  background-color: #1890ff;
}
.plan-desc {
  border-top: 0.03125rem solid #eee;
  padding-top: 0.625rem;
}
.plan-desc .desc-title {
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 0.25rem;
  display: block;
}
.plan-desc .desc-content {
  font-size: 0.875rem;
  color: #333;
  line-height: 1.6;
}
.task-list .task-item {
  border-bottom: 0.03125rem solid #eee;
  padding: 0.625rem 0;
}
.task-list .task-item:last-child {
  border-bottom: none;
}
.task-list .task-item .task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.task-list .task-item .task-header .task-left {
  display: flex;
  align-items: center;
}
.task-list .task-item .task-header .task-left .task-status {
  width: 0.375rem;
  height: 0.375rem;
  border-radius: 50%;
  margin-right: 0.5rem;
}
.task-list .task-item .task-header .task-left .task-status.pending {
  background-color: #666;
}
.task-list .task-item .task-header .task-left .task-status.completed {
  background-color: #52c41a;
}
.task-list .task-item .task-header .task-left .task-status.skipped {
  background-color: #faad14;
}
.task-list .task-item .task-header .task-left .task-title {
  font-size: 0.875rem;
  font-weight: bold;
  color: #333;
}
.task-list .task-item .task-header .iconfont {
  font-size: 0.875rem;
  color: #666;
}
.task-list .task-item .task-details {
  margin-top: 0.5rem;
  padding: 0.5rem;
  background-color: #f8f8f8;
  border-radius: 0.25rem;
}
.task-list .task-item .task-details .detail-row {
  display: flex;
  margin-bottom: 0.375rem;
}
.task-list .task-item .task-details .detail-row:last-child {
  margin-bottom: 0;
}
.task-list .task-item .task-details .detail-row .detail-label {
  width: 3.75rem;
  font-size: 0.8125rem;
  color: #666;
}
.task-list .task-item .task-details .detail-row .detail-value {
  flex: 1;
  font-size: 0.8125rem;
  color: #333;
}
.task-list .task-item .task-details .detail-row .detail-value.value-abnormal {
  color: #f5222d;
}
.task-list .task-item .task-details .task-images {
  margin-top: 0.5rem;
}
.task-list .task-item .task-details .task-images .images-title {
  font-size: 0.8125rem;
  color: #666;
  margin-bottom: 0.25rem;
  display: block;
}
.task-list .task-item .task-details .task-images .image-list {
  display: flex;
  flex-wrap: wrap;
}
.task-list .task-item .task-details .task-images .image-list uni-image {
  width: 4.375rem;
  height: 4.375rem;
  margin-right: 0.3125rem;
  margin-bottom: 0.3125rem;
  border-radius: 0.25rem;
}
.stats-container {
  display: flex;
  justify-content: space-between;
}
.stats-container .stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stats-container .stats-item .stats-value {
  font-size: 1.125rem;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 0.25rem;
}
.stats-container .stats-item .stats-label {
  font-size: 0.75rem;
  color: #666;
}
.action-buttons {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 0.625rem;
  background-color: #fff;
  display: flex;
  justify-content: center;
  box-shadow: 0 -0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.action-buttons .action-btn {
  flex: 1;
  height: 2.5rem;
  line-height: 2.5rem;
  text-align: center;
  border-radius: 0.25rem;
  font-size: 0.9375rem;
  margin: 0 0.3125rem;
}
.action-buttons .action-btn.start {
  background-color: #1890ff;
  color: #fff;
}
.action-buttons .action-btn.continue {
  background-color: #52c41a;
  color: #fff;
}
.action-buttons .action-btn.complete {
  background-color: #faad14;
  color: #fff;
}
.action-buttons .action-btn.export {
  background-color: #1890ff;
  color: #fff;
}