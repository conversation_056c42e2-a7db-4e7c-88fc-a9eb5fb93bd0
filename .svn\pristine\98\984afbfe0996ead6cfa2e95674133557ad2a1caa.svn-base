<template>
  <view class="datetime-picker-container">
    <view class="header">
      <text class="title">{{ title }}</text>
      <text class="close-btn" @click="cancel">取消</text>
    </view>
    
    <view class="picker-body">
      <picker-view class="picker" :indicator-style="indicatorStyle" :value="pickerValue" @change="bindChange">
        <picker-view-column>
          <view class="picker-item" v-for="(item, index) in years" :key="'year-'+index">{{ item }}年</view>
        </picker-view-column>
        <picker-view-column>
          <view class="picker-item" v-for="(item, index) in months" :key="'month-'+index">{{ item }}月</view>
        </picker-view-column>
        <picker-view-column>
          <view class="picker-item" v-for="(item, index) in days" :key="'day-'+index">{{ item }}日</view>
        </picker-view-column>
        <picker-view-column>
          <view class="picker-item" v-for="(item, index) in hours" :key="'hour-'+index">{{ item }}时</view>
        </picker-view-column>
        <picker-view-column>
          <view class="picker-item" v-for="(item, index) in minutes" :key="'minute-'+index">{{ item }}分</view>
        </picker-view-column>
        <picker-view-column>
          <view class="picker-item" v-for="(item, index) in seconds" :key="'second-'+index">{{ item }}秒</view>
        </picker-view-column>
      </picker-view>
    </view>
    
    <view class="btn-area">
      <button class="confirm-btn" @click="confirm">确定</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      title: '选择日期时间',
      indicatorStyle: 'height: 50px;',
      pickerValue: [0, 0, 0, 0, 0, 0],
      years: [],
      months: [],
      days: [],
      hours: [],
      minutes: [],
      seconds: [],
      year: 2020,
      month: 1,
      day: 1,
      hour: 0,
      minute: 0,
      second: 0,
      hasInit: false
    };
  },
  onLoad(option) {
    if (option.params) {
      const params = JSON.parse(decodeURIComponent(option.params));
      if (params.title) {
        this.title = params.title;
      }
      
      if (params.current) {
        this.initPickerFromDateTime(params.current);
      } else {
        this.initPicker();
      }
    } else {
      this.initPicker();
    }
  },
  methods: {
    // 初始化选择器数据
    initPicker() {
      const date = new Date();
      this.year = date.getFullYear();
      this.month = date.getMonth() + 1;
      this.day = date.getDate();
      this.hour = date.getHours();
      this.minute = date.getMinutes();
      this.second = date.getSeconds();
      
      // 生成年份数据，当前年份前后10年
      this.years = [];
      for (let i = this.year - 10; i <= this.year + 10; i++) {
        this.years.push(i);
      }
      
      // 生成月份数据
      this.months = [];
      for (let i = 1; i <= 12; i++) {
        this.months.push(i);
      }
      
      this.updateDays();
      
      // 生成小时数据
      this.hours = [];
      for (let i = 0; i <= 23; i++) {
        this.hours.push(i);
      }
      
      // 生成分钟和秒数据
      this.minutes = [];
      this.seconds = [];
      for (let i = 0; i <= 59; i++) {
        this.minutes.push(i);
        this.seconds.push(i);
      }
      
      // 设置当前值
      this.updatePickerValue();
      this.hasInit = true;
    },
    
    // 根据日期时间字符串初始化选择器
    initPickerFromDateTime(dateTimeStr) {
      const date = new Date(dateTimeStr);
      if (isNaN(date.getTime())) {
        this.initPicker();
        return;
      }
      
      this.year = date.getFullYear();
      this.month = date.getMonth() + 1;
      this.day = date.getDate();
      this.hour = date.getHours();
      this.minute = date.getMinutes();
      this.second = date.getSeconds();
      
      // 生成年份数据，当前年份前后10年
      this.years = [];
      for (let i = this.year - 10; i <= this.year + 10; i++) {
        this.years.push(i);
      }
      
      // 生成月份数据
      this.months = [];
      for (let i = 1; i <= 12; i++) {
        this.months.push(i);
      }
      
      this.updateDays();
      
      // 生成小时数据
      this.hours = [];
      for (let i = 0; i <= 23; i++) {
        this.hours.push(i);
      }
      
      // 生成分钟和秒数据
      this.minutes = [];
      this.seconds = [];
      for (let i = 0; i <= 59; i++) {
        this.minutes.push(i);
        this.seconds.push(i);
      }
      
      // 设置当前值
      this.updatePickerValue();
      this.hasInit = true;
    },
    
    // 更新天数，根据年月确定
    updateDays() {
      let daysInMonth = 31;
      
      // 计算当月天数
      if (this.month === 2) {
        // 闰年2月29天，平年28天
        daysInMonth = (this.year % 4 === 0 && this.year % 100 !== 0) || this.year % 400 === 0 ? 29 : 28;
      } else if ([4, 6, 9, 11].includes(this.month)) {
        daysInMonth = 30;
      }
      
      this.days = [];
      for (let i = 1; i <= daysInMonth; i++) {
        this.days.push(i);
      }
      
      // 如果当前选择的日期超出了当月的最大天数，则调整为当月最后一天
      if (this.day > daysInMonth) {
        this.day = daysInMonth;
      }
    },
    
    // 更新pickerValue
    updatePickerValue() {
      const yearIndex = this.years.indexOf(this.year);
      const monthIndex = this.months.indexOf(this.month);
      const dayIndex = this.days.indexOf(this.day);
      const hourIndex = this.hours.indexOf(this.hour);
      const minuteIndex = this.minutes.indexOf(this.minute);
      const secondIndex = this.seconds.indexOf(this.second);
      
      this.pickerValue = [
        yearIndex !== -1 ? yearIndex : 0,
        monthIndex !== -1 ? monthIndex : 0,
        dayIndex !== -1 ? dayIndex : 0,
        hourIndex !== -1 ? hourIndex : 0,
        minuteIndex !== -1 ? minuteIndex : 0,
        secondIndex !== -1 ? secondIndex : 0
      ];
    },
    
    // 当picker值发生变化时的处理函数
    bindChange(e) {
      const val = e.detail.value;
      
      // 更新选中的值
      this.year = this.years[val[0]];
      this.month = this.months[val[1]];
      
      // 更新天数列表
      this.updateDays();
      
      // 如果当前选中的日期超出了更新后的天数范围，则重置为1号
      if (val[2] >= this.days.length) {
        val[2] = 0;
      }
      
      this.day = this.days[val[2]];
      this.hour = this.hours[val[3]];
      this.minute = this.minutes[val[4]];
      this.second = this.seconds[val[5]];
    },
    
    // 确认选择
    confirm() {
      // 格式化日期时间为字符串
      const datetime = this.formatDateTime();
      
      // 将选择结果返回给调用页面
      const eventChannel = this.getOpenerEventChannel();
      eventChannel.emit('dateTimeSelected', {
        datetime: datetime
      });
      
      // 返回上一页
      uni.navigateBack();
    },
    
    // 取消选择
    cancel() {
      uni.navigateBack();
    },
    
    // 格式化日期时间
    formatDateTime() {
      const year = this.year;
      const month = String(this.month).padStart(2, '0');
      const day = String(this.day).padStart(2, '0');
      const hour = String(this.hour).padStart(2, '0');
      const minute = String(this.minute).padStart(2, '0');
      const second = String(this.second).padStart(2, '0');
      
      return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
    }
  }
};
</script>

<style>
.datetime-picker-container {
  background-color: #fff;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1px solid #eee;
}

.title {
  font-size: 32rpx;
  font-weight: 500;
}

.close-btn {
  font-size: 28rpx;
  color: #999;
}

.picker-body {
  flex: 1;
  padding: 20rpx 0;
}

.picker {
  width: 100%;
  height: 400rpx;
}

.picker-item {
  line-height: 50px;
  text-align: center;
}

.btn-area {
  padding: 30rpx;
}

.confirm-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #1890ff;
  color: #fff;
  border-radius: 8rpx;
}
</style> 