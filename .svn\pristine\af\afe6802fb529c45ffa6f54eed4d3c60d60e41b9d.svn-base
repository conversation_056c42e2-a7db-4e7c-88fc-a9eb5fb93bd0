package com.heating.controller;

import com.heating.dto.patrol.PatrolPlanCreateRequest;
import com.heating.dto.patrol.PatrolPlanCreateResponse;
import com.heating.dto.patrol.PatrolPlanListRequest;
import com.heating.dto.patrol.PatrolPlanListResponse;
import com.heating.dto.patrol.PatrolPlanDetailResponse;
import com.heating.dto.patrol.PatrolRecordSubmitRequest;
import com.heating.dto.patrol.PatrolRecordUpdateRequest;
import com.heating.dto.patrol.PatrolItemResponse;
import com.heating.service.PatrolService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.heating.dto.patrol.PatrolResultUpdateRequest;
import com.heating.dto.patrol.PatrolStatsResponse;
import com.heating.dto.patrol.PatrolItemDictionaryResponse;
import com.heating.dto.patrol.PatrolCategoryResponse;
import com.heating.dto.ApiResponse;
import com.heating.exception.GlobalExceptionHandler;
import org.springframework.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.heating.dto.patrol.PatrolRecordListRequest;
import com.heating.dto.patrol.PatrolRecordListResponse;
import com.heating.dto.patrol.PatrolResultDetailResponse;
import com.heating.dto.patrol.PatrolExecutorResponse;
import com.heating.entity.patrol.TPatrolRecord;
import com.heating.entity.patrol.TPatrolPlan;
import com.heating.entity.user.TUser;
import com.heating.repository.PatrolRecordRepository;
import com.heating.repository.PatrolPlanRepository;
import com.heating.repository.UserRepository;
import java.time.Duration;
import com.heating.dto.patrol.PatrolResultDetailFullResponse;
import com.heating.dto.patrol.PatrolMessageResponse;
import java.util.ArrayList;

@RestController
@RequestMapping("/api/patrols")
public class PatrolController {

    private static final Logger log = LoggerFactory.getLogger(PatrolController.class);

    @Autowired
    private PatrolService patrolService;

    @Autowired
    private PatrolRecordRepository patrolRecordRepository;
    
    @Autowired
    private PatrolPlanRepository patrolPlanRepository;
    
    @Autowired
    private UserRepository userRepository;

    /**
     * 创建巡检计划
     * 
     * @param request 巡检计划创建请求
     * @return 巡检计划创建结果
     */
    @PostMapping("/plans")
    public ResponseEntity<Map<String, Object>> createPatrolPlan(@RequestBody PatrolPlanCreateRequest request) {
        try {
            PatrolPlanCreateResponse response = patrolService.createPatrolPlan(request);
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("code", 200);
            responseData.put("data", response);
            
            return ResponseEntity.ok(responseData);
            
        } catch (IllegalArgumentException e) {
            return ResponseEntity.ok(Map.of(
                "code", 400,
                "message", "创建巡检计划失败: " + e.getMessage()
            ));
        } catch (Exception e) {
            return ResponseEntity.ok(Map.of(
                "code", 500,
                "message", "创建巡检计划失败: " + e.getMessage()
            ));
        }
    }
 
    /**
     * 提交巡检记录
     * POST /api/patrols/records
     * 
     * 请求体中的巡检项目结果(patrol_results)包含以下字段:
     * - patrol_item_id: 巡检项目ID
     * - check_result: 检查结果，可以是normal(正常)或abnormal(异常)
     * - param_value: 参数值
     * - description: 描述
     * - images: 图片列表，使用base64编码的图片数据数组
     * - latitude: 纬度
     * - longitude: 经度
     * 
     * @param request 巡检记录提交请求
     * @return 巡检记录提交结果
     */
    @PostMapping("/records")
    public ResponseEntity<Map<String, Object>> submitPatrolRecord(
            @RequestBody PatrolRecordSubmitRequest request) {
        try {
            patrolService.submitPatrolRecord(request);
            return ResponseEntity.ok(Map.of(
                "code", 200,
                "message", "巡检记录提交成功"
            ));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.ok(Map.of(
                "code", 400,
                "message", "提交巡检记录失败: " + e.getMessage()
            ));
        } catch (Exception e) {
            return ResponseEntity.ok(Map.of(
                "code", 500,
                "message", "提交巡检记录失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 更新巡检记录
     * 
     * @param recordId 巡检记录ID
     * @param request 巡检记录更新请求
     * @return 巡检记录更新结果
     */
    @PutMapping("/records/{id}")
    public ResponseEntity<Map<String, Object>> updatePatrolRecord(
            @PathVariable("id") Long recordId,
            @RequestBody PatrolRecordUpdateRequest request) {
        try {
            patrolService.updatePatrolRecord(recordId, request);
            return ResponseEntity.ok(Map.of(
                "code", 200,
                "message", "巡检记录更新成功"
            ));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.ok(Map.of(
                "code", 400,
                "message", "更新巡检记录失败: " + e.getMessage()
            ));
        } catch (Exception e) {
            return ResponseEntity.ok(Map.of(
                "code", 500,
                "message", "更新巡检记录失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 更新巡检结果
     * 
     * @param resultId 巡检结果ID
     * @param request 巡检结果更新请求
     * @return 巡检结果更新结果
     */
    @PutMapping("/result/{id}")
    public ResponseEntity<Map<String, Object>> updatePatrolResult(
            @PathVariable("id") Long resultId,
            @RequestBody PatrolResultUpdateRequest request) {
        try {
            patrolService.updatePatrolResult(resultId, request);
            return ResponseEntity.ok(Map.of(
                "code", 200,
                "message", "巡检结果更新成功"
            ));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.ok(Map.of(
                "code", 400,
                "message", "更新巡检结果失败: " + e.getMessage()
            ));
        } catch (Exception e) {
            return ResponseEntity.ok(Map.of(
                "code", 500,
                "message", "更新巡检结果失败: " + e.getMessage()
            ));
        }
    }

   
   
    /**
     * 获取巡检记录统计数据
     * @return 统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getPatrolStats() {
        try {
            PatrolStatsResponse stats = patrolService.getPatrolStats();
            
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("data", stats);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.ok(Map.of(
                "code", 500,
                "message", "获取巡检统计数据失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 获取巡检计划列表
     */
    @PostMapping("/plans/list")
    public ResponseEntity<Map<String, Object>> getPatrolPlanList(@RequestBody(required = false) PatrolPlanListRequest request) {
        try {
            Map<String, Object>  plans = patrolService.getPatrolPlanList(request);
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "获取巡检计划列表成功");
            response.put("data", plans);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取巡检计划列表失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("code", 500);
            response.put("message", "获取巡检计划列表失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 获取巡检计划详情
     * 返回巡检计划的详细信息，包括基本信息和巡检项目列表
     * 
     * @param id 巡检计划ID
     * @return 巡检计划详情
     */
    @GetMapping("/plans/{id}")
    public ResponseEntity<Map<String, Object>> getPatrolPlanDetail(@PathVariable("id") Long id) {
        try {
            // 参数验证
            if (id == null) {
                return ResponseEntity.ok(Map.of(
                    "code", 400,
                    "message", "获取巡检计划详情失败: 无效的计划ID"
                ));
            }
            
            // 添加日志
            log.info("获取巡检计划详情: {}", id);
            
            // 调用服务获取巡检计划详情
            PatrolPlanDetailResponse detail = patrolService.getPatrolPlanDetail(id);
            
            // 构建标准响应
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "获取巡检计划详情成功");
            response.put("data", detail);
            
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            log.error("获取巡检计划详情参数错误", e);
            return ResponseEntity.ok(Map.of(
                "code", 400,
                "message", "获取巡检计划详情失败: " + e.getMessage()
            ));
        } catch (Exception e) {
            log.error("获取巡检计划详情异常", e);
            return ResponseEntity.ok(Map.of(
                "code", 500,
                "message", "获取巡检计划详情失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 根据巡检计划ID获取巡检项目列表
     * 用于移动端应用在执行巡检时获取需要检查的项目明细
     * 
     * @param id 巡检计划ID
     * @return 巡检项目列表
     */
    @GetMapping("/plans/{id}/items")
    public ResponseEntity<Map<String, Object>> getPatrolItems(@PathVariable("id") String id) {
        try {
            // 验证ID参数是否有效
            if (id == null || id.trim().isEmpty() || "undefined".equals(id.trim()) || "null".equals(id.trim())) {
                return ResponseEntity.ok(Map.of(
                    "code", 400,
                    "message", "获取巡检项目列表失败: 无效的计划ID"
                ));
            }
            
            Long planId;
            try {
                planId = Long.parseLong(id);
            } catch (NumberFormatException e) {
                return ResponseEntity.ok(Map.of(
                    "code", 400,
                    "message", "获取巡检项目列表失败: 计划ID必须为数字"
                ));
            }
            
            List<PatrolItemResponse> items = patrolService.getPatrolItems(planId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "获取巡检项目列表成功");
            response.put("data", items);
            
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.ok(Map.of(
                "code", 400,
                "message", "获取巡检项目列表失败: " + e.getMessage()
            ));
        } catch (Exception e) {
            return ResponseEntity.ok(Map.of(
                "code", 500,
                "message", "获取巡检项目列表失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 获取巡检项目字典列表
     * GET /api/patrols/dictionary/items
     * 
     * @param categoryId 可选的类别ID参数，为null时获取全部
     * @return 巡检项目字典列表
     */
    @GetMapping("/dictionary/items")
    public ResponseEntity<ApiResponse<List<PatrolItemDictionaryResponse>>> getPatrolItemDictionary(
            @RequestParam(required = false) Long categoryId) {
        try {
            List<PatrolItemDictionaryResponse> items = patrolService.getPatrolItemDictionary(categoryId);
            return ResponseEntity.ok(new ApiResponse<>(200, "获取巡检项目字典列表成功", items));
        } catch (Exception e) {
            log.error("获取巡检项目字典列表失败", e);
            return ResponseEntity.ok(new ApiResponse<>(500, "获取巡检项目字典列表失败: " + e.getMessage(), null));
        }
    }

    /**
     * 获取巡检项目类别列表
     * GET /api/patrols/dictionary/categories
     * 
     * @return 巡检项目类别列表
     */
    @GetMapping("/dictionary/categories")
    public ResponseEntity<ApiResponse<List<PatrolCategoryResponse>>> getPatrolCategories() {
        try {
            List<PatrolCategoryResponse> categories = patrolService.getPatrolCategories();
            return ResponseEntity.ok(new ApiResponse<>(200, "获取巡检项目类别列表成功", categories));
        } catch (Exception e) {
            log.error("获取巡检项目类别列表失败", e);
            return ResponseEntity.ok(new ApiResponse<>(500, "获取巡检项目类别列表失败: " + e.getMessage(), null));
        }
    }

    /**
     * 获取巡检记录列表
     * @param request 包含查询条件的请求体，其中role参数用于权限控制：
     *                - 若role='admin'，则获取所有记录
     *                - 若role不是'admin'，则根据executorId进行过滤
     * @return 巡检记录列表及分页信息
     */
    @PostMapping("/records/list")
    public ResponseEntity<Map<String, Object>> getPatrolRecordList(
            @RequestBody(required = false) PatrolRecordListRequest request) {
        try {
            // 记录请求信息
            log.info("获取巡检记录列表请求: role={}, executorId={}, status={}", 
                    request != null ? request.getRole() : null,
                    request != null ? request.getExecutorId() : null,
                    request != null ? request.getStatus() : null);
            
            // 调用服务层方法获取巡检记录
            Map<String, Object> records = patrolService.getPatrolRecordList(request);
            
            // 构建成功响应
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "获取巡检记录列表成功");
            response.put("data", records);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取巡检记录列表失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("code", 500);
            response.put("message", "获取巡检记录列表失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 获取巡检结果详情
     * 返回巡检结果的详细信息
     *
     * @param recordId 巡检记录ID
     * @return 巡检结果详情
     */
    @GetMapping(value = "/records/{recordId}/results", produces = "application/json")
    public ResponseEntity<Map<String, Object>> getPatrolResultDetail(
            @PathVariable("recordId") String recordId) {
        try {
            if (recordId == null || recordId.trim().isEmpty() || "undefined".equals(recordId.trim()) || "null".equals(recordId.trim())) {
                return ResponseEntity.ok(Map.of(
                    "code", 400,
                    "message", "获取巡检结果详情失败: 无效的记录ID"
                ));
            }
            
            Long recordIdLong;
            try {
                recordIdLong = Long.parseLong(recordId);
            } catch (NumberFormatException e) {
                return ResponseEntity.ok(Map.of(
                    "code", 400,
                    "message", "获取巡检结果详情失败: 记录ID必须为数字"
                ));
            }
            
            long startTime = System.currentTimeMillis();
            
            // 直接调用优化后的服务方法获取完整响应
            PatrolResultDetailFullResponse fullResponse = patrolService.getPatrolResultDetailFull(recordIdLong);
            
            long endTime = System.currentTimeMillis();
            log.info("PatrolController.getPatrolResultDetail 总耗时: {}ms", endTime - startTime);
            
            // 构建响应
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "获取巡检结果详情成功");
            response.put("data", fullResponse);
            
            // 让Spring Boot自己处理压缩，不要手动设置Content-Encoding
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.ok(Map.of(
                "code", 400,
                "message", "获取巡检结果详情失败: " + e.getMessage()
            ));
        } catch (Exception e) {
            log.error("获取巡检结果详情失败", e);
            return ResponseEntity.ok(Map.of(
                "code", 500,
                "message", "获取巡检结果详情失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 获取巡检计划执行人列表
     * GET /api/patrols/plans/{id}/executors
     * 
     * @param id 巡检计划ID
     * @return 执行人列表
     */
    @GetMapping("/plans/{id}/executors")
    public ResponseEntity<Map<String, Object>> getPatrolExecutors(@PathVariable("id") String id) {
        try {
            // 验证ID参数是否有效
            if (id == null || id.trim().isEmpty() || "undefined".equals(id.trim()) || "null".equals(id.trim())) {
                return ResponseEntity.ok(Map.of(
                    "code", 400,
                    "message", "获取巡检计划执行人列表失败: 无效的计划ID"
                ));
            }
            
            Long planId;
            try {
                planId = Long.parseLong(id);
            } catch (NumberFormatException e) {
                return ResponseEntity.ok(Map.of(
                    "code", 400,
                    "message", "获取巡检计划执行人列表失败: 计划ID必须为数字"
                ));
            }
            
            List<PatrolExecutorResponse> executors = patrolService.getPatrolExecutors(planId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "获取巡检计划执行人列表成功");
            response.put("data", executors);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取巡检计划执行人列表失败", e);
            return ResponseEntity.ok(Map.of(
                "code", 500,
                "message", "获取巡检计划执行人列表失败: " + e.getMessage()
            ));
        }
    }
    
    /**
     * 获取巡检消息
     * GET /api/patrols/plans/messages
     * 
     * 在巡检计划中，根据配置的巡检周期类型和对应的巡检时间，与当前时间进行比对
     * 如果到巡检时，自动提醒巡检人（对应配置）巡检
     * 
     * @param uid 用户ID
     * @param role 用户角色，如果是'admin'或'manage'，则获取所有记录，否则根据uid过滤
     * @return 巡检消息列表
     */
    @GetMapping("/plans/messages")
    public ResponseEntity<Map<String, Object>> getPatrolMessages(
            @RequestParam(required = false) Long uid,
            @RequestParam(required = false) String role) {
        try {
            log.info("获取巡检提醒消息: uid={}, role={}", uid, role);
            List<PatrolMessageResponse> messages = patrolService.getPatrolMessages(uid, role);
            
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "获取巡检提醒消息成功");
            response.put("data", messages);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取巡检提醒消息失败", e);
            return ResponseEntity.ok(Map.of(
                "code", 500,
                "message", "获取巡检提醒消息失败: " + e.getMessage()
            ));
        }
    }
} 