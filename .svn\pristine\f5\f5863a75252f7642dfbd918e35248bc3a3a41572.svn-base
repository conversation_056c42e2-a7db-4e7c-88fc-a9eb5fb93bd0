# 智慧供暖移动端APP UI设计规范

## 一、全局配置

### 1.1 导航结构

```json
{
"pages": [
"pages/home/<USER>", // 角色首页
"pages/device/list", // 设备列表
"pages/device/detail", // 设备详情
"pages/patrol/plans", // 巡检计划
"pages/patrol/records", // 巡检记录
"pages/fault/report", // 故障上报
"pages/fault/list", // 故障列表
"pages/fault/detail", // 故障详情
"pages/hes/list", // 换热站列表
"pages/hes/detail", // 换热站详情
"pages/hes/control", // 换热站控制
"pages/hes/alarms", // 换热站告警
"pages/valves/list", // 入户阀门列表
"pages/valves/control", // 入户阀门控制
"pages/payment/records", // 缴费记录
"pages/payment/stats", // 缴费统计
"pages/message/center", // 消息中心
"pages/message/settings", // 消息设置
"pages/user/login", // 用户登录
"pages/user/register", // 用户注册
"pages/user/info", // 用户信息
"pages/user/modify", // 修改用户信息
"pages/system/settings" // 系统设置
],
"tabBar": {
"list": [
{
"pagePath": "pages/home/<USER>",
"text": "首页",
"iconPath": "static/tab/home.png",
"selectedIconPath": "static/tab/home-active.png"
},
{
"pagePath": "pages/device/list",
"text": "设备",
"iconPath": "static/tab/device.png",
"selectedIconPath": "static/tab/device-active.png"
},
{
"pagePath": "pages/hes/list",
"text": "换热站",
"iconPath": "static/tab/hes.png",
"selectedIconPath": "static/tab/hes-active.png"
},
{
"pagePath": "pages/message/center",
"text": "消息",
"iconPath": "static/tab/notification.png",
"selectedIconPath": "static/tab/notification-active.png"
},
{
"pagePath": "pages/user/info",
"text": "我的",
"iconPath": "static/tab/profile.png",
"selectedIconPath": "static/tab/profile-active.png"
}
]
}
}
```

### 1.2 颜色规范

| 类型     | 色值    | 应用场景          |
| -------- | ------- | ----------------- |
| 主色     | #1890ff | 按钮/重要文字     |
| 辅助色   | #52c41a | 成功状态/完成提示 |
| 警示色   | #faad14 | 警告状态          |
| 错误色   | #f5222d | 错误状态/紧急操作 |
| 背景色   | #f8f8f8 | 页面底色          |
| 文字色   | #333    | 主要文字          |
| 次要文字 | #666    | 辅助说明文字      |

## 二、核心页面设计

### 2.1 用户认证

#### 2.1.1 登录页面

```示例
[Logo] 智慧供暖运维系统

[输入框] 用户名
[输入框] 密码
[按钮] 登录

[链接] 注册账号
```

### 2.2 设备管理页

设计要点说明：
1. 列表页-增加「高级筛选」折叠面板，支持多维度组合查询
2. 详情页-采用时间轴展示操作记录，符合运维审计需求
3. 录入页-区分扫码/手动模式，表单增加实时校验提示
4. 可视化-热力图支持区域下钻分析，折线图可对比多设备状态
5. 交互细节-设备卡片采用状态色标（绿/黄/红）快速识别
6. 数据安全-删除操作需二次确认并记录操作日志
与需求文档对应关系：
1. 设备管理 → 扫码录入+手动表单
2. 设备记录查询 → 高级筛选+排序控制
3. 换热站监测 → 状态可视化面板
4. 数据报表 → 趋势图表分析

#### 2.2.1 设备列表页
```设备列表界面规范
[搜索栏]
- 即时搜索：输入时实时过滤结果
- 高级筛选按钮：▼ 展开筛选条件

[筛选面板]
○ 设备状态：全部/在线/离线/故障
○ 安装区域：下拉多选（东区/西区...）
○ 设备类型：下拉选择（阀门/泵...）
○ 时间范围：日期选择器

[列表项设计]
▢ 设备卡片（每项包含）：
  设备名称：#1890ff 加粗
  设备编号：灰色小字
  状态标签：🟢 在线 / 🔴 故障
  最后维护时间：YYYY-MM-DD HH:mm
  快捷操作：▶ 详情  ✎ 编辑

[排序控制]
▼ 排序方式：
  安装时间（最新）
  维护周期（最短优先）
  报警次数（最多优先）
```

#### 2.2.2 设备详情页
```设备详情规范
[基本信息区]
- 设备照片：轮播图（最多3张）
- 核心信息：
  型号：XGZ-2023-Pro
  安装位置：地图微件+文字地址
  当前状态：实时更新指示器
  维护周期：15天（剩余3天）

[状态监控]
▢ 实时数据卡片：
  温度：🌡️ 65℃（正常范围50-80℃）
  压力：📌 0.8MPa（阈值提醒）
  流量：💧 120m³/h

[操作记录]
▢ 时间轴布局：
  ▼ 2023-12-05 14:00 张三 修改维护周期 30→15天
  ▼ 2023-12-01 09:30 系统 压力异常报警
  ▼ 2023-11-28 10:15 李四 完成例行维护

[底部操作栏]
[编辑信息] [删除设备] [生成维护工单]
```

#### 2.2.3 设备录入页
```扫码录入流程
[扫码界面]
- 动态扫描框：绿色激光动画
- 手动输入切换按钮
- 状态提示：
  成功：✅ 已识别DEV-202312-001
  失败：❌ 无法识别，请手动输入

[自动填充表单]
设备编号：DEV-202312-001（不可编辑）
型号：XGZ-2023（根据二维码解析）
安装位置：自动获取GPS+手动微调
```

```手动录入表单
[表单结构]
1. 基础信息（带*必填）：
   - 设备名称*：输入框（最大20字）
   - 设备类型*：下拉选择（10个选项）
   - 安装位置*：地图选点+文字备注

2. 技术参数：
   - 额定功率：带单位kW的数字输入
   - 工作温度：范围选择（-20℃~150℃）
   - 通讯协议：单选（Modbus/HTTP/MQTT）

3. 维护设置：
   - 维护周期*：天数选择（1-365）
   - 维护人员：多选（从组织架构选择）

[表单验证]
- 实时验证：输入时显示校验状态
- 提交拦截：未通过项红色边框+提示文字
- 错误示例：❗设备名称不能超过20个字符
```

#### 2.2.4 设备状态可视化
```状态面板设计
[全局统计]
▢ 看板卡片：
  设备总数：356台 
  在线率：98.5% 
  本周报警：12次

[热力图]
- 区域设备分布：按楼宇颜色梯度显示
- 点击热区可下钻到具体设备列表

[趋势图表]
▢ 折线图：
  X轴：时间（按周/月切换）
  Y轴：报警次数/离线时长
  多设备对比：可选2-3个设备曲线
```

### 2.3 巡检管理

设计要点说明：
1. 计划配置-采用「技能匹配推荐」算法，自动推荐合适人员
2. 日历视图-三色状态标识（绿/黄/红）直观显示执行情况
3. 移动巡检-结合NFC感应和扫码双模式设备核验
4. 数据录入-带单位换算和范围提示的智能表单
5. 异常上报-红色紧急按钮触发即时通知机制
6. 审核对比-左右分屏显示当前与历史数据差异
7. 时间控制-工单列表显示剩余时间，逾期红色警示
交互细节：
1. 巡检执行页面的表单字段会根据设备类型动态变化
2. 拍照功能集成AI识别，自动标记疑似异常区域
3. 审核通过后自动生成维护工单（如需）
4. 历史工单支持导出PDF报告（含所有多媒体记录）

#### 2.3.1 巡检计划配置
```计划设置界面
[周期设定]
○ 日常  ○ 每周  ○ 每月  ○ 自定义
▢ 自定义规则：
  每 [3] 天执行一次
  每周 [周一][周三][周五]
  每月 [5日][20日]

[任务分配]
[设备选择] 地图点选+列表勾选（最多50台）
[人员分配] 技能匹配推荐：
  张三（擅长泵类设备）
  李四（持压力容器证书）

[提醒设置]
◉ 小程序消息  ○ 模板消息
提前 [30] 分钟提醒
重复提醒间隔 [15] 分钟/次
```

#### 2.3.2 巡检日历
```日历视图
[月视图] 
  ▢ 日期格子：
    ✅ 12/5 已完成（绿色底色）
    ⚠️ 12/7 异常（黄色标记）
    🔴 12/10 逾期（红色边框）
[周视图]
  ▢ 时间轴模式：
    09:00-10:00 1号泵房巡检
    14:00-15:00 西区阀门检查
[任务详情]
  点击事件弹出：
    📌 位置导航
    👥 负责人员
    📋 检查清单
```

#### 2.3.3 巡检工单
```工单生成界面
[自动生成]
▢ 计划关联：
  计划名称：12月日常巡检
  生成时间：每天08:00
[手动创建]
▢ 快速创建：
  设备选择：搜索或扫码添加
  紧急程度：○ 一般 ○ 紧急

[工单列表]
▢ 列表项：
  状态标签：待处理/进行中/待审核
  设备数量：3台（2正常 1异常）
  倒计时：剩余2小时15分
  操作按钮：▶ 开始巡检
```

#### 2.3.4 巡检执行
```移动端巡检界面
[设备核验]
▢ NFC感应区：靠近设备自动识别
▢ 扫码备选：手动扫描设备二维码

[数据录入]
▢ 表单字段：
  泵体温度：_[36]_℃（正常范围30-50℃）
  压力读数：_[0.8]_MPa（带单位换算）
  运行噪音：○ 正常 ○ 异常

[异常上报]
📸 现场拍照（最多6张）
📝 备注说明：多行文本输入
[!] 立即上报按钮（红色突出显示）
```

#### 2.3.5 审核归档
```管理端审核界面
[待审列表]
▢ 筛选条件：
  仅显示异常工单
  按设备类型过滤
[审核详情]
▢ 对比视图：
  左侧：巡检提交数据
  右侧：设备历史数据
[审批操作]
◉ 通过 ○ 驳回
📝 审批意见：多行文本输入
```
### 2.4 故障管理
设计要点说明：
1. 上报流程-区分自动预警触发和手动发起两种路径
2. 紧急处理-红色视觉体系+双重确认机制
3. 证据管理-支持多格式文件上传和画廊浏览
4. 智能派单-结合距离、技能、空闲状态推荐
5. 审核对比-左右分屏验证故障真实性
6. 可视化-热力图定位故障高发区域
7. 时间控制-处理倒计时和超时预警
8. 隐私保护-提供位置隐藏开关
交互细节：
1. 语音输入转文字功能（需用户授权）
2. 视频上传自动生成封面缩略图
3. 紧急故障触发App内推送+短信通知
4. 处理时间轴支持添加现场记录
5. 看板数据每5分钟自动刷新
6. 重复故障设备突出显示黄色边框

#### 2.4.1 故障上报
```用户端上报界面
[智能检测]
▢ 自动预警卡片（当检测到异常时弹出）：
  ⚠️ 检测到1号泵温度异常（当前85℃/阈值80℃）
  [立即上报] [忽略]

[手动上报表单]
1. 基础信息：
   - 故障类型：下拉选择（机械/电气/控制...）
   - 设备关联：扫码或搜索选择
   - 紧急程度：○ 一般 ○ 紧急

2. 故障描述：
   - 现象说明：多行文本+语音输入
   - 现场证据：上传图片/视频（最多9个）

3. 位置信息：
   📍 自动定位+地图微调
   [隐藏位置] 开关（保护隐私）

[提交控制]
▢ 双重确认：
   ! 紧急故障需二次确认联系人
   [提交并通知负责人] 红色按钮
```

#### 2.4.2 故障列表
```列表界面规范
[状态筛选]
○ 全部 ○ 待处理 ○ 进行中 ○ 已关闭
○ 紧急优先（红色置顶）

[列表项设计]
▢ 故障卡片：
  紧急标识：🔥（紧急） / ⚠️（一般）
  故障代码：#F-202312-001
  设备名称：1号循环泵
  上报时间：12-05 14:23
  当前状态：待派单（灰色标签）

[高级筛选]
▼ 展开更多条件：
  - 设备类型：多选（泵/阀门...）
  - 上报人员：下拉选择
  - 时间范围：开始/结束日期
```

#### 2.4.3 故障详情
```详情页结构
[基本信息区]
- 故障等级：🔴 紧急（红色背景）
- 处理时限：剩余4小时23分
- 关联设备：带状态跳转链接

[过程追踪]
▢ 时间轴组件：
  ▼ 14:23 用户上报（附3张照片）
  ▼ 14:30 系统自动派单
  ▼ 14:45 李四接单出发
  ▼ 15:10 现场处理中...

[多媒体证据]
▢ 证据画廊：
  缩略图网格布局（3列）
  点击放大查看原图/视频
  支持滑动浏览和下载
```

#### 2.4.4 审核派单
```管理端操作界面
[审核面板]
▢ 双栏对比：
  左侧：用户提交信息
  右侧：设备实时数据
[审核决策]
◉ 有效故障 ○ 无效误报
📝 驳回原因输入（必填）

[智能派单]
▢ 推荐方案：
  最佳匹配：王工（距离1.2km）
  备选方案：张工（空闲状态）
[紧急通道]
📞 直接拨打负责人电话
🚨 触发声光报警（控制室）
```

#### 2.4.5 故障看板
```数据可视化
[全局统计]
▢ 实时看板：
  今日故障：12起（3紧急）
  平均响应：43分钟
  重复故障：2设备

[热力图]
- 按楼宇显示故障密度
- 点击下钻到具体设备

[趋势分析]
▢ 折线图对比：
  故障数量 vs 维护工单
  按周/月切换时间维度
```

### 2.5 维修工单管理
设计要点说明：
1. 状态管理-五色标签体系+进度条直观显示处理阶段
2. 过程跟踪-时间轴集成多媒体记录（照片/语音）
3. 电子签名-手写板组件+双确认机制（用户/维修方）
4. 智能派单-三维度推荐算法（技能/距离/空闲）
5. 移动维修-集成扫码录入备件库存，确保数据准确
6. 统计分析-多图表联动分析，支持穿透查询
7. 异常处理-快速上报通道直通技术支援团队
交互细节：
1. 工单卡片显示"预计超时"红色闪烁提示（剩余＜30分钟）
2. 维修照片自动添加时间水印和地理位置信息
3. 电子签名需至少包含姓名首字母和日期
4. 自动派单推荐结果每5分钟刷新一次
5. 耗材使用扫码后自动扣减库存数量
6. 语音备注支持实时转文字存档
7. 报表导出PDF自动生成带公章版本

#### 2.5.1 工单列表
```工单列表界面
[创建入口]
▢ 浮动按钮：+ 新建工单（支持扫码创建）
▢ 自动生成标记：系统工单带⚙️图标

[状态标签]
○ 待处理（#8c8c8c） ○ 处理中（#1890ff） 
○ 已完成（#52c41a） ○ 异常（#faad14） 
○ 已撤销（#8c8c8c带删除线）

[列表项设计]
▢ 工单卡片：
  工单编号：#R-202312-001
  设备名称：2号楼换热泵
  紧急程度：🔴 紧急（倒计时4h） 
  当前进度：▶ 60%（进度条）

[高级筛选]
▼ 展开更多：
  - 创建方式：自动/手动
  - 维修类型：机械/电气...
  - 耗时范围：＜1h / 1-3h / ＞3h
```

#### 2.5.2 工单详情
```详情页结构
[基本信息]
- 创建时间：2023-12-05 14:23
- 关联故障：#F-202312-001
- 维修人员：李四（距离1.2km）

[过程跟踪]
▢ 时间轴布局：
  ▼ 14:30 工单创建（系统自动）
  ▼ 14:45 王五接单出发
  ▼ 15:10 现场诊断完成
  ▼ 15:30 更换密封圈（拍照上传）

[维修报告]
▢ 表单结构：
  故障原因：密封圈老化
  更换部件：NSK-2023密封圈
  维修时长：1小时15分
  用户反馈：⭐️⭐️⭐️⭐

[电子签名]
▢ 签名面板：
  用户签名区：手写板组件
  维修确认：◉ 已解决 ○ 需跟进
```

#### 2.5.3 工单统计
```数据分析界面
[全局看板]
▢ 统计卡片：
  本月工单：56单（同比+12%）
  平均响应：38分钟
  返修率：5.2%

[可视化图表]
▢ 柱状图：各类型工单数量对比
▢ 折线图：响应时间趋势（周/月）
▢ 饼图：维修结果分布

[明细表格]
| 工单号   | 设备    | 耗时 | 评分 |
|----------|---------|------|------|
| #R-1201 | 1号泵   | 1.2h | 4.5  |
| #R-1202 | 西区阀门 | 2.5h | 3.8  |

[导出功能]
▼ 导出格式：Excel/PDF/图片
◉ 包含图表 ○ 仅数据
```

#### 2.5.4 任务派单
```派单界面设计
[自动派单]
▢ 推荐列表：
  1. 张三（技能匹配度95%）
  2. 李四（距离最近800m）
  3. 王五（当前空闲）

[手动派单]
▢ 人员选择：
  搜索框：按姓名/技能查找
  地图模式：显示人员实时位置
  详细信息：接单量/好评率

[任务转移]
▢ 再分派流程：
  选择新负责人：从组织架构选择
  填写转移原因：多行文本输入
  通知原负责人：◉ 站内信 ○ 短信
```

#### 2.5.5 移动端维修
```维修人员界面
[工单认领]
▢ 待接单列表：
  距离：1.5km（预计8分钟到达）
  紧急提示：❗️需30分钟内到达
  [导航] [电话] [接单] 按钮

[过程记录]
▢ 现场记录表单：
  开始/结束时间：自动记录
  使用备件：扫码录入库存
  维修照片：带水印（时间+位置）
  语音备注：60秒语音输入

[异常上报]
▢ 快速通道：
  备件不足 ○ 技术难题 ○ 其他
  请求支援：选择支援人员类型
```

### 2.6 缴费管理

设计要点说明：
1. 费用透明-公式化展示计算过程，消除用户疑虑  
2. 安全支付-双重验证机制保障资金安全
3. 状态联动-实时反馈阀门控制进度
4. 异常处理-滞纳金红色标记+预警倒计时
5. 凭证管理-带电子章的PDF账单下载
6. 分期支持-灵活的分期方案选择
7. 数据同步-房户信息变更二次确认

#### 2.7.1 缴费记录列表页面

```缴费记录列表页面
[筛选条件]
  [选择器] 状态: 全部 | 已缴费 | 未缴费
  [日期范围选择器] 开始时间 - 结束时间
  
[缴费记录列表]
  [缴费记录卡片]
    [标签] 已缴费
    [文本] 用户: 张三
    [文本] 热力单元: 金色家园
    [文本] 金额: ¥1000.00
    [文本] 支付方式: 微信支付
    [文本] 支付时间: 2025-03-01 10:15:30
    [文本] 支付渠道: APP
    [文本] 账单周期: 2024-2025供暖季
```

**数据绑定**:
- 筛选条件: `status`, `start_time`, `end_time`, `page`, `pageSize`
- 缴费记录列表: 调用 `/api/payments/records` 接口
- 缴费记录卡片数据: 从接口返回数据中获取缴费记录信息，包括 `user_name`, `heat_unit_name`, `amount`, `status`, `payment_method`, `payment_time`, `payment_channel`, `billing_period`

#### 2.7.2 缴费详情页面

```缴费详情页面
[账单信息卡片]
  [文本] 账单编号: PAY202503010001
  [文本] 用户: 张三
  [文本] 热力单元: 金色家园
  [文本] 房屋信息: 1号楼1单元101室
  [文本] 供热面积: 89.6㎡
  [文本] 账单周期: 2024-2025供暖季
  [文本] 账单状态: 已缴费

[费用明细卡片]
  [文本] 基础费用: ¥30.00/㎡ × 89.6㎡ = ¥2,688.00
  [文本] 楼层调节费: ¥5.00/层 × 15层 = ¥75.00
  [文本] 优惠金额: -¥200.00
  [文本] 滞纳金: ¥0.00
  [文本] 应缴总额: ¥2,563.00

[支付信息卡片]
  [文本] 支付方式: 微信支付
  [文本] 支付时间: 2025-03-01 10:15:30
  [文本] 支付渠道: APP
  [文本] 交易流水号: 4200000123202503011234567890

[按钮] 下载电子发票
[按钮] 查看供热状态
```

**数据绑定**:
- 页面加载: 调用 `/api/payments/detail/{id}` 接口
- 账单信息: 从接口返回数据中获取账单基本信息
- 费用明细: 从接口返回数据中获取费用计算明细
- 支付信息: 从接口返回数据中获取支付相关信息
- 下载电子发票按钮: 调用 `/api/payments/invoice/{id}` 接口
- 查看供热状态按钮: 跳转到阀门控制页面

#### 2.7.3 缴费统计页面

```缴费统计页面
[查询条件]
  [选择器] 热力单元: 金色家园
  [日期范围选择器] 开始时间 - 结束时间
  [选择器] 状态: 全部 | 已缴费 | 未缴费
  
[统计卡片]
  [数据] 总金额: ¥1,000,000.00
  [数据] 已缴费: ¥800,000.00
  [数据] 未缴费: ¥200,000.00
  [数据] 缴费率: 80%
  
[图表] 缴费趋势图
  [折线图] 按日/周/月显示缴费金额变化
  [饼图] 已缴费/未缴费比例
  [柱状图] 各热力单元缴费情况对比
```

**数据绑定**:
- 查询条件: `heat_unit_name`, `start_time`, `end_time`, `status`
- 统计卡片: 调用 `/api/payments/stats` 接口
- 统计数据: 从接口返回数据中获取 `total`, `paid`, `unpaid`, `payment_rate`
- 图表数据: 从接口返回数据中获取趋势数据和分布数据

#### 2.7.4 缴费页面

```缴费页面
[账单信息卡片]
  [文本] 用户: 张三
  [文本] 热力单元: 金色家园
  [文本] 房屋信息: 1号楼1单元101室
  [文本] 供热面积: 89.6㎡
  [文本] 账单周期: 2024-2025供暖季

[费用计算卡片]
  [文本] 基础费用: ¥30.00/㎡ × 89.6㎡ = ¥2,688.00
  [文本] 楼层调节费: ¥5.00/层 × 15层 = ¥75.00
  [文本] 优惠金额: -¥200.00 (早缴费优惠)
  [文本] 滞纳金: ¥0.00
  [文本] 应缴总额: ¥2,563.00

[支付方式选择]
  [单选] 微信支付
  [单选] 支付宝
  [单选] 银行卡支付
  [单选] 线下支付

[按钮] 立即支付
```

**数据绑定**:
- 页面加载: 调用 `/api/payments/bill/{user_id}` 接口
- 账单信息: 从接口返回数据中获取账单基本信息
- 费用计算: 从接口返回数据中获取费用计算明细
- 立即支付按钮: 调用 `/api/payments/pay` 接口，传递 `bill_id`, `payment_method`, `payment_channel`

#### 2.7.5 阀门控制页面

```阀门控制页面
[状态指示]
  [图标] 阀门状态: 开启/关闭/异常
  [文本] 当前开度: 80%
  [文本] 上次操作: 2025-03-01 10:15:30 自动开启(缴费成功)
  [文本] 操作人员: 系统

[控制面板]
  [滑动条] 开度调节: 0-100%
  [按钮] 设置开度
  [按钮] 一键开启
  [按钮] 一键关闭

[操作记录]
  [时间线]
    [节点] 2025-03-01 10:15:30 自动开启(缴费成功)
    [节点] 2025-02-28 09:10:15 手动关闭(欠费)
    [节点] 2025-02-15 08:30:45 开度调整至50%
```

**数据绑定**:
- 页面加载: 调用 `/api/valves/status/{id}` 接口
- 阀门状态: 从接口返回数据中获取阀门当前状态
- 操作记录: 从接口返回数据中获取阀门操作历史记录
- 控制按钮: 调用 `/api/valves/control` 接口，传递 `valve_id`, `open_degree`, `operator_id`

### 2.8 换热站监测
设计要点说明：
1. 态势感知-3D模型+数字孪生 技术直观展示设备状态
2. 智能预警-三级告警体系与自动预案触发
3. 安全控制-生物识别+动态口令双因子验证
4. 视频增强-AR叠加显示设备实时参数
5. 协同作业-远程标注工具实现精准指导
6. 审计追踪-完整记录从告警到处置的全过程
7. 能效分析-多维度对比历史数据优化运行
交互细节：
1. 温度参数鼠标悬停显示最近24小时波动
2. 告警确认后自动生成运维工单
3. 视频画面支持数字放大（最高8倍）
4. 远程控制操作延迟显示进度条
5. 历史数据支持导出CSV格式
6. 3D模型点击部件显示维护手册
7. 协助会话自动生成操作纪要
权限控制：
1. 普通运维：仅查看权限
2. 高级工程师：参数调整
3. 管理员：规则配置+审计日志
4. 远程专家：临时访问权限（2小时）

#### 2.8.1 实时监控
```监控仪表盘
[核心参数]
▢ 数字仪表组：
  供水温度：89℃（绿色）
  回水压力：0.78MPa（黄色预警）
  瞬时流量：120m³/h
  能耗：356kW（同比+12%）

[3D模型]
▢ 设备仿真：
  可旋转的3D管道模型
  异常部位红色闪烁
  点击查看部件详情
```

#### 2.8.2 历史趋势
```数据分析界面
[图表切换]
▼ 选择参数：温度/压力/流量
○ 折线图 ○ 柱状图 ○ 散点图

[对比模式]
▢ 双轴图表：
  主Y轴：温度值（℃）
  次Y轴：阀门开度（%）
  支持拖动时间范围
```

#### 2.8.3 告警管理
```告警配置界面
[规则设置]
▢ 阈值配置：
  参数：回水压力
  条件：＞0.8MPa持续30秒
  级别：🔴 紧急

[告警列表]
▢ 颜色编码：
  红色-紧急 黄色-警告 蓝色-通知
▢ 列表项：
  ⚠️ 2023-12-05 14:23 1#站压力超标
  [消音] [确认] [派单] 按钮
```

#### 2.8.4 远程控制
```控制台界面
[设备列表]
▢ 带状态指示：
  循环泵1#：◉ 运行中
  电动阀2#：○ 已关闭
  调节阀3#：◔ 故障

[操作面板]
▢ 二次验证：
  生物识别（指纹/面部）
  动态口令（60秒有效）
▢ 指令记录：
  14:25 开启循环泵（操作员张三）
  14:26 调节阀开度至75%
```

#### 2.8.5 视频集成
```视频监控界面
[多画面布局]
▢ 四宫格模式：
  主画面：泵房全景
  子画面：压力表特写
  画中画：控制柜状态

[智能联动]
▢ 告警触发时：
  自动放大相关摄像头
  开启10秒预录像
  显示温度叠加层
```

#### 2.8.6 远程协助
```技术支持界面
[双流传输]
▢ 分屏显示：
  左：现场实时视频
  右：系统数据面板
[标注工具]
▢ 实时标记：
  箭头/圆圈/文字批注
  截图共享至聊天
[会话记录]
▢ 保存内容：
  操作指令时间轴
  沟通过程文字转录
```

### 2.9 个人中心
设计要点说明：
1. 身份区分-普通用户与员工显示不同数据维度
2. 安全体系-多重验证机制+操作日志审计
3. 工作聚合-个人看板集成所有待办事项
4. 智能帮助-知识库搜索联想+问题分类
5. 消息分级-颜色编码区分通知紧急程度
6. 权限控制-敏感操作（如注销）加强验证
7. 移动办公-扫码接单快速入口提升效率
交互细节：
1. 头像上传实时生成圆形预览
2. 工单地图显示周边待处理任务
3. 反馈提交后生成服务编号供查询
4. 安全日志支持导出为PDF
5. 知识库文章带"是否有帮助"评分
6. 日报提交关联当日工单数据
7. 证书上传自动OCR识别关键信息
角色适配：
1. 普通用户：突出缴费、报修功能
2. 运维人员：强化工单处理工具
3. 管理人员：内置数据分析入口
4. 客服人员：快捷消息模板调用

#### 2.9.1 登录注册
```认证界面规范
[登录入口]
▢ 微信一键登录（绿色按钮）
▢ 手机验证码登录
▢ 密码登录（带忘记密码）

[安全增强]
▢ 异地登录检测：
  新设备登录需短信验证
  可疑活动触发人脸识别
```

#### 2.9.2 个人信息
```个人资料页
[角色标识]
▢ 身份徽章：
  维修工程师（金色边框）
  持证类别：压力容器操作

[数据概览]
▢ 工作统计：
  本月工单：23单
  平均评分：4.8
  出勤率：100%

[资料编辑]
▢ 表单结构：
  头像：圆形裁剪上传
  紧急联系人：+ 添加
  技能证书：扫描件上传
```

#### 2.9.3 消息中心
```通知界面设计
[分类标签]
○ 系统公告 ○ 工单提醒 
○ 缴费通知 ○ 安全预警

[消息卡片]
▢ 未读标记：蓝色圆点
▢ 重要公告：红色置顶
▢ 操作按钮：
  [标为已读] [删除]

[推送管理]
▢ 订阅设置：
  ◉ 工单提醒 ○ 仅工作时间
  ◉ 缴费预警 ○ 关闭营销信息
```

#### 2.9.4 帮助反馈
```帮助系统界面
[智能搜索]
▢ 知识库搜索框：
  自动补全常见问题
  高亮匹配关键词

[反馈流程]
▢ 多类型提交：
  ○ 问题反馈 ○ 功能建议
  ○ 投诉举报
▢ 附件上传：
  最多9张图片/1个视频
  自动压缩大文件

[处理追踪]
▢ 状态标签：
  待处理 → 已受理 → 已解决
  显示最后更新时间
```

#### 2.9.5 工单聚合
```个人工作台
[数据看板]
▢ 今日任务：
  待处理：3单
  进行中：2单
  超时预警：1单

[快速入口]
▢ 按钮组：
  [扫码接单] [工单地图]
  [考勤补卡] [日报提交]

[历史记录]
▢ 时间轴布局：
  12-05 14:23 完成#R-1201
  12-05 16:40 提交日报
```

#### 2.9.6 安全设置
```账户安全页
[验证方式]
▢ 绑定项：
  ✅ 手机号 ✅ 微信
  ○ 邮箱 ○ 指纹

[操作记录]
▢ 安全日志：
  12-05 14:25 密码修改
  12-05 09:10 新设备登录

[注销流程]
▢ 风险提示：
  需验证身份+填写原因
  数据保留30天后清除
```

### 2.10 系统管理
设计要点说明：
1. 权限控制-三维度管理（角色/人员/设备组）
2. 安全策略-关键操作留痕+双人复核机制
3. 版本追溯-参数配置支持历史版本对比
4. 接口监控-实时ping检测+流量可视化
5. 审计追踪-操作日志关联视频录像
6. 模板化输出-预设常用报表格式
7. 风险隔离-测试支付使用独立沙箱环境
交互细节：
1. 权限变更需上级审批后生效（工作流集成）
2. 参数输入超出安全范围时红色闪烁警示
3. 日志详情点击时间戳可查看当时系统快照
4. 接口测试成功显示绿色对勾动画
5. 自定义报表支持拖拽字段排序
6. 敏感配置项修改需要动态口令验证
7. 系统健康度仪表盘显示综合评分
权限分级示例：
Level1（客服）：日志查看
Level2（主管）：报表生成
Level3（工程师）：参数调整
Level4（管理员）：接口配置
Level5（审计员）：日志导出

#### 2.10.1 权限管理
```角色配置界面
[角色列表]
▢ 角色卡片：
  管理员（8人） ▶ 设备控制/参数修改
  维修组长（12人） ▶ 工单派发/审核
  客服（5人） ▶ 工单创建/查询

[权限树]
▢ 模块结构：
  ▼ 设备管理
    ○ 远程控制 ○ 参数修改
  ▼ 工单系统
    ○ 创建 ○ 派发 ○ 审核
  ▢ 批量操作：全选/反选
```

#### 2.10.2 参数配置
```系统设置界面
[分类导航]
○ 安全策略 ○ 告警规则 ○ 缴费设置

[阈值设置]
▢ 输入组：
  最高温度：<input type="number" min="0"> ℃
  压力容差：± <input type="range"> %
  支付超时：<select><option>30分钟</option>...

[版本控制]
▢ 历史对比：
  当前版本：V2.3.1
  回滚选择：▼ 选择历史版本
  差异显示：红色删除线标记旧值
```

#### 2.10.3 报表中心
```报表生成器
[模板选择]
▢ 预制模板：
  日报（设备状态+工单统计）
  月报（能耗分析+故障趋势）
  ▼ 自定义字段：勾选数据维度

[导出设置]
▢ 格式选项：
  ◉ Excel ○ PDF ○ 图片
  ▢ 包含图表 ○ 仅数据
  ▢ 自动发送到邮箱
```

#### 2.10.4 接口管理
```第三方服务配置
[接口状态]
▢ 健康检查：
  微信支付 ✅ 正常（5秒前）
  短信平台 ⚠️ 延迟（1200ms） 
  视频服务 🔴 断开

[配置面板]
▢ 微信支付：
  API密钥：********
  回调地址：/api/wxpay
  [测试支付] 按钮（￥0.01）
```

#### 2.10.5 审计日志
```日志查看器
[高级筛选]
▼ 操作类型：56种 ▾
○ 成功 ○ 失败
▢ 时间范围：自定义

[日志详情]
| 时间         | 用户   | 操作               |
|--------------|--------|--------------------|
| 12-05 14:23 | 张三   | 修改压力阈值→80kPa |
| 12-05 14:25 | 系统   | 触发高温告警       |
```

### 2.11 消息通知
设计要点说明：
1. 分级提醒-三级紧急程度对应不同交互反馈
2. 渠道管控-多通道互补确保消息可达
3. 智能聚合-相同事件消息自动归并
4. 模板化-预设常用通知格式提升效率
5. 状态追踪-显示消息阅读状态和触达情况
6. 合规管理-静默时段遵守通信管理条例
7. 审计留痕-保留6个月消息发送记录
交互细节：
1. 紧急消息触发手机振动+全屏浮层
2. 长按消息卡片可快速标为已读/未读
3. 失败消息自动重试机制（最多3次）
4. 模板变量输入时显示数据字典
5. 消息日志支持导出为CSV格式
6. 夜间模式自动降低消息弹窗亮度
7. 敏感词过滤（如"故障"转"设备异常"）


#### 2.11.1 消息中心
```通知聚合界面
[消息分类]
▢ 标签导航：
  全部 ● 工单（12） ● 告警（3）
  缴费 ● 系统 ● 其他

[消息卡片]
▢ 紧急消息：
  🔴 紧急：1#泵故障停机（置顶）
  ⏰ 15分钟后巡检（黄色边框）
  ✅ 工单#R-1201已完成（绿色标记）

[批量操作]
▢ 底部工具栏：
  [全部已读] [删除] [筛选]
  ▼ 按紧急程度：紧急/重要/常规
```

#### 2.11.2 推送配置
```通知设置界面
[渠道管理]
▢ 接收方式：
  ◉ 小程序推送 ○ 短信 ○ 邮件
  ▢ 静默时段：22:00-08:00免打扰

[规则配置]
▢ 事件类型：
  ○ 设备告警（强提醒）
  ○ 工单更新（振动）
  ○ 缴费提醒（每日1次）
  ○ 系统公告（仅通知）
```

#### 2.11.3 消息记录
```发送日志界面
[状态筛选]
○ 成功 ○ 失败 ○ 待发送
▢ 搜索框：按内容/接收人查找

[日志详情]
| 时间         | 类型       | 接收人  | 状态     |
|--------------|------------|---------|----------|
| 12-05 14:23 | 故障告警   | 张三    | ✅ 已读   |
| 12-05 14:25 | 缴费提醒   | 李四    | ⚪ 未读   |
```

#### 2.11.4 模板管理
```消息模板库
[预制模板]
▢ 工单提醒：
  "【工单通知】您有新的#{type}工单待处理"
▢ 缴费预警：
  "【供暖费提醒】#{room}欠费#{amount}元"

[变量插入]
▼ 可用字段：$[设备编号]$ $[联系人]$
▢ 预览模式：手机模拟器实时预览

```

### 2.12 室温监测
设计要点说明：
1. 双模输入-手动上报与设备监测数据互补验证
2. 可信度设计-OCR置信度提示+温差对比分析
3. 舒适指引-用色温变化反映18-24℃舒适区间
4. 激励体系-用游戏化设计提升用户参与度
5. 异常联动-低温预警直连故障管理系统
6. 隐私保护-模糊处理用户上传照片背景
7. 数据纠偏-设备与人测温差超阈值时标黄提示
交互细节：
1. 温度输入时背景色随数值冷暖变化
2. 连续3天上报奖励额外积分
3. 照片上传自动模糊处理敏感信息
4. 折线图支持双指缩放查看细节
5. 兑换服务关联线下核销二维码
6. 设备离线超24小时自动生成巡检工单
7. 异常工单处理进度推送温度补偿方案
与系统联动：
1. 用户上报数据→故障管理系统（分析区域性低温）
2. 设备监测数据→换热站控制系统（调整供热参数）
3. 积分记录→用户画像系统（识别活跃用户）  
4. 低温预警→维修工单系统（生成优先级工单）

#### 2.12.1 手动上报
```上报界面设计
[入口设计]
▢ 首页悬浮按钮：🌡️ 报温度
▢ 个人中心快捷入口：温度卡片

[上报表单]
▢ 输入组：
  当前温度：__℃（带上下调节按钮）
  上传照片：📷 拍摄/🖼️ 相册
  定位信息：3号楼1201（可修改）

[智能识别]
▢ OCR结果预览：
  识别值：23.5℃（置信度92%）
  [确认采用] [重新拍摄]
```

#### 2.12.2 自动监测
```智能设备界面
[设备状态]
▢ 温控器卡片：
  🟢 在线（最后更新2分钟前）
  当前温度：22.8℃
  设定温度：24.0℃
  [立即同步] 按钮

[离线处理]
▢ 异常提示：
  🔴 设备离线（持续4小时）
  可能原因：电源断开/WiFi故障
  [重新连接] [报修]
```

#### 2.12.3 数据展示
```温度历史界面
[可视化图表]
▢ 折线图模式：
  24小时温度波动（18℃-26℃）
  标红超出舒适区间(18-24℃)时段

[对比分析]
▼ 对比维度：昨日/上周/去年同期
▢ 双线图表：
  用户上报温度 vs 设备监测温度
  温差超±1℃时显示黄色差异带
```

#### 2.12.4 异常处理
```温度预警
[自动工单]
▢ 异常弹窗：
  ⚠️ 检测到1201室持续低温（18℃）
  可能原因：管道堵塞/压力不足
  [立即报修] [暂不处理]

[用户反馈]
▢ 补充描述：
  体感选择：○ 冷 ○ 正常 ○ 热
  问题描述：多行文本输入框
```

#### 2.12.5 积分激励
```积分系统
[获取记录]
▢ 积分卡片：
  当前积分：350分（可兑换🧤）
  今日可获：50分（3/5次上报）

[兑换商城]
▢ 虚拟物品：
  50分=故障优先处理券
  200分=供暖周边优惠券
  500分=免费清洗服务
```

## 三、组件规范

### 3.1 通用组件

#### 3.1.1 按钮

| 类型     | 尺寸          | 样式                      |
| -------- | ------------- | ------------------------- |
| 主按钮   | 686rpx×80rpx | 背景#1890ff 圆角8rpx      |
| 次要按钮 | 320rpx×64rpx | 边框1rpx #1890ff 透明背景 |
| 警示按钮 | 686rpx×80rpx | 背景#faad14 圆角8rpx      |

#### 3.1.2 表单元素

```示例
<输入框>
标题文字：14rpx #333
边框：1rpx #ddd 圆角4rpx
内边距：20rpx 左右边距

<下拉选择>
右侧箭头图标 ▼
选项高度：96rpx
选中状态：背景#f0f9ff
```

### 3.2 数据可视化

#### 3.2.1 温度曲线

```配置
{
  xAxis: {type: 'time'},
  yAxis: {min: 0, max: 100},
  series: [{
    type: 'line',
    smooth: true,
    color: '#1890ff'
  }]
}
```

#### 3.2.2 设备状态图

```示例
[仪表盘]
外环颜色：根据状态变化
指针动画：缓动效果过渡
数值显示：居中大字模式
```

## 四、适配方案

### 4.1 响应式布局

| 屏幕宽度   | 布局策略          | 示例组件          |
| ---------- | ----------------- | ----------------- |
| <600rpx    | 单列流式布局      | 设备卡片100%宽度  |
| 600-900rpx | 两列网格布局      | 数据卡片50%宽度   |
| >900rpx    | 三列固定+侧边导航 | 详情页30%-70%分栏 |

### 4.2 字体适配

```scss
.title {
  font-size: 32rpx; /* 小屏 */
  @media (min-width: 600rpx) {
    font-size: 36rpx;
  }
}
```

## 五、设计交付物

1. **切图资源**

   - 图标：@2x/@3x PNG + SVG
   - 组件状态：正常/按下/禁用
2. **样式变量**

```css
:root {
  --primary-color: #1890ff;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #f5222d;
}
```

3. **动效参数**
   - 页面切换：300ms缓动
   - 按钮点击：缩放至90%
   - 数据刷新：渐显500ms 