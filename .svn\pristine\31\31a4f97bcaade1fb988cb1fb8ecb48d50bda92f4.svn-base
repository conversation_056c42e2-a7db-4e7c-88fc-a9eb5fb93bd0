package com.heating.service;

import org.springframework.data.domain.Page;

import com.heating.dto.device.DeviceCreateRequest;
import com.heating.dto.device.DeviceCreateResponse;
import com.heating.dto.device.DeviceDetailResponse;
import com.heating.dto.device.DeviceHeatUnitResponse;
import com.heating.dto.device.DeviceListRequest;
import com.heating.dto.device.DeviceListResponse;
import com.heating.dto.device.DevicePatrolItemResponse;
import com.heating.dto.device.DeviceStatsResponse;
import com.heating.dto.device.DeviceUpdateRequest;

import java.util.List;
import java.util.Map;

public interface DeviceService {
    /**
     * 获取设备列表
     * @param request 设备列表请求
     * @return 设备列表响应
     */
    Page<DeviceListResponse> getDeviceList(DeviceListRequest request);
    
    /**
     * 获取设备详情
     * @param deviceId 设备ID
     * @return 设备详情响应
     */
    DeviceDetailResponse getDeviceDetail(Long deviceId);

    /**
     * 创建设备
     * @param request 创建请求
     * @return 创建响应
     */
    DeviceCreateResponse createDevice(DeviceCreateRequest request);

    /**
     * 判断设备是否存在
     * @param deviceId 设备ID
     * @return 是否存在
     */
    boolean isDeviceExists(Long deviceId); 

    /**
     * 更新设备信息
     * @param deviceId 设备ID
     * @param request 更新请求
     * @return 更新结果
     */
    Map<String, Object> updateDevice(Long deviceId, DeviceUpdateRequest request);

    /**
     * 获取设备维护记录
     * @param deviceId 设备ID
     * @return 维护记录响应
     */
    Map<String, Object> getDeviceMaintenance(Long deviceId);

    /**
     * 获取设备统计数据
     * @return 设备统计信息
     */
    DeviceStatsResponse getDeviceStats();
    
    /**
     * 根据热用户ID获取设备列表
     * @param heatUnitId 热用户ID
     * @return 设备列表
     */
    List<DeviceHeatUnitResponse> getDevicesByHeatUnitId(Long heatUnitId);
    
    /**
     * 根据设备ID获取设备巡检项
     * @param deviceId 设备ID
     * @return 设备巡检项列表
     */
    List<DevicePatrolItemResponse> getDevicePatrolItems(Long deviceId);
} 