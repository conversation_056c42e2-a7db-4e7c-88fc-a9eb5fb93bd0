package com.heating.dto.attendance;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AttendanceStaffResponse {
    private int total; // 总数
    private List<StaffAttendance> list; // 员工考勤列表
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StaffAttendance {
        private Long userId; // 用户ID
        private String userName; // 用户名称
        private String department; // 部门
        private String position; // 职位
        private Double attendanceRate; // 出勤率
        private Integer lateCount; // 迟到次数
        private Integer earlyLeaveCount; // 早退次数
        private Integer absentCount; // 缺勤次数
    }
} 