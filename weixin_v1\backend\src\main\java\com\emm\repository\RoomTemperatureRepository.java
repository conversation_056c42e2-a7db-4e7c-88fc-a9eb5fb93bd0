package com.emm.repository;

import com.emm.model.RoomTemperature;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.data.domain.Pageable;

import java.sql.Date;
import java.time.LocalDateTime;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Repository
public interface RoomTemperatureRepository extends JpaRepository<RoomTemperature, Long> {

    @Query("SELECT AVG(r.indoorTemp) FROM RoomTemperature r WHERE DATE(r.reportTime) = CURRENT_DATE")
    Double findAverageTemperatureToday();

    @Query("SELECT new map(r.communityName as name, AVG(r.indoorTemp) as temperature, MAX(r.reportTime) as latest_time) " +
           "FROM RoomTemperature r " +
           "WHERE DATE(r.reportTime) = CURRENT_DATE " +
           "GROUP BY r.communityName " +
           "ORDER BY r.communityName")
    List<Map<String, Object>> findCommunityTemperatures();

    @Query("SELECT rt FROM RoomTemperature rt WHERE rt.reportTime >= :startTime ORDER BY rt.reportTime DESC")
    List<RoomTemperature> findRecentTemperatures(@Param("startTime") LocalDateTime startTime);

    @Query("SELECT rt FROM RoomTemperature rt WHERE rt.reportTime >= :startTime ORDER BY rt.reportTime DESC")
    List<RoomTemperature> findTopNRecentTemperatures(@Param("startTime") LocalDateTime startTime, Pageable pageable);
 
    @Query("SELECT rt FROM RoomTemperature rt WHERE rt.id = :id")
    Optional<RoomTemperature> findById(@Param("id") Long id); 

    @Query("SELECT rt FROM RoomTemperature rt WHERE rt.communityName = :communityName")
    List<RoomTemperature> findByCommunityName(@Param("communityName") String communityName);

    @Query("SELECT rt FROM RoomTemperature rt WHERE DATE(rt.reportTime) = :date")
    List<RoomTemperature> findByDate(@Param("date") Date date);

    @Query("SELECT rt FROM RoomTemperature rt WHERE rt.communityName = :communityName AND DATE(rt.reportTime) = :date")
    List<RoomTemperature> findByCommunityNameAndDate(@Param("communityName") String communityName, @Param("date") Date date);

    @Query("SELECT rt FROM RoomTemperature rt")
    List<RoomTemperature> findAll();

}