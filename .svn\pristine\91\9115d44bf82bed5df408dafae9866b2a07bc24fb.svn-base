package com.heating.dto.attendance;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AttendanceRulesResponse {
    private String clockInTime; // 上班打卡时间
    private String clockOutTime; // 下班打卡时间
    private Integer allowedDistance; // 允许的打卡距离(米)
    private Integer lateThreshold; // 迟到阈值(分钟)
    private Integer earlyLeaveThreshold; // 早退阈值(分钟)
} 