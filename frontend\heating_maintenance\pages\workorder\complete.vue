<template>
	<view class="page-wrapper">
		<view class="complete-container">
			<view class="form-card">
				<view class="form-title">完成工单</view>
				
				<view class="form-item">
					<view class="form-label required">维修内容</view>
					<textarea class="form-textarea" v-model="formData.repairContent" placeholder="请输入维修内容和过程描述"></textarea>
				</view>
				
				<view class="form-item">
					<view class="form-label required">维修结果</view>
					<textarea class="form-textarea" v-model="formData.repairResult" placeholder="请输入维修结果和效果"></textarea>
				</view>
				
				<view class="form-item">
					<view class="form-label">维修耗材及数量</view>
					<view class="materials-container">
						<view v-for="(item, index) in repairMaterialsList" :key="index" class="material-item">
							<view class="material-row">
								<view class="material-selector" @click="showMaterialSelectorPopup(index)">
									<view class="form-input material-input">
										<text>{{ item.name || '请选择耗材' }}</text>
										<text class="arrow-down">▼</text>
									</view>
								</view>
								<input type="number" class="quantity-input" v-model="item.quantity" placeholder="数量" />
								<text class="delete-btn" @click="removeMaterial(index)">×</text>
							</view>
						</view>
						<view class="add-material-btn" @click="addMaterial">
							<text class="iconfont icon-add"></text>
							<text>添加耗材</text>
						</view>
					</view>
				</view>
				
				<view class="form-item">
					<view class="form-label required">维修时间</view>
					<picker mode="multiSelector" @change="onDateTimeChange" @columnchange="onColumnChange" :value="dateTimeIndex" :range="dateTimeArray">
						<view class="picker-view">
							<text>{{ formData.repairDate }} {{ formData.repairTime }}</text>
							<text class="iconfont icon-calendar"></text>
						</view>
					</picker>
				</view>
				
				<view class="form-item">
					<view class="form-label">附件上传</view>
					<view class="upload-area">
						<!-- 图片上传部分 -->
						<view class="form-header">
							<text class="form-subtitle">上传维修现场图片（最多6张）</text>
						</view>
						<view class="image-grid">
							<view 
								v-for="(image, index) in imageList" 
								:key="index" 
								class="image-item"
							>
								<image :src="image" mode="aspectFill" @click="previewImage(index)"></image>
								<text class="delete-icon" @click="deleteImage(index)">×</text>
							</view>
							
							<view class="upload-item" v-if="imageList.length < 6" @click="chooseImage">
								<text class="iconfont icon-camera"></text>
								<text>上传图片</text>
							</view>
						</view>
						
						<!-- 视频上传部分 -->
						<view class="form-header" style="margin-top: 30rpx;">
							<text class="form-subtitle">上传维修现场视频（最多1个）</text>
						</view>
						<view class="upload-area">
							<view class="video-container" v-if="videoPath">
								<video 
									:src="getVideoUrl()" 
									controls
									object-fit="fill"
									initial-time="0"
									show-fullscreen-btn="true"
									show-play-btn="true"
									show-center-play-btn="true"
									enable-progress-gesture="true"
									auto-pause-if-navigate="true"
									auto-pause-if-open-native="true"
									codec="h264"
									@error="onVideoError"
								></video>
								<text class="delete-icon" @click="deleteVideo">×</text>
							</view>
							
							<view class="upload-item" v-if="!videoPath" @click="chooseVideo">
								<text class="iconfont icon-video"></text>
								<text>上传视频</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<view class="action-buttons">
				<button class="btn-cancel" @click="cancelComplete">取消</button>
				<button class="btn-submit" @click="submitComplete">提交</button>
			</view>
		</view>
		
		<!-- 材料选择弹窗 -->
		<uni-popup ref="materialSelector" type="bottom">
			<view class="selector material-selector-popup">
				<view class="selector-header">
					<text class="selector-title">选择耗材</text>
					<view class="header-actions">
						<text class="confirm-button-header" @click="confirmMaterialSelection">确定</text>
						<text class="close-button" @click="hideMaterialSelector">关闭</text>
					</view>
				</view>
				
				<view class="selector-content">
					<view class="template-search">
						<input 
							type="text" 
							v-model="materialSearchKeyword" 
							placeholder="搜索耗材" 
							confirm-type="search"
							@input="handleMaterialSearch"
							class="search-input"
						/>
						<text 
							class="search-clear" 
							v-if="materialSearchKeyword" 
							@click="clearMaterialSearch"
						>×</text>
						<view class="search-btn" @click="handleMaterialSearch">搜索</view>
					</view>
					
					<view class="material-list">
						<view 
							class="material-item-popup" 
							v-for="material in filteredMaterials" 
							:key="material.id"
							@click="selectMaterial(material)"
						>
							<view class="material-info">
								<text class="material-name">{{ material.name }}</text>
								<text class="material-type">{{ material.material_type }}</text>
							</view>
							<view class="checkbox" :class="{ checked: tempSelectedMaterial && tempSelectedMaterial.id === material.id }">
								<text class="iconfont icon-check" v-if="tempSelectedMaterial && tempSelectedMaterial.id === material.id"></text>
							</view>
						</view>
					</view>
					
					<view class="empty-tip" v-if="filteredMaterials.length === 0">
						<text>没有找到相关耗材</text>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import { workOrderApi, materialsApi } from '../../utils/api';
	import uploadUtils from '@/utils/upload.js';
	
	export default {
		data() {
			return {
				orderId: null,
				formData: {
					repairContent: '',
					repairResult: '',
					repairDate: '',
					repairTime: '',
					repairDateTime: '',
					attachment: []
				},
				repairMaterialsList: [
					{ name: '', quantity: '' }
				],
				rules: {
					repairContent: [
						{ required: true, message: '请输入维修内容' }
					],
					repairResult: [
						{ required: true, message: '请输入维修结果' }
					],
					repairDate: [
						{ required: true, message: '请选择维修日期' }
					],
					repairTime: [
						{ required: true, message: '请选择维修时间' }
					]
				},
				dateTimeArray: [[], [], [], [], [], []],
				dateTimeIndex: [0, 0, 0, 0, 0, 0],
				imageList: [], // 本地临时路径
				serverImageList: [], // 服务器路径
				videoPath: '', // 本地临时路径
				serverVideoPath: '', // 服务器路径
				isUploading: false,
				// 新增材料相关数据
				materialOptions: [], // 材料选项列表
				materialSearchKeyword: '', // 材料搜索关键词
				filteredMaterials: [], // 过滤后的材料列表
				currentMaterialIndex: -1, // 当前正在编辑的材料索引
				tempSelectedMaterial: null, // 临时选中的材料
				isPopupOpen: false // 弹窗状态
			}
		},
		onLoad(options) {
			if (options.id) {
				this.orderId = options.id;
			} else {
				this.showError('缺少工单ID');
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			}
			
			// 初始化日期时间选择器
			this.initDateTimePicker();
			
			// 默认设置当前时间
			const now = new Date();
			const year = now.getFullYear();
			const month = String(now.getMonth() + 1).padStart(2, '0');
			const day = String(now.getDate()).padStart(2, '0');
			const hours = String(now.getHours()).padStart(2, '0');
			const minutes = String(now.getMinutes()).padStart(2, '0');
			
			this.formData.repairDate = `${year}-${month}-${day}`;
			this.formData.repairTime = `${hours}:${minutes}:00`;
			this.updateRepairDateTime();
			
			// 加载材料列表
			this.loadMaterialsList();
		},
		methods: {
			// 添加维修耗材
			addMaterial() {
				this.repairMaterialsList.push({ name: '', quantity: '' });
			},
			
			// 删除维修耗材
			removeMaterial(index) {
				if (this.repairMaterialsList.length > 1) {
					this.repairMaterialsList.splice(index, 1);
				} else {
					// 至少保留一个输入框
					this.repairMaterialsList = [{ name: '', quantity: '' }];
				}
			},
			
			// 获取耗材数据（JSON格式）
			getRepairMaterialsQuantity() {
				const result = {};
				this.repairMaterialsList.forEach(item => {
					if (item.name && item.name.trim()) {
						result[item.name.trim()] = item.quantity || 0;
					}
				});
				return result;
			},
			
			// 加载材料列表
			loadMaterialsList() {
				uni.showLoading({
					title: '加载材料列表...'
				});
				
				materialsApi.getMaterialsList()
					.then(res => {
						uni.hideLoading();
						if (res.code === 200 && res.data) {
							this.materialOptions = res.data;
							this.filteredMaterials = [...res.data];
						} else {
							this.showError('获取材料列表失败');
						}
					})
					.catch(err => {
						uni.hideLoading();
						console.error('获取材料列表失败:', err);
						this.showError('获取材料列表失败，请重试');
					});
			},
			
			// 显示材料选择器
			showMaterialSelectorPopup(index) {
				this.currentMaterialIndex = index;
				this.tempSelectedMaterial = null;
				
				// 如果当前行已经选择了材料，则预选中
				const currentMaterial = this.repairMaterialsList[index];
				if (currentMaterial && currentMaterial.name) {
					const foundMaterial = this.materialOptions.find(m => m.name === currentMaterial.name);
					if (foundMaterial) {
						this.tempSelectedMaterial = foundMaterial;
					}
				}
				
				// 重置搜索
				this.materialSearchKeyword = '';
				this.filteredMaterials = [...this.materialOptions];
				
				this.isPopupOpen = true;
				this.$refs.materialSelector.open();
			},
			
			// 隐藏材料选择器
			hideMaterialSelector() {
				this.isPopupOpen = false;
				this.$refs.materialSelector.close();
			},
			
			// 选择材料
			selectMaterial(material) {
				this.tempSelectedMaterial = material;
			},
			
			// 确认材料选择
			confirmMaterialSelection() {
				if (this.tempSelectedMaterial && this.currentMaterialIndex !== -1) {
					this.repairMaterialsList[this.currentMaterialIndex].name = this.tempSelectedMaterial.name;
				}
				
				this.hideMaterialSelector();
			},
			
			// 搜索材料
			handleMaterialSearch() {
				if (!this.materialSearchKeyword || this.materialSearchKeyword.trim() === '') {
					this.filteredMaterials = [...this.materialOptions];
					return;
				}
				
				const keyword = this.materialSearchKeyword.toLowerCase().trim();
				this.filteredMaterials = this.materialOptions.filter(material => 
					material.name.toLowerCase().includes(keyword) || 
					(material.material_type && material.material_type.toLowerCase().includes(keyword))
				);
			},
			
			// 清空材料搜索
			clearMaterialSearch() {
				this.materialSearchKeyword = '';
				this.filteredMaterials = [...this.materialOptions];
			},
			
			// 初始化日期时间选择器
			initDateTimePicker() {
				// 生成年份数组，前后各5年
				const date = new Date();
				const currentYear = date.getFullYear();
				const years = [];
				for (let i = currentYear - 2; i <= currentYear + 2; i++) {
					years.push(i + '年');
				}
				
				// 生成月份数组
				const months = [];
				for (let i = 1; i <= 12; i++) {
					months.push(i + '月');
				}
				
				// 生成日期数组
				const days = [];
				for (let i = 1; i <= 31; i++) {
					days.push(i + '日');
				}
				
				// 生成小时数组
				const hours = [];
				for (let i = 0; i <= 23; i++) {
					hours.push(i + '时');
				}
				
				// 生成分钟数组
				const minutes = [];
				for (let i = 0; i <= 59; i++) {
					minutes.push(i + '分');
				}
				
				// 生成秒数组
				const seconds = [];
				for (let i = 0; i <= 59; i++) {
					seconds.push(i + '秒');
				}
				
				// 设置到data中
				this.dateTimeArray = [years, months, days, hours, minutes, seconds];
				
				// 设置默认选中值
				const yearIndex = 2; // 默认选中当前年份
				const monthIndex = date.getMonth(); // 1月对应索引0
				const dayIndex = date.getDate() - 1; // 1日对应索引0
				const hourIndex = date.getHours();
				const minuteIndex = date.getMinutes();
				const secondIndex = date.getSeconds();
				
				this.dateTimeIndex = [yearIndex, monthIndex, dayIndex, hourIndex, minuteIndex, secondIndex];
			},
			
			// 处理日期时间选择器变化
			onDateTimeChange(e) {
				const values = e.detail.value;
				this.dateTimeIndex = values;
				
				// 更新日期和时间
				const year = parseInt(this.dateTimeArray[0][values[0]]);
				const month = String(parseInt(this.dateTimeArray[1][values[1]])).padStart(2, '0');
				const day = String(parseInt(this.dateTimeArray[2][values[2]])).padStart(2, '0');
				const hour = String(parseInt(this.dateTimeArray[3][values[3]])).padStart(2, '0');
				const minute = String(parseInt(this.dateTimeArray[4][values[4]])).padStart(2, '0');
				const second = String(parseInt(this.dateTimeArray[5][values[5]])).padStart(2, '0');
				
				this.formData.repairDate = `${year}-${month}-${day}`;
				this.formData.repairTime = `${hour}:${minute}:${second}`;
				this.updateRepairDateTime();
			},
			
			// 处理日期时间选择器列变化
			onColumnChange(e) {
				let column = e.detail.column;
				let value = e.detail.value;
				
				// 当年份或月份变化时，需要调整天数
				if (column === 0 || column === 1) {
					const year = parseInt(this.dateTimeArray[0][this.dateTimeIndex[0]]);
					const month = parseInt(this.dateTimeArray[1][value === undefined ? this.dateTimeIndex[1] : value]);
					
					// 获取当月的天数
					const daysInMonth = new Date(year, month, 0).getDate();
					
					// 更新天数数组
					const days = [];
					for (let i = 1; i <= daysInMonth; i++) {
						days.push(i + '日');
					}
					
					// 更新天数数组
					this.dateTimeArray[2] = days;
					
					// 如果当前选中的日期超过了当月的最大天数，则调整为当月的最后一天
					if (this.dateTimeIndex[2] >= daysInMonth) {
						this.dateTimeIndex[2] = daysInMonth - 1;
					}
				}
			},
			
			// 更新合并后的日期时间
			updateRepairDateTime() {
				if (this.formData.repairDate && this.formData.repairTime) {
					const dateTimeStr = `${this.formData.repairDate} ${this.formData.repairTime}`;
					this.formData.repairDateTime = dateTimeStr;
				}
			},
			
			// 获取一年前的日期
			getOneYearAgo() {
				const date = new Date();
				date.setFullYear(date.getFullYear() - 1);
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				return `${year}-${month}-${day}`;
			},
			
			// 获取当前日期
			getCurrentDate() {
				const date = new Date();
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				return `${year}-${month}-${day}`;
			},
			
			// 选择图片
			chooseImage() {
				uni.chooseImage({
					count: 6 - this.imageList.length,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						// 显示上传中提示
						uni.showLoading({
							title: '上传中...',
							mask: true
						});
						
						// 检查token是否存在，不存在则提示用户登录
						const token = uni.getStorageSync('token');
						if (!token) {
							uni.hideLoading();
							uni.showToast({
								title: '请先登录',
								icon: 'none'
							});
							return;
						}
						
						// 逐个上传图片
						const uploadPromises = res.tempFilePaths.map(path => {
							return uploadUtils.uploadImage(path)
								.then(serverPath => {
									// 保存本地路径和服务器路径
									this.imageList.push(path);
									this.serverImageList.push(serverPath);
									return serverPath;
								});
						});
						
						// 等待所有图片上传完成
						Promise.all(uploadPromises)
							.then(() => {
								uni.hideLoading();
								uni.showToast({
									title: '上传成功',
									icon: 'success'
								});
							})
							.catch(err => {
								uni.hideLoading();
								uni.showToast({
									title: err.message || '上传失败',
									icon: 'none'
								});
							});
					}
				});
			},
			
			// 预览图片
			previewImage(index) {
				// 将本地路径转换为可访问的URL
				const previewUrls = this.imageList.map((path, i) => {
					// 如果有对应的服务器路径，使用服务器路径构建完整URL
					if (this.serverImageList[i]) {
						return uploadUtils.getFileUrl(this.serverImageList[i]);
					}
					return path;
				});
				
				uni.previewImage({
					urls: previewUrls,
					current: previewUrls[index]
				});
			},
			
			// 删除图片
			deleteImage(index) {
				this.imageList.splice(index, 1);
				this.serverImageList.splice(index, 1);
			},
			
			// 选择视频
			chooseVideo() {
				uni.chooseVideo({
					sourceType: ['album', 'camera'],
					maxDuration: 60,
					camera: 'back',
					success: (res) => {
						// 显示上传中提示
						uni.showLoading({
							title: '上传中...',
							mask: true
						});
						
						// 检查token是否存在，不存在则提示用户登录
						const token = uni.getStorageSync('token');
						if (!token) {
							uni.hideLoading();
							uni.showToast({
								title: '请先登录',
								icon: 'none'
							});
							return;
						}
						
						// 上传视频到服务器
						uploadUtils.uploadVideo(res.tempFilePath)
							.then(serverPath => {
								// 保存本地路径和服务器路径
								this.videoPath = res.tempFilePath;
								this.serverVideoPath = serverPath;
								
								uni.hideLoading();
								uni.showToast({
									title: '上传成功',
									icon: 'success'
								});
							})
							.catch(err => {
								uni.hideLoading();
								uni.showToast({
									title: err.message || '上传失败',
									icon: 'none'
								});
							});
					}
				});
			},
			
			// 获取视频URL
			getVideoUrl() {
				// 特殊处理以@开头的URL
				if (this.serverVideoPath && this.serverVideoPath.startsWith('@http')) {
					const cleanUrl = this.serverVideoPath.substring(1); // 去除@符号
					console.log('视频路径以@http开头，清理后:', cleanUrl);
					return cleanUrl;
				}
				
				// 如果是服务器路径，使用工具方法获取完整URL
				if (this.serverVideoPath) {
					// 确保它真的是一个http URL
					if (this.serverVideoPath.startsWith('http')) {
						return this.serverVideoPath;
					}
					const url = uploadUtils.getFileUrl(this.serverVideoPath);
					console.log('处理后的视频URL:', url);
					return url;
				}
				
				// 否则返回本地临时路径
				console.log('使用本地视频路径:', this.videoPath);
				return this.videoPath;
			},
			
			// 视频加载错误处理
			onVideoError(e) {
				console.error('视频加载失败:', e.detail);
				console.error('视频路径:', this.videoPath);
				console.error('服务器视频路径:', this.serverVideoPath);
				console.error('处理后URL:', this.getVideoUrl());
				
				uni.showToast({
					title: '视频加载失败，请尝试其他方式查看',
					icon: 'none',
					duration: 2000
				});
			},
			
			// 删除视频
			deleteVideo() {
				this.videoPath = '';
				this.serverVideoPath = '';
			},
			
			// 取消完成
			cancelComplete() {
				uni.navigateBack();
			},
			
			// 验证表单
			validateForm() {
				let isValid = true;
				
				if (!this.formData.repairDate) {
					this.showError('请选择维修日期');
					return false;
				}
				
				if (!this.formData.repairTime) {
					this.showError('请选择维修时间');
					return false;
				}
				
				for (const key in this.rules) {
					if (key === 'repairDate' || key === 'repairTime') continue;
					
					const value = this.formData[key];
					const rules = this.rules[key];
					
					for (const rule of rules) {
						if (rule.required && !value) {
							this.showError(rule.message);
							isValid = false;
							break;
						}
					}
					
					if (!isValid) break;
				}
				
				return isValid;
			},
			
			// 上传附件
			uploadAttachments() {
				return new Promise((resolve) => {
					const attachments = [];
					
					// 添加已上传的图片
					this.serverImageList.forEach(serverPath => {
						attachments.push({
							file_type: 'image',
							file_path: serverPath
						});
					});
					
					// 添加已上传的视频
					if (this.serverVideoPath) {
						attachments.push({
							file_type: 'video',
							file_path: this.serverVideoPath
						});
					}
					
					// 直接返回已上传的附件列表
					resolve(attachments);
				});
			},
			
			// 提交表单
			submitComplete() {
				if (!this.validateForm()) return;
				
				uni.showModal({
					title: '确认完成',
					content: '是否确认完成工单？完成后将无法修改',
					success: (res) => {
						if (res.confirm) {
							uni.showLoading({
								title: '提交中...'
							});
							
							// 上传附件
							this.uploadAttachments().then(attachments => {
								this.submitOrderComplete(attachments);
							}).catch(err => {
								uni.hideLoading();
								this.showError('附件处理失败: ' + err);
							});
						}
					}
				});
			},
			
			// 提交工单完成
			submitOrderComplete(attachments) {
				// 处理耗材数量
				const repairMaterialsQuantity = this.getRepairMaterialsQuantity();
				
				const params = {
					order_id: this.orderId,
					repair_user_id: this.getCurrentUserId(),
					order_status: '已完成',
					repair_content: this.formData.repairContent,
					repair_result: this.formData.repairResult,
					repair_materials_quantity: repairMaterialsQuantity,
					repair_time: this.formData.repairDateTime,
					attachment: attachments
				};
				
				console.log('提交参数:', params);
				
				workOrderApi.completeOrder(params)
					.then(res => {
						uni.hideLoading();
						if (res.code === 200) {
							this.showSuccess('工单已完成');
							setTimeout(() => {
								uni.navigateBack({
									delta: 1
								});
							}, 1500);
						} else {
							this.showError(res.message || '提交失败');
						}
					})
					.catch(err => {
						uni.hideLoading();
						this.showError('网络异常，请稍后重试');
						console.error('完成工单失败:', err);
					});
			},
			
			// 获取当前用户ID
			getCurrentUserId() {
				const userId = uni.getStorageSync('userId');
				return userId || 1;
			},
			
			// 显示成功提示
			showSuccess(message) {
				uni.showToast({
					title: message,
					icon: 'success'
				});
			},
			
			// 显示错误提示
			showError(message) {
				uni.showToast({
					title: message,
					icon: 'none'
				});
			}
		}
	}
</script>

<style lang="scss">
	.complete-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding: 20rpx;
	}
	
	.form-card {
		background-color: #fff;
		border-radius: 12rpx;
		margin-bottom: 20rpx;
		padding: 30rpx;
		
		.form-title {
			font-size: 32rpx;
			font-weight: bold;
			color: $uni-text-color;
			margin-bottom: 30rpx;
			padding-bottom: 20rpx;
			border-bottom: 1rpx solid #eee;
		}
		
		.form-item {
			margin-bottom: 30rpx;
			
			.form-label {
				font-size: 28rpx;
				color: $uni-text-color;
				margin-bottom: 16rpx;
				
				&.required::before {
					content: '*';
					color: #f5222d;
					margin-right: 6rpx;
				}
			}
			
			.form-textarea {
				width: 100%;
				height: 200rpx;
				padding: 20rpx;
				background-color: #f5f5f5;
				border-radius: 8rpx;
				font-size: 28rpx;
				color: $uni-text-color;
				box-sizing: border-box;
			}
			
			.picker-view {
				width: 100%;
				height: 80rpx;
				padding: 0 20rpx;
				background-color: #f5f5f5;
				border-radius: 8rpx;
				font-size: 28rpx;
				color: $uni-text-color;
				box-sizing: border-box;
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-bottom: 10rpx;
				
				.iconfont {
					font-size: 32rpx;
					color: #999;
				}
			}
			
			.upload-area {
				.form-header {
					margin-bottom: 20rpx;
					
					.form-subtitle {
						font-size: 24rpx;
						color: $uni-text-color-grey;
					}
				}
				
				.image-grid {
					display: flex;
					flex-wrap: wrap;
					
					.image-item, .upload-item {
						width: 200rpx;
						height: 200rpx;
						margin-right: 20rpx;
						margin-bottom: 20rpx;
						position: relative;
					}
					
					.image-item {
						image {
							width: 100%;
							height: 100%;
							border-radius: 8rpx;
						}
						
						.delete-icon {
							position: absolute;
							top: -16rpx;
							right: -16rpx;
							width: 40rpx;
							height: 40rpx;
							background-color: rgba(0, 0, 0, 0.5);
							color: #fff;
							border-radius: 50%;
							display: flex;
							justify-content: center;
							align-items: center;
							font-size: 32rpx;
						}
					}
					
					.upload-item {
						border: 2rpx dashed #ddd;
						border-radius: 8rpx;
						display: flex;
						flex-direction: column;
						justify-content: center;
						align-items: center;
						
						.iconfont {
							font-size: 60rpx;
							color: $uni-text-color-grey;
							margin-bottom: 16rpx;
						}
						
						text {
							font-size: 24rpx;
							color: $uni-text-color-grey;
						}
					}
				}
				
				.video-container {
					width: 100%;
					height: 400rpx;
					position: relative;
					
					video {
						width: 100%;
						height: 100%;
						border-radius: 8rpx;
					}
					
					.delete-icon {
						position: absolute;
						top: 16rpx;
						right: 16rpx;
						width: 40rpx;
						height: 40rpx;
						background-color: rgba(0, 0, 0, 0.5);
						color: #fff;
						border-radius: 50%;
						display: flex;
						justify-content: center;
						align-items: center;
						font-size: 32rpx;
					}
				}
			}
		}
	}
	
	.action-buttons {
		display: flex;
		padding: 20rpx 0;
		
		button {
			flex: 1;
			height: 80rpx;
			line-height: 80rpx;
			text-align: center;
			border-radius: 40rpx;
			font-size: 28rpx;
			margin: 0 10rpx;
			
			&::after {
				border: none;
			}
		}
		
		.btn-cancel {
			background-color: #f5f5f5;
			color: $uni-text-color;
			border: 1px solid #ddd;
		}
		
		.btn-submit {
			background-color: $uni-color-primary;
			color: #fff;
		}
	}
	
	// 耗材相关样式
	.materials-container {
		.material-item {
			margin-bottom: 15rpx;
			
			.material-row {
				display: flex;
				align-items: center;
				
				.material-selector {
					flex: 2;
					margin-right: 15rpx;
					
					.material-input {
						height: 70rpx;
						background-color: #f5f5f5;
						border-radius: 8rpx;
						padding: 0 20rpx;
						font-size: 28rpx;
						display: flex;
						justify-content: space-between;
						align-items: center;
						
						.arrow-down {
							font-size: 24rpx;
							color: #999;
						}
					}
				}
				
				.quantity-input {
					flex: 1;
					height: 70rpx;
					background-color: #f5f5f5;
					border-radius: 8rpx;
					padding: 0 20rpx;
					font-size: 28rpx;
				}
				
				.delete-btn {
					width: 60rpx;
					height: 60rpx;
					line-height: 60rpx;
					text-align: center;
					font-size: 40rpx;
					color: #999;
				}
			}
		}
		
		.add-material-btn {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 70rpx;
			background-color: #f5f5f5;
			border-radius: 8rpx;
			color: $uni-color-primary;
			font-size: 28rpx;
			border: 1px dashed #ccc;
			
			.iconfont {
				margin-right: 10rpx;
			}
		}
	}
	
	// 材料选择器弹窗样式
	.material-selector-popup {
		background-color: #fff;
		border-radius: 20rpx 20rpx 0 0;
		max-height: 80vh;
		height: 60vh;
		display: flex;
		flex-direction: column;
		z-index: 100;
		
		.selector-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 30rpx;
			border-bottom: 1rpx solid #e5e5e5;
			
			.selector-title {
				font-size: 32rpx;
				font-weight: bold;
				color: $uni-text-color;
			}
			
			.header-actions {
				display: flex;
				align-items: center;
			}
			
			.close-button {
				font-size: 28rpx;
				color: $uni-text-color-grey;
			}
			
			.confirm-button-header {
				font-size: 28rpx;
				color: $uni-color-primary;
				font-weight: 500;
				margin-right: 20rpx;
			}
		}
		
		.selector-content {
			flex: 1;
			overflow-y: auto;
			padding: 0 30rpx;
			-webkit-overflow-scrolling: touch;
			
			.template-search {
				display: flex;
				align-items: center;
				padding: 20rpx 0;
				border-bottom: 1rpx solid #f0f0f0;
				margin-bottom: 20rpx;
				position: relative;
				
				.search-input {
					flex: 1;
					height: 70rpx;
					border-radius: 35rpx;
					background: #f5f5f5;
					font-size: 28rpx;
					padding: 0 70rpx 0 30rpx;
					color: $uni-text-color;
				}
				
				.search-clear {
					position: absolute;
					right: 130rpx;
					top: 50%;
					transform: translateY(-50%);
					width: 40rpx;
					height: 40rpx;
					line-height: 40rpx;
					text-align: center;
					border-radius: 50%;
					background: #ccc;
					color: #fff;
					font-size: 24rpx;
				}
				
				.search-btn {
					padding: 0 30rpx;
					font-size: 28rpx;
					color: $uni-color-primary;
					height: 70rpx;
					line-height: 70rpx;
				}
			}
			
			.material-list {
				.material-item-popup {
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding: 25rpx 0;
					border-bottom: 1rpx solid #f0f0f0;
					
					&:last-child {
						border-bottom: none;
					}
					
					.material-info {
						flex: 1;
						
						.material-name {
							font-size: 28rpx;
							color: $uni-text-color;
							margin-bottom: 6rpx;
							display: block;
						}
						
						.material-type {
							font-size: 24rpx;
							color: $uni-text-color-grey;
						}
					}
					
					.checkbox {
						width: 40rpx;
						height: 40rpx;
						border: 1rpx solid #e5e5e5;
						border-radius: 50%;
						display: flex;
						align-items: center;
						justify-content: center;
						
						&.checked {
							background-color: $uni-color-primary;
							border-color: $uni-color-primary;
							
							.iconfont {
								color: #fff;
								font-size: 24rpx;
							}
						}
					}
				}
			}
			
			.empty-tip {
				text-align: center;
				padding: 40rpx 0;
				color: $uni-text-color-grey;
				font-size: 28rpx;
			}
		}
	}
	
	// 图标样式
	.iconfont {
		font-size: 16px;
		font-style: normal;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
	}
	
	.icon-add:before {
		content: "+";
	}
	
	.icon-check:before {
		content: "✓";
	}
</style> 