<template>
	<view class="container">
		<view class="title">消息服务测试页面</view>
		<view class="status">当前服务状态：<text :class="{ 'running': isServiceRunning, 'stopped': !isServiceRunning }">{{ serviceStatusText }}</text></view>
		<button type="primary" @click="startMsgService" :disabled="isServiceRunning">启动消息服务</button>
		<button type="warn" @click="stopMsgService" :disabled="!isServiceRunning">停止消息服务</button>
		<view class="tips">
			<text>请打开浏览器控制台查看服务运行日志和接口调用情况。</text>
		</view>
	</view>
</template>

<script>
	// 引入消息服务
	import { messageService } from '../../utils/messageService.js';

	export default {
		data() {
			return {
				// 用于在模板中显示服务状态
				isServiceRunning: false
			};
		},
		computed: {
			// 计算属性，返回更友好的状态文本
			serviceStatusText() {
				return this.isServiceRunning ? '运行中' : '已停止';
			}
		},
		onLoad() {
			// 页面加载时，获取当前服务的实际运行状态
			this.isServiceRunning = messageService.getStatus();
		},
		methods: {
			// 启动服务按钮点击事件
			startMsgService() {
				console.log("测试页面：尝试启动消息服务...");
				messageService.start();
				// 更新页面显示状态
				this.isServiceRunning = messageService.getStatus();
			},
			// 停止服务按钮点击事件
			stopMsgService() {
				console.log("测试页面：尝试停止消息服务...");
				messageService.stop();
				// 更新页面显示状态
				this.isServiceRunning = messageService.getStatus();
			}
		}
	}
</script>

<style scoped>
	.container {
		padding: 20px;
		text-align: center;
	}

	.title {
		font-size: 20px;
		font-weight: bold;
		margin-bottom: 20px;
	}

	.status {
		margin-bottom: 20px;
		font-size: 16px;
	}

	.status .running {
		color: #18bc37; /* 绿色 */
		font-weight: bold;
	}

	.status .stopped {
		color: #e43d33; /* 红色 */
		font-weight: bold;
	}

	button {
		margin-top: 10px;
	}

	.tips {
		margin-top: 30px;
		font-size: 14px;
		color: #888;
	}
</style>
