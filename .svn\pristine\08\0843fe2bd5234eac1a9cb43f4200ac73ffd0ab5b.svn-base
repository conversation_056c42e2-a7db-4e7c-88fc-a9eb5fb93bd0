package com.heating.dto.alarm;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 告警消息响应DTO
 * 用于返回告警记录列表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlarmMessageResponse {
    
    /**
     * 告警记录ID
     */
    private Long id;

    /**
     * 热用户Id
     */
    private Long heatUnitId; 

    /**
     * 热用户名称
     */ 
    private String heatUnitName; 
    
    /**
     * 项目管理员ID列表
     */
    private List<Long> managerIds;
    
    /**
     * 告警描述
     */
    private String alarmDesc;
    
    /**
     * 告警时间
     */
    private String alarmDt;
} 