<template>
  <view class="station-control"> 

    <!-- 标签切换栏 -->
    <view class="tab-section">
      <view class="tab-bar">
        <view 
          class="tab-item" 
          :class="{ active: activeTab === 'control' }"
          @tap="activeTab = 'control'"
        >
          控制视图
        </view>
        <view 
          class="tab-item" 
          :class="{ active: activeTab === 'process' }"
          @tap="activeTab = 'process'"
        >
          工艺视图
        </view>
      </view>
    </view>
    
    <!-- 站点选择区域 -->
    <view class="station-selector" @tap="showStationSelect">
      <view class="station-label">换热站：</view>
      <view class="station-value-wrapper">
        <text class="station-value-text" :class="{'placeholder': !selectedStation}">
          {{ selectedStation ? selectedStation.name : '请选择换热站' }}
        </text>
        <text class="station-arrow">></text>
      </view>
    </view>
    
    <!-- 站点内容区域 -->
    <view class="station-content" v-if="selectedStation">
      <!-- 根据当前tab显示不同内容 -->
      <view v-if="activeTab === 'control'" class="control-view">
        <view class="card basic-info">
          <view class="card-title">基本信息</view>
          <view class="info-row">
            <text class="label">站点名称：</text>
            <text class="value">{{ selectedStation.name }}</text>
          </view>
          <view class="info-row">
            <text class="label">站点地址：</text>
            <text class="value">{{ selectedStation.address || selectedStation.heat_unit_name }}</text>
          </view>
          <view class="info-row">
            <text class="label">运行状态：</text>
            <text class="value" :class="selectedStation.status">
              {{ selectedStation.status === 'online' ? '在线' : selectedStation.status === 'fault' ? '故障' : '离线' }}
            </text>
          </view>
        </view>
        
        <!-- 实时数据卡片 -->
        <view class="card real-time-data" v-if="stationData">
          <view class="card-title">实时数据</view>
          <view class="data-grid">
            <view class="data-item">
              <text class="data-label">一次供水温度</text>
              <text class="data-value">{{ stationData.realtime_data?.primary_system?.supply_temp || '-' }}℃</text>
            </view>
            <view class="data-item">
              <text class="data-label">一次回水温度</text>
              <text class="data-value">{{ stationData.realtime_data?.primary_system?.return_temp || '-' }}℃</text>
            </view>
            <view class="data-item">
              <text class="data-label">一次供水压力</text>
              <text class="data-value">{{ stationData.realtime_data?.primary_system?.supply_pressure || '-' }}MPa</text>
            </view>
            <view class="data-item">
              <text class="data-label">一次回水压力</text>
              <text class="data-value">{{ stationData.realtime_data?.primary_system?.return_pressure || '-' }}MPa</text>
            </view>
          </view>
        </view>
        <view class="card real-time-data" v-else>
          <view class="card-title">实时数据</view>
          <view class="data-grid">
            <view class="data-item">
              <text class="data-label">供水温度</text>
              <text class="data-value">{{ selectedStation.supply_temp || '-' }}℃</text>
            </view>
            <view class="data-item">
              <text class="data-label">回水温度</text>
              <text class="data-value">{{ selectedStation.return_temp || '-' }}℃</text>
            </view>
            <view class="data-item">
              <text class="data-label">供水压力</text>
              <text class="data-value">{{ selectedStation.supply_pressure || '-' }}MPa</text>
            </view>
            <view class="data-item">
              <text class="data-label">回水压力</text>
              <text class="data-value">{{ selectedStation.return_pressure || '-' }}MPa</text>
            </view>
          </view>
        </view>
        
        <!-- 工作模式卡片 -->
        <view class="card work-mode">
          <view class="mode-header">
            <text class="mode-title">运行模式:</text>
            <view class="mode-buttons">
              <view 
                class="mode-button" 
                :class="{ active: workMode === 'auto' }"
                @tap="handleWorkModeChange('auto')"
              >自动</view>
              <view 
                class="mode-button" 
                :class="{ active: workMode === 'manual' }"
                @tap="handleWorkModeChange('manual')"
              >手动</view>
            </view>
          </view>
          <view class="mode-desc">
            {{ workMode === 'auto' ? '自动模式: 系统将根据预设算法自动调节设备运行' : '手动模式: 您可以手动调节设备运行参数' }}
          </view>
        </view>
        
        <!-- 设备控制卡片 -->
        <view class="card device-control">
          <view class="card-title">设备控制</view>
          
          <!-- 设备类型切换标签 -->
          <view class="device-tabs">
            <view 
              class="device-tab" 
              :class="{ active: activeDeviceTab === 'valve' }"
              @tap="activeDeviceTab = 'valve'"
            >阀门控制</view>
            <view 
              class="device-tab" 
              :class="{ active: activeDeviceTab === 'pump' }"
              @tap="activeDeviceTab = 'pump'"
            >水泵控制</view>
          </view>
          
          <!-- 阀门控制 -->
          <view v-if="activeDeviceTab === 'valve'" class="device-section">
            <view class="device-control-block">
              <picker 
                @change="handleValveSelect" 
                :value="selectedValveIndex" 
                :range="deviceControlData.valves" 
                range-key="name"
                :disabled="workMode === 'auto' || selectedStation.status !== 'online'"
              >
                <view class="device-selector">
                  <view class="selector-wrap">
                    <text class="dropdown-label">选择阀门:</text>
                    <view class="dropdown-value">
                      <text class="device-name">{{ deviceControlData.valves[selectedValveIndex].name }}</text>
                      <text class="dropdown-arrow">▼</text>
                    </view>
                  </view>
                  <switch 
                    :checked="deviceControlData.valves[selectedValveIndex].isOpen" 
                    @change="(e) => handleValveSwitch(deviceControlData.valves[selectedValveIndex].id, e)" 
                    :disabled="workMode === 'auto' || selectedStation.status !== 'online'"
                    color="#0088ff"
                  />
                </view>
              </picker>
              
              <view class="device-control-params">
                <text class="param-label">开度: {{ deviceControlData.valves[selectedValveIndex].openDegree }}%</text>
                <view class="slider-row">
                  <slider 
                    :value="tempValveOpenDegree" 
                    min="0" 
                    max="100" 
                    @change="(e) => updateTempValveOpenDegree(e)"
                    :disabled="workMode === 'auto' || !deviceControlData.valves[selectedValveIndex].isOpen || selectedStation.status !== 'online'"
                    activeColor="#0088ff"
                    block-size="20"
                    block-color="#ffffff"
                  />
                  <text class="param-value">{{ tempValveOpenDegree }}</text>
                </view>
                <view class="confirm-button-row">
                  <button 
                    class="confirm-button" 
                    @tap="confirmValveSettings"
                    :disabled="workMode === 'auto' || !deviceControlData.valves[selectedValveIndex].isOpen || selectedStation.status !== 'online'"
                  >确认</button>
                </view>
              </view>
            </view>
          </view>
          
          <!-- 水泵控制 -->
          <view v-if="activeDeviceTab === 'pump'" class="device-section">
            <view class="device-control-block">
              <picker 
                @change="handlePumpSelect" 
                :value="selectedPumpIndex" 
                :range="deviceControlData.pumps" 
                range-key="name"
                :disabled="workMode === 'auto' || selectedStation.status !== 'online'"
              >
                <view class="device-selector">
                  <view class="selector-wrap">
                    <text class="dropdown-label">选择水泵:</text>
                    <view class="dropdown-value">
                      <text class="device-name">{{ deviceControlData.pumps[selectedPumpIndex].name }}</text>
                      <text class="dropdown-arrow">▼</text>
                    </view>
                  </view>
                  <switch 
                    :checked="deviceControlData.pumps[selectedPumpIndex].isRunning" 
                    @change="(e) => handlePumpSwitch(deviceControlData.pumps[selectedPumpIndex].id, e)" 
                    :disabled="workMode === 'auto' || selectedStation.status !== 'online'"
                    color="#0088ff"
                  />
                </view>
              </picker>
              
              <view class="device-control-params">
                <text class="param-label">频率: {{ deviceControlData.pumps[selectedPumpIndex].frequency }}Hz</text>
                
                <!-- 工业变频器风格的频率控制 -->
                <view class="frequency-control" :class="{'disabled': workMode === 'auto' || !deviceControlData.pumps[selectedPumpIndex].isRunning || selectedStation.status !== 'online'}">
                  <view class="frequency-display">
                    <input 
                      type="digit" 
                      v-model="tempPumpFrequency" 
                      class="frequency-input"
                      :disabled="workMode === 'auto' || !deviceControlData.pumps[selectedPumpIndex].isRunning || selectedStation.status !== 'online'"
                    />
                    <text class="frequency-unit">Hz</text>
                  </view>
                  <view class="frequency-buttons">
                    <view 
                      class="frequency-button increase" 
                      @tap="increasePumpFrequency"
                      :class="{'disabled': workMode === 'auto' || !deviceControlData.pumps[selectedPumpIndex].isRunning || selectedStation.status !== 'online'}"
                    >▲</view>
                    <view 
                      class="frequency-button decrease" 
                      @tap="decreasePumpFrequency"
                      :class="{'disabled': workMode === 'auto' || !deviceControlData.pumps[selectedPumpIndex].isRunning || selectedStation.status !== 'online'}"
                    >▼</view>
                  </view>
                </view>
                
                <view class="confirm-button-row">
                  <button 
                    class="confirm-button" 
                    @tap="confirmPumpSettings"
                    :disabled="workMode === 'auto' || !deviceControlData.pumps[selectedPumpIndex].isRunning || selectedStation.status !== 'online'"
                  >确认</button>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 控制算法卡片 -->
        <view class="card control-algorithm">
          <view class="card-title">控制算法</view>
          <view class="algorithm-tip" v-if="workMode === 'manual'">
            <text class="tip-text">注意：算法设置仅在自动模式下生效，请先切换到自动模式</text>
          </view>
          <view class="algorithm-tip" v-if="selectedStation.status !== 'online'">
            <text class="tip-text">注意：该站点当前不在线，无法应用算法设置</text>
          </view>
          <view class="algorithm-selector">
            <radio-group @change="handleAlgorithmChange">
              <label class="algorithm-item" v-for="(algo, index) in algorithms" :key="index">
                <view class="algorithm-radio">
                  <radio 
                    :value="algo.value" 
                    :checked="selectedAlgorithm === algo.value"
                    color="#0088ff"
                    :disabled="selectedStation.status !== 'online'"
                  />
                </view>
                <text class="algorithm-label">{{ algo.label }}</text>
              </label>
            </radio-group>
          </view>
          <view class="algorithm-desc">
            {{ getAlgorithmDescription() }}
          </view>
          <!-- 显示目标值设定区域，但根据条件禁用输入和按钮 -->
          <view class="algorithm-params">
            <view class="param-item">
              <text class="param-label">目标值设定:</text>
              <input 
                type="digit" 
                v-model="algorithmTargetValue" 
                class="param-input"
                :disabled="workMode === 'manual' || selectedStation.status !== 'online'"
              />
              <text class="param-unit">{{ getAlgorithmUnit() }}</text>
            </view>
            <button 
              class="apply-button" 
              @tap="applyAlgorithmSettings"
              :disabled="workMode === 'manual' || selectedStation.status !== 'online'"
            >应用</button>
            <view class="button-tip" v-if="workMode === 'manual'">
              <text class="tip-text">需要在自动模式下才能应用算法</text>
            </view>
          </view>
        </view>
      </view>
      
      <view v-else-if="activeTab === 'process'" class="process-view">
        <view class="process-image">
          <image src="/static/images/process_flow.png" mode="widthFix"></image>
        </view>
        <view class="process-legend">
          <view class="legend-item">
            <view class="legend-color" style="background-color: #f00;"></view>
            <text class="legend-text">供水</text>
          </view>
          <view class="legend-item">
            <view class="legend-color" style="background-color: #00f;"></view>
            <text class="legend-text">回水</text>
          </view>
          <view class="legend-item">
            <view class="legend-color" style="background-color: #0c0;"></view>
            <text class="legend-text">运行设备</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 未选择站点时的提示 -->
    <view class="no-station" v-else>
      <text class="tip">请选择换热站进行控制</text>
    </view>
    
    <!-- 站点选择弹窗 -->
    <uni-popup ref="stationPopup" type="bottom" background-color="#f5f5f5">
      <view class="station-picker">
        <view class="picker-header">
          <text class="picker-title">选择换热站</text>
          <view class="picker-close" @tap="closeStationPopup">关闭</view>
        </view>
        
        <!-- 搜索栏 -->
        <view class="search-bar">
          <view class="search-input-wrap">
            <view class="search-icon">🔍</view>
            <input 
              type="text" 
              v-model="stationFilter.keyword" 
              placeholder="搜索换热站" 
              confirm-type="search"
              @confirm="searchStations"
              class="search-input"
            />
            <view class="search-clear" v-if="stationFilter.keyword" @tap="stationFilter.keyword = ''">×</view>
          </view>
        </view>
        
        <!-- 状态筛选 -->
        <view class="filter-tabs">
          <view 
            class="filter-tab" 
            :class="{ active: stationFilter.status === 'all' }"
            @tap="setStatusFilter('all')"
          >全部</view>
          <view 
            class="filter-tab" 
            :class="{ active: stationFilter.status === 'online' }"
            @tap="setStatusFilter('online')"
          >在线</view>
          <view 
            class="filter-tab" 
            :class="{ active: stationFilter.status === 'fault' }"
            @tap="setStatusFilter('fault')"
          >故障</view>
          <view 
            class="filter-tab" 
            :class="{ active: stationFilter.status === 'offline' }"
            @tap="setStatusFilter('offline')"
          >离线</view>
        </view>
        
        <!-- 站点列表 -->
        <scroll-view scroll-y="true" class="station-list">
          <view v-if="filteredStations.length === 0" class="empty-tip">
            <text>暂无符合条件的换热站</text>
          </view>
          <view 
            class="station-item" 
            v-for="station in filteredStations" 
            :key="station.id"
            @tap="handleSelectStation(station)"
          >
            <view class="station-item-content">
              <view class="station-main-info">
                <text class="station-name">{{ station.name }}</text>
                <text class="station-address">{{ station.heat_unit_name }}</text>
              </view>
              <view class="station-temp-info">
                <text class="temp-item">供水温度: {{ station.supply_temp ? station.supply_temp.toFixed(2) + '℃' : '-' }}</text>
                <text class="temp-item">回水温度: {{ station.return_temp ? station.return_temp.toFixed(2) + '℃' : '-' }}</text>
              </view>
              <text class="station-status-tag" :class="station.status">
                {{ station.status === 'online' ? '在线' : station.status === 'fault' ? '故障' : '离线' }}
              </text>
            </view>
          </view>
        </scroll-view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { heatingStationApi } from '@/utils/api.js';

export default {
  components: {
    // 确保组件正确引入
  },
  data() {
    return {
      activeTab: 'control',
      selectedStation: null,
      stationList: [],
      stationData: null,
      tempSliderValue: 80,
      pressureSliderValue: 60,
      loading: false,
      // 站点筛选条件
      stationFilter: {
        status: 'all',
        keyword: ''
      },
      // 工作模式
      workMode: 'auto',
      // 设备控制标签
      activeDeviceTab: 'valve',
      // 设备控制数据
      deviceControlData: {
        valves: [
          { id: 1, name: '一次供水电动阀', isOpen: false, openDegree: 0 },
          { id: 2, name: '一次回水电动阀', isOpen: false, openDegree: 0 },
          { id: 3, name: '二次供水电动阀', isOpen: false, openDegree: 0 }
        ],
        pumps: [
          { id: 1, name: '一次循环泵', isRunning: false, frequency: 0 },
          { id: 2, name: '二次循环泵', isRunning: false, frequency: 0 }
        ]
      },
      // 当前选中的阀门和水泵索引
      selectedValveIndex: 0,
      selectedPumpIndex: 0,
      // 控制算法
      algorithms: [
        { value: 'primary_flow', label: '一次流量控制算法' },
        { value: 'primary_supply_temp', label: '一次供水温度控制算法' },
        { value: 'secondary_supply_temp', label: '二次供水温度控制算法' },
        { value: 'secondary_return_temp', label: '二次回水温度控制算法' },
        { value: 'ai_prediction', label: 'AI预算算法' }
      ],
      selectedAlgorithm: 'primary_flow',
      algorithmTargetValue: 75,
      tempValveOpenDegree: 0,
      tempPumpFrequency: 0
    };
  },
  computed: {
    // 过滤后的站点列表
    filteredStations() {
      if (!this.stationList || this.stationList.length === 0) return [];
      
      return this.stationList.filter(station => {
        // 状态筛选
        if (this.stationFilter.status !== 'all' && station.status !== this.stationFilter.status) {
          return false;
        }
        
        // 关键词筛选
        if (this.stationFilter.keyword && !station.name.includes(this.stationFilter.keyword)) {
          return false;
        }
        
        return true;
      });
    }
  },
  onReady() {
    // 确保popup组件已加载
    if (this.$refs.stationPopup) {
      console.log('Popup component loaded');
    } else {
      console.error('Popup component not found');
    }
  },
  onLoad() {
    // 加载站点列表
    this.loadStationList();
    
    // 检查是否有缓存的站点信息
    const cachedStation = uni.getStorageSync('selectedStation');
    if (cachedStation) {
      try {
        this.selectedStation = JSON.parse(cachedStation);
        this.loadStationData();
      } catch (e) {
        console.error('解析缓存站点数据失败', e);
      }
    }
    
    // 初始化算法默认值
    this.initAlgorithmDefaults();
  },
  onShow() {
    // 获取全局组件引用
    this.$nextTick(() => {
      // 如果页面上还没有popup对象，尝试延迟获取
      if (!this.$refs.stationPopup) {
        setTimeout(() => {
          this.initPopupComponents();
        }, 300);
      }
      
      // 如果已有选择的站点，刷新数据以获取最新的控制状态
      if (this.selectedStation && this.selectedStation.id) {
        console.log('页面显示，刷新站点数据');
        this.loadStationData();
      }
    });
  },
  methods: {
    // 初始化popup组件
    initPopupComponents() {
      if (!this.$refs.stationPopup) {
        console.error('Popup component not found, trying alternative access');
        // 尝试通过uni API创建
        uni.showToast({
          title: '请重新点击选择换热站',
          icon: 'none',
          duration: 2000
        });
      }
    },
    // 加载换热站列表
    async loadStationList() {
      uni.showLoading({
        title: '加载站点列表...'
      });
      
      try {
        const res = await heatingStationApi.getList({
          "status": "",   
          "keyword": ""
        });
        if (res.code === 200 && res.data && res.data.list) {
          this.stationList = res.data.list;
        } else {
          uni.showToast({
            title: '获取站点列表失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('获取站点列表出错:', error);
        uni.showToast({
          title: '网络异常，请重试',
          icon: 'none'
        });
      } finally {
        uni.hideLoading();
      }
    },
    
    // 显示站点选择
    showStationSelect() {
      if (this.stationList.length === 0) {
        this.loadStationList().then(() => {
          this.openStationPopup();
        });
      } else {
        this.openStationPopup();
      }
    },
    
    // 打开站点选择弹窗
    openStationPopup() {
      // 重置筛选条件
      this.stationFilter.status = 'all';
      this.stationFilter.keyword = '';
      
      // 打开弹窗
      if (this.$refs.stationPopup) {
        this.$refs.stationPopup.open();
      } else {
        console.error('站点选择弹窗组件未找到');
        uni.showToast({
          title: '组件加载失败，请重试',
          icon: 'none'
        });
      }
    },
    
    // 关闭站点选择弹窗
    closeStationPopup() {
      if (this.$refs.stationPopup) {
        this.$refs.stationPopup.close();
      }
    },
    
    // 设置状态筛选
    setStatusFilter(status) {
      this.stationFilter.status = status;
    },
    
    // 搜索站点
    searchStations() {
      console.log('搜索关键词:', this.stationFilter.keyword);
      // 关键词搜索已经通过计算属性filteredStations自动实现
    },
    
    // 处理站点选择
    handleSelectStation(station) {
      this.selectedStation = station;
      // 缓存选择的站点
      uni.setStorageSync('selectedStation', JSON.stringify(station));
      this.loadStationData();
      this.closeStationPopup();
    },
    
    // 加载站点详细数据
    async loadStationData() {
      if (!this.selectedStation) return;
      
      uni.showLoading({
        title: '加载站点数据...'
      });
      
      try {
        const res = await heatingStationApi.getDetail(this.selectedStation.id);
        console.log('站点详细数据:', JSON.stringify(res));
        
        if (res.code === 200 && res.data) {
          this.stationData = res.data;
          
          // 设置滑块初始值
          if (this.stationData.realtime_data && this.stationData.realtime_data.primary_system) {
            // 温度滑块值
            const supplyTemp = this.stationData.realtime_data.primary_system.supply_temp;
            if (supplyTemp) {
              this.tempSliderValue = Math.max(70, Math.min(95, Math.round(supplyTemp)));
            }
            
            // 压力滑块值 (转换为0.4-0.8MPa对应的40-80)
            const supplyPressure = this.stationData.realtime_data.primary_system.supply_pressure;
            if (supplyPressure) {
              this.pressureSliderValue = Math.max(40, Math.min(80, Math.round(supplyPressure * 100)));
            }
          } else {
            console.warn('一次系统数据缺失，使用默认温度和压力值');
          }
          
          // 获取当前工作模式（添加默认值处理）
          if (this.stationData.operation_mode) {
            console.log('服务器返回的工作模式:', this.stationData.operation_mode);
            this.workMode = this.stationData.operation_mode === 'automatic' ? 'auto' : 'manual';
          } else {
            // 如果服务器未返回工作模式，默认设为手动模式以便操作设备
            console.warn('未获取到工作模式信息，默认设为手动模式');
            this.workMode = 'manual';
          }
          
          // 获取当前控制算法
          if (this.stationData.control_algorithm) {
            this.selectedAlgorithm = this.stationData.control_algorithm.type || 'primary_flow';
            if (this.stationData.control_algorithm.target_value) {
              this.algorithmTargetValue = this.stationData.control_algorithm.target_value;
            }
          } else {
            console.warn('未获取到控制算法信息，使用默认算法');
            this.selectedAlgorithm = 'primary_flow';
            this.algorithmTargetValue = 75;
          }
          
          // 如果有设备数据，更新设备控制状态
          if (this.stationData.realtime_data) {
            console.log('开始更新设备控制数据');
            this.updateDeviceControlData();
          } else {
            console.error('站点实时数据缺失，无法更新设备状态');
            // 确保控制参数有默认值
            this.resetControlParameters();
          }
          
        } else {
          console.error('获取站点数据响应异常:', res.message || '未知错误');
          uni.showToast({
            title: '获取站点数据失败',
            icon: 'none'
          });
          // 确保控制参数有默认值
          this.resetControlParameters();
        }
      } catch (error) {
        console.error('获取站点数据出错:', error);
        uni.showToast({
          title: '网络异常，请重试',
          icon: 'none'
        });
        // 确保控制参数有默认值
        this.resetControlParameters();
      } finally {
        uni.hideLoading();
      }
    },
    
    // 重置控制参数到默认值
    resetControlParameters() {
      try {
        this.tempValveOpenDegree = this.deviceControlData.valves[this.selectedValveIndex]?.openDegree || 0;
        this.tempPumpFrequency = this.deviceControlData.pumps[this.selectedPumpIndex]?.frequency || 0;
      } catch (e) {
        console.error('重置控制参数失败:', e);
        // 强制设置安全默认值
        this.tempValveOpenDegree = 0;
        this.tempPumpFrequency = 0;
        // 确保索引在有效范围内
        this.selectedValveIndex = 0;
        this.selectedPumpIndex = 0;
      }
      console.log('重置后的控制参数:', {
        tempValveOpenDegree: this.tempValveOpenDegree,
        tempPumpFrequency: this.tempPumpFrequency,
        selectedValveIndex: this.selectedValveIndex,
        selectedPumpIndex: this.selectedPumpIndex
      });
    },
    
    // 更新设备控制数据
    updateDeviceControlData() {
      console.log('更新设备控制数据', JSON.stringify(this.stationData));
      
      // 检查是否有设备状态数据
      if (!this.stationData.realtime_data || !this.stationData.realtime_data.equipment_status) {
        console.error('设备数据缺失，使用默认设置');
        // 先确保索引在合法范围内
        this.selectedValveIndex = Math.min(this.selectedValveIndex, this.deviceControlData.valves.length - 1);
        this.selectedPumpIndex = Math.min(this.selectedPumpIndex, this.deviceControlData.pumps.length - 1);
        
        // 确保默认值被设置
        this.tempValveOpenDegree = this.deviceControlData.valves[this.selectedValveIndex].openDegree || 0;
        this.tempPumpFrequency = this.deviceControlData.pumps[this.selectedPumpIndex].frequency || 0;
        return;
      }
      
      const equipStatus = this.stationData.realtime_data.equipment_status;
      console.log('设备状态数据:', JSON.stringify(equipStatus));
      
      // 更新阀门状态
      if (equipStatus.valves && equipStatus.valves.length > 0) {
        console.log('更新阀门数据:', JSON.stringify(equipStatus.valves));
        this.deviceControlData.valves = equipStatus.valves.map(valve => {
          return {
            id: valve.id,
            name: valve.name || `阀门${valve.id}`,
            isOpen: valve.status === 'open',
            openDegree: valve.opening_degree || 0
          };
        });
        
        // 确保选中的索引有效
        if (this.selectedValveIndex >= this.deviceControlData.valves.length) {
          this.selectedValveIndex = 0;
        }
      } else {
        console.warn('阀门数据缺失，保留默认数据');
      }
      
      // 同步更新临时变量
      if (this.deviceControlData.valves.length > 0) {
        // 先检查所选阀门的开度是否存在
        if (this.deviceControlData.valves[this.selectedValveIndex]) {
          const currentDegree = this.deviceControlData.valves[this.selectedValveIndex].openDegree;
          console.log(`阀门[${this.selectedValveIndex}]当前开度:`, currentDegree);
          this.tempValveOpenDegree = currentDegree;
        } else {
          console.warn('选中的阀门索引无效，重置为0');
          this.selectedValveIndex = 0;
          this.tempValveOpenDegree = this.deviceControlData.valves[0].openDegree || 0;
        }
      }
      
      // 更新水泵状态
      if (equipStatus.pumps && equipStatus.pumps.length > 0) {
        console.log('更新水泵数据:', JSON.stringify(equipStatus.pumps));
        this.deviceControlData.pumps = equipStatus.pumps.map(pump => {
          return {
            id: pump.id,
            name: pump.name || `水泵${pump.id}`,
            isRunning: pump.status === 'running',
            frequency: pump.frequency || 0
          };
        });
        
        // 确保选中的索引有效
        if (this.selectedPumpIndex >= this.deviceControlData.pumps.length) {
          this.selectedPumpIndex = 0;
        }
      } else {
        console.warn('水泵数据缺失，保留默认数据');
      }
      
      // 同步更新临时变量
      if (this.deviceControlData.pumps.length > 0) {
        // 先检查所选水泵的频率是否存在
        if (this.deviceControlData.pumps[this.selectedPumpIndex]) {
          const currentFreq = this.deviceControlData.pumps[this.selectedPumpIndex].frequency;
          console.log(`水泵[${this.selectedPumpIndex}]当前频率:`, currentFreq);
          this.tempPumpFrequency = currentFreq;
        } else {
          console.warn('选中的水泵索引无效，重置为0');
          this.selectedPumpIndex = 0;
          this.tempPumpFrequency = this.deviceControlData.pumps[0].frequency || 0;
        }
      }
      
      // 记录当前工作模式和站点状态，用于调试
      console.log(`当前工作模式:${this.workMode}, 站点状态:${this.selectedStation.status}, 临时开度值:${this.tempValveOpenDegree}, 临时频率值:${this.tempPumpFrequency}`);
    },
    
    // 工作模式切换
    async handleWorkModeChange(mode) {
      console.log('尝试切换工作模式:', mode);
      if (this.workMode === mode || !this.selectedStation) return;
      
      // 确认切换前先检查站点状态
      if (this.selectedStation.status !== 'online') {
        console.warn('站点不在线，无法切换工作模式');
        uni.showToast({
          title: '站点不在线，无法切换模式',
          icon: 'none'
        });
        return;
      }
      
      // 如果从自动切换到手动，提醒用户算法将不再生效
      if (mode === 'manual' && this.workMode === 'auto') {
        uni.showModal({
          title: '确认切换',
          content: '切换到手动模式后，控制算法将不再生效。是否继续？',
          success: res => {
            if (res.confirm) {
              this.doWorkModeChange(mode);
            }
          }
        });
      } 
      // 如果从手动切换到自动，提醒用户算法将会生效
      else if (mode === 'auto' && this.workMode === 'manual') {
        uni.showModal({
          title: '确认切换',
          content: '切换到自动模式后，系统将根据选择的控制算法自动调节设备。是否继续？',
          success: res => {
            if (res.confirm) {
              this.doWorkModeChange(mode);
            }
          }
        });
      }
    },
    
    // 执行工作模式切换
    async doWorkModeChange(mode) {
      try {
        // 显示加载中提示
        uni.showLoading({
          title: '正在切换模式...',
          mask: true
        });
        
        const params = {
          hes_id: this.selectedStation.id,
          mode: mode === 'auto' ? 'automatic' : 'manual',
          operator_id: uni.getStorageSync('userId') || 1
        };
        
        console.log('发送模式切换请求:', JSON.stringify(params));
        const result = await heatingStationApi.setOperationMode(params);
        console.log('模式切换结果:', JSON.stringify(result));
        
        if (result.code === 200) {
          // 先更新本地模式，便于UI响应
          this.workMode = mode;
          
          // 模式相关提示
          let successMessage = '';
          if (mode === 'auto') {
            successMessage = '已切换到自动模式，控制算法已生效';
          } else {
            successMessage = '已切换到手动模式，可手动控制设备';
          }
          
          uni.showToast({
            title: successMessage,
            icon: 'success',
            duration: 2000
          });
          
          // 提示用户切换成功
          console.log(`成功将工作模式切换为: ${mode}`);
          
          // 重新加载站点数据以刷新UI状态
          setTimeout(() => {
            console.log('模式切换成功，重新加载站点数据...');
            this.loadStationData();
          }, 1000);
        } else {
          console.error('模式切换失败:', result.message || '未知错误');
          uni.showToast({
            title: result.message || '模式切换失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('模式切换异常:', error);
        uni.showToast({
          title: error.message || '操作失败，请重试',
          icon: 'none'
        });
      } finally {
        // 确保隐藏加载提示
        uni.hideLoading();
      }
    },
    
    // 处理阀门选择
    handleValveSelect(e) {
      this.selectedValveIndex = e.detail.value;
      // 更新临时存储的开度值
      this.tempValveOpenDegree = this.deviceControlData.valves[this.selectedValveIndex].openDegree;
    },
    
    // 处理水泵选择
    handlePumpSelect(e) {
      this.selectedPumpIndex = e.detail.value;
      // 更新临时存储的频率值
      this.tempPumpFrequency = this.deviceControlData.pumps[this.selectedPumpIndex].frequency;
    },
    
    // 更新阀门开度
    updateTempValveOpenDegree(event) {
      // 只更新临时值，不直接修改设备状态
      const newValue = parseInt(event.detail.value) || 0;
      console.log(`阀门开度临时值从 ${this.tempValveOpenDegree} 更新为 ${newValue}`);
      this.tempValveOpenDegree = newValue;
    },
    
    // 确认阀门设置
    confirmValveSettings() {
      // 检查当前阀门状态
      const currentValve = this.deviceControlData.valves[this.selectedValveIndex];
      if (!currentValve) {
        console.error('当前选中的阀门不存在');
        uni.showToast({
          title: '阀门数据错误',
          icon: 'none'
        });
        return;
      }
      
      if (!currentValve.isOpen) {
        uni.showToast({
          title: '请先打开阀门',
          icon: 'none'
        });
        return;
      }
      
      // 显示确认弹窗
      uni.showModal({
        title: '确认设置',
        content: `确认将"${currentValve.name}"开度设置为${this.tempValveOpenDegree}%吗？`,
        success: async (res) => {
          if (res.confirm) {
            // 更新本地数据
            this.updateValveState(currentValve.id, { openDegree: this.tempValveOpenDegree });
            // 发送控制命令
            await this.sendValveControlCommand(currentValve.id);
          }
        }
      });
    },
    
    // 更新水泵频率
    updateTempPumpFrequency(event) {
      // 只更新临时值，不直接修改设备状态
      const newValue = parseInt(event.detail.value) || 0;
      console.log(`水泵频率临时值从 ${this.tempPumpFrequency} 更新为 ${newValue}`);
      this.tempPumpFrequency = newValue;
    },
    
    // 确认水泵设置
    confirmPumpSettings() {
      // 检查当前水泵状态
      const currentPump = this.deviceControlData.pumps[this.selectedPumpIndex];
      if (!currentPump) {
        console.error('当前选中的水泵不存在');
        uni.showToast({
          title: '水泵数据错误',
          icon: 'none'
        });
        return;
      }
      
      if (!currentPump.isRunning) {
        uni.showToast({
          title: '请先启动水泵',
          icon: 'none'
        });
        return;
      }
      
      // 显示确认弹窗
      uni.showModal({
        title: '确认设置',
        content: `确认将"${currentPump.name}"频率设置为${this.tempPumpFrequency}Hz吗？`,
        success: async (res) => {
          if (res.confirm) {
            // 更新本地数据
            this.updatePumpState(currentPump.id, { frequency: this.tempPumpFrequency });
            // 发送控制命令
            await this.sendPumpControlCommand(currentPump.id);
          }
        }
      });
    },
    
    // 阀门开关控制
    async handleValveSwitch(valveId, event) {
      const isOpen = event.detail.value;
      
      // 获取当前阀门
      const valveIndex = this.deviceControlData.valves.findIndex(v => v.id === valveId);
      if (valveIndex === -1) {
        console.error('找不到ID为', valveId, '的阀门');
        uni.showToast({
          title: '操作失败，设备数据错误',
          icon: 'none'
        });
        return;
      }
      
      const valve = this.deviceControlData.valves[valveIndex];
      // 显示确认弹窗
      uni.showModal({
        title: '确认操作',
        content: `确认${isOpen ? '打开' : '关闭'}"${valve.name}"吗？`,
        success: async (res) => {
          if (res.confirm) {
            // 更新本地数据
            this.updateValveState(valveId, { isOpen });
            
            // 如果关闭阀门，自动设置开度为0
            if (!isOpen) {
              this.updateValveState(valveId, { openDegree: 0 });
              this.tempValveOpenDegree = 0;
            } else {
              // 如果打开阀门，更新临时开度值
              this.tempValveOpenDegree = valve.openDegree;
            }
            
            // 发送控制命令
            await this.sendValveControlCommand(valveId);
          } else {
            // 用户取消，还原开关状态
            // 通过下一个渲染周期更新UI
            this.$nextTick(() => {
              this.deviceControlData.valves[valveIndex].isOpen = !isOpen;
            });
          }
        }
      });
    },
    
    // 水泵开关控制
    async handlePumpSwitch(pumpId, event) {
      const isRunning = event.detail.value;
      
      // 获取当前水泵
      const pumpIndex = this.deviceControlData.pumps.findIndex(p => p.id === pumpId);
      if (pumpIndex === -1) {
        console.error('找不到ID为', pumpId, '的水泵');
        uni.showToast({
          title: '操作失败，设备数据错误',
          icon: 'none'
        });
        return;
      }
      
      const pump = this.deviceControlData.pumps[pumpIndex];
      // 显示确认弹窗
      uni.showModal({
        title: '确认操作',
        content: `确认${isRunning ? '启动' : '停止'}"${pump.name}"吗？`,
        success: async (res) => {
          if (res.confirm) {
            // 更新本地数据
            this.updatePumpState(pumpId, { isRunning });
            
            // 如果关闭水泵，自动设置频率为0
            if (!isRunning) {
              this.updatePumpState(pumpId, { frequency: 0 });
              this.tempPumpFrequency = 0;
            } else {
              // 如果启动水泵，更新临时频率值
              this.tempPumpFrequency = pump.frequency;
            }
            
            // 发送控制命令
            await this.sendPumpControlCommand(pumpId);
          } else {
            // 用户取消，还原开关状态
            // 通过下一个渲染周期更新UI
            this.$nextTick(() => {
              this.deviceControlData.pumps[pumpIndex].isRunning = !isRunning;
            });
          }
        }
      });
    },
    
    // 更新阀门状态
    updateValveState(valveId, newState) {
      const valveIndex = this.deviceControlData.valves.findIndex(v => v.id === valveId);
      if (valveIndex !== -1) {
        this.deviceControlData.valves[valveIndex] = {
          ...this.deviceControlData.valves[valveIndex],
          ...newState
        };
      }
    },
    
    // 更新水泵状态
    updatePumpState(pumpId, newState) {
      const pumpIndex = this.deviceControlData.pumps.findIndex(p => p.id === pumpId);
      if (pumpIndex !== -1) {
        this.deviceControlData.pumps[pumpIndex] = {
          ...this.deviceControlData.pumps[pumpIndex],
          ...newState
        };
      }
    },
    
    // 发送阀门控制命令
    async sendValveControlCommand(valveId) {
      const valve = this.deviceControlData.valves.find(v => v.id === valveId);
      if (!valve) return;
      
      try {
        const controlParams = {
          hes_id: this.selectedStation.id,
          control_type: 'valve',
          device_id: valveId,
          action: valve.isOpen ? 'open' : 'close',
          value: valve.openDegree, // 阀门开度
          operator_id: uni.getStorageSync('userId') || 1,
          remark: `${valve.isOpen ? '打开' : '关闭'}阀门，开度设为${valve.openDegree}%`
        };
        
        const result = await heatingStationApi.controlDevice(controlParams);
        
        if (result.code === 200) {
          uni.showToast({
            title: '阀门控制成功',
            icon: 'success'
          });
        } else {
          uni.showToast({
            title: result.message || '阀门控制失败',
            icon: 'none'
          });
          // 失败时恢复原状态
          this.loadStationData();
        }
      } catch (error) {
        console.error('阀门控制错误:', error);
        uni.showToast({
          title: error.message || '操作失败，请重试',
          icon: 'none'
        });
      }
    },
    
    // 发送水泵控制命令
    async sendPumpControlCommand(pumpId) {
      const pump = this.deviceControlData.pumps.find(p => p.id === pumpId);
      if (!pump) return;
      
      try {
        const controlParams = {
          hes_id: this.selectedStation.id,
          control_type: 'pump',
          device_id: pumpId,
          action: pump.isRunning ? 'start' : 'stop',
          value: pump.frequency, // 水泵频率
          operator_id: uni.getStorageSync('userId') || 1,
          remark: `${pump.isRunning ? '启动' : '停止'}水泵，频率设为${pump.frequency}Hz`
        };
        
        const result = await heatingStationApi.controlDevice(controlParams);
        
        if (result.code === 200) {
          uni.showToast({
            title: '水泵控制成功',
            icon: 'success'
          });
        } else {
          uni.showToast({
            title: result.message || '水泵控制失败',
            icon: 'none'
          });
          // 失败时恢复原状态
          this.loadStationData();
        }
      } catch (error) {
        console.error('水泵控制错误:', error);
        uni.showToast({
          title: error.message || '操作失败，请重试',
          icon: 'none'
        });
      }
    },
    
    // 处理算法选择变更
    handleAlgorithmChange(e) {
      const oldAlgorithm = this.selectedAlgorithm;
      this.selectedAlgorithm = e.detail.value;
      console.log(`算法选择从 ${oldAlgorithm} 变更为 ${this.selectedAlgorithm}`);
      
      // 如果在手动模式下，提示用户需要切换到自动模式
      if (this.workMode === 'manual') {
        uni.showToast({
          title: '请切换到自动模式以应用算法',
          icon: 'none',
          duration: 2000
        });
      }
      
      // 根据选择的算法类型设置默认的目标值
      if (this.selectedAlgorithm !== oldAlgorithm) {
        const defaultValues = {
          'primary_flow': 80,
          'primary_supply_temp': 85,
          'secondary_supply_temp': 60,
          'secondary_return_temp': 45,
          'ai_prediction': 75
        };
        
        if (defaultValues[this.selectedAlgorithm]) {
          this.algorithmTargetValue = defaultValues[this.selectedAlgorithm];
          console.log(`设置默认目标值: ${this.algorithmTargetValue} ${this.getAlgorithmUnit()}`);
        }
      }
    },
    
    // 获取算法描述
    getAlgorithmDescription() {
      const algoMap = {
        'primary_flow': '通过控制一次侧流量来调节换热效果',
        'primary_supply_temp': '通过调节一次供水温度来控制整体热量输出',
        'secondary_supply_temp': '通过精确控制二次供水温度来满足用户需求',
        'secondary_return_temp': '通过监控和调整二次回水温度优化系统效率',
        'ai_prediction': '利用AI技术预测负荷变化并提前调整系统参数'
      };
      
      return algoMap[this.selectedAlgorithm] || '请选择一种控制算法';
    },
    
    // 获取算法目标值单位
    getAlgorithmUnit() {
      const unitMap = {
        'primary_flow': 'm³/h',
        'primary_supply_temp': '℃',
        'secondary_supply_temp': '℃',
        'secondary_return_temp': '℃',
        'ai_prediction': ''
      };
      
      return unitMap[this.selectedAlgorithm] || '';
    },
    
    // 应用算法设置
    async applyAlgorithmSettings() {
      if (!this.selectedStation || !this.selectedAlgorithm) {
        console.error('缺少站点或算法信息');
        uni.showToast({
          title: '请选择算法和设置目标值',
          icon: 'none'
        });
        return;
      }
      
      // 验证站点状态
      if (this.selectedStation.status !== 'online') {
        console.warn('站点不在线，无法应用算法设置');
        uni.showToast({
          title: '站点不在线，无法设置算法',
          icon: 'none'
        });
        return;
      }
      
      // 验证工作模式
      if (this.workMode !== 'auto') {
        console.warn('当前为手动模式，无法应用算法设置');
        uni.showToast({
          title: '自动模式下才能应用算法',
          icon: 'none'
        });
        return;
      }
      
      // 验证目标值
      const targetValue = parseFloat(this.algorithmTargetValue);
      if (isNaN(targetValue) || targetValue <= 0) {
        console.error('目标值无效:', this.algorithmTargetValue);
        uni.showToast({
          title: '请输入有效的目标值',
          icon: 'none'
        });
        return;
      }
      
      try {
        // 显示加载中提示
        uni.showLoading({
          title: '正在应用算法设置...',
          mask: true
        });
        
        const params = {
          hes_id: this.selectedStation.id,
          algorithm_type: this.selectedAlgorithm,
          target_value: targetValue,
          operator_id: uni.getStorageSync('userId') || 1
        };
        
        console.log('发送算法设置请求:', JSON.stringify(params));
        const result = await heatingStationApi.setControlAlgorithm(params);
        console.log('算法设置结果:', JSON.stringify(result));
        
        if (result.code === 200) {
          uni.showToast({
            title: '算法设置成功',
            icon: 'success'
          });
          
          // 成功后可以重新加载数据以确认设置已生效
          setTimeout(() => {
            console.log('算法设置成功，重新加载站点数据...');
            this.loadStationData();
          }, 1000);
        } else {
          console.error('应用算法设置失败:', result.message || '未知错误');
          uni.showToast({
            title: result.message || '算法设置失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('算法设置异常:', error);
        uni.showToast({
          title: error.message || '操作失败，请重试',
          icon: 'none'
        });
      } finally {
        // 确保隐藏加载提示
        uni.hideLoading();
      }
    },
    
    // 初始化算法默认值
    initAlgorithmDefaults() {
      // 设置默认算法选项
      if (!this.selectedAlgorithm) {
        this.selectedAlgorithm = 'primary_flow';
      }
      
      // 设置默认目标值
      const defaultValues = {
        'primary_flow': 80,
        'primary_supply_temp': 85,
        'secondary_supply_temp': 60,
        'secondary_return_temp': 45,
        'ai_prediction': 75
      };
      
      // 如果没有设置目标值或目标值无效，设置默认值
      if (!this.algorithmTargetValue || isNaN(parseFloat(this.algorithmTargetValue))) {
        this.algorithmTargetValue = defaultValues[this.selectedAlgorithm] || 75;
        console.log(`初始化算法默认值: ${this.selectedAlgorithm}, 目标值: ${this.algorithmTargetValue}`);
      }
    },
    
    // 增加频率方法
    increasePumpFrequency() {
      if (this.workMode === 'auto' || !this.deviceControlData.pumps[this.selectedPumpIndex].isRunning || this.selectedStation.status !== 'online') {
        return;
      }
      
      // 获取当前值
      let currentValue = parseFloat(this.tempPumpFrequency) || 0;
      // 增加0.5，最大为50
      let newValue = Math.min(50, currentValue + 0.5);
      // 格式化为一位小数
      newValue = parseFloat(newValue.toFixed(1));
      console.log(`增加水泵频率: ${currentValue} -> ${newValue}`);
      // 更新临时值
      this.tempPumpFrequency = newValue;
    },
    
    // 执行水泵频率减少
    decreasePumpFrequency() {
      if (this.workMode === 'auto' || !this.deviceControlData.pumps[this.selectedPumpIndex].isRunning || this.selectedStation.status !== 'online') {
        return;
      }
      
      // 获取当前值
      let currentValue = parseFloat(this.tempPumpFrequency) || 0;
      // 减少0.5，最小为0
      let newValue = Math.max(0, currentValue - 0.5);
      // 格式化为一位小数
      newValue = parseFloat(newValue.toFixed(1));
      console.log(`减少水泵频率: ${currentValue} -> ${newValue}`);
      // 更新临时值
      this.tempPumpFrequency = newValue;
    },
    
    // 返回上一页
    navigateBack() {
      uni.navigateBack();
    }
  }
};
</script>

<style>
.station-control {
  min-height: 100vh;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: column;
}

.page-title {
  background-color: #0088ff;
  color: #fff;
  padding: 20rpx 30rpx;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  font-weight: bold;
}

.back-button {
  position: absolute;
  left: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.tab-section {
  background-color: #0088ff;
  padding: 0 20rpx 16rpx;
}

.tab-bar {
  display: flex;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 8rpx;
  padding: 6rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 16rpx 0;
  font-size: 28rpx;
  border-radius: 6rpx;
  color: #fff;
}

.tab-item.active {
  background-color: rgba(255, 255, 255, 0.9);
  color: #0088ff;
  font-weight: bold;
}

.station-selector {
  background-color: #fff;
  padding: 30rpx;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #eee;
}

.station-label {
  font-size: 30rpx;
  color: #666;
  min-width: 150rpx;
}

.station-value-wrapper {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #e5e5e5;
  padding: 12rpx 20rpx;
  border-radius: 6rpx;
}

.station-value-text {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
}

.station-value-text.placeholder {
  color: #999;
  font-weight: normal;
}

.station-arrow {
  font-size: 30rpx;
  color: #999;
}

.station-content {
  flex: 1;
  padding: 20rpx;
}

.card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 16rpx;
}

.info-row {
  display: flex;
  padding: 16rpx 0;
}

.label {
  color: #666;
  width: 180rpx;
}

.value {
  color: #333;
  flex: 1;
}

.value.online {
  color: #00c853;
}

.value.offline {
  color: #ef5350;
}

.value.fault {
  color: #ff9800;
}

.data-grid {
  display: flex;
  flex-wrap: wrap;
}

.data-item {
  width: 50%;
  padding: 20rpx 0;
  display: flex;
  flex-direction: column;
}

.data-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.data-value {
  font-size: 36rpx;
  color: #0088ff;
  font-weight: bold;
}

.control-grid {
  display: flex;
  flex-wrap: wrap;
}

.control-item {
  width: 50%;
  padding: 20rpx 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.control-label {
  font-size: 28rpx;
  color: #333;
}

.slider-control {
  margin: 30rpx 0;
}

.slider-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.slider-box {
  padding: 0 20rpx;
}

.process-view {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.process-image {
  width: 100%;
  text-align: center;
}

.process-image image {
  width: 100%;
  max-width: 690rpx;
}

.process-legend {
  display: flex;
  justify-content: center;
  margin-top: 30rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  margin: 0 20rpx;
}

.legend-color {
  width: 30rpx;
  height: 16rpx;
  margin-right: 10rpx;
}

.legend-text {
  font-size: 24rpx;
  color: #666;
}

.no-station {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #999;
  font-size: 32rpx;
}

/* 站点选择器样式 */
.station-picker {
  background-color: #f5f5f5;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background-color: #fff;
}

.picker-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.picker-close {
  font-size: 28rpx;
  color: #666;
}

/* 搜索栏样式 */
.search-bar {
  padding: 20rpx 30rpx;
  background-color: #f5f5f5;
}

.search-input-wrap {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 8rpx;
  padding: 0 20rpx;
  height: 80rpx;
}

.search-icon {
  margin-right: 10rpx;
  font-size: 28rpx;
  color: #999;
}

.search-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
}

.search-clear {
  width: 40rpx;
  height: 40rpx;
  line-height: 40rpx;
  text-align: center;
  color: #999;
  font-size: 30rpx;
}

/* 状态筛选标签样式 */
.filter-tabs {
  display: flex;
  padding: 20rpx 20rpx;
  background-color: #f5f5f5;
}

.filter-tab {
  padding: 12rpx 24rpx;
  margin: 0 10rpx;
  font-size: 28rpx;
  color: #666;
  background-color: #fff;
  border-radius: 50rpx;
}

.filter-tab.active {
  color: #fff;
  background-color: #0088ff;
}

/* 站点列表样式 */
.station-list {
  flex: 1;
  max-height: 60vh;
}

.station-item {
  margin: 20rpx;
  background-color: #fff;
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.station-item-content {
  position: relative;
  padding: 24rpx;
}

.station-main-info {
  margin-bottom: 16rpx;
}

.station-name {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 8rpx;
  display: block;
}

.station-address {
  font-size: 24rpx;
  color: #999;
  display: block;
}

.station-temp-info {
  margin-top: 12rpx;
  display: flex;
  justify-content: space-between;
}

.temp-item {
  font-size: 24rpx;
  color: #666;
  flex: 1;
}

.station-status-tag {
  position: absolute;
  right: 24rpx;
  bottom: 24rpx;
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
}

.station-status-tag.online {
  color: #00c853;
  background-color: rgba(0, 200, 83, 0.1);
}

.station-status-tag.offline {
  color: #ff5252;
  background-color: rgba(255, 82, 82, 0.1);
}

.station-status-tag.fault {
  color: #ff9800;
  background-color: rgba(255, 152, 0, 0.1);
}

.empty-tip {
  padding: 60rpx 0;
  text-align: center;
  color: #999;
  font-size: 28rpx;
  background-color: #f5f5f5;
}

/* 工作模式卡片样式 */
.mode-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.mode-title {
  font-size: 30rpx;
  color: #333;
  margin-right: 20rpx;
}

.mode-buttons {
  display: flex;
  background-color: #f0f0f0;
  border-radius: 8rpx;
  overflow: hidden;
}

.mode-button {
  padding: 12rpx 40rpx;
  font-size: 28rpx;
  color: #666;
  text-align: center;
}

.mode-button.active {
  background-color: #0088ff;
  color: #fff;
}

.mode-desc {
  font-size: 24rpx;
  color: #999;
  margin-top: 16rpx;
}

/* 设备控制卡片样式 */
.device-section {
  margin-bottom: 20rpx;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.blue-indicator {
  width: 8rpx;
  height: 32rpx;
  background-color: #0088ff;
  margin-right: 10rpx;
}

.section-title-text {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
}

.device-control-block {
  background-color: #fff;
  border: 1rpx solid #eeeeee;
  border-radius: 8rpx;
  overflow: hidden;
  padding: 20rpx;
}

.device-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.selector-wrap {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.dropdown-label {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 6rpx;
}

.dropdown-value {
  display: flex;
  align-items: center;
  border-bottom: 1px dashed #e0e0e0;
  padding-bottom: 8rpx;
}

.device-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.dropdown-arrow {
  font-size: 20rpx;
  color: #0088ff;
  margin-left: 10rpx;
}

.device-control-params {
  padding-top: 10rpx;
}

.param-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 16rpx;
  display: block;
}

.slider-row {
  display: flex;
  align-items: center;
}

.param-value {
  width: 50rpx;
  text-align: right;
  font-size: 28rpx;
  color: #333;
  margin-left: 10rpx;
}

/* 控制算法卡片样式 */
.algorithm-selector {
  margin-bottom: 20rpx;
}

.algorithm-item {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
}

.algorithm-radio {
  display: flex;
  align-items: center;
}

.algorithm-label {
  font-size: 28rpx;
  color: #333;
  margin-left: 5rpx;
}

.algorithm-desc {
  font-size: 24rpx;
  color: #666;
  padding: 10rpx;
  background-color: #f9f9f9;
  border-radius: 6rpx;
  margin-bottom: 20rpx;
}

.algorithm-params {
  margin-top: 20rpx;
}

.param-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.param-input {
  flex: 1;
  height: 70rpx;
  border: 1rpx solid #e5e5e5;
  border-radius: 6rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}

.param-unit {
  font-size: 28rpx;
  color: #666;
  margin-left: 10rpx;
}

.apply-button {
  background-color: #0088ff;
  color: #fff;
  font-size: 28rpx;
  padding: 16rpx 0;
  border-radius: 6rpx;
  margin-top: 20rpx;
}

.apply-button[disabled] {
  background-color: #cccccc;
  color: #999;
}

/* 新增样式 */
.confirm-button-row {
  display: flex;
  justify-content: flex-end;
  margin-top: 20rpx;
}

.confirm-button {
  background-color: #0088ff;
  color: #fff;
  font-size: 28rpx;
  padding: 12rpx 24rpx;
  border-radius: 6rpx;
}

.confirm-button[disabled] {
  background-color: #cccccc;
  color: #999;
}

/* 设备控制标签页样式 */
.device-tabs {
  display: flex;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.device-tab {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.device-tab.active {
  color: #0088ff;
  font-weight: bold;
}

.device-tab.active:after {
  content: "";
  position: absolute;
  bottom: -2rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40%;
  height: 4rpx;
  background-color: #0088ff;
  border-radius: 2rpx;
}

/* 工业变频器风格的频率控制样式 */
.frequency-control {
  display: flex;
  margin: 20rpx 0;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  overflow: hidden;
}

.frequency-control.disabled {
  opacity: 0.6;
}

.frequency-display {
  flex: 1;
  background: #fff;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  border: 1rpx solid #e0e0e0;
  height: 80rpx;
  border-top-left-radius: 8rpx;
  border-bottom-left-radius: 8rpx;
}

.frequency-input {
  flex: 1;
  height: 80rpx;
  font-size: 40rpx;
  color: #333;
  font-weight: bold;
  text-align: right;
  background-color: transparent;
}

.frequency-unit {
  font-size: 30rpx;
  color: #666;
  margin-left: 10rpx;
  width: 40rpx;
}

.frequency-buttons {
  width: 80rpx;
  display: flex;
  flex-direction: column;
}

.frequency-button {
  height: 40rpx;
  line-height: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #0088ff;
  color: white;
  font-size: 24rpx;
}

.frequency-button.disabled {
  background-color: #cccccc;
}

.frequency-button.increase {
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.3);
}

</style> 