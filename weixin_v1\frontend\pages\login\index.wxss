.container {
  min-height: 100vh;
  padding: 0 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #fff;
}

.logo-box {
  margin-top: 120rpx;
  margin-bottom: 80rpx;
  text-align: center;
}

.logo {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 30rpx;
}

.app-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.form-box {
  width: 100%;
}

.form-item {
  display: flex;
  align-items: center;
  height: 100rpx;
  padding: 0 30rpx;
  margin-bottom: 30rpx;
  background: #f8f8f8;
  border-radius: 50rpx;
}

.form-item .wx-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
}

.remember-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20rpx 0 40rpx;
  padding: 0 20rpx;
}

.remember-pwd {
  display: flex;
  align-items: center;
}

.checkbox {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #ddd;
  border-radius: 6rpx;
  margin-right: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkbox.checked {
  background: #4CAF50;
  border-color: #4CAF50;
}

.checkbox .wx-icon {
  font-size: 24rpx;
  color: #fff;
}

.remember-pwd text,
.forget-pwd {
  font-size: 24rpx;
  color: #999;
}

.forget-pwd {
  text-decoration: underline;
}

.login-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 45rpx;
  font-size: 32rpx;
  background: #4CAF50;
}

.footer {
  position: fixed;
  bottom: 60rpx;
  font-size: 24rpx;
  color: #999;
}

.footer .link {
  color: #4CAF50;
  display: inline;
} 