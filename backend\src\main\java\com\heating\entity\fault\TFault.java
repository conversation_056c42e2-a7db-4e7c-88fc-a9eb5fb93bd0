
package com.heating.entity.fault;

import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;

@Entity
@Table(name = "t_fault")
@Data
public class TFault {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;
    
    @Column(name = "fault_no")
    private String faultNo;

    @Column(name = "heat_unit_id")
    private long heatUnitId;

    @Column(name = "house_id")
    private long houseId;
    
    @Column(name = "fault_type")
    private String faultType;

    @Column(name = "fault_source")
    private String faultSource;
    
    @Column(name = "fault_level")
    private String faultLevel;
    
    @Column(name = "fault_desc")
    private String faultDesc;
    
    @Column(name = "occur_time")
    private LocalDateTime occurTime;
    
    @Column(name = "report_user_id")
    private long reportUserId;
    
    @Column(name = "address")
    private String address;
    
    @Column(name = "report_time")
    private LocalDateTime reportTime;
    
    @Column(name = "fault_status")
    private String faultStatus;
    
    @Column(name = "alarm_id")
    private Long alarmId;

    @Column(name = "manager_id")
    private Long managerId;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
}