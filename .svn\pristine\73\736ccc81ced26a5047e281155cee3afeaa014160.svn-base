/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-load-more[data-v-2c1dd21f] {
  display: flex;
  flex-direction: row;
  height: 40px;
  align-items: center;
  justify-content: center;
}
.uni-load-more__text[data-v-2c1dd21f] {
  font-size: 14px;
  margin-left: 8px;
}
.uni-load-more__img[data-v-2c1dd21f] {
  width: 24px;
  height: 24px;
}
.uni-load-more__img--nvue[data-v-2c1dd21f] {
  color: #666666;
}
.uni-load-more__img--android[data-v-2c1dd21f],
.uni-load-more__img--ios[data-v-2c1dd21f] {
  width: 24px;
  height: 24px;
  transform: rotate(0deg);
}
.uni-load-more__img--android[data-v-2c1dd21f] {
  animation: loading-ios 1s 0s linear infinite;
}
@keyframes loading-android-2c1dd21f {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
.uni-load-more__img--ios-H5[data-v-2c1dd21f] {
  position: relative;
  animation: loading-ios-H5-2c1dd21f 1s 0s step-end infinite;
}
.uni-load-more__img--ios-H5 uni-image[data-v-2c1dd21f] {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}
@keyframes loading-ios-H5-2c1dd21f {
0% {
    transform: rotate(0deg);
}
8% {
    transform: rotate(30deg);
}
16% {
    transform: rotate(60deg);
}
24% {
    transform: rotate(90deg);
}
32% {
    transform: rotate(120deg);
}
40% {
    transform: rotate(150deg);
}
48% {
    transform: rotate(180deg);
}
56% {
    transform: rotate(210deg);
}
64% {
    transform: rotate(240deg);
}
73% {
    transform: rotate(270deg);
}
82% {
    transform: rotate(300deg);
}
91% {
    transform: rotate(330deg);
}
100% {
    transform: rotate(360deg);
}
}
.uni-load-more__img--android-MP[data-v-2c1dd21f] {
  position: relative;
  width: 24px;
  height: 24px;
  transform: rotate(0deg);
  animation: loading-ios 1s 0s ease infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-2c1dd21f] {
  position: absolute;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: solid 2px transparent;
  border-top: solid 2px #777777;
  transform-origin: center;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-2c1dd21f]:nth-child(1) {
  animation: loading-android-MP-1-2c1dd21f 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-2c1dd21f]:nth-child(2) {
  animation: loading-android-MP-2-2c1dd21f 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-2c1dd21f]:nth-child(3) {
  animation: loading-android-MP-3-2c1dd21f 1s 0s linear infinite;
}
@keyframes loading-android-2c1dd21f {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-1-2c1dd21f {
0% {
    transform: rotate(0deg);
}
50% {
    transform: rotate(90deg);
}
100% {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-2-2c1dd21f {
0% {
    transform: rotate(0deg);
}
50% {
    transform: rotate(180deg);
}
100% {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-3-2c1dd21f {
0% {
    transform: rotate(0deg);
}
50% {
    transform: rotate(270deg);
}
100% {
    transform: rotate(360deg);
}
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-popup-dialog[data-v-678a307f] {
  width: 300px;
  border-radius: 11px;
  background-color: #fff;
}
.uni-dialog-title[data-v-678a307f] {
  display: flex;
  flex-direction: row;
  justify-content: center;
  padding-top: 25px;
}
.uni-dialog-title-text[data-v-678a307f] {
  font-size: 16px;
  font-weight: 500;
}
.uni-dialog-content[data-v-678a307f] {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 20px;
}
.uni-dialog-content-text[data-v-678a307f] {
  font-size: 14px;
  color: #6C6C6C;
}
.uni-dialog-button-group[data-v-678a307f] {
  display: flex;
  flex-direction: row;
  border-top-color: #f5f5f5;
  border-top-style: solid;
  border-top-width: 1px;
}
.uni-dialog-button[data-v-678a307f] {
  display: flex;
  flex: 1;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 45px;
}
.uni-border-left[data-v-678a307f] {
  border-left-color: #f0f0f0;
  border-left-style: solid;
  border-left-width: 1px;
}
.uni-dialog-button-text[data-v-678a307f] {
  font-size: 16px;
  color: #333;
}
.uni-button-color[data-v-678a307f] {
  color: #007aff;
}
.uni-dialog-input[data-v-678a307f] {
  flex: 1;
  font-size: 14px;
  border: 1px #eee solid;
  height: 40px;
  padding: 0 10px;
  border-radius: 5px;
  color: #555;
}
.uni-popup__success[data-v-678a307f] {
  color: #4cd964;
}
.uni-popup__warn[data-v-678a307f] {
  color: #f0ad4e;
}
.uni-popup__error[data-v-678a307f] {
  color: #dd524d;
}
.uni-popup__info[data-v-678a307f] {
  color: #909399;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-popup[data-v-7db519c7] {
  position: fixed;
  z-index: 99;
}
.uni-popup.top[data-v-7db519c7], .uni-popup.left[data-v-7db519c7], .uni-popup.right[data-v-7db519c7] {
  top: 0;
}
.uni-popup .uni-popup__wrapper[data-v-7db519c7] {
  display: block;
  position: relative;
  /* iphonex 等安全区设置，底部安全区适配 */
}
.uni-popup .uni-popup__wrapper.left[data-v-7db519c7], .uni-popup .uni-popup__wrapper.right[data-v-7db519c7] {
  padding-top: 0;
  flex: 1;
}
.fixforpc-z-index[data-v-7db519c7] {
  z-index: 999;
}
.fixforpc-top[data-v-7db519c7] {
  top: 0;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-popup-message[data-v-515d10d7] {
  display: flex;
  flex-direction: row;
  justify-content: center;
}
.uni-popup-message__box[data-v-515d10d7] {
  background-color: #e1f3d8;
  padding: 10px 15px;
  border-color: #eee;
  border-style: solid;
  border-width: 1px;
  flex: 1;
}
@media screen and (min-width: 500px) {
.fixforpc-width[data-v-515d10d7] {
    margin-top: 20px;
    border-radius: 4px;
    flex: none;
    min-width: 380px;
    max-width: 50%;
}
}
.uni-popup-message-text[data-v-515d10d7] {
  font-size: 14px;
  padding: 0;
}
.uni-popup__success[data-v-515d10d7] {
  background-color: #e1f3d8;
}
.uni-popup__success-text[data-v-515d10d7] {
  color: #67C23A;
}
.uni-popup__warn[data-v-515d10d7] {
  background-color: #faecd8;
}
.uni-popup__warn-text[data-v-515d10d7] {
  color: #E6A23C;
}
.uni-popup__error[data-v-515d10d7] {
  background-color: #fde2e2;
}
.uni-popup__error-text[data-v-515d10d7] {
  color: #F56C6C;
}
.uni-popup__info[data-v-515d10d7] {
  background-color: #F2F6FC;
}
.uni-popup__info-text[data-v-515d10d7] {
  color: #909399;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.valve-detail-page {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 0.9375rem;
}
.header {
  display: flex;
  align-items: center;
  height: 2.75rem;
  background-color: #fff;
  padding: 0 0.9375rem;
  position: relative;
  box-shadow: 0 0.0625rem 0.1875rem rgba(0, 0, 0, 0.05);
}
.header .back-button {
  width: 1.875rem;
  height: 1.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.header .page-title {
  flex: 1;
  text-align: center;
  font-size: 1.0625rem;
  font-weight: 500;
  padding-right: 1.875rem;
}
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3.125rem 0;
}
.valve-content {
  padding: 0.625rem 0.9375rem;
}
.status-card {
  background-color: #fff;
  border-radius: 0.375rem;
  padding: 0.9375rem;
  margin-bottom: 0.625rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
  display: flex;
}
.status-card .status-icon {
  width: 3.75rem;
  height: 3.75rem;
  border-radius: 1.875rem;
  margin-right: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.status-card .status-icon.open {
  background-color: rgba(46, 204, 113, 0.1);
}
.status-card .status-icon.open .iconfont {
  color: #2ecc71;
}
.status-card .status-icon.closed {
  background-color: rgba(149, 165, 166, 0.1);
}
.status-card .status-icon.closed .iconfont {
  color: #95a5a6;
}
.status-card .status-icon.error {
  background-color: rgba(231, 76, 60, 0.1);
}
.status-card .status-icon.error .iconfont {
  color: #e74c3c;
}
.status-card .status-icon .iconfont {
  font-size: 1.875rem;
}
.status-card .status-info {
  flex: 1;
}
.status-card .status-info .status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}
.status-card .status-info .status-header .valve-name {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  flex: 1;
  margin-right: 0.625rem;
}
.status-card .status-info .status-detail .detail-item {
  margin-bottom: 0.375rem;
}
.status-card .status-info .status-detail .detail-item .detail-label {
  font-size: 0.8125rem;
  color: #666;
  margin-right: 0.3125rem;
}
.status-card .status-info .status-detail .detail-item .detail-value {
  font-size: 0.8125rem;
  color: #333;
  font-weight: 500;
}
.status-tag {
  padding: 0.1875rem 0.5rem;
  border-radius: 0.9375rem;
  font-size: 0.75rem;
  color: #fff;
}
.status-tag.status-open {
  background-color: #2ecc71;
}
.status-tag.status-closed {
  background-color: #95a5a6;
}
.status-tag.status-error {
  background-color: #e74c3c;
}
.control-panel {
  background-color: #fff;
  border-radius: 0.375rem;
  padding: 0.9375rem;
  margin-bottom: 0.625rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.control-panel .panel-title {
  font-size: 0.9375rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.625rem;
  position: relative;
  padding-left: 0.625rem;
}
.control-panel .panel-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0.25rem;
  width: 0.1875rem;
  height: 0.875rem;
  background-color: #1989fa;
  border-radius: 0.09375rem;
}
.control-panel .opening-control {
  margin-bottom: 0.9375rem;
}
.control-panel .opening-control .control-label {
  font-size: 0.875rem;
  color: #333;
  margin-bottom: 0.3125rem;
  display: block;
}
.control-panel .opening-control .opening-slider {
  margin: 0.625rem 0;
}
.control-panel .control-buttons {
  display: flex;
  justify-content: space-between;
}
.control-panel .control-buttons .control-btn {
  width: 30%;
  height: 2.5rem;
  line-height: 2.5rem;
  text-align: center;
  border-radius: 0.25rem;
  font-size: 0.875rem;
}
.control-panel .control-buttons .control-btn.set-btn {
  background-color: #1989fa;
  color: #fff;
}
.control-panel .control-buttons .control-btn.open-btn {
  background-color: #2ecc71;
  color: #fff;
}
.control-panel .control-buttons .control-btn.close-btn {
  background-color: #e74c3c;
  color: #fff;
}
.operation-records {
  background-color: #fff;
  border-radius: 0.375rem;
  padding: 0.9375rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.operation-records .records-title {
  font-size: 0.9375rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.625rem;
  position: relative;
  padding-left: 0.625rem;
}
.operation-records .records-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0.25rem;
  width: 0.1875rem;
  height: 0.875rem;
  background-color: #1989fa;
  border-radius: 0.09375rem;
}
.operation-records .timeline {
  position: relative;
  padding-left: 0.9375rem;
}
.operation-records .timeline::before {
  content: "";
  position: absolute;
  left: 0.3125rem;
  top: 0;
  bottom: 0;
  width: 0.0625rem;
  background-color: #e0e0e0;
}
.operation-records .timeline .timeline-item {
  position: relative;
  padding-bottom: 0.9375rem;
}
.operation-records .timeline .timeline-item:last-child {
  padding-bottom: 0;
}
.operation-records .timeline .timeline-item .timeline-dot {
  position: absolute;
  left: -0.9375rem;
  top: 0.3125rem;
  width: 0.625rem;
  height: 0.625rem;
  border-radius: 50%;
  background-color: #1989fa;
  z-index: 1;
}
.operation-records .timeline .timeline-item .timeline-content .timeline-time {
  font-size: 0.75rem;
  color: #999;
  margin-bottom: 0.25rem;
  display: block;
}
.operation-records .timeline .timeline-item .timeline-content .timeline-text {
  font-size: 0.875rem;
  color: #333;
  margin-bottom: 0.25rem;
  display: block;
}
.operation-records .timeline .timeline-item .timeline-content .timeline-operator {
  font-size: 0.75rem;
  color: #666;
  display: block;
}