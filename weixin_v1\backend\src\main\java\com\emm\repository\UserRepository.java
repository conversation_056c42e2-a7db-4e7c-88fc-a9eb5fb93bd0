package com.emm.repository;

import com.emm.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    Optional<User> findByNameOrPhone(String name, String phone);

    @Query("SELECT new map(u.id as id, u.name as name) FROM User u where u.roleId = 5")
    List<Map<Integer, String>> findRepairUsers();
}