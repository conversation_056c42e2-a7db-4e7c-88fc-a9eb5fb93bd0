<template>
  <view class="page-container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="right-area">
        <view class="record-status" :class="recordInfo.status" v-if="false">{{
          getStatusText(recordInfo.status)
        }}</view>
      </view>
    </view>

    <!-- 加载中状态显示 -->
    <view class="loading-container" v-if="loading">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 错误提示 -->
    <view class="error-container" v-if="showError">
      <view class="error-icon">!</view>
      <text class="error-message">{{ errorMessage }}</text>
      <button class="retry-button" @click="loadPatrolResults">重试</button>
    </view>

    <!-- 详情内容 -->
    <scroll-view scroll-y class="detail-container" v-if="!loading && !showError">
      <!-- 基本信息卡片 -->
      <view class="detail-card">
        <view class="card-header">
          <text class="card-title">基本信息</text>
        </view>
        <view class="card-body">
          <view class="info-item">
            <text class="info-label">巡检计划</text>
            <text class="info-value">{{ recordInfo.planName || "-" }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">执行人员</text>
            <text class="info-value">{{ recordInfo.executorName || "-" }}</text>
          </view>
          <view class="info-item" v-if="recordInfo.executorPhone">
            <text class="info-label">联系电话</text>
            <text class="info-value">{{ recordInfo.executorPhone }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">巡检地点</text>
            <text class="info-value">{{ recordInfo.locations || "-" }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">执行日期</text>
            <text class="info-value">{{ formatDateOnly(recordInfo.executionDate) }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">开始时间</text>
            <text class="info-value time-value">{{
              formatTimeOnly(recordInfo.startTime)
            }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">结束时间</text>
            <text class="info-value time-value">{{
              formatTimeOnly(recordInfo.endTime)
            }}</text>
          </view>
          <view class="info-item" v-if="recordInfo.remark">
            <text class="info-label">备注</text>
            <text class="info-value">{{ recordInfo.remark }}</text>
          </view>
        </view>
      </view>

      <!-- 巡检结果卡片 -->
      <view class="detail-card">
        <view class="card-header">
          <text class="card-title">巡检结果</text>
          <view class="result-summary">
            <text class="result-count">共 {{ patrolResults.length }} 项</text>
            <text
              class="abnormal-count"
              v-if="resultSummary && resultSummary.abnormalCount > 0"
            >
              异常 {{ resultSummary.abnormalCount }} 项
            </text>
          </view>
        </view>
        <view class="card-body">
          <view v-if="patrolResults.length === 0" class="empty-results">
            <text class="empty-text">暂无巡检结果</text>
          </view>
          <view v-else class="result-list">
            <view
              class="result-item"
              v-for="(result, index) in patrolResults"
              :key="index"
            >
              <view class="result-header">
                <text class="result-title">{{
                  result.itemName || `巡检项 ${index + 1}`
                }}</text>
                <text
                  class="result-status"
                  :class="{
                    'status-normal': result.checkResult === 'normal',
                    'status-abnormal': result.checkResult === 'abnormal',
                  }"
                >
                  {{ result.checkResult === "normal" ? "正常" : "异常" }}
                </text>
              </view>
              <view class="result-content">
                <view class="result-info">
                  <view class="info-row" v-if="result.deviceName">
                    <text class="result-label">设备信息</text>
                    <text class="result-value"
                      >{{ result.deviceName }} ({{
                        result.deviceType || "未知类型"
                      }})</text
                    >
                  </view>
                  <view class="info-row" v-if="result.categoryName">
                    <text class="result-label">检查类别</text>
                    <text class="result-value">{{ result.categoryName }}</text>
                  </view>
				  <view class="info-row" v-if="result.checkMethod">
				    <text class="result-label">检查方法</text>
				    <text class="result-value">{{ result.checkMethod }}</text>
				  </view>
				  <view class="info-row" v-if="result.checkDescription">
				    <text class="result-label">描述说明</text>
				    <text class="result-value">{{ result.checkDescription }}</text>
				  </view>
                  <view class="info-row" v-if="result.paramValue">
                    <text class="result-label">参数值</text>
                    <text class="result-value"
                      >{{ result.paramValue
                      }}{{ result.unit ? " " + result.unit : "" }}</text
                    >
                  </view>
                  <view class="info-row" v-if="result.normalRange">
                    <text class="result-label">正常范围</text>
                    <text class="result-value">{{ result.normalRange }}</text>
                  </view>
                  <view class="info-row" v-if="result.description">
                    <text class="result-label">描述</text>
                    <text class="result-value">{{ result.description }}</text>
                  </view>
                </view>

                <!-- 图片展示 -->
                <view
                  class="result-images"
                  v-if="result.images && result.images.length > 0"
                >
                  <view class="image-scroller">
                    <view
                      class="image-item"
                      v-for="(image, imgIndex) in result.images"
                      :key="imgIndex"
                      @click="previewImage(result.images, imgIndex)"
                    >
                      <image :src="getFullImageUrl(image)" mode="aspectFill"></image>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { patrolApi } from "@/utils/api.js";
import uploadUtils from "@/utils/upload.js";

export default {
  data() {
    return {
      loading: true,
      showError: false,
      errorMessage: "",
      recordId: null,
      recordInfo: {},
      patrolResults: [],
      resultSummary: null,
    };
  },
  onLoad(options) {
    if (options && options.id) {
      this.recordId = options.id;
      this.loadPatrolResults();
    } else {
      this.showError = true;
      this.errorMessage = "未找到记录ID，无法加载详情";
      this.loading = false;
    }
  },
  methods: {
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        pending: "待执行",
        processing: "执行中",
        completed: "已完成",
      };
      return statusMap[status] || "未知";
    },

    // 加载巡检结果
    loadPatrolResults() {
      // 检查API是否可用
      if (!patrolApi || !patrolApi.getPatrolResultDetail) {
        this.showError = true;
        this.errorMessage = "API服务不可用";
        this.loading = false;
        return;
      }
      // 加载巡检结果详情
      patrolApi
        .getPatrolResultDetail(this.recordId)
        .then((res) => {
          this.loading = false;

          if (res.code === 200 && res.data) {
            // 处理API响应数据
            this.recordInfo = res.data.recordInfo || {};
            
            // 处理巡检结果列表，确保图片数据格式正确
            if (res.data.resultList && Array.isArray(res.data.resultList)) {
              this.patrolResults = res.data.resultList.map(result => {
                // 确保images字段是数组
                if (result.images && !Array.isArray(result.images)) {
                  // 如果是字符串，尝试解析JSON
                  try {
                    result.images = JSON.parse(result.images);
                  } catch (e) {
                    // 如果解析失败，将其转为数组
                    result.images = result.images ? [result.images] : [];
                  }
                }
                return result;
              });
            } else {
              this.patrolResults = [];
            }
            
            this.resultSummary = res.data.summary || null;
			  // uni.hideLoading();
          } else {
            // 只显示警告，不阻止显示其他信息
			// uni.hideLoading();
            console.warn("获取巡检结果失败:", res.message);
            uni.showToast({
              title: "获取巡检结果失败",
              icon: "none",
              duration: 2000,
            });
            this.patrolResults = [];
			
          }
		
        })
        .catch((err) => {
          this.loading = false;
          console.error("加载巡检结果失败:", err);
          uni.showToast({
            title: "网络异常，无法加载巡检结果",
            icon: "none",
            duration: 2000,
          });
          this.patrolResults = [];
		  // uni.hideLoading();
        });
    },

    // 返回上一页
    goBack() {
      uni.navigateBack();
    },

    // 预览图片
    previewImage(images, current) {
      // 将所有图片路径转换为完整URL
      const fullUrls = images.map(path => this.getFullImageUrl(path));
      uni.previewImage({
        urls: fullUrls,
        current: fullUrls[current],
      });
    },

    // 获取完整的图片URL
    getFullImageUrl(path) {
      if (!path) return '';
      
      // 如果已经是完整URL，直接返回
      if (path.startsWith('http')) {
        return path;
      }
      
      // 使用uploadUtils获取完整URL
      return uploadUtils.getFileUrl(path);
    },

    // 格式化日期（不含时间）
    formatDateOnly(date) {
      if (!date) return "-";

      // 处理数组格式的日期 [year, month, day]
      if (Array.isArray(date)) {
        if (date.length >= 3) {
          const year = date[0];
          const month = String(date[1]).padStart(2, "0");
          const day = String(date[2]).padStart(2, "0");
          return `${year}-${month}-${day}`;
        }
        return date.join("-"); // 如果无法解析，返回用-连接的数组
      }

      // 其他格式处理
      try {
        const d = new Date(date);
        if (!isNaN(d.getTime())) {
          const year = d.getFullYear();
          const month = String(d.getMonth() + 1).padStart(2, "0");
          const day = String(d.getDate()).padStart(2, "0");
          return `${year}-${month}-${day}`;
        }
        return String(date);
      } catch (e) {
        console.error("格式化日期出错:", e);
        return String(date);
      }
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return "-";

      // 处理数组格式的日期时间 [year, month, day]或[year, month, day, hour, minute, second]
      if (Array.isArray(dateTime)) {
        if (dateTime.length >= 3) {
          const year = dateTime[0];
          const month = String(dateTime[1]).padStart(2, "0");
          const day = String(dateTime[2]).padStart(2, "0");

          // 如果包含时间部分
          if (dateTime.length >= 5) {
            const hour = String(dateTime[3]).padStart(2, "0");
            const minute = String(dateTime[4]).padStart(2, "0");
            const second =
              dateTime.length > 5 ? String(dateTime[5]).padStart(2, "0") : "00";
            return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
          }

          return `${year}-${month}-${day}`;
        }
        return dateTime.join("-"); // 如果无法解析，返回用-连接的数组
      }

      // 确保dateTime是字符串类型
      const dateTimeStr = String(dateTime);

      // 处理ISO格式日期时间
      if (dateTimeStr.includes("T")) {
        const date = new Date(dateTimeStr);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        const hours = String(date.getHours()).padStart(2, "0");
        const minutes = String(date.getMinutes()).padStart(2, "0");
        const seconds = String(date.getSeconds()).padStart(2, "0");
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      }

      // 如果是数字类型（时间戳），转换为日期字符串
      if (!isNaN(dateTime) && typeof dateTime === "number") {
        const date = new Date(dateTime);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        const hours = String(date.getHours()).padStart(2, "0");
        const minutes = String(date.getMinutes()).padStart(2, "0");
        const seconds = String(date.getSeconds()).padStart(2, "0");
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      }

      // 对于其他格式的字符串，尝试用Date解析
      try {
        const date = new Date(dateTimeStr);
        if (!isNaN(date.getTime())) {
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, "0");
          const day = String(date.getDate()).padStart(2, "0");
          const hours = String(date.getHours()).padStart(2, "0");
          const minutes = String(date.getMinutes()).padStart(2, "0");
          const seconds = String(date.getSeconds()).padStart(2, "0");

          // 确认是否有时间部分
          if (hours !== "00" || minutes !== "00" || seconds !== "00") {
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
          } else {
            return `${year}-${month}-${day}`;
          }
        }

        // 如果Date解析失败，尝试按原样返回或处理特殊格式
        return dateTimeStr;
      } catch (e) {
        console.error("格式化日期时间出错:", e, dateTimeStr);
        return dateTimeStr; // 如果无法格式化，返回原始值
      }
    },

    // 格式化时间，只提取时间部分 (HH:MM:SS)
    formatTimeOnly(dateTime) {
      if (!dateTime) return "-";

      // 先获取完整的日期时间格式
      const fullDateTime = this.formatDateTime(dateTime);

      // 如果包含空格（日期和时间分隔符），提取时间部分
      if (fullDateTime.includes(" ")) {
        return fullDateTime.split(" ")[1];
      }

      return fullDateTime; // 如果没有空格，可能只是时间或格式不符合预期，直接返回
    },
  },
};
</script>

<style lang="scss">
.page-container {
  padding: 20rpx;
  flex-direction: column;
  align-items: center;
  background-color: #f5f5f5;
  min-height: 100vh;
  box-sizing: border-box;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 10rpx;

  .left-area {
    display: flex;
    align-items: center;

    .icon-arrow-left {
      font-size: 40rpx;
      margin-right: 10rpx;
      color: #fff;
    }

    .header-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #fff;
    }
  }

  .right-area {
    .record-status {
      padding: 6rpx 16rpx;
      border-radius: 6rpx;
      font-size: 24rpx;
      text-align: center;
      min-width: 80rpx;

      &.pending {
        background-color: rgba(250, 173, 20, 0.1);
        color: #faad14; // 黄色
      }

      &.processing {
        background-color: rgba(24, 144, 255, 0.1);
        color: #1890ff; // 蓝色
      }

      &.completed {
        background-color: rgba(82, 196, 26, 0.1);
        color: #52c41a; // 绿色
      }
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 40vh;

  .loading-spinner {
    width: 70rpx;
    height: 70rpx;
    border: 6rpx solid #f3f3f3;
    border-top: 6rpx solid #1890ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20rpx;
  }

  .loading-text {
    font-size: 28rpx;
    color: #666;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  width: 100%;
  box-sizing: border-box;

  .error-icon {
    width: 80rpx;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    background-color: #fff2f0;
    border-radius: 50%;
    color: #f5222d;
    font-size: 50rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
  }

  .error-message {
    font-size: 28rpx;
    color: #666;
    text-align: center;
    margin-bottom: 30rpx;
  }

  .retry-button {
    background-color: #1890ff;
    color: #fff;
    font-size: 28rpx;
    padding: 16rpx 40rpx;
    border-radius: 8rpx;
    border: none;
  }
}

.detail-container {
  flex: 1;
  padding: 20rpx;
  width: 100%;
  max-width: 690rpx;
  box-sizing: border-box;
}

.detail-card {
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

  .card-header {
    padding: 30rpx;
    border-bottom: 2rpx solid #f5f5f5;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .card-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      position: relative;
      padding-left: 20rpx;

      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 6rpx;
        height: 30rpx;
        background-color: #1890ff;
        border-radius: 3rpx;
      }
    }

    .result-summary {
      display: flex;
      align-items: center;

      .result-count {
        font-size: 26rpx;
        color: #999;
        margin-right: 16rpx;
      }

      .abnormal-count {
        font-size: 26rpx;
        color: #f5222d;
      }
    }
  }

  .card-body {
    padding: 20rpx 30rpx 30rpx;
  }
}

.info-item {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 2rpx solid #f5f5f5;

  &:last-child {
    border-bottom: none;
  }

  .info-label {
    width: 160rpx;
    font-size: 28rpx;
    color: #666;
  }

  .info-value {
    flex: 1;
    font-size: 28rpx;
    color: #333;

    &.time-value {
      font-weight: bold;
    }
  }
}

.empty-results {
  padding: 60rpx 0;
  text-align: center;

  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}

.result-list {
  .result-item {
    margin-bottom: 30rpx;
    padding: 20rpx;
    background-color: #f8f8f8;
    border-radius: 8rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .result-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;

      .result-title {
        font-size: 30rpx;
        font-weight: bold;
        color: #333;
      }

      .result-status {
        padding: 4rpx 16rpx;
        border-radius: 20rpx;
        font-size: 24rpx;

        &.status-normal {
          background-color: rgba(82, 196, 26, 0.1);
          color: #52c41a;
        }

        &.status-abnormal {
          background-color: rgba(245, 34, 45, 0.1);
          color: #f5222d;
        }
      }
    }

    .result-content {
      .result-info {
        .info-row {
          display: flex;
          margin-bottom: 16rpx;

          .result-label {
            width: 120rpx;
            font-size: 26rpx;
            color: #666;
          }

          .result-value {
            flex: 1;
            font-size: 26rpx;
            color: #333;
            word-break: break-all;
          }
        }
      }

      .result-images {
        margin-top: 20rpx;

        .image-scroller {
          display: flex;
          flex-wrap: wrap;
          margin: 0 -10rpx;

          .image-item {
            width: 33.33%;
            padding: 10rpx;
            box-sizing: border-box;
            flex-shrink: 0;

            image {
              width: 100%;
              height: 200rpx;
              border-radius: 8rpx;
              background-color: #f5f5f5;
            }
          }
        }
      }
    }
  }
}
</style>
