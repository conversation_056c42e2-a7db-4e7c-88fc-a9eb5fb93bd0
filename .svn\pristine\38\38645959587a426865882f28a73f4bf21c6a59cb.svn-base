package com.heating.dto.device;

import lombok.Data;
import java.math.BigDecimal; 

@Data
public class DeviceListResponse {
    private Long deviceId;
    private String name;
    private String type;
    private String model;
    private String status;
    private LocationInfo location;
    private String lastMaintenance;
    private String nextMaintenance;
    private Integer alarmCount;
    private RealTimeData realTimeData;  
    private String manufacturer;
    private Integer period;

    @Data
    public static class LocationInfo {
        private String building;
        private String floor;
        private String room;
        private Coordinates coordinates;

        @Data
        public static class Coordinates {
            private Double lat;
            private Double lng;
        }
    }

    @Data
    public static class RealTimeData {
        private BigDecimal temperature;
        private BigDecimal pressure;
        private BigDecimal flow;
    }
} 