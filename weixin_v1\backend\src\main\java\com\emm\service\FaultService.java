package com.emm.service;

import com.emm.controller.FaultController;
import com.emm.model.Fault;
import com.emm.model.FaultAttachment;
import com.emm.model.WorkOrder;
import com.emm.model.OperationLog;
import com.emm.repository.FaultRepository;
import com.emm.repository.FaultAttachmentRepository;
import com.emm.repository.WorkOrderRepository;
import com.emm.repository.OperationLogRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;
import java.util.ArrayList;

@Service
public class FaultService {

    private static final Logger logger = LoggerFactory.getLogger(FaultService.class);

    @Autowired
    private FaultRepository faultRepository;

    @Autowired
    private FaultAttachmentRepository attachmentRepository;

    @Autowired
    private WorkOrderRepository workOrderRepository;

    @Autowired
    private OperationLogRepository operationLogRepository;

    @Transactional
    public String reportFault(Fault fault, List<FaultAttachment> attachments) {
        try {
            // 生成故障ID并设置基本信息
            String faultId = generateFaultId();
            fault.setFaultId(faultId);
            fault.setFaultStatus("待确认");
            fault.setCreatedAt(LocalDateTime.now());
            fault.setReportTime(LocalDateTime.now());

            // 保存故障信息
            logger.info("Saving fault with ID: {}", faultId);
            faultRepository.save(fault);

            // 保存附件信息
            if (attachments != null && !attachments.isEmpty()) {
                logger.info("Processing {} attachments for fault ID: {}", attachments.size(), faultId);
                for (FaultAttachment attachment : attachments) {
                    // 设置附件基本信息
                    attachment.setFaultId(faultId);
                    attachment.setCreatedAt(LocalDateTime.now());
                    
                    // 验证附件信息
                    if (attachment.getFilePath() == null || attachment.getFilePath().trim().isEmpty()) {
                        throw new IllegalArgumentException("附件路径不能为空");
                    }
                    if (attachment.getFileType() == null || attachment.getFileType().trim().isEmpty()) {
                        throw new IllegalArgumentException("附件类型不能为空");
                    }

                    // 保存附件
                    logger.debug("Saving attachment: type={}, path={}", 
                               attachment.getFileType(), 
                               attachment.getFilePath());
                    attachmentRepository.save(attachment);
                }
                logger.info("Successfully saved {} attachments for fault ID: {}", 
                          attachments.size(), 
                          faultId);
            }

            logger.info("Successfully reported fault with ID: {}", faultId);
            return faultId;
            
        } catch (Exception e) {
            logger.error("Error reporting fault: {}", e.getMessage(), e);
            throw new RuntimeException("故障上报失败: " + e.getMessage());
        }
    }

    @Transactional
    public void confirmFault(String faultId, String action, Long repairUserId, Long operatorId) {
        Fault fault = faultRepository.findById(faultId).orElseThrow(() -> new RuntimeException("Fault not found"));

        WorkOrder workOrder = null;
        if ("confirm".equals(action)) {
            fault.setFaultStatus("已确认");
            fault.setUpdatedAt(LocalDateTime.now());
            faultRepository.save(fault);

            workOrder = new WorkOrder();
            workOrder.setOrderId(generateOrderId());
            workOrder.setFaultId(faultId);
            workOrder.setStationId(fault.getStationId());
            workOrder.setRepairUserId(repairUserId);
            workOrder.setRepairContent(fault.getFaultDesc()); 
            workOrder.setOrderStatus("待接单");
            workOrder.setCreatedAt(LocalDateTime.now());
            workOrderRepository.save(workOrder); 

        } else {
            fault.setFaultStatus("已退回");
            fault.setUpdatedAt(LocalDateTime.now());
            faultRepository.save(fault);
        }

        OperationLog log = new OperationLog();
        log.setOrderId(workOrder != null ? workOrder.getOrderId() : null);
        log.setFaultId(faultId);
        log.setOperationType("confirm".equals(action) ? "故障确认" : "故障退回");
        log.setOperationDesc("confirm".equals(action) ? "管理员确认故障并生成工单" : "管理员退回故障");
        log.setOperatorId(operatorId);
        log.setCreatedAt(LocalDateTime.now());
        operationLogRepository.save(log);  // 保存操作日志
    }

    public List<Map<String, Object>> getFaultList(String status, Date date) {
        return faultRepository.findFaultList(status, date);
    }

    public Map<String, Object> getFaultDetail(String faultId) {
        try {
            logger.info("=== Fault Detail Debug ===");
            logger.info("Requested fault_id: {}", faultId);
            
            // 获取故障基本信息
            Map<String, Object> faultDetail = faultRepository.findFaultDetail(faultId);
            if (faultDetail == null) {
                throw new RuntimeException("故障信息不存在");
            }
            
            // 获取附件信息
            List<Map<String, Object>> attachments = attachmentRepository.findAttachmentsByFaultId(faultId);
            logger.debug("Attachments: {}", attachments);
            
            // 分类处理附件
            List<Map<String, Object>> images = attachments.stream()
                .filter(att -> "image".equals(att.get("file_type")))
                .collect(Collectors.toList());
                
            List<Map<String, Object>> videos = attachments.stream()
                .filter(att -> "video".equals(att.get("file_type")))
                .collect(Collectors.toList());
            
            // 构造返回结果
            Map<String, Object> attachmentsMap = new HashMap<>();
            attachmentsMap.put("images", images);
            attachmentsMap.put("videos", videos);
            
            Map<String, Object> data = new HashMap<>();
            data.put("fault", faultDetail);
            data.put("attachments", attachmentsMap);
            data.put("workOrders", new ArrayList<>()); // 暂时返回空数组
            
            return data;
            
        } catch (Exception e) {
            logger.error("Error in get_fault_detail: {}", e.getMessage(), e);
            logger.error("Error type: {}", e.getClass().getName());
            logger.error("Stacktrace:", e);
            throw new RuntimeException("获取故障详情失败: " + e.getMessage());
        }
    }

    private String generateFaultId() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
    }

    private String generateOrderId() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
    }
}