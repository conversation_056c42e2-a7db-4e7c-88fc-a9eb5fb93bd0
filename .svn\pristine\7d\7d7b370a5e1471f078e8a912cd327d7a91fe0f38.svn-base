package com.heating.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import com.heating.entity.fault.TFault;
import com.heating.entity.TManagerHeatUnit;
import java.sql.Date;
import java.util.List;
import java.util.Map;

@Repository
public interface FaultRepository extends JpaRepository<TFault, String> {

    @Query(value = "SELECT " +
            "f.id as fault_id, " +
            "f.fault_no, " +
            "f.heat_unit_id, " +
            "hu.name as heat_unit_name, " +
            "f.fault_type, " +
            "f.fault_source, " +
            "f.fault_level, " +
            "f.fault_desc, " +
            "f.occur_time, " +
            "f.report_user_id, " +
            "f.report_time, " +
            "f.fault_status " +
            "FROM t_fault f " +
            "LEFT JOIN t_heat_unit hu ON f.heat_unit_id = hu.id " +
            "LEFT JOIN t_user u ON f.report_user_id = u.id " +
            "WHERE (:status IS NULL OR f.fault_status = :status) " +
            "AND (:date IS NULL OR DATE(f.occur_time) = :date) " +
            "ORDER BY f.report_time DESC " +
            "LIMIT :offset, :pageSize", nativeQuery = true)
    List<Map<String, Object>> findFaultListWithPaging(
            @Param("status") String status,
            @Param("date") Date date,
            @Param("offset") int offset,
            @Param("pageSize") Integer pageSize);

    /**
     * 根据用户ID获取故障列表
     */
    @Query(value = "SELECT " +
            "f.id as fault_id, " +
            "f.fault_no, " +
            "f.heat_unit_id, " +
            "hu.name as heat_unit_name, " +
            "f.fault_type, " +
            "f.fault_source, " +
            "f.fault_level, " +
            "f.fault_desc, " +
            "f.occur_time, " +
            "f.report_user_id, " +
            "f.report_time, " +
            "f.fault_status " +
            "FROM t_fault f " +
            "LEFT JOIN t_heat_unit hu ON f.heat_unit_id = hu.id " +
            "LEFT JOIN t_user u ON f.report_user_id = u.id " +
            "WHERE (:status IS NULL OR f.fault_status = :status) " +
            "AND (:date IS NULL OR DATE(f.occur_time) = :date) " +
            "AND f.report_user_id = :reportUserId " +
            "ORDER BY f.report_time DESC " +
            "LIMIT :offset, :pageSize", nativeQuery = true)
    List<Map<String, Object>> findFaultListByUserWithPaging(
            @Param("status") String status,
            @Param("date") Date date,
            @Param("reportUserId") Long reportUserId,
            @Param("offset") int offset,
            @Param("pageSize") Integer pageSize);

    @Query(value = "SELECT COUNT(*) " +
            "FROM t_fault f " +
            "WHERE (:status IS NULL OR f.fault_status = :status) " +
            "AND (:date IS NULL OR DATE(f.occur_time) = :date)", nativeQuery = true)
    long countFaultList(@Param("status") String status, @Param("date") Date date);

    /**
     * 统计用户上报的故障数量
     */
    @Query(value = "SELECT COUNT(*) " +
            "FROM t_fault f " +
            "WHERE (:status IS NULL OR f.fault_status = :status) " +
            "AND (:date IS NULL OR DATE(f.occur_time) = :date) " +
            "AND f.report_user_id = :reportUserId", nativeQuery = true)
    long countFaultListByUser(@Param("status") String status, @Param("date") Date date, @Param("reportUserId") Long reportUserId);

    @Query(" SELECT f.id as fault_id,f.heatUnitId as heat_unit_id, " +
           " m.managerId as manager_id, m.heatUnitName as heat_unit_name, f.faultDesc as fault_desc, " +
           " f.faultLevel as fault_level, DATE_FORMAT(f.occurTime, '%Y-%m-%d %H:%i') as occur_time," +
           " DATE_FORMAT(f.reportTime, '%Y-%m-%d %H:%i') as report_time, " +
           " u.name as report_user_name, f.faultStatus as fault_status " +
           " FROM TFault f " +
           " LEFT JOIN TManagerHeatUnit m ON f.heatUnitId = m.heatUnitId " +
           " LEFT JOIN TUser u ON f.reportUserId = u.id " +
           " WHERE (:status IS NULL OR f.faultStatus = :status) " +
           " AND (:date IS NULL OR DATE(f.occurTime) = :date) " +
           " ORDER BY f.createdAt DESC")
    List<Map<String, Object>> findFaultList(@Param("status") String status,
                                            @Param("date") Date date);

    @Query(value = "SELECT " +
            "f.id as id, " +
            "f.faultNo as fault_no, "+
            "f.heatUnitId as heat_unit_id, " +
            "h.name as heat_unit_name, " +
            "f.faultType as fault_type, " +
            "f.faultLevel as fault_level, " +
            "f.faultDesc as fault_desc, " +
            "f.faultStatus as fault_status, " +
            "f.faultSource as fault_source, " +
            "f.address as address, " +
            "f.managerId as manager_id, " +
            "f.alarmId as alarm_id, " +
            "DATE_FORMAT(f.reportTime, '%Y-%m-%d %H:%i') as report_time, " +
            "DATE_FORMAT(f.occurTime, '%Y-%m-%d %H:%i') as occur_time, " +
            "DATE_FORMAT(f.createdAt, '%Y-%m-%d %H:%i') as created_time, " +
            "u.name as report_user_name " +
            "FROM TFault f " +
            "LEFT JOIN TUser u ON f.reportUserId = u.id " +
            "LEFT JOIN THeatUnit h ON h.id = f.heatUnitId " +
            "WHERE f.id = :faultId")
    Map<String, Object> findFaultDetail(@Param("faultId") long faultId);

    @Query(value = "SELECT COUNT(*) FROM TFault f WHERE f.faultStatus = :status")
    Long countByStatus(@Param("status") String status);

    @Query(value = "SELECT COUNT(*) FROM TFault f")
    Long getTotalCount();

    @Query(value = "SELECT " +
           "f.id as id, " +
           "h.name as heat_unit_name, " +
           "f.faultType as type, " +
           "f.faultLevel as level, " +
           "f.faultDesc as description, " +
           "f.occurTime as time, " +
           "f.managerId as manager_id, " +
           "CASE " +
           "    WHEN f.faultStatus = '待确认' THEN 'pending' " +
           "    WHEN f.faultStatus = '已确认' THEN 'processing' " +
           "    WHEN f.faultStatus = '已退回' THEN 'returned' " +
           "END as status, " +
           "f.faultStatus as status_text " +
           "FROM TFault f " +
           "LEFT JOIN THeatUnit h ON f.heatUnitId = h.id " +
           "WHERE DATE(f.occurTime) = CURRENT_DATE " +
           "ORDER BY f.occurTime DESC")
    List<Map<String, Object>> findRecentFaults();
    
    /**
     * 统计本周故障告警数量
     * @return 本周故障告警数量
     */
    @Query("SELECT COUNT(f) FROM TFault f WHERE WEEK(f.occurTime) = WEEK(CURRENT_DATE) AND YEAR(f.occurTime) = YEAR(CURRENT_DATE)")
    Long countCurrentWeekFaults();
    
    /**
     * 根据管理员ID查询故障列表
     */
    @Query("SELECT f.id as fault_id, h.name as heat_unit_name, f.faultDesc as fault_desc, " +
           "f.faultLevel as fault_level, DATE_FORMAT(f.occurTime, '%Y-%m-%d %H:%i') as occur_time," +
           "DATE_FORMAT(f.reportTime, '%Y-%m-%d %H:%i') as report_time, " +
           "u.name as report_user_name, f.faultStatus as fault_status " +
           "FROM TFault f " +
           "LEFT JOIN THeatUnit h ON f.heatUnitId = h.id " +
           "LEFT JOIN TUser u ON f.reportUserId = u.id " +
           "WHERE f.managerId = :managerId " +
           "ORDER BY f.createdAt DESC")
    List<Map<String, Object>> findByManagerId(@Param("managerId") Long managerId);
}