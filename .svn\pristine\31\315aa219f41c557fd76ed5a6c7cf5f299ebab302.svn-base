<<<<<<< .mine
package com.heating.dto.fault;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class FaultSetStatusRequest {
    @JsonProperty("fault_id")
    private int faultId;
    
    @JsonProperty("operator_id")
    private int operatorId;
    
    @JsonProperty("fault_status")
    private String faultStatus;
    
    @JsonProperty("manager_id")
    private Long managerId;
    
    @JsonProperty("repair_user_id")
    private Long repairUserId;
    
    @JsonProperty("heat_unit_id")
    private Long heatUnitId;
}
||||||| .r0
=======
package com.heating.dto.fault;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class FaultSetStatusRequest {
    @JsonProperty("fault_id")
    private int faultId;
    
    @JsonProperty("operator_id")
    private int operatorId;
    
    @JsonProperty("fault_status")
    private String faultStatus;
    
    @JsonProperty("manager_id")
    private Long managerId;
    
    @JsonProperty("repair_user_id")
    private Long repairUserId;
}
>>>>>>> .r5108
