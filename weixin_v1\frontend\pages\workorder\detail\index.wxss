.container {
  padding: 24rpx;
  background: #F7F8FA;
  min-height: 100vh;
}

.section {
  background: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.03);
}

.section-title {
  font-size: 32rpx;
  color: #323233;
  font-weight: 500;
  margin-bottom: 24rpx;
  position: relative;
  padding-left: 16rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 28rpx;
  background: #1989FA;
  border-radius: 3rpx;
}

.info-item {
  display: flex;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  line-height: 1.6;
  padding: 0 8rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  color: #969799;
  min-width: 160rpx;
  padding-right: 20rpx;
}

.value {
  color: #323233;
  flex: 1;
}

.status-pending {
  color: #FF976A;
  font-weight: 500;
}

.status-processing {
  color: #1989FA;
  font-weight: 500;
}

.status-completed {
  color: #07C160;
  font-weight: 500;
}

.status-returned {
  color: #EE0A24;
  font-weight: 500;
}

.input-area {
  flex: 1;
  height: 160rpx;
  border: 2rpx solid #EBEDF0;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 28rpx;
  color: #323233;
  background: #FFFFFF;
}

.action-buttons {
  margin-top: 32rpx;
  display: flex;
  justify-content: center;
  gap: 24rpx;
}

.btn-edit, .btn-complete {
  min-width: 180rpx;
  height: 72rpx;
  line-height: 72rpx;
  text-align: center;
  border-radius: 36rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
}

.btn-edit {
  background: #1989FA;
  color: #FFFFFF;
}

.btn-edit::after {
  border: none;
}

.btn-complete {
  background: #07C160;
  color: #FFFFFF;
}

.btn-complete::after {
  border: none;
} 

.image-preview {
  width: 180rpx;
  height: 180rpx;
  border-radius: 8rpx;
  background: #F7F8FA;
}

.file-type {
  font-size: 24rpx;
  color: #969799;
  margin-top: 8rpx;
}

.log-list {
  border-left: 2rpx solid #EBEDF0;
  padding-left: 24rpx;
  margin: 8rpx 0 0 8rpx;
}

.log-item {
  position: relative;
  padding: 16rpx 0;
}

.log-item::before {
  content: '';
  position: absolute;
  left: -31rpx;
  top: 28rpx;
  width: 14rpx;
  height: 14rpx;
  background: #1989FA;
  border-radius: 50%;
  border: 4rpx solid #E8F3FF;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.log-type {
  font-size: 28rpx;
  color: #323233;
  font-weight: 500;
}

.log-time {
  font-size: 24rpx;
  color: #969799;
}

.log-content {
  font-size: 26rpx;
  color: #646566;
  line-height: 1.5;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 16rpx;
}

.image-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
}

.fault-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
  background: #F7F8FA;
}

.upload-btn {
  width: 160rpx;
  height: 160rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 16rpx;
}
 
.upload-box {
  margin-bottom: 20rpx;
}

.form-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}
.form-item:last-child {
  border-bottom: none;
}

.image-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
}
.image-item image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.delete-btn {
  position: absolute;
  top: -16rpx;
  right: -16rpx;
  background: rgba(0, 0, 0, 0.5);
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}
.delete-btn .wx-icon {
  font-size: 24rpx;
  color: #fff;
}

.upload-actions {
  display: flex;
  gap: 20rpx;
}
.upload-btn .wx-icon {
  font-size: 48rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.upload-text {
  font-size: 24rpx;
  color: #999;
}
.level-1 {
  color: #e6a23c; 
}

.level-2 {
  color: #f56c6c; 
}

.level-3 {
  color: #f56c6c;
  font-weight: bold;
}