/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-load-more[data-v-2c1dd21f] {
  display: flex;
  flex-direction: row;
  height: 40px;
  align-items: center;
  justify-content: center;
}
.uni-load-more__text[data-v-2c1dd21f] {
  font-size: 14px;
  margin-left: 8px;
}
.uni-load-more__img[data-v-2c1dd21f] {
  width: 24px;
  height: 24px;
}
.uni-load-more__img--nvue[data-v-2c1dd21f] {
  color: #666666;
}
.uni-load-more__img--android[data-v-2c1dd21f],
.uni-load-more__img--ios[data-v-2c1dd21f] {
  width: 24px;
  height: 24px;
  transform: rotate(0deg);
}
.uni-load-more__img--android[data-v-2c1dd21f] {
  animation: loading-ios 1s 0s linear infinite;
}
@keyframes loading-android-2c1dd21f {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
.uni-load-more__img--ios-H5[data-v-2c1dd21f] {
  position: relative;
  animation: loading-ios-H5-2c1dd21f 1s 0s step-end infinite;
}
.uni-load-more__img--ios-H5 uni-image[data-v-2c1dd21f] {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}
@keyframes loading-ios-H5-2c1dd21f {
0% {
    transform: rotate(0deg);
}
8% {
    transform: rotate(30deg);
}
16% {
    transform: rotate(60deg);
}
24% {
    transform: rotate(90deg);
}
32% {
    transform: rotate(120deg);
}
40% {
    transform: rotate(150deg);
}
48% {
    transform: rotate(180deg);
}
56% {
    transform: rotate(210deg);
}
64% {
    transform: rotate(240deg);
}
73% {
    transform: rotate(270deg);
}
82% {
    transform: rotate(300deg);
}
91% {
    transform: rotate(330deg);
}
100% {
    transform: rotate(360deg);
}
}
.uni-load-more__img--android-MP[data-v-2c1dd21f] {
  position: relative;
  width: 24px;
  height: 24px;
  transform: rotate(0deg);
  animation: loading-ios 1s 0s ease infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-2c1dd21f] {
  position: absolute;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: solid 2px transparent;
  border-top: solid 2px #777777;
  transform-origin: center;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-2c1dd21f]:nth-child(1) {
  animation: loading-android-MP-1-2c1dd21f 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-2c1dd21f]:nth-child(2) {
  animation: loading-android-MP-2-2c1dd21f 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-2c1dd21f]:nth-child(3) {
  animation: loading-android-MP-3-2c1dd21f 1s 0s linear infinite;
}
@keyframes loading-android-2c1dd21f {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-1-2c1dd21f {
0% {
    transform: rotate(0deg);
}
50% {
    transform: rotate(90deg);
}
100% {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-2-2c1dd21f {
0% {
    transform: rotate(0deg);
}
50% {
    transform: rotate(180deg);
}
100% {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-3-2c1dd21f {
0% {
    transform: rotate(0deg);
}
50% {
    transform: rotate(270deg);
}
100% {
    transform: rotate(360deg);
}
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.payment-stats-container {
  padding: 0.9375rem;
  background-color: #f8f8f8;
  min-height: 100vh;
}
.page-header {
  margin-bottom: 0.9375rem;
}
.page-header .page-title {
  font-size: 1.125rem;
  font-weight: bold;
  color: #333;
}
.query-form {
  background-color: #fff;
  border-radius: 0.375rem;
  padding: 0.9375rem;
  margin-bottom: 0.9375rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.query-form .form-item {
  margin-bottom: 0.625rem;
}
.query-form .form-item .form-label {
  display: block;
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 0.3125rem;
}
.query-form .form-item .form-picker {
  height: 2.5rem;
  border: 1px solid #e8e8e8;
  border-radius: 0.25rem;
  padding: 0 0.625rem;
  display: flex;
  align-items: center;
  background-color: #f8f8f8;
}
.query-form .form-item .form-picker .picker-text {
  font-size: 0.875rem;
  color: #333;
}
.query-form .form-item .date-range {
  display: flex;
  align-items: center;
}
.query-form .form-item .date-range .date-picker {
  flex: 1;
  height: 2.5rem;
  border: 1px solid #e8e8e8;
  border-radius: 0.25rem;
  padding: 0 0.625rem;
  display: flex;
  align-items: center;
  background-color: #f8f8f8;
}
.query-form .form-item .date-range .date-picker .picker-text {
  font-size: 0.875rem;
  color: #333;
}
.query-form .form-item .date-range .date-separator {
  margin: 0 0.625rem;
  color: #999;
}
.query-form .query-btn {
  background-color: #1890ff;
  color: #fff;
  border-radius: 0.25rem;
  height: 2.5rem;
  line-height: 2.5rem;
  font-size: 0.875rem;
  margin-top: 0.625rem;
}
.query-form .query-btn:active {
  background-color: #0e80eb;
}
.stats-cards {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -0.3125rem 0.9375rem;
}
.stats-cards .stats-card {
  width: calc(50% - 0.625rem);
  margin: 0 0.3125rem 0.625rem;
  background-color: #fff;
  border-radius: 0.375rem;
  padding: 0.9375rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.stats-cards .stats-card .stats-value {
  font-size: 1.125rem;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 0.3125rem;
}
.stats-cards .stats-card .stats-label {
  font-size: 0.75rem;
  color: #666;
}
.stats-cards .stats-card:nth-child(3) .stats-value {
  color: #faad14;
}
.stats-cards .stats-card:nth-child(4) .stats-value {
  color: #52c41a;
}
.loading-container {
  padding: 1.25rem 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
.chart-container {
  background-color: #fff;
  border-radius: 0.375rem;
  padding: 0.9375rem;
  margin-bottom: 0.9375rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.chart-container .chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.9375rem;
}
.chart-container .chart-header .chart-title {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
}
.chart-container .chart-header .chart-tabs {
  display: flex;
}
.chart-container .chart-header .chart-tabs .chart-tab {
  font-size: 0.875rem;
  color: #666;
  margin-left: 0.9375rem;
  padding-bottom: 0.3125rem;
  position: relative;
}
.chart-container .chart-header .chart-tabs .chart-tab.active {
  color: #1890ff;
}
.chart-container .chart-header .chart-tabs .chart-tab.active:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 0.125rem;
  background-color: #1890ff;
  border-radius: 0.0625rem;
}
.chart-container .chart-content {
  min-height: 15.625rem;
}
.chart-container .chart-content .chart-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}
.chart-container .chart-content .chart-placeholder .trend-chart-mock {
  width: 100%;
  height: 15.625rem;
  padding: 0.625rem 0;
}
.chart-container .chart-content .chart-placeholder .trend-chart-mock .chart-legend {
  display: flex;
  justify-content: center;
  margin-bottom: 0.625rem;
}
.chart-container .chart-content .chart-placeholder .trend-chart-mock .chart-legend .legend-item {
  display: flex;
  align-items: center;
  margin: 0 0.625rem;
}
.chart-container .chart-content .chart-placeholder .trend-chart-mock .chart-legend .legend-item .legend-color {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 0.125rem;
  margin-right: 0.3125rem;
}
.chart-container .chart-content .chart-placeholder .trend-chart-mock .chart-legend .legend-item .legend-text {
  font-size: 0.75rem;
  color: #666;
}
.chart-container .chart-content .chart-placeholder .trend-chart-mock .chart-bars {
  flex: 1;
  display: flex;
  justify-content: space-around;
  align-items: flex-end;
  height: 12.5rem;
}
.chart-container .chart-content .chart-placeholder .trend-chart-mock .chart-bars .chart-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 16.6666666667%;
}
.chart-container .chart-content .chart-placeholder .trend-chart-mock .chart-bars .chart-bar .bar-label {
  font-size: 0.75rem;
  color: #999;
  margin-top: 0.3125rem;
}
.chart-container .chart-content .chart-placeholder .trend-chart-mock .chart-bars .chart-bar .bar-wrapper {
  width: 1.875rem;
  height: 9.375rem;
  display: flex;
  flex-direction: column-reverse;
  position: relative;
}
.chart-container .chart-content .chart-placeholder .trend-chart-mock .chart-bars .chart-bar .bar-wrapper .bar-value {
  position: absolute;
  bottom: 0;
  width: 100%;
  transition: height 0.5s ease;
}
.chart-container .chart-content .chart-placeholder .trend-chart-mock .chart-bars .chart-bar .bar-wrapper .bar-value.paid {
  background-color: #1890ff;
  border-radius: 0.25rem 0.25rem 0 0;
  z-index: 2;
}
.chart-container .chart-content .chart-placeholder .trend-chart-mock .chart-bars .chart-bar .bar-wrapper .bar-value.unpaid {
  background-color: #faad14;
  border-radius: 0 0 0.25rem 0.25rem;
  z-index: 1;
}
.chart-container .chart-content .chart-placeholder .pie-chart-mock {
  width: 100%;
  height: 15.625rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.chart-container .chart-content .chart-placeholder .pie-chart-mock .pie-chart {
  width: 9.375rem;
  height: 9.375rem;
  position: relative;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 0.9375rem;
}
.chart-container .chart-content .chart-placeholder .pie-chart-mock .pie-chart .pie-slice {
  position: absolute;
  width: 100%;
  height: 100%;
}
.chart-container .chart-content .chart-placeholder .pie-chart-mock .pie-chart .pie-slice.paid {
  background-color: #1890ff;
}
.chart-container .chart-content .chart-placeholder .pie-chart-mock .pie-chart .pie-slice.unpaid {
  background-color: #faad14;
}
.chart-container .chart-content .chart-placeholder .pie-chart-mock .pie-chart .pie-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 6.25rem;
  height: 6.25rem;
  background-color: white;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 0.3125rem rgba(0, 0, 0, 0.05);
}
.chart-container .chart-content .chart-placeholder .pie-chart-mock .pie-chart .pie-center .pie-percent {
  font-size: 1.25rem;
  font-weight: bold;
  color: #1890ff;
}
.chart-container .chart-content .chart-placeholder .pie-chart-mock .pie-chart .pie-center .pie-label {
  font-size: 0.75rem;
  color: #666;
}
.chart-container .chart-content .chart-placeholder .pie-chart-mock .pie-legend {
  display: flex;
  justify-content: center;
}
.chart-container .chart-content .chart-placeholder .pie-chart-mock .pie-legend .legend-item {
  display: flex;
  align-items: center;
  margin: 0 0.9375rem;
}
.chart-container .chart-content .chart-placeholder .pie-chart-mock .pie-legend .legend-item .legend-color {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 0.125rem;
  margin-right: 0.3125rem;
}
.chart-container .chart-content .chart-placeholder .pie-chart-mock .pie-legend .legend-item .legend-info {
  display: flex;
  flex-direction: column;
}
.chart-container .chart-content .chart-placeholder .pie-chart-mock .pie-legend .legend-item .legend-info .legend-text {
  font-size: 0.75rem;
  color: #666;
}
.chart-container .chart-content .chart-placeholder .pie-chart-mock .pie-legend .legend-item .legend-info .legend-value {
  font-size: 0.6875rem;
  color: #999;
}
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3.125rem 0;
}
.empty-container .empty-icon {
  width: 6.25rem;
  height: 6.25rem;
  margin-bottom: 0.625rem;
}
.empty-container .empty-text {
  font-size: 0.875rem;
  color: #999;
}