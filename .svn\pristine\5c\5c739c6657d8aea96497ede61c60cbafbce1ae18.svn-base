package com.heating.dto.patrol;

import lombok.Data;
import java.time.LocalDate;
import java.util.List;

/**
 * 巡检计划详情响应DTO
 */
@Data
public class PatrolPlanDetailResponse {
    /**
     * 巡检计划ID
     */
    private Long id;
    
    /**
     * 计划编号
     */
    private String planNo;
    
    /**
     * 巡检计划名称
     */
    private String name;
    
    /**
     * 巡检类型：换热站巡检，日常巡检，设备巡检，管道巡检，阀门巡检
     */
    private String patrolType;
    
    /**
     * 开始日期
     */
    private LocalDate startDate;
    
    /**
     * 结束日期
     */
    private LocalDate endDate;
    
    /**
     * 巡检执行人IDs，使用Object类型以适应Integer或Long
     */
    private List<Object> executorIds;
    
    /**
     * 巡检执行人姓名列表
     */
    private List<String> executorNames;
    
    /**
     * 调度类型: daily/weekly/monthly/custom
     */
    private String scheduleType;
    
    /**
     * 巡检间隔
     */
    private Integer scheduleInterval;
    
    /**
     * 每周巡检日期（1-7代表周一到周日）
     */
    private List<Integer> scheduleWeekDays;
    
    /**
     * 每月巡检日期
     */
    private List<Integer> scheduleMonthDays;
    
    /**
     * 巡检设备ID列表
     * 改为List<Object>以支持数据库中可能存储的Integer或String类型
     */
    private List<Object> deviceIds;
    
    /**
     * 巡检地点
     */
    private String locations;
    
    /**
     * 状态: pending-待执行, processing-执行中, completed-已完成
     */
    private String status;
    
    /**
     * 巡检项目列表
     */
    private List<PatrolItemDetail> patrolItems;
    
    /**
     * 巡检项目详情
     */
    @Data
    public static class PatrolItemDetail {
      
        /**
         * 巡检项目ID
         */
        private Long id;
        
        /**
         * 设备ID
         */
        private String deviceName;    

        /**
         * 巡检项目名称
         */
        private String itemName; 

        /**
         * 所属类别名称
         */
        private String categoryName;

        /**
         * 参数类型
         */
        private String paramType;

        /**
         * 单位
        */
        private String unit;

        /**
         * 正常范围
        */
        private String normalRange;

        /**
         * 检查方法
        */
        private String checkMethod; 

        /** 
         * 重要性
        */
        private String importance;

        /**
         * 描述说明
        */
        private String description;
 
    }
} 