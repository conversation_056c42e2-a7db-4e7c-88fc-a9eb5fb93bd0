<template>
	<view class="message-settings-container">
	<!-- 	<view class="page-header">
			<text class="page-title">消息设置</text>
		</view> -->
		
		<view class="settings-card">
			<!-- <view class="settings-section">
				<view class="section-title">工单通知</view>
				
				<view class="setting-item">
					<view class="setting-info">
						<text class="setting-name">新工单通知</text>
						<text class="setting-desc">当有新的工单分配给您时通知</text>
					</view>
					<switch :checked="settings.newOrderNotice" @change="toggleSetting('newOrderNotice')" color="#1890ff" />
				</view>
				
				<view class="setting-item">
					<view class="setting-info">
						<text class="setting-name">工单状态变更</text>
						<text class="setting-desc">当工单状态发生变化时通知</text>
					</view>
					<switch :checked="settings.orderStatusNotice" @change="toggleSetting('orderStatusNotice')" color="#1890ff" />
				</view>
				
				<view class="setting-item">
					<view class="setting-info">
						<text class="setting-name">工单即将超时</text>
						<text class="setting-desc">当工单临近超时时发送提醒</text>
					</view>
					<switch :checked="settings.orderTimeoutNotice" @change="toggleSetting('orderTimeoutNotice')" color="#1890ff" />
				</view>
			</view>
			
			<view class="settings-section">
				<view class="section-title">巡检通知</view>
				
				<view class="setting-item">
					<view class="setting-info">
						<text class="setting-name">巡检计划提醒</text>
						<text class="setting-desc">当有新的巡检计划时通知</text>
					</view>
					<switch :checked="settings.patrolPlanNotice" @change="toggleSetting('patrolPlanNotice')" color="#1890ff" />
				</view>
				
				<view class="setting-item">
					<view class="setting-info">
						<text class="setting-name">巡检计划延期</text>
						<text class="setting-desc">当巡检计划被延期时通知</text>
					</view>
					<switch :checked="settings.patrolDelayNotice" @change="toggleSetting('patrolDelayNotice')" color="#1890ff" />
				</view>
			</view> -->
			
			<view class="settings-section">
				<view class="section-title">系统通知</view>
				
				<view class="setting-item">
					<view class="setting-info">
						<text class="setting-name">系统更新</text>
						<text class="setting-desc">当系统有更新时通知</text>
					</view>
					<switch :checked="settings.systemUpdateNotice" @change="toggleSetting('systemUpdateNotice')" color="#1890ff" />
				</view>
				
				<view class="setting-item">
					<view class="setting-info">
						<text class="setting-name">账号安全</text>
						<text class="setting-desc">当账号出现安全风险时通知</text>
					</view>
					<switch :checked="settings.accountSecurityNotice" @change="toggleSetting('accountSecurityNotice')" color="#1890ff" />
				</view>
			</view>
			
			<view class="settings-section">
				<view class="section-title">通知方式</view>
				
				<view class="setting-item">
					<view class="setting-info">
						<text class="setting-name">应用内通知</text>
						<text class="setting-desc">在应用内收到通知消息</text>
					</view>
					<switch :checked="settings.inAppNotice" @change="toggleSetting('inAppNotice')" color="#1890ff" />
				</view>
				
				<view class="setting-item">
					<view class="setting-info">
						<text class="setting-name">短信通知</text>
						<text class="setting-desc">通过短信接收重要通知</text>
					</view>
					<switch :checked="settings.smsNotice" @change="toggleSetting('smsNotice')" color="#1890ff" />
				</view>
			</view>
		</view>
		
		<view class="note-text">注：短信通知可能会产生额外费用，请谨慎开启</view>
		
		<view class="submit-btn" @click="saveSettings">保存设置</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			settings: {
				newOrderNotice: true,
				orderStatusNotice: true,
				orderTimeoutNotice: true,
				patrolPlanNotice: true,
				patrolDelayNotice: false,
				systemUpdateNotice: true,
				accountSecurityNotice: true,
				inAppNotice: true,
				smsNotice: false
			}
		}
	},
	onLoad() {
		// 加载用户的消息设置
		this.loadMessageSettings();
	},
	methods: {
		// 加载消息设置
		loadMessageSettings() {
			// 从本地存储获取消息设置
			const settings = uni.getStorageSync('messageSettings');
			if (settings) {
				this.settings = Object.assign({}, this.settings, settings);
			}
		},
		
		// 切换设置状态
		toggleSetting(key) {
			this.settings[key] = !this.settings[key];
		},
		
		// 保存设置
		saveSettings() {
			uni.showLoading({
				title: '保存中...'
			});
			
			// 在实际应用中，这里应该调用API保存消息设置
			// 这里模拟一个保存成功的情况
			setTimeout(() => {
				uni.hideLoading();
				
				// 保存到本地存储
				uni.setStorageSync('messageSettings', this.settings);
				
				uni.showToast({
					title: '设置已保存',
					icon: 'success'
				});
				
				// 返回上一页
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			}, 1000);
		}
	}
}
</script>

<style lang="scss">
.message-settings-container {
	background-color: #f5f7fa;
	min-height: 100vh;
	padding-bottom: 40rpx;
}

.page-header {
	background-color: #fff;
	padding: 30rpx;
	border-bottom: 1rpx solid #eee;
	
	.page-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}
}

.settings-card {
	margin: 30rpx;
	background-color: #fff;
	border-radius: 12rpx;
	box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.05);
	overflow: hidden;
	
	.settings-section {
		padding: 0 20rpx;
		
		.section-title {
			font-size: 30rpx;
			font-weight: bold;
			padding: 30rpx 10rpx 20rpx;
			color: #333;
			position: relative;
			display: block;
			width: 100%;
			box-sizing: border-box;
			&::before {
				content: '';
				position: absolute;
				left: 0;
				top: 50%;
				transform: translateY(-50%);
				width: 6rpx;
				height: 30rpx;
				background-color: $uni-color-primary;
				border-radius: 3rpx;
			}
			padding-left: 20rpx;
		}
		
		.setting-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 24rpx 10rpx;
			border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
			
			&:last-child {
				border-bottom: none;
			}
			
			.setting-info {
				flex: 1;
				
				.setting-name {
					font-size: 28rpx;
					color: #333;
					margin-bottom: 8rpx;
					display: block;
				}
				
				.setting-desc {
					font-size: 24rpx;
					color: #999;
					display: block;
				}
			}
			
			switch {
				transform: scale(0.8);
				transform-origin: right center;
				margin-left: 20rpx;
				/* 修复在某些机型上的显示问题 */
				flex-shrink: 0;
				min-width: 80rpx;
			}
		}
	}
}

.note-text {
	font-size: 24rpx;
	color: #999;
	padding: 0 40rpx;
	margin-top: 10rpx;
}

.submit-btn {
	margin: 60rpx 30rpx;
	height: 90rpx;
	line-height: 90rpx;
	background-color: $uni-color-primary;
	color: #fff;
	text-align: center;
	border-radius: 12rpx;
	font-size: 32rpx;
	box-shadow: 0 6rpx 20rpx rgba(24, 144, 255, 0.3);
	font-weight: bold;
	
	&:active {
		opacity: 0.9;
		transform: translateY(2rpx);
	}
}
</style> 