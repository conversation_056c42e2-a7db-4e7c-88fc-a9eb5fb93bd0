package com.heating.entity.order;

import lombok.Data;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 工单耗材实体类
 */
@Entity
@Table(name = "t_work_order_material")
@Data
public class TWorkOrderMaterial {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 关联的工单ID
     */
    @Column(name = "work_order_id")
    private Long workOrderId;


    /**
     * 材料名称
     */
    @Column(name = "material_name", nullable = false)
    private String materialName;

    /**
     * 使用数量
     */
    @Column(name = "quantity_used", nullable = false)
    private BigDecimal quantityUsed;

    /**
     * 记录时间
     */
    @Column(name = "recorded_at")
    private LocalDateTime recordedAt;
} 