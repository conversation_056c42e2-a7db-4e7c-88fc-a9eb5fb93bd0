/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.device-detail-container {
  padding: 0.9375rem;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.detail-card {
  background-color: #fff;
  border-radius: 0.375rem;
  padding: 0.9375rem;
  margin-bottom: 0.9375rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.detail-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}
.detail-card .card-header .card-title {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
  position: relative;
  padding-left: 0.625rem;
}
.detail-card .card-header .card-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0.25rem;
  width: 0.1875rem;
  height: 0.875rem;
  background-color: #1890ff;
  border-radius: 0.09375rem;
}
.detail-card .card-header .device-status {
  padding: 0.125rem 0.5rem;
  font-size: 0.75rem;
  border-radius: 0.625rem;
  color: #fff;
}
.detail-card .card-header .device-status.online {
  background-color: #52c41a;
}
.detail-card .card-header .device-status.offline {
  background-color: #666;
}
.detail-card .card-header .device-status.fault {
  background-color: #f5222d;
}
.detail-card .card-header .refresh-btn, .detail-card .card-header .view-all {
  font-size: 0.8125rem;
  color: #1890ff;
}
.info-item {
  display: flex;
  margin-bottom: 0.625rem;
}
.info-item .info-label {
  width: 5.625rem;
  font-size: 0.875rem;
  color: #666;
}
.info-item .info-value {
  flex: 1;
  font-size: 0.875rem;
  color: #333;
}
.status-panel {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 0.9375rem;
}
.status-panel .status-item {
  width: 33.33%;
  text-align: center;
  margin-bottom: 0.9375rem;
}
.status-panel .status-item .metric-value {
  font-size: 1.125rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.25rem;
}
.status-panel .status-item .metric-value.alert {
  color: #f5222d;
}
.status-panel .status-item .metric-label {
  font-size: 0.8125rem;
  color: #666;
}
.chart-container {
  margin-top: 0.625rem;
}
.chart-container .chart-placeholder {
  height: 9.375rem;
  background-color: #f5f5f5;
  border-radius: 0.25rem;
  display: flex;
  justify-content: center;
  align-items: center;
}
.chart-container .chart-placeholder uni-text {
  font-size: 0.875rem;
  color: #666;
}
.alarm-list .alarm-item {
  display: flex;
  margin-bottom: 0.625rem;
}
.alarm-list .alarm-item .alarm-icon {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  margin-top: 0.375rem;
  margin-right: 0.5rem;
}
.alarm-list .alarm-item .alarm-icon.warning {
  background-color: #faad14;
}
.alarm-list .alarm-item .alarm-icon.error {
  background-color: #f5222d;
}
.alarm-list .alarm-item .alarm-content {
  flex: 1;
}
.alarm-list .alarm-item .alarm-content .alarm-title {
  font-size: 0.875rem;
  color: #333;
  margin-bottom: 0.25rem;
}
.alarm-list .alarm-item .alarm-content .alarm-desc {
  font-size: 0.8125rem;
  color: #666;
  margin-bottom: 0.25rem;
}
.alarm-list .alarm-item .alarm-content .alarm-time {
  font-size: 0.75rem;
  color: #666;
}
.maintenance-list .maintenance-item {
  display: flex;
  margin-bottom: 0.9375rem;
}
.maintenance-list .maintenance-item .maintenance-time {
  width: 3.75rem;
  margin-right: 0.625rem;
}
.maintenance-list .maintenance-item .maintenance-time .date {
  display: block;
  font-size: 0.8125rem;
  color: #666;
  margin-bottom: 0.125rem;
}
.maintenance-list .maintenance-item .maintenance-time .time {
  display: block;
  font-size: 0.75rem;
  color: #666;
}
.maintenance-list .maintenance-item .maintenance-content {
  flex: 1;
  position: relative;
  padding-left: 0.9375rem;
}
.maintenance-list .maintenance-item .maintenance-content::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 0.0625rem;
  background-color: #ddd;
}
.maintenance-list .maintenance-item .maintenance-content::after {
  content: "";
  position: absolute;
  left: -0.125rem;
  top: 0.3125rem;
  width: 0.3125rem;
  height: 0.3125rem;
  border-radius: 50%;
  background-color: #1890ff;
}
.maintenance-list .maintenance-item .maintenance-content .maintenance-title {
  font-size: 0.875rem;
  color: #333;
  margin-bottom: 0.25rem;
}
.maintenance-list .maintenance-item .maintenance-content .maintenance-desc {
  font-size: 0.8125rem;
  color: #666;
  margin-bottom: 0.25rem;
}
.maintenance-list .maintenance-item .maintenance-content .maintenance-operator {
  font-size: 0.75rem;
  color: #666;
}
.maintenance-list .empty-list {
  text-align: center;
  padding: 1.25rem 0;
}
.maintenance-list .empty-list uni-text {
  font-size: 0.875rem;
  color: #666;
}
.action-buttons {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 0.625rem 0.9375rem;
  background-color: #fff;
  box-shadow: 0 -0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
  display: flex;
}
.action-buttons .action-btn {
  flex: 1;
  height: 2.5rem;
  border-radius: 1.25rem;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 0.875rem;
  margin: 0 0.3125rem;
}
.action-buttons .action-btn .iconfont {
  margin-right: 0.25rem;
}
.action-buttons .action-btn.primary {
  background-color: #1890ff;
  color: #fff;
}
.action-buttons .action-btn.warning {
  background-color: #fff;
  color: #faad14;
  border: 1px solid #faad14;
}