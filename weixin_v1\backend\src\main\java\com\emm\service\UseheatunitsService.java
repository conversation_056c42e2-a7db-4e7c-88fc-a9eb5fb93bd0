package com.emm.service;

import org.springframework.beans.factory.annotation.Autowired; 
import org.springframework.stereotype.Service;

import com.emm.repository.UseHeatUnitRepository; 

import java.util.List;
import java.util.Map;

@Service
public class UseheatunitsService {
 
    @Autowired
    private UseHeatUnitRepository useHeatUnitRepository;

    public List<Map<Integer, String>> getUseheatunits() {
        return useHeatUnitRepository.findUseheatunits();
    }
    
} 