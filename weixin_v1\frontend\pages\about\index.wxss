.container {
  padding: 30rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.logo-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
}

.logo {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 30rpx;
}

.app-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.version {
  font-size: 24rpx;
  color: #999;
}

.info-list {
  margin-bottom: 40rpx;
}

.info-group {
  background: #fff;
  border-radius: 16rpx;
  padding: 0 30rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #eee;
}

.info-item:last-child {
  border-bottom: none;
}

.label {
  font-size: 28rpx;
  color: #666;
}

.value {
  font-size: 28rpx;
  color: #333;
}

.copyright {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  padding: 40rpx 0;
} 