package com.emm.service;

import com.emm.dto.LoginRequest;
import com.emm.dto.LoginResponse;
import com.emm.model.User;
import com.emm.repository.UserRepository;
import com.emm.util.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Optional;

@Service
public class AuthService implements UserDetailsService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private JwtUtil jwtUtil;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        Optional<User> user = userRepository.findByNameOrPhone(username, username);
        if (!user.isPresent()) {
            throw new UsernameNotFoundException("User not found with username: " + username);
        }
        return new org.springframework.security.core.userdetails.User(user.get().getName(), user.get().getPwd(), new ArrayList<>());
    }

    public LoginResponse login(LoginRequest loginRequest) {
        LoginResponse response = new LoginResponse();

        if (loginRequest.getUsername() == null || loginRequest.getPassword() == null) {
            response.setSuccess(false);
            response.setMessage("用户名和密码不能为空");
            return response;
        }

        Optional<User> userOptional = userRepository.findByNameOrPhone(loginRequest.getUsername(), loginRequest.getUsername());

        if (userOptional.isPresent()) {
            User user = userOptional.get();
            if (user.getPwd().equals(loginRequest.getPassword())) {
                UserDetails userDetails = loadUserByUsername(user.getName());
                String token = jwtUtil.generateToken(userDetails);

                LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo();
                userInfo.setId(user.getId());
                userInfo.setName(user.getName());
                userInfo.setRole(user.getRoleId());
                userInfo.setPhone(user.getPhone());
                userInfo.setLevel(user.getNlevel());
                userInfo.setHeadpic(user.getHeadpic());

                response.setSuccess(true);
                response.setToken(token);
                response.setUserInfo(userInfo);
            } else {
                response.setSuccess(false);
                response.setMessage("用户名或密码错误");
            }
        } else {
            response.setSuccess(false);
            response.setMessage("用户名或密码错误");
        }

        return response;
    }
}