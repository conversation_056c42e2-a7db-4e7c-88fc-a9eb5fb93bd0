/**
 * 全局样式定义
 */

/* 这是关键: 让组件不生成任何DOM结构，直接显示内容 */
permission-check, PermissionCheck {
  display: inline !important;
  width: auto !important;
  height: auto !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 这种情况专门针对flex布局下的permission-check */
.flex-container > permission-check,
.flex-container > PermissionCheck {
  display: flex !important;
  flex: 1;
}

/* 这种情况专门针对grid布局下的permission-check */
.grid-container > permission-check,
.grid-container > PermissionCheck {
  display: grid !important;
}

/* 确保快捷功能网格布局中的permission-check正确显示 */
.quick-grid permission-check,
.quick-grid PermissionCheck {
  display: block !important;
  width: 25% !important;
}

/* 确保快捷功能网格布局中的PermissionCheck内容正确显示 */
.quick-grid > permission-check .quick-item,
.quick-grid > PermissionCheck .quick-item {
  width: 25% !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: flex-start !important;
  margin-bottom: 30rpx !important;
  position: relative !important;
  z-index: 1 !important;
  padding: 10rpx !important;
  box-sizing: border-box !important;
}

/* 为快捷功能按钮提供不同的颜色 - 使用更具体的选择器提高优先级 */
.quick-grid > permission-check:nth-child(8n-7) .quick-icon-wrapper,
.quick-grid > PermissionCheck:nth-child(8n-7) .quick-icon-wrapper,
.quick-item:nth-child(8n-7) .quick-icon-wrapper {
  background: linear-gradient(135deg, #6a9eef, #4483e5) !important;
}

.quick-grid > permission-check:nth-child(8n-6) .quick-icon-wrapper,
.quick-grid > PermissionCheck:nth-child(8n-6) .quick-icon-wrapper,
.quick-item:nth-child(8n-6) .quick-icon-wrapper {
  background: linear-gradient(135deg, #60c6a8, #3aaf8f) !important;
}

.quick-grid > permission-check:nth-child(8n-5) .quick-icon-wrapper,
.quick-grid > PermissionCheck:nth-child(8n-5) .quick-icon-wrapper,
.quick-item:nth-child(8n-5) .quick-icon-wrapper {
  background: linear-gradient(135deg, #f3a768, #ee8c3c) !important;
}

.quick-grid > permission-check:nth-child(8n-4) .quick-icon-wrapper,
.quick-grid > PermissionCheck:nth-child(8n-4) .quick-icon-wrapper,
.quick-item:nth-child(8n-4) .quick-icon-wrapper {
  background: linear-gradient(135deg, #e47474, #e05555) !important;
}

.quick-grid > permission-check:nth-child(8n-3) .quick-icon-wrapper,
.quick-grid > PermissionCheck:nth-child(8n-3) .quick-icon-wrapper,
.quick-item:nth-child(8n-3) .quick-icon-wrapper {
  background: linear-gradient(135deg, #8387ea, #5a5fd3) !important;
}

.quick-grid > permission-check:nth-child(8n-2) .quick-icon-wrapper,
.quick-grid > PermissionCheck:nth-child(8n-2) .quick-icon-wrapper,
.quick-item:nth-child(8n-2) .quick-icon-wrapper {
  background: linear-gradient(135deg, #55c1e3, #35a6c8) !important;
}

.quick-grid > permission-check:nth-child(8n-1) .quick-icon-wrapper,
.quick-grid > PermissionCheck:nth-child(8n-1) .quick-icon-wrapper,
.quick-item:nth-child(8n-1) .quick-icon-wrapper {
  background: linear-gradient(135deg, #e680b3, #d65698) !important;
}

.quick-grid > permission-check:nth-child(8n) .quick-icon-wrapper,
.quick-grid > PermissionCheck:nth-child(8n) .quick-icon-wrapper,
.quick-item:nth-child(8n) .quick-icon-wrapper {
  background: linear-gradient(135deg, #9dc75b, #7bae35) !important;
}

/* 确保quick-grid本身有正确的布局属性 */
.quick-grid {
  display: flex !important;
  flex-wrap: wrap !important;
  justify-content: flex-start !important;
  align-items: flex-start !important;
} 