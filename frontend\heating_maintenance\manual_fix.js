const fs = require('fs');
const path = require('path');

// Define the correct format for the problematic sections
const methodSection = `		getImportanceClass(importance) {
			switch (importance) {
				case 'normal':
					return 'normal-importance';
				case 'important':
					return 'important-importance';
				case 'critical':
					return 'critical-importance';
				default:
					return 'normal-importance';
			}
		},
	}
}

</script>`;

const styleEndSection = `	margin-bottom: 8rpx;
}

</style>`;

// Read the file
const filePath = path.join(process.cwd(), 'pages/patrol/create.vue');
fs.readFile(filePath, 'utf8', (err, data) => {
  if (err) {
    console.error('Error reading file:', err);
    return;
  }

  // Find the getImportanceClass method and replace it with the correct version
  const methodRegex = /getImportanceClass\(importance\)[^}]+}[\s\n]+}[\s\n]+}[\s\n]+}[\s\n]+<\/script>/s;
  let fixed = data.replace(methodRegex, methodSection);

  // Find the end of the style section and replace it
  const styleRegex = /margin-bottom: 8rpx;[\s\n]+}[\s\n]+[\s\n]+}[\s\n]+[\s\n]+<\/style>/s;
  fixed = fixed.replace(styleRegex, styleEndSection);

  // Write the fixed content back to the file
  fs.writeFile(filePath, fixed, 'utf8', (err) => {
    if (err) {
      console.error('Error writing file:', err);
      return;
    }
    console.log('File successfully fixed!');
  });
}); 