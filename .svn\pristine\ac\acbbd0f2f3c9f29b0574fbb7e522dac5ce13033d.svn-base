<<<<<<< .mine
package com.heating.dto.order;

import java.time.LocalDateTime;

/**
 * DTO for work order messages response
 * 工单消息响应DTO
 */
public record WorkOrderMessageResponse(
    Long id,                  // 工单ID
    String faultSource,       // 故障来源
    String faultDesc,         // 故障描述
    String orderNo,           // 工单编号
    String heatUnitName,       // 热用户
    LocalDateTime createdTime, // 创建时间
    LocalDateTime updatedTime  // 更新时间
) {
    /**
     * 验证输入参数数据
     * @param id 工单ID
     * @param faultSource 故障来源
     * @param faultDesc 故障描述
     * @param createdTime 创建时间
     * @param updatedTime 更新时间
     */
    public WorkOrderMessageResponse {
        // 验证工单ID不为空
        if (id == null) {
            throw new IllegalArgumentException("工单ID不能为空");
        }
        
        // 验证故障描述不为空
        if (faultDesc == null || faultDesc.isBlank()) {
            throw new IllegalArgumentException("故障描述不能为空");
        }
    }
} ||||||| .r0
=======
package com.heating.dto.order;

import java.time.LocalDateTime;

/**
 * DTO for work order messages response
 * 工单消息响应DTO
 */
public record WorkOrderMessageResponse(
    Long id,                  // 工单ID
    String faultSource,       // 故障来源
    String faultDesc,         // 故障描述
    String orderNo,           // 工单编号
    LocalDateTime createdTime, // 创建时间
    LocalDateTime updatedTime  // 更新时间
) {
    /**
     * 验证输入参数数据
     * @param id 工单ID
     * @param faultSource 故障来源
     * @param faultDesc 故障描述
     * @param createdTime 创建时间
     * @param updatedTime 更新时间
     */
    public WorkOrderMessageResponse {
        // 验证工单ID不为空
        if (id == null) {
            throw new IllegalArgumentException("工单ID不能为空");
        }
        
        // 验证故障描述不为空
        if (faultDesc == null || faultDesc.isBlank()) {
            throw new IllegalArgumentException("故障描述不能为空");
        }
    }
} >>>>>>> .r5108
