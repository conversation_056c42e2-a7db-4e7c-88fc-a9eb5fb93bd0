package com.heating.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 管理人员与热用户关联DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ManagerHeatUnitDto {
    
    private Long id;
    private Long managerId;
    private String managerName;
    private Long heatUnitId;
    private String heatUnitName;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    
    /**
     * 紧凑构造函数用于验证输入参数
     */
    public ManagerHeatUnitDto(Long managerId, String managerName, Long heatUnitId, String heatUnitName) {
        this.managerId = managerId;
        this.managerName = managerName;
        this.heatUnitId = heatUnitId;
        this.heatUnitName = heatUnitName;
    }
} 