package com.heating.controller;

import com.heating.service.DeviceService;
import com.heating.util.ApiResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.heating.dto.device.DeviceCreateRequest;
import com.heating.dto.device.DeviceCreateResponse;
import com.heating.dto.device.DeviceDetailResponse;
import com.heating.dto.device.DeviceHeatUnitResponse;
import com.heating.dto.device.DeviceListRequest;
import com.heating.dto.device.DeviceListResponse;
import com.heating.dto.device.DevicePatrolItemResponse;
import com.heating.dto.device.DeviceStatsResponse;
import com.heating.dto.device.DeviceUpdateRequest;

@RestController
@RequestMapping("/api/devices")
public class DeviceController {
    @Autowired
    private DeviceService deviceService;

    @PostMapping("/list")
    public ResponseEntity<ApiResponse<?>> getDeviceList(@RequestBody DeviceListRequest request) {
        try {
            Page<DeviceListResponse> devices = deviceService.getDeviceList(request); 
            ApiResponse<Page<DeviceListResponse>> response = ApiResponse.success("获取设备列表成功", devices);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.ok(ApiResponse.error("获取设备列表失败: " + e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.ok(ApiResponse.error("获取设备列表失败: " + e.getMessage()));
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<?>> getDeviceDetail(@PathVariable("id") Long deviceId) {
        try {
            DeviceDetailResponse device = deviceService.getDeviceDetail(deviceId);
            ApiResponse<DeviceDetailResponse> response = ApiResponse.success("获取设备详情成功", device);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.ok(ApiResponse.error("获取设备详情失败: " + e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.ok(ApiResponse.error("获取设备详情失败: " + e.getMessage()));
        }
    }

    @PostMapping("/add")
    public ResponseEntity<ApiResponse<?>> createDevice(@RequestBody DeviceCreateRequest request) {
        try { 
            // 判断设备是否存在
            // if (deviceService.isDeviceExists(request.getScanCode())) { 
            //     return ResponseEntity.ok(ApiResponse.error("设备已存在")); 
            // } 
            DeviceCreateResponse response = deviceService.createDevice(request); 
            return ResponseEntity.ok(ApiResponse.success("添加设备成功", response));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.ok(ApiResponse.error("添加设备失败: " + e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.ok(ApiResponse.error("添加设备失败: " + e.getMessage()));
        }
    }

    @PostMapping("/update/{id}")
    public ResponseEntity<ApiResponse<?>> updateDevice(
        @PathVariable("id") Long deviceId,
        @RequestBody DeviceUpdateRequest request) {
        try {
            Map<String, Object> result = deviceService.updateDevice(deviceId, request);
            return ResponseEntity.ok(ApiResponse.success("更新设备成功", result));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.ok(ApiResponse.error("更新设备失败: " + e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.ok(ApiResponse.error("更新设备失败: " + e.getMessage()));
        }
    } 

    @GetMapping("/maintenance/{id}")
    public ResponseEntity<ApiResponse<?>> getDeviceMaintenance(
            @PathVariable("id") Long deviceId) {
        try {
            Map<String, Object> history = deviceService.getDeviceMaintenance(deviceId);
            return ResponseEntity.ok(ApiResponse.success("获取设备维护记录成功", history));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.ok(ApiResponse.error("获取设备维护记录失败: " + e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.ok(ApiResponse.error("获取设备维护记录失败: " + e.getMessage()));
        }
    }

    /**
     * 获取设备统计数据
     * @return 设备统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<ApiResponse<?>> getDeviceStats() {
        try {
            DeviceStatsResponse stats = deviceService.getDeviceStats(); 
            return ResponseEntity.ok(ApiResponse.success("获取设备统计数据成功", stats));
        } catch (Exception e) {
            return ResponseEntity.ok(ApiResponse.error("获取设备统计数据失败: " + e.getMessage()));
        }
    }
    
    /**
     * 根据热用户ID获取设备列表
     * 
     * 接口说明：
     * 该接口用于获取指定热用户下的所有设备列表，包括设备ID、设备所属父级、设备类型和设备名称等信息
     * 
     * @param heatUnitId 热用户ID
     * @return 设备列表
     */
    @GetMapping("/heat-unit/{heatUnitId}")
    public ResponseEntity<ApiResponse<?>> getDevicesByHeatUnit(@PathVariable("heatUnitId") Long heatUnitId) {
        try {
            // 参数校验
            if (heatUnitId == null) {
                return ResponseEntity.ok(ApiResponse.error("热用户ID不能为空"));
            }
            
            // 调用服务获取设备列表
            List<DeviceHeatUnitResponse> devices = deviceService.getDevicesByHeatUnitId(heatUnitId);
            
            // 返回成功响应
            return ResponseEntity.ok(ApiResponse.success("获取设备列表成功", devices));
        } catch (IllegalArgumentException e) {
            // 处理参数错误异常
            return ResponseEntity.ok(ApiResponse.error("获取设备列表失败: " + e.getMessage()));
        } catch (Exception e) {
            // 处理其他异常
            return ResponseEntity.ok(ApiResponse.error("获取设备列表失败: " + e.getMessage()));
        }
    }
    
    /**
     * 根据设备ID获取设备巡检项
     * 
     * 接口说明：
     * 该接口用于获取指定设备的所有巡检项，包括巡检项ID、巡检项名称、类别名称、参数类型、单位、正常范围等信息
     * 数据来源：
     * 1. 从t_device_patrol_item表中获取设备巡检项关联信息
     * 2. 通过patrol_item_dict_id关联t_patrol_item_dictionary表获取巡检项详细信息
     * 
     * @param deviceId 设备ID
     * @return 设备巡检项列表
     */
    @GetMapping("/patrol-items/{deviceId}")
    public ResponseEntity<ApiResponse<?>> getDevicePatrolItems(@PathVariable("deviceId") Long deviceId) {
        try {
            // 参数校验
            if (deviceId == null) {
                return ResponseEntity.ok(ApiResponse.error("设备ID不能为空"));
            }
            
            // 调用服务获取设备巡检项列表
            List<DevicePatrolItemResponse> patrolItems = deviceService.getDevicePatrolItems(deviceId);
            
            // 返回成功响应
            return ResponseEntity.ok(ApiResponse.success("获取设备巡检项列表成功", patrolItems));
        } catch (IllegalArgumentException e) {
            // 处理参数错误异常
            return ResponseEntity.ok(ApiResponse.error("获取设备巡检项列表失败: " + e.getMessage()));
        } catch (Exception e) {
            // 处理其他异常
            return ResponseEntity.ok(ApiResponse.error("获取设备巡检项列表失败: " + e.getMessage()));
        }
    }
} 