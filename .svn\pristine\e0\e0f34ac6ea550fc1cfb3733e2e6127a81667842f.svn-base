package com.heating.dto.attendance;

import com.heating.dto.user.UserBasicInfo;
import lombok.Data;
import java.util.List;
import java.util.Map;

@Data
public class AttendanceRecordResponse {
    private Map<String, Object> summary;
    private List<AttendanceRecordDetail> records;
    
    @Data
    public static class AttendanceRecordDetail {
        private String clockType;
        private String clockTime;
        private Double latitude;
        private Double longitude;
        private List<String> facePhoto;
        private Map<String, Object> livenessData;
        private Integer outdoorFlag;
        private String status;
        private String leaveType;
        private String leaveReason;
        private List<String> leaveProof;
        private String createTime;
        private UserBasicInfo user;
        
        // 新增字段，用于显示上下班打卡时间
        private String clockInTime;
        private String clockOutTime;
        private String clockInStatus;
        private String clockOutStatus;
        private String date; // 日期，格式：MM-DD
        private String week; // 星期几，格式：周一、周二等
    }
} 