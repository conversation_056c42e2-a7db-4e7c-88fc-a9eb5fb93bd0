<template>
  <view class="workorder-list-container">
    <!-- Tab 切换 -->
    <view class="tab-container">
      <view 
        class="tab-item" 
        :class="{ active: activeTab === 'my' }" 
        @click="switchTab('my')"
      >
        我的工单
      </view>
      <view 
        class="tab-item" 
        :class="{ active: activeTab === 'pending' }" 
        @click="switchTab('pending')"
      >
        待接单
      </view>
    </view>

    <!-- 顶部筛选 -->
    <view class="filter-section-seach">
      <view class="date-filter" style="flex: 1;">
        <picker mode="date" :value="filterDate" @change="onDateChange">
          <view class="date-picker">
             <text class="date-text">{{ filterDate || "选择日期" }}</text>
          </view>
        </picker>
      </view>
      <button class="refresh-button" @click="refreshData">重置</button>
    </view>
    
    <!-- 筛选条件区域 -->
    <view class="filter-condition" v-if="showFilterCondition">
      <view class="condition-header">
        <text class="condition-title">筛选条件</text>
        <text class="close-icon" @click="toggleFilterCondition">×</text>
      </view>

      <!-- 查询日期 -->
      <view class="filter-section">
        <view class="section-header">
          <view class="blue-bar"></view>
          <text class="section-title">查询日期</text>
        </view>
        <view class="date-range">
          <view class="date-picker full-width">
            <picker mode="date" :value="filters.startDate" @change="onStartDateChange">
              <view class="date-text">{{ filters.startDate || "选择日期" }}</view>
            </picker>
          </view>
        </view>
      </view>

      <!-- 筛选操作按钮 -->
      <view class="filter-actions">
        <button class="filter-reset" @click="resetFilters">重置</button>
        <button class="filter-confirm" @click="applyFilters">确认</button>
      </view>
    </view>

    <!-- 工单列表 -->
    <scroll-view
      scroll-y="true"
      class="workorder-list"
      :style="{
        height: showFilterCondition ? 'calc(100vh - 480rpx)' : 'calc(100vh - 290rpx)',
      }"
      @scrolltolower="loadMoreData"
      lower-threshold="100"
      :refresher-enabled="true"
      :refresher-triggered="isRefreshing"
      @refresherrefresh="refreshData"
    >
      <template v-if="workorderList.length > 0">
        <view
          class="workorder-item"
          v-for="(item, index) in workorderList"
          :key="index"
          @click="viewOrderDetail(item.orderId)"
        >
          <view class="workorder-header">
            <text class="workorder-code">{{ item.orderNo }}</text>
            <view class="status-tag" :class="getStatusClass(getDisplayStatus(item))">{{ getDisplayStatus(item) }}</view>
          </view>
          <view class="workorder-info">
            <view class="info-item">
              <text class="location">{{ item.heatUnitName }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">故障类型:</text>
              <text class="fault-type">{{ item.faultType }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">故障等级:</text>
              <text
                class="fault-level level-tag"
                :class="getFaultLevelClass(item.faultLevel)"
              >
                {{ item.faultLevel }}
              </text>
            </view>
          </view>
          
          <view class="workorder-footer">
            <view class="workorder-meta">
              <text class="workorder-time">{{ item.createdTime }}</text>
            </view>
            <view class="workorder-actions">
              <view class="action-button" @click.stop="handleAction(item, 'detail')">查看</view>
              <view
                class="action-button primary"
                @click.stop="handleAction(item, 'accept')"
                v-if="activeTab === 'pending' && item.orderStatus === '待接单'"
              >接单</view>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <view class="loading-more" v-if="isLoadingMore">
          <text>正在加载更多...</text>
        </view>
        <view class="no-more" v-if="hasNoMore">
          <text>没有更多数据了</text>
        </view>
      </template>

      <!-- 空状态 -->
      <view class="empty-state" v-else>
        <image
          class="empty-image"
          src="/static/images/empty-workorder.png"
          mode="aspectFit"
        ></image>
        <text class="empty-text">{{ activeTab === 'my' ? '暂无我的工单' : '暂无待接单工单' }}</text>
      <!--  <button class="refresh-button" @click="refreshData">刷新</button> -->
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { workOrderApi } from "@/utils/api.js";

export default {
  data() {
    return {
      // 当前用户ID
      userId: uni.getStorageSync('userId') || '',
      // 用户项目权限
      heatUnitId: uni.getStorageSync('heatUnitId') || '',
      // 当前激活的标签页
      activeTab: 'my', // 'my' 或 'pending'
      // 列表数据
      workorderList: [],
      currentPage: 1,
      pageSize: 10,
      hasNoMore: false,
      isLoadingMore: false,
      isRefreshing: false,
      isLoading: false,
      loadError: false,

      // 筛选条件显示控制
      showFilterCondition: false,

      // 筛选条件
      filterDate: "",
      
      // 筛选条件
      filters: {
        startDate: "",
        endDate: "",
      }
    };
  },

  onLoad() {
    this.getWorkorderList();
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.refreshData().then(() => {
      uni.stopPullDownRefresh();
    });
  },

  // 上拉加载更多
  onReachBottom() {
    console.log("执行上拉加载")
    this.loadMoreData();
  },

  methods: {
    // 切换标签页
    switchTab(tab) {
      if (this.activeTab !== tab) {
        this.activeTab = tab;
        this.currentPage = 1;
        this.workorderList = [];
        this.hasNoMore = false;
        this.getWorkorderList();
      }
    },
    
    // 加载工单列表
    async getWorkorderList(isLoadMore = false) {
      if (this.isLoading) return;

      this.isLoading = true;
      if (!isLoadMore) {
        this.loadError = false;
      }

      try {
        // 构建查询参数
        const params = {
          page: this.currentPage,
          pageSize: this.pageSize,
          uid: this.userId,
          heatUnitId: this.heatUnitId // 添加用户项目权限参数
        };

        // 添加日期筛选
        if (this.filterDate) {
          params.date = this.filterDate;
        }

        // 根据当前标签页添加不同的查询参数
        if (this.activeTab === 'my') {
          params.type = 'my'; // 我的工单
        } else if (this.activeTab === 'pending') {
          params.type = 'pending'; // 待接单工单
          params.status = '待接单'; // 强制设置状态为待接单
        }

        // 调用API获取工单列表
        const res = await workOrderApi.getList(params);

        if (res.code === 200) {
          const { list, total, totalPages } = res.data;
          
          if (isLoadMore) {
            // 加载更多模式：追加数据
            this.workorderList = [...this.workorderList, ...list];
          } else {
            // 初始加载模式：替换数据
            this.workorderList = list;
          }

          // 判断是否还有更多数据
          this.hasNoMore = this.currentPage >= totalPages;
        } else {
          this.loadError = true;
          uni.showToast({
            title: res.message || "获取工单列表失败",
            icon: "none",
          });
        }
      } catch (err) {
        console.error("获取工单列表异常:", err);
        this.loadError = true;
        uni.showToast({
          title: "网络异常，请稍后重试",
          icon: "none",
        });
      } finally {
        this.isLoading = false;
        this.isLoadingMore = false;
        this.isRefreshing = false;
      }
    },

    // 加载更多数据
    loadMoreData() {
      if (!this.isLoadingMore && !this.hasNoMore && !this.isLoading) {
        this.isLoadingMore = true;
        this.currentPage++;
        this.getWorkorderList(true);
      }
    },

    // 下拉刷新
    async refreshData() {
      this.isRefreshing = true;
      this.currentPage = 1;
      this.hasNoMore = false;
      this.workorderList = [];
      this.filterDate="";
      await this.getWorkorderList(false);
    },

    // 日期选择变更
    onDateChange(e) {
      this.filterDate = e.detail.value;
      this.currentPage = 1;
      this.workorderList = [];
      this.hasNoMore = false;
      this.getWorkorderList();
    },

    // 获取状态对应的样式类
    getStatusClass(status) {
      const statusClassMap = {
        '待接单': 'pending',
        '处理中': 'processing',
        '已完成': 'completed',
        '已转派': 'transferred',
        '已取消': 'cancelled'
      };
      return statusClassMap[status] || 'default';
    },
    
    // 获取显示的工单状态
    getDisplayStatus(item) {
      // 如果工单有transferUserId字段且等于当前用户的userId，显示为"已转派"
      if (item.transferUserId && item.transferUserId === this.userId) {
        return '已转派';
      }
      // 否则显示原始状态
      return item.orderStatus;
    },
    
    // 获取故障等级对应的样式类
    getFaultLevelClass(level) {
      const levelClassMap = {
        提示: "notice",
        一般: "normal",
        重要: "important",
        严重: "critical",
      };
      return levelClassMap[level] || "default";
    },
    
    // 查看工单详情
    viewOrderDetail(orderId) {
      uni.navigateTo({
        url: `/pages/workorder/detail?id=${orderId}`,
      });
    },

    // 处理工单操作
    handleAction(item, action) {
      switch (action) {
        case "detail":
          this.viewOrderDetail(item.orderId);
          break;
        case "accept":
          this.acceptOrder(item);
          break;
        case "complete":
          uni.navigateTo({
            url: `/pages/workorder/complete?id=${item.orderId}`,
          });
          break;
      }
    },
    
    // 接单操作
    acceptOrder(item) {
      uni.showModal({
        title: '接单确认',
        content: '确认接单？接单后您将负责处理此工单',
        success: (res) => {
          if (res.confirm) {
            // 显示加载中
            uni.showLoading({
              title: '处理中...'
            });
            
            // 构建接单请求参数
            const requestData = {
              order_id: item.orderId,
              repair_user_id: this.userId,
              order_status: '处理中'
            };
            
            // 调用接单API
            workOrderApi.updateStatus(requestData)
              .then(res => {
                if (res.code === 200) {
                  uni.showToast({
                    title: '接单成功',
                    icon: 'success'
                  });
                  // 刷新列表
                  this.refreshData();
                } else {
                  uni.showToast({
                    title: res.message || '接单失败',
                    icon: 'none'
                  });
                }
              })
              .catch(err => {
                console.error('接单失败:', err);
                uni.showToast({
                  title: '网络异常，请稍后重试',
                  icon: 'none'
                });
              })
              .finally(() => {
                uni.hideLoading();
              });
          }
        }
      });
    },

    // 切换筛选条件区域显示/隐藏
    toggleFilterCondition() {
      this.showFilterCondition = !this.showFilterCondition;
    },

    // 日期选择
    onStartDateChange(e) {
      this.filters.startDate = e.detail.value;
    },

    // 重置筛选
    resetFilters() {
      this.filters = {
        startDate: "",
      };
    },

    // 应用筛选
    applyFilters() {
      this.toggleFilterCondition();
      // 重置页码
      this.currentPage = 1;
      this.getWorkorderList();
    },
  },
};
</script>

<style lang="scss">
.workorder-list-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

/* Tab切换 */
.tab-container {
  display: flex;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
  position: sticky;
  top: 0;
  z-index: 999;
  
  .tab-item {
    flex: 1;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    font-size: 30rpx;
    color: #666;
    position: relative;
    
    &.active {
      color: #007aff;
      font-weight: 500;
      
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 60rpx;
        height: 4rpx;
        background-color: #007aff;
        border-radius: 2rpx;
      }
    }
  }
}

/* 顶部筛选 */
.filter-section-seach {
  background-color: #ffffff;
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: sticky;
  height:75rpx;
  top: 165rpx;
  z-index: 999;
  

  .date-filter,
  .status-filter {
    flex: 1;
    height: 70rpx;
    .date-picker,
    .status-picker {
      display: flex;
      align-items: center;
      height: 70rpx;
      border: 1px solid #e5e5e5;
      border-radius: 6rpx;
      padding: 0 20rpx;
      font-size: 28rpx;
      color: #333;

      .date-text,
      .status-text {
        flex: 1;
      }

      .iconfont {
        margin-left: 10rpx;
        color: #999;
      }
    }
  }

  .date-filter {
    margin-right: 20rpx;
  }
  
  .status-filter {
    margin-right: 20rpx;
  }
}

/* 筛选条件区域 */
.filter-condition {
  background-color: #fff;
  margin-bottom: 20rpx;
  padding: 0 30rpx 20rpx;

  .condition-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1px solid #f5f5f5;
    margin-bottom: 20rpx;

    .condition-title {
      font-size: 30rpx;
      font-weight: 600;
      color: #333;
    }

    .close-icon {
      font-size: 40rpx;
      color: #999;
      padding: 0 10rpx;
    }
  }

  .filter-section {
    margin-bottom: 20rpx;

    .section-header {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;

      .blue-bar {
        width: 6rpx;
        height: 30rpx;
        background-color: #007aff;
        margin-right: 15rpx;
        border-radius: 3rpx;
      }

      .section-title {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
      }
    }

    .filter-options {
      display: flex;
      flex-wrap: wrap;

      .filter-option {
        padding: 10rpx 24rpx;
        margin-right: 20rpx;
        margin-bottom: 15rpx;
        font-size: 26rpx;
        color: #666;
        background-color: #f5f7fa;
        border-radius: 30rpx;

        &.active {
          color: #fff;
          background-color: #007aff;
        }
      }
    }

    .date-range {
      .date-picker {
        height: 70rpx;
        background-color: #f5f7fa;
        border-radius: 8rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .date-text {
          font-size: 26rpx;
          color: #666;
        }
      }
    }
  }

  .filter-actions {
    display: flex;
    margin-top: 30rpx;

    button {
      flex: 1;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      font-size: 28rpx;
      border-radius: 40rpx;

      &.filter-reset {
        color: #666;
        background-color: #f5f5f5;
        margin-right: 20rpx;
      }

      &.filter-confirm {
        color: #fff;
        background-color: #007aff;
      }
    }
  }
}

/* 工单列表 */
.workorder-list {
  .workorder-item {
    margin: 20rpx;
    padding: 30rpx;
    background-color: #fff;
    border-radius: 16rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

    .workorder-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;

      .workorder-code {
        font-size: 28rpx;
        color: #666;
      }

      .status-tag {
        padding: 4rpx 16rpx;
        border-radius: 20rpx;
        font-size: 24rpx;

        &.pending {
          background-color: #e6f7ff;
          color: #1890ff;
        }

        &.processing {
          background-color: #fff7e6;
          color: #fa8c16;
        }

        &.completed {
          background-color: #f6ffed;
          color: #52c41a;
        }
        
        &.transferred {
          background-color: #f2f4f8;
          color: #6777ef;
        }

        &.cancelled {
          background-color: #f5f5f5;
          color: #999;
        }
      }
    }

    .workorder-info {
      margin-bottom: 20rpx;
       .info-item {
          display: flex;
          align-items: center;
          margin-bottom: 10rpx;

          .info-label {
            font-size: 26rpx;
            color: #999;
            width: 150rpx;
          }
          .location {
            font-size: 30rpx;
            font-weight: 600;
            color: #333;
            margin-bottom: 8rpx;
          }

          .fault-type {
            font-size: 28rpx;
            color: #333;
            margin-bottom: 8rpx;
          }

          .fault-level {
            font-size: 28rpx;
            color: #666;
            line-height: 1.5;
          }
          .level-tag {
            padding: 4rpx 12rpx;
            border-radius: 4rpx;
            font-size: 24rpx;
          
            &.notice {
              background-color: #e6f7ff;
              color: #1890ff;
            }
          
            &.normal {
              background-color: #e6f7ff;
              color: #1890ff;
            }
          
            &.important {
              background-color: #fff7e6;
              color: #fa8c16;
            }
          
            &.critical {
              background-color: #fff1f0;
              color: #f5222d;
            }
          
            &.default {
              background-color: #f5f5f5;
              color: #999;
            }
          }
      }
    }

    .workorder-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .workorder-meta {
        display: flex;
        flex-direction: column;

        .workorder-time {
          font-size: 24rpx;
          color: #999;
          margin-bottom: 8rpx;
        }
      }

      .workorder-actions {
        display: flex;
        align-items: center;

        .action-button {
          padding: 10rpx 24rpx;
          margin-left: 20rpx;
          font-size: 24rpx;
          color: #666;
          background-color: #f5f7fa;
          border-radius: 30rpx;

          &.primary {
            color: #fff;
            background-color: #007aff;
          }
        }
      }
    }
  }
}

/* 加载状态 */
.loading-more,
.no-more {
  text-align: center;
  padding: 30rpx;
  font-size: 26rpx;
  color: #999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .empty-image {
    width: 240rpx;
    height: 240rpx;
    margin-bottom: 30rpx;
  }

  .empty-text {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 40rpx;
  }

  .refresh-button {
    font-size: 28rpx;
    color: #007aff;
    background-color: #fff;
    border: 2rpx solid #007aff;
    border-radius: 40rpx;
    padding: 10rpx 60rpx;
  }
}
/* 重置按钮 */
.refresh-button {
    font-size: 28rpx;
    background-color: #fff;
    border-radius: 5rpx;
    align-items: center;
    height: 72rpx;
    border-radius: 6rpx;
    padding: 0 20rpx;
    font-size: 28rpx;
    position: relative;
  }
</style>
