Page({
  data: {
    stats: {
      avgTemp: 0,
      maxTemp: 0,
      minTemp: 0,
      count: 0
    },
    temperatures: [], 
    loading: false, 
    currentFilter: 0,
    communities: [],
    selectedCommunity: null,
    selectedCommunity_name: '', 
    selectedBuilding: '',
    selectedDate: ''
  },

  onLoad() {
    // 设置默认日期为今天
    // const today = new Date().toISOString().split('T')[0];
    // this.setData({ selectedDate: today });
    
    this.loadCommunities();

    this.loadData();
  },

  loadData() {
    if (this.data.loading) return;
    this.setData({ loading: true }); 
    wx.request({
      url: 'http://localhost:5000/api/temperature/list',
      method: 'GET',
      data: {
        community_name: this.data.selectedCommunity_name, 
        date: this.data.selectedDate
      },
      success: (res) => {
        if (res.data.success) {
          // 获取统计数据
          const temps = res.data.data.list || [];
          if (temps.length > 0) {
            const temperatures = temps.map(temp => ({
              ...temp,
              status: this.getTemperatureStatus(temp.temperature)
            }));

            // 计算统计数据
            const stats = this.calculateStats(temperatures);
            
            this.setData({
              temperatures: [...this.data.temperatures, ...temperatures],
              stats
            });
          }
        }
      },
      complete: () => {
        this.setData({ loading: false });
      }
    });
  },

  loadCommunities() {
    wx.request({
      url: 'http://localhost:5000/api/useheatunits/list',
      success: (res) => {
        if (res.data.success) {  
          this.setData({ communities: res.data.data }); 
        }
      }
    });
    
  }, 

  selectCommunity(e) {
    const community = this.data.communities[e.detail.value]; 
    this.setData({
      selectedCommunity: community, 
      selectedCommunity_name: community.name, 
      temperatures: []
    }, () => {
      this.loadData();
    });
  },

  onBuildingInput(e) {
    this.setData({
      selectedBuilding: e.detail.value,
      page: 1,
      temperatures: []
    }, () => {
      this.loadData();
    });
  },

 
  bindDateChange(e) {
    this.setData({
      selectedDate: e.detail.value,
      page: 1,
      temperatures: []
    }, () => {
      this.loadData();
    });
  },

  calculateStats(temperatures) {
    if (!temperatures.length) return this.data.stats;

    // 将字符串温度转换为数值
    const temps = temperatures.map(t => parseFloat(t.indoor_temp));
    return {
      avgTemp: (temps.reduce((a, b) => a + b, 0) / temps.length).toFixed(1),
      maxTemp: Math.max(...temps).toFixed(1),
      minTemp: Math.min(...temps).toFixed(1),
      count: temperatures.length
    };
  },

  getTemperatureStatus(temp) {
    if (temp < 18) return 'low';
    if (temp > 24) return 'high';
    return 'normal';
  },

  onReachBottom() {
    if (!this.data.loading) {
      this.setData({
        page: this.data.page + 1
      }, () => {
        this.loadData();
      });
    }
  },

  goToDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/temperature/detail/index?id=${id}`
    });
  }
}); 