{"@platforms": ["android", "iPhone", "iPad"], "id": "__UNI__6A10D03", "name": "热擎云", "version": {"name": "1.0.0", "code": "100"}, "description": "热擎云 是专为暖通供热行业打造的一站式智能管理平台，致力于提升供热系统的能效水平与运维效率。平台融合物联网、大数据、人工智能与视频分析技术，面向供热企业、工程公司及运维单位，提供从能耗分析到设备监控的全链路数字化解决方案", "developer": {"name": "", "email": "", "url": ""}, "permissions": {"VideoPlayer": {}, "Speech": {}, "Geolocation": {}, "Barcode": {}, "Camera": {}, "SQLite": {}, "UniNView": {"description": "UniNView原生渲染"}}, "plus": {"useragent": {"value": "uni-app", "concatenate": true}, "splashscreen": {"target": "id:1", "autoclose": true, "waiting": true, "delay": 0}, "popGesture": "close", "launchwebview": {"id": "1", "kernel": "WKWebview"}, "usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "network": {"request": {"timeout": 10000, "header": {"Content-Type": "application/json"}}}, "statusbar": {"immersed": "supportedDevice", "style": "light", "background": "#1890ff"}, "uniStatistics": {"enable": false}, "allowsInlineMediaPlayback": true, "safearea": {"background": "#ffffff", "bottom": {"offset": "auto"}}, "uni-app": {"control": "uni-v3", "vueVersion": "3", "compilerVersion": "4.66", "nvueCompiler": "uni-app", "renderer": "auto", "nvue": {"flex-direction": "column"}, "nvueLaunchMode": "normal", "webView": {"minUserAgentVersion": "49.0"}}, "tabBar": {"position": "bottom", "color": "#999", "selectedColor": "#1890ff", "borderStyle": "rgba(255,255,255,0.4)", "blurEffect": "none", "fontSize": "10px", "iconWidth": "24px", "spacing": "3px", "height": "60px", "list": [{"pagePath": "pages/home/<USER>", "text": "首页", "iconPath": "/static/tab/home.png", "selectedIconPath": "/static/tab/home-active.png"}, {"pagePath": "pages/hes/list", "text": "换热站", "iconPath": "/static/tab/hes.png", "selectedIconPath": "/static/tab/hes-active.png"}, {"pagePath": "pages/message/center", "text": "消息", "iconPath": "/static/tab/notification.png", "selectedIconPath": "/static/tab/notification-active.png"}, {"pagePath": "pages/user/info", "text": "我的", "iconPath": "/static/tab/profile.png", "selectedIconPath": "/static/tab/profile-active.png"}], "backgroundColor": "#ffffff", "selectedIndex": 0, "shown": true}}, "app-harmony": {"useragent": {"value": "uni-app", "concatenate": true}, "uniStatistics": {"enable": false}, "safearea": {"background": "#ffffff", "bottom": {"offset": "auto"}}}, "locale": "zh-Hans", "launch_path": "__uniappview.html"}