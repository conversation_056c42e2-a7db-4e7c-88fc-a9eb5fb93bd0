<template>
	<view class="patrol-create-container">
		<form @submit="submitPlan">
			<!-- 基本信息 -->
			<view class="form-card">
				<view class="card-title">基本信息</view> 
				
				<view class="form-item">
					<text class="form-label required">计划名称</text>
					<input 
						class="form-input plan-name-input" 
						type="text" 
						v-model="formData.name" 
						placeholder="请输入计划名称"
						:maxlength="50"
					/>
				</view>
				
				<view class="form-item">
					<text class="form-label required">巡检周期</text>
					<picker 
						class="form-picker" 
						:range="scheduleTypeOptions" 
						range-key="label"
						:value="scheduleTypeIndex" 
						@change="handleScheduleTypeChange"
					>
						<view class="picker-value placeholder-style" style="padding-left: 20rpx;">{{ scheduleTypeOptions[scheduleTypeIndex].label }}</view>
					</picker>
				</view>
				
				<view class="form-item">
					<text class="form-label required">巡检类型</text>
					<picker 
						class="form-picker" 
						:range="patrolTypeOptions"
						range-key="label"
						:value="patrolTypeIndex" 
							@change="handlePatrolTypeChange"
					>
						<view class="picker-value placeholder-style" style="padding-left: 20rpx;">{{ patrolTypeOptions[patrolTypeIndex].label }}</view>
					</picker>
				</view>
				<view class="form-item">
					<text class="form-label required">开始日期</text>
					<picker 
						class="form-picker" 
						mode="date" 
						:value="formData.startDate" 
						start="2023-01-01" 
						end="2030-12-31" 
						@change="handleStartDateChange"
					>
						<view class="picker-value placeholder-style" style="padding-left: 20rpx;">{{ formData.startDate || '请选择开始日期' }}</view>
					</picker>
				</view>
				
				<view class="form-item">
					<text class="form-label required">结束日期</text>
					<picker 
						class="form-picker" 
						mode="date" 
						:value="formData.endDate" 
						:start="formData.startDate || '2023-01-01'" 
						end="2030-12-31" 
						@change="handleEndDateChange"
					>
						<view class="picker-value placeholder-style" style="padding-left: 20rpx;">{{ formData.endDate || '请选择结束日期' }}</view>
					</picker>
				</view>
				<!-- 修改巡检地点为热用户选择 -->
				<view class="form-item">
					<text class="form-label required">巡检地点</text>
					<view class="heatunit-selector" @click="showHeatUnitSelector">
						<view class="form-input location-input">
							<text v-if="selectedHeatUnit">{{ selectedHeatUnit.name }}</text>
							<text v-else>{{ filteredHeatUnits.length > 0 ? '请选择热用户' : '无可用热用户' }}</text>
							<text class="arrow-down">▼</text>
						</view>
					</view>
				</view>
				<view class="form-item">
					<text class="form-label">巡检人</text>
					<view class="staff-selector" @click="showStaffSelector">
						<view class="form-input location-input">
							<text>{{ selectedExecutors.length > 0 ? `已选择 ${selectedExecutors.length} 个人员` : '请选择巡检人员' }}</text>
							<text class="arrow-down">▼</text>
						</view>
					</view>
					
					<!-- 已选人员显示区域 -->
					<view class="selected-items" v-if="selectedExecutors.length > 0">
						<view class="selected-item" v-for="(executor, index) in selectedExecutors" :key="'executor-' + index">
							<text class="item-text">{{ executor.name }}</text>
							<text class="remove-icon" @click.stop="removeExecutor(executor.id)">×</text>
						</view>
					</view>
				</view>
				
				
				
				<!-- 修改巡检设备部分，关联到选择的热用户 -->
				<view class="form-item">
					<text class="form-label required">巡检设备</text>
					<view class="device-selector" @click="showDeviceSelector">
						<view class="form-input location-input">
							<text>{{ selectedDevices.length > 0 ? `已选择 ${selectedDevices.length} 个设备` : '请选择巡检设备' }}</text>
							<text class="arrow-down">▼</text>
						</view>
					</view>
					
					<!-- 已选设备显示区域 -->
					<view class="selected-items" v-if="selectedDevices.length > 0">
						<view class="selected-item" v-for="(device, index) in selectedDevices" :key="'device-' + index">
							<text class="item-text">{{ device.name }}</text>
							<text class="remove-icon" @click.stop="removeDevice(device.id)">×</text>
						</view>
					</view>
				</view>
				
				<view class="form-item">
					<text class="form-label">计划描述</text>
					<textarea 
						class="form-textarea" 
						v-model="formData.description" 
						placeholder="请输入巡检计划描述（选填）"
						:maxlength="500"
					/>
				</view>
				
				<!-- 添加周期类型相关的组件 -->
				<view class="form-item" v-if="formData.scheduleType === 'custom'">
					<text class="form-label required">巡检间隔(天)</text>
					<input 
						class="form-input" 
						type="number" 
						v-model="formData.scheduleInterval" 
						placeholder="请输入巡检间隔天数"
					/>
				</view>
				
				<view class="form-item" v-if="formData.scheduleType === 'weekly'">
					<text class="form-label required">巡检星期</text>
					<view class="tag-group">
						<view 
							v-for="(day, index) in weekOptions" 
							:key="'week-' + index"
							class="tag" 
							:class="{ active: formData.scheduleWeekDays.includes(day.value) }"
							@click="toggleWeekDay(day.value)"
						>
							{{ day.label }}
						</view>
					</view>
				</view>
				
				<view class="form-item" v-if="formData.scheduleType === 'monthly'">
					<text class="form-label required">巡检日期</text>
					<view class="tag-group">
						<view 
							v-for="(day, index) in filteredMonthOptions" 
							:key="'month-' + index"
							class="tag" 
							:class="{ active: formData.scheduleMonthDays.includes(day.value) }"
							@click="toggleMonthDay(day.value)"
						>
							{{ day.label }}
						</view>
					</view>
				</view>
			</view>
			
			<!-- 巡检任务 -->
			<view class="form-card">
				<view class="card-header">
					<view class="card-title">巡检任务</view>
					<view class="add-button" @click="showTaskSelector" :class="{ disabled: selectedDevices.length === 0 }">
						<text class="iconfont icon-add"></text>
						<text>添加巡检项目</text>
					</view>
				</view>
				
				<view class="task-list" v-if="formData.patrolItem.length > 0">
					<view class="task-item" v-for="(task, index) in formData.patrolItem" :key="'task-' + index">
						<view class="task-content">
							<text class="task-title">{{ task.itemName }}</text>
							<view class="task-info-row">
								<text class="task-info">所属设备: {{ getDeviceName(task.deviceId) }}</text>
								<text class="importance-tag" :class="getImportanceClass(task.importance)">{{ getImportanceLabel(task.importance) }}</text>
							</view>
							<text class="task-info">所属类型: {{ task.categoryName }}</text>
							<text class="task-info">检测标准: {{ task.description }}</text>
							<text class="task-info">检测方法: {{ task.checkMethod }}</text>
							<text class="task-info" v-if="task.paramType === 'number'">检测范围: {{ task.normalRange }} {{ task.unit }}</text>
							<text class="task-info" v-else-if="task.paramType === 'selection'">可选值: {{ task.normalRange }}</text>
							<text class="task-info" v-else-if="task.paramType === 'boolean'">期望值: {{ task.normalRange === 'true' ? '是' : '否' }}</text>
						</view>
						<view class="task-actions">
							<text class="delete-icon" @click="removeTask(index)">×</text>
						</view>
					</view>
				</view>
				
				<view class="empty-tip" v-else>
					<text>请添加巡检任务</text>
				</view>
			</view>
			
			<!-- 提交按钮 -->
			<button class="submit-button" form-type="submit" :style="{ display: isPopupOpen ? 'none' : 'block' }">创建巡检计划</button>
		</form>
		
		<!-- 热用户选择弹窗 -->
		<uni-popup ref="heatUnitSelector" type="bottom">
			<view class="selector heat-unit-selector">
				<view class="selector-header">
					<text class="selector-title">选择热用户</text>
					<view class="header-actions">
						<text class="confirm-button-header" @click="confirmHeatUnitSelection">确定</text>
						<text class="close-button" @click="hideHeatUnitSelector">关闭</text>
					</view>
				</view>
				
				<view class="selector-content">
					<view class="template-search">
						<input 
							type="text" 
							v-model="heatUnitSearchKeyword" 
							placeholder="搜索热用户" 
							confirm-type="search"
							@input="handleHeatUnitSearch"
							class="search-input"
						/>
						<text 
							class="icon-clear" 
							v-if="heatUnitSearchKeyword" 
							@click="clearHeatUnitSearch"
						></text>
						<view class="search-btn" @click="handleHeatUnitSearch">搜索</view>
					</view>
					
					<view class="heatunit-list">
						<view 
							class="heatunit-item" 
							v-for="heatUnit in filteredHeatUnits" 
							:key="heatUnit.id"
							@click="selectHeatUnit(heatUnit)"
						>
							<text class="heatunit-name">{{ heatUnit.name }}</text>
							<view class="checkbox" :class="{ checked: selectedHeatUnit && selectedHeatUnit.id === heatUnit.id }">
								<text class="iconfont icon-check" v-if="selectedHeatUnit && selectedHeatUnit.id === heatUnit.id"></text>
							</view>
						</view>
					</view>
					
					<view class="empty-tip" v-if="filteredHeatUnits.length === 0">
						<text>没有找到相关热用户</text>
					</view>
				</view>
			</view>
		</uni-popup>
		
		<!-- 设备选择弹窗 -->
		<uni-popup ref="deviceSelector" type="bottom">
			<view class="selector device-selector-popup">
				<view class="selector-header">
					<text class="selector-title">选择巡检设备</text>
					<view class="header-actions">
						<text class="confirm-button-header" @click="confirmDeviceSelection">确定</text>
						<text class="close-button" @click="hideDeviceSelector">关闭</text>
					</view>
				</view>

				<view class="selector-content">
					<!-- 设备全选功能 - 重新设计 -->
					<view class="device-select-all-wrapper" v-if="deviceOptions.length > 0">
						<view class="device-select-all-card" @click="toggleDeviceSelectAll">
							<view class="device-select-all-content">
								<view class="device-select-checkbox" :class="{ checked: isAllDevicesSelected }">
									<text class="iconfont icon-check" v-if="isAllDevicesSelected">✓</text>
								</view>
								<view class="device-select-info">
									<view class="device-select-title">
										<text class="select-title-text">全选设备</text>
										<view class="select-status-badge" :class="{ active: isAllDevicesSelected }">
											<text class="badge-text">{{ tempSelectedDevices.length }}/{{ deviceOptions.length }}</text>
										</view>
									</view>
									<text class="device-select-desc">点击可快速选择当前热用户下的所有设备</text>
								</view>
							</view>
						</view>
					</view>

					<view class="device-list">
						<view
							class="device-item"
							v-for="device in deviceOptions"
							:key="device.id"
							@click="toggleDeviceSelection(device)"
						>
							<text class="device-name">{{ device.name }}</text>
							<view class="checkbox" :class="{ checked: isDeviceSelected(device.id) }">
								<text class="iconfont icon-check" v-if="isDeviceSelected(device.id)"></text>
							</view>
						</view>
					</view>

					<view class="empty-tip" v-if="deviceOptions.length === 0">
						<text>暂无可选设备</text>
					</view>
				</view>
			</view>
		</uni-popup>
		
		<!-- 人员选择弹窗 -->
		<uni-popup ref="staffSelector" type="bottom">
			<view class="selector staff-selector-popup">
				<view class="selector-header">
					<text class="selector-title">选择巡检人员</text>
					<view class="header-actions">
						<text class="confirm-button-header" @click="confirmStaffSelection">确定</text>
						<text class="close-button" @click="hideStaffSelector">关闭</text>
					</view>
				</view>
				
				<view class="selector-content">
					<view class="staff-list">
						<view 
							class="staff-item" 
							v-for="staff in staffOptions" 
							:key="staff.id"
							@click="toggleStaffSelection(staff)"
						>
							<text class="staff-name">{{ staff.name }}</text>
							<view class="checkbox" :class="{ checked: isStaffSelected(staff.id) }">
								<text class="iconfont icon-check" v-if="isStaffSelected(staff.id)"></text>
							</view>
						</view>
					</view>
					
					<view class="empty-tip" v-if="staffOptions.length === 0">
						<text>暂无可选人员</text>
					</view>
				</view>
			</view>
		</uni-popup>
		
		<!-- 巡检项选择弹窗 -->
		<uni-popup ref="taskSelector" type="bottom">
			<view class="task-selector task-item-selector">
				<view class="selector-header">
					<text class="selector-title">选择巡检项目</text>
					<view class="header-actions">
						<text class="confirm-button-header" @click="confirmTaskSelection">确定</text>
						<text class="close-button" @click="hideTaskSelector">关闭</text>
					</view>
				</view>
				
				<view class="selector-content">
					<!-- 添加设备筛选选择 -->
					<view style="display: flex; align-items: center; padding: 0 10rpx 20rpx; position: relative;">
						<picker
							style="flex: 1; height: 80rpx;"
							:range="[{name: '全部设备'}, ...selectedDevices]"
							range-key="name"
							:value="deviceFilterIndex"
							@change="handleDeviceFilterChange"
						>
							<view style="background: #f5f5f5; border-radius: 40rpx; padding: 0 30rpx; height: 80rpx; font-size: 28rpx; color: #333; display: flex; justify-content: space-between; align-items: center;">
								<text>{{ deviceFilterIndex === 0 ? '全部设备' : selectedDevices[deviceFilterIndex-1]?.name }}</text>
								<text style="color: #999; font-size: 24rpx;">▼</text>
							</view>
						</picker>
					</view>

					<view class="template-search">
						<input
							type="text"
							v-model="searchKeyword"
							placeholder="搜索巡检项"
							confirm-type="search"
							@input="handleSearch"
							class="search-input"
						/>
						<text
							class="search-clear"
							v-if="searchKeyword"
							@click="clearSearch"
						>×</text>
						<view class="search-btn" @click="handleSearch">搜索</view>
					</view>

					<!-- 全选巡检项功能 - 使用内联样式确保生效 -->
					<view
						v-if="filteredTemplates.length > 0"
						@click="toggleTaskSelectAll"
						style="
							background: linear-gradient(135deg, #f0f8ff 0%, #ffffff 100%);
							border: 2px solid #007aff;
							border-radius: 12px;
							padding: 24px;
							margin-bottom: 20px;
							box-shadow: 0 2px 8px rgba(0, 122, 255, 0.1);
							display: flex;
							align-items: flex-start;
						"
					>
						<view
							:style="{
								width: '40px',
								height: '40px',
								border: '2px solid #007aff',
								borderRadius: '8px',
								display: 'flex',
								alignItems: 'center',
								justifyContent: 'center',
								marginRight: '20px',
								marginTop: '5px',
								flexShrink: '0',
								backgroundColor: isAllTasksSelected ? '#007aff' : '#fff'
							}"
						>
							<text
								v-if="isAllTasksSelected"
								style="color: #fff; font-size: 24px; font-weight: bold;"
							>✓</text>
						</view>
						<view style="flex: 1;">
							<view style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
								<text style="font-size: 32px; font-weight: bold; color: #007aff;">全选巡检项</text>
								<view
									:style="{
										backgroundColor: isAllTasksSelected ? '#007aff' : '#e6f7ff',
										color: isAllTasksSelected ? '#fff' : '#007aff',
										fontSize: '24px',
										padding: '6px 16px',
										borderRadius: '20px',
										fontWeight: '600',
										border: isAllTasksSelected ? '1px solid #007aff' : '1px solid #91d5ff'
									}"
								>
									<text>{{ selectedTaskCount }}/{{ filteredTemplates.length }}</text>
								</view>
							</view>
							<text style="font-size: 24px; color: #999; line-height: 1.4;">点击可快速选择当前筛选的所有巡检项目</text>
						</view>
					</view>

					<view class="task-templates">
						<view
							class="template-item"
							v-for="(template, index) in filteredTemplates"
							:key="index"
							:class="{ active: template.selected }"
							@click="toggleTaskSelection(index)"
						>
							<view class="checkbox" :class="{ checked: template.selected }">
								<text class="iconfont icon-check" v-if="template.selected"></text>
							</view>
							<view class="template-left">
								<view class="template-header">
									<text class="template-title">{{ template.itemName }}</text>
									<text class="importance-tag" :class="getImportanceClass(template.importance)">{{ getImportanceLabel(template.importance) }}</text>
								</view>
								<text class="template-info">所属设备: {{ getDeviceName(template.deviceId) }}</text>
								<text class="template-info">所属类型: {{ template.categoryName }}</text>
								<text class="template-info template-description">检测标准: {{ template.description }}</text>
								<text class="template-info">检测方法: {{ template.checkMethod }}</text>
								<text class="template-info" v-if="template.paramType === 'number'">检测范围: {{ template.normalRange }} {{ template.unit }}</text>
								<text class="template-info" v-else-if="template.paramType === 'selection'">可选值: {{ template.normalRange }}</text>
								<text class="template-info" v-else-if="template.paramType === 'boolean'">期望值: {{ template.normalRange === 'true' ? '是' : '否' }}</text>
							</view>
						</view>
					</view>

					<view class="empty-tip" v-if="filteredTemplates.length === 0">
						<text>没有找到相关巡检项模板</text>
					</view>
				</view>
				
				<view class="selector-content-footer">
					<view style="padding: 15rpx 0; border-top: 1rpx solid #e5e5e5; background-color: #007aff; box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.1); position: fixed; bottom: 0; left: 0; right: 0; z-index: 999;">
						<view style="display: flex; align-items: center; justify-content: center; padding: 15rpx 0;">
							<view style="width: 48rpx; height: 48rpx; background-color: white; border-radius: 50%; margin-right: 16rpx; display: flex; align-items: center; justify-content: center;">
								<text style="color: #007aff; font-size: 28rpx; font-weight: bold;">{{ selectedCount }}</text>
							</view>
							<text style="color: white; font-size: 30rpx; font-weight: bold;">已选择项目</text>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>

import { patrolApi, deviceApi, userApi, heatUnitApi, dictApi } from '@/utils/api.js';

export default {
	data() {
		return {
			formData: {
				planNo: '',
				name: '',
				startDate: '',
				endDate: '',
				executorIds: [],
				scheduleType: 'daily',
				scheduleInterval: null,
				scheduleWeekDays: [],
				scheduleMonthDays: [],
				deviceIds: [],
				locations: '',
				patrolItem: [],
				description: '',
				heatUnitId: null
			},
			scheduleTypeOptions: [
				{ label: '每日', value: 'daily' },
				{ label: '每周', value: 'weekly' },
				{ label: '每月', value: 'monthly' }
			],
			patrolTypeOptions: [], // 初始化为空数组，将通过API动态获取
			scheduleTypeIndex: 0,
			patrolTypeIndex:0,
			staffOptions: [],
			staffIndex: 0,
			deviceOptions: [],
			deviceIndex: 0,
			
			// 任务模板
			taskTemplates: [],
			devicePatrolItems: {},
			searchKeyword: '',
			
			// 周和月选项
			weekOptions: [
				{ label: '周一', value: 1 },
				{ label: '周二', value: 2 },
				{ label: '周三', value: 3 },
				{ label: '周四', value: 4 },
				{ label: '周五', value: 5 },
				{ label: '周六', value: 6 },
				{ label: '周日', value: 7 }
			],
			monthOptions: Array.from({ length: 31 }, (_, i) => ({
				label: `${i + 1}`,
				value: i + 1
			})),
			
			// 已选执行人和设备的显示列表
			selectedExecutors: [],
			selectedDevices: [],
			
			// 热用户相关
			selectedHeatUnit: null,
			heatUnitOptions: [],
			heatUnitSearchKeyword: '',
			filteredHeatUnits: [],
			
			// 设备筛选
			deviceFilterIndex: 0,
			currentFilterDeviceId: null,
			
			// 弹窗状态
			isPopupOpen: false,
			tempSelectedDevices: [], // 临时存储选中的设备
			tempSelectedExecutors: [], // 临时存储选中的人员
		}
	},
	computed: {
		filteredTemplates() {
			let templates = [];

			if (this.currentFilterDeviceId) {
				templates = this.devicePatrolItems[this.currentFilterDeviceId] || [];
			} else {
				templates = [];
				this.selectedDevices.forEach(device => {
					const items = this.devicePatrolItems[device.id] || [];
					templates = templates.concat(items);
				});
			}

			if (!this.searchKeyword || this.searchKeyword.trim() === '') {
				return templates;
			}

			const keyword = this.searchKeyword.toLowerCase().trim();
			return templates.filter(template => {
				return (template.itemName && template.itemName.toLowerCase().includes(keyword)) ||
					(template.description && template.description.toLowerCase().includes(keyword)) ||
					(template.checkMethod && template.checkMethod.toLowerCase().includes(keyword)) ||
					(template.categoryName && template.categoryName.toLowerCase().includes(keyword));
			});
		},
		selectedCount() {
			let count = 0;
			Object.values(this.devicePatrolItems).forEach(items => {
				count += items.filter(item => item.selected).length;
			});
			return count;
		},
		filteredMonthOptions() {
			return this.monthOptions.filter((_, index) => index < 31);
		},
		// 设备全选相关计算属性
		isAllDevicesSelected() {
			return this.deviceOptions.length > 0 && this.tempSelectedDevices.length === this.deviceOptions.length;
		},
		// 巡检项全选相关计算属性
		isAllTasksSelected() {
			return this.filteredTemplates.length > 0 && this.selectedTaskCount === this.filteredTemplates.length;
		},
		selectedTaskCount() {
			return this.filteredTemplates.filter(template => template.selected).length;
		}
	},
	onLoad() {
		this.loadHeatUnitList();
		this.loadStaffOptions();
		this.loadPatrolTypeOptions(); // 添加加载巡检类型选项的方法调用
		
		// 初始化已选设备和人员列表
		this.selectedDevices = [];
		this.selectedExecutors = [];
		
		// 确保设备筛选默认为"全部设备"
		this.deviceFilterIndex = 0;
		this.currentFilterDeviceId = null;
	},
	methods: {
		// 加载热用户列表
		loadHeatUnitList() {
			// 获取用户项目权限
			const userHeatUnitId = uni.getStorageSync("heatUnitId") || "";
			console.log('用户项目权限:', userHeatUnitId);
			
			// 判断用户是否有全局权限(heatUnitId=0)
			const hasAllPermission = userHeatUnitId === "0" || 
			                        (userHeatUnitId.split(",").length > 0 && 
									 userHeatUnitId.split(",").includes("0"));
			
			heatUnitApi.getList()
				.then(res => {
					if (res.code === 200 && res.data) {
						// 所有热用户列表
						let allHeatUnits = res.data;
						
						// 如果用户没有全局权限，则需要过滤出用户有权限的热用户
						if (!hasAllPermission && userHeatUnitId) {
							// 将权限ID字符串转换为数组
							const authorizedIds = userHeatUnitId.split(",").map(id => parseInt(id.trim()));
							
							// 过滤出用户有权限的热用户
							allHeatUnits = allHeatUnits.filter(unit => authorizedIds.includes(unit.id));
						}
					 
						this.heatUnitOptions = allHeatUnits;
						this.filteredHeatUnits = [...allHeatUnits];
						
						// 设置默认选中的热用户（如果有权限的热用户列表不为空）
						if (this.filteredHeatUnits.length > 0) {
							this.selectedHeatUnit = this.filteredHeatUnits[0];
							this.formData.heatUnitId = this.selectedHeatUnit.id;
							
							// 加载该热用户下的设备列表
							this.loadDevicesByHeatUnit(this.selectedHeatUnit.id);
						}
						
						console.log('过滤后的热用户列表:', this.filteredHeatUnits);
						console.log('默认选中的热用户:', this.selectedHeatUnit);
					}
				})
				.catch(err => {
					console.error('获取热用户列表失败:', err);
				});
		},
		
		// 加载人员列表
		loadStaffOptions() {
			userApi.getInspectorList({ role: 'inspector' })
				.then(res => {
					if (res.code === 200 && res.data) {
						this.staffOptions = res.data.map(user => ({
							id: user.id,
							name: user.name
						}));
					}
				})
				.catch(err => {
					console.error('获取人员列表失败:', err);
				});
		},
		
		// 根据热用户ID加载设备列表
		loadDevicesByHeatUnit(heatUnitId) {
			if (!heatUnitId) {
				console.error('无效的热用户ID');
				return;
			}
			
			console.log('开始加载热用户设备列表, 热用户ID:', heatUnitId);
			
			// 显示加载中提示
			uni.showLoading({
				title: '加载设备列表...'
			});
			
			deviceApi.getDevicesByHeatUnitId(heatUnitId)
				.then(res => {
					console.log('设备列表API响应:', res);
					uni.hideLoading();
					
					if (res.code === 200 && res.data) {
						// 处理API返回的设备数组
						this.deviceOptions = res.data.map(device => ({
							id: device.id,
							name: device.name,
							type: device.type,
							device_parent: device.deviceParent,
							heat_unit_id: this.selectedHeatUnit.id
						}));
						
						console.log('处理后的设备列表:', this.deviceOptions);
						
						// 清空已选设备
						this.selectedDevices = [];
						this.formData.deviceIds = [];
						
						// 清空设备巡检项
						this.devicePatrolItems = {};
						this.formData.patrolItem = [];
					} else {
						console.error('获取设备列表失败:', res.message || '未知错误');
						uni.showToast({
							title: '获取设备列表失败',
							icon: 'none'
						}); 
					}
				})
				.catch(err => {
					uni.hideLoading();
					console.error(`获取热用户ID:${heatUnitId})的设备列表失败`, err);
					uni.showToast({
						title: '获取设备列表失败，请重试',
						icon: 'none'
					});
					 
				});
		},
		
		// 根据设备ID加载巡检项
		loadPatrolItemsByDevice(deviceId) {
			if (!deviceId) return; 
			deviceApi.getPatrolItemsByDeviceId(deviceId)
				.then(res => {
					if (res.code === 200 && res.data) {
						const items = res.data.map(item => ({
							id: item.patrolItemDictId,
							itemId: item.id,
							deviceId: item.deviceId,
							itemName: item.itemName,
							categoryName: item.categoryName,
							paramType: item.paramType,
							unit: item.unit,
							normalRange: item.normalRange,
							checkMethod: item.checkMethod,
							importance: item.importance,
							description: item.description,
							selected: false
						})); 
						this.$set(this.devicePatrolItems, deviceId, items);
					}
				})
				.catch(err => {
					console.error(`获取设备(ID:${deviceId})的巡检项失败`, err);
				});
		},
		
		// 处理设备选择变更
		handleDeviceChange(e) {
			this.deviceIndex = e.detail.value;
			const selectedDevice = this.deviceOptions[this.deviceIndex]; 
			if (!this.formData.deviceIds.includes(selectedDevice.id)) {
				this.formData.deviceIds.push(selectedDevice.id);
				this.selectedDevices.push(selectedDevice);
				
				this.loadPatrolItemsByDevice(selectedDevice.id);
			} else {
				uni.showToast({
					title: '该设备已添加',
					icon: 'none'
				});
			}
		},
		
		// 移除已选设备
		removeDevice(deviceId) {
			const index = this.formData.deviceIds.findIndex(id => id === deviceId);
			if (index !== -1) {
				this.formData.deviceIds.splice(index, 1);
				
				const deviceIndex = this.selectedDevices.findIndex(device => device.id === deviceId);
				if (deviceIndex !== -1) {
					this.selectedDevices.splice(deviceIndex, 1);
				}
				
				if (this.devicePatrolItems[deviceId]) {
					delete this.devicePatrolItems[deviceId];
				}
				
				this.formData.patrolItem = this.formData.patrolItem.filter(item => item.deviceId !== deviceId);
			}
		},
		
		// 多选执行人员变更
		handleStaffChange(e) {
			this.staffIndex = e.detail.value;
			const selectedStaff = this.staffOptions[this.staffIndex];
			
			if (!this.formData.executorIds.includes(selectedStaff.id)) {
				this.formData.executorIds.push(selectedStaff.id);
				this.selectedExecutors.push(selectedStaff);
			} else {
				uni.showToast({
					title: '该人员已添加',
					icon: 'none'
				});
			}
		},
		
		// 移除已选执行人
		removeExecutor(executorId) {
			const index = this.formData.executorIds.findIndex(id => id === executorId);
			if (index !== -1) {
				this.formData.executorIds.splice(index, 1);
				
				const staffIndex = this.selectedExecutors.findIndex(staff => staff.id === executorId);
				if (staffIndex !== -1) {
					this.selectedExecutors.splice(staffIndex, 1);
				}
			}
		},
		
		// 类型选择变更
		handleScheduleTypeChange(e) {
			this.scheduleTypeIndex = e.detail.value;
			this.formData.scheduleType = this.scheduleTypeOptions[this.scheduleTypeIndex].value;
		},
		// 巡检类型选择变更
		handlePatrolTypeChange(e) {
			this.patrolTypeIndex = e.detail.value;
			this.formData.patrolType = this.patrolTypeOptions[this.patrolTypeIndex].value;
		},
		// 开始日期变更
		handleStartDateChange(e) {
			this.formData.startDate = e.detail.value;
			
			if (this.formData.endDate && this.formData.endDate < this.formData.startDate) {
				this.formData.endDate = '';
			}
		},
		
		// 结束日期变更
		handleEndDateChange(e) {
			this.formData.endDate = e.detail.value;
		},
		
		// 显示任务选择
		showTaskSelector() {
			if (this.selectedDevices.length === 0) {
				uni.showToast({
					title: '请先选择巡检设备',
					icon: 'none'
				});
				return;
			}
			
			// 重置设备筛选为"全部设备"
			this.deviceFilterIndex = 0;
			this.currentFilterDeviceId = null;
			this.isPopupOpen = true;
			
			this.$refs.taskSelector.open();
		},
		
		// 隐藏任务选择
		hideTaskSelector() {
			// 用户点击关闭按钮时，提示未保存选择
			uni.showModal({
				title: '提示',
				content: '您尚未确认选择，关闭后已选项将不会保存，是否确认关闭？',
				success: (res) => {
					if (res.confirm) {
						this.isPopupOpen = false;
						this.$refs.taskSelector.close();
					}
				}
			});
		},
		
		// 切换任务选择状态
		toggleTaskSelection(index) {
			const template = this.filteredTemplates[index];
			const deviceItems = this.devicePatrolItems[template.deviceId];
			if (deviceItems) {
				const itemIndex = deviceItems.findIndex(item => item.id === template.id);
				if (itemIndex !== -1) {
					this.$set(deviceItems[itemIndex], 'selected', !deviceItems[itemIndex].selected);
				}
			}
		},
		
		// 切换周几选择
		toggleWeekDay(day) {
			const index = this.formData.scheduleWeekDays.indexOf(day);
			if (index === -1) {
				this.formData.scheduleWeekDays.push(day);
			} else {
				this.formData.scheduleWeekDays.splice(index, 1);
			}
		},
		
		// 切换月日选择
		toggleMonthDay(day) {
			const index = this.formData.scheduleMonthDays.indexOf(day);
			if (index === -1) {
				this.formData.scheduleMonthDays.push(day);
			} else {
				this.formData.scheduleMonthDays.splice(index, 1);
			}
		},
		
		// 确认任务选择
		confirmTaskSelection() {
			let selectedItems = [];
			Object.entries(this.devicePatrolItems).forEach(([deviceId, items]) => {
				const selected = items.filter(item => item.selected);
				if (selected.length > 0) {
					selectedItems = selectedItems.concat(selected.map(item => ({
						...item,
						deviceId: parseInt(deviceId)
					})));
				}
			});
			
			this.formData.patrolItem = selectedItems.map(item => ({
				id: item.id,
				itemId: item.itemId,
				deviceId: item.deviceId,
				itemName: item.itemName,
				categoryName: item.categoryName,
				checkMethod: item.checkMethod,
				normalRange: item.normalRange,
				description: item.description,
				paramType: item.paramType,
				unit: item.unit,
				importance: item.importance
			}));
			
			this.isPopupOpen = false;
			this.$refs.taskSelector.close();
			
			// 显示选择成功提示
			uni.showToast({
				title: `已添加${selectedItems.length}个巡检项`,
				icon: 'success',
				duration: 2000
			});
		},
		
		// 移除任务
		removeTask(index) {
			const task = this.formData.patrolItem[index];
			
			if (task && task.deviceId && this.devicePatrolItems[task.deviceId]) {
				const items = this.devicePatrolItems[task.deviceId];
				const itemIndex = items.findIndex(item => item.id === task.id);
				if (itemIndex !== -1) {
					this.$set(items[itemIndex], 'selected', false);
				}
			}
			
			this.formData.patrolItem.splice(index, 1);
		},
		
		// 提交表单
		submitPlan() {
			// 检查用户是否有可用的热用户权限
			if (this.filteredHeatUnits.length === 0) {
				uni.showToast({
					title: '您没有任何热用户的权限，无法创建巡检计划',
					icon: 'none',
					duration: 2000
				});
				return;
			}
			
			if (!this.selectedHeatUnit) {
				uni.showToast({
					title: '请选择热用户',
					icon: 'none'
				});
				return;
			}
			
			if (!this.formData.name) {
				uni.showToast({
					title: '请输入计划名?',
					icon: 'none'
				});
				return;
			}
			
			if (!this.formData.startDate) {
				uni.showToast({
					title: '请选择开始日期',
					icon: 'none'
				});
				return;
			}
			
			if (!this.formData.endDate) {
				uni.showToast({
					title: '请选择结束日期',
					icon: 'none'
				});
				return;
			}
			
			if (this.formData.executorIds.length === 0) {
				uni.showToast({
					title: '请选择执行人员',
					icon: 'none'
				});
				return;
			}
			
			if (this.formData.deviceIds.length === 0) {
				uni.showToast({
					title: '请选择巡检设备',
					icon: 'none'
				});
				return;
			}
			
			if (this.formData.scheduleType === 'custom' && !this.formData.scheduleInterval) {
				uni.showToast({
					title: '请输入巡检间隔天数',
					icon: 'none'
				});
				return;
			}
			
			if (this.formData.scheduleType === 'weekly' && this.formData.scheduleWeekDays.length === 0) {
				uni.showToast({
					title: '请选择巡检星期',
					icon: 'none'
				});
				return;
			}
			
			if (this.formData.scheduleType === 'monthly' && this.formData.scheduleMonthDays.length === 0) {
				uni.showToast({
					title: '请选择巡检日期',
					icon: 'none'
				});
				return;
			}
			
			if (this.formData.patrolItem.length === 0) {
				uni.showToast({
					title: '请添加巡检任务',
					icon: 'none'
				});
				return;
			}
			
			this.formData.patrolItem.forEach(item => {
				if (!item.deviceId && this.formData.deviceIds.length > 0) {
					item.deviceId = this.formData.deviceIds[0];
				}
			});
			
			const submitData = { 
				name: this.formData.name,
				start_date: this.formData.startDate,
				end_date: this.formData.endDate,
				executor_ids: this.formData.executorIds,
				patrol_type: this.formData.patrolType,
				locations: this.selectedHeatUnit.name, 
				device_ids: this.formData.deviceIds,
				schedule_type: this.formData.scheduleType,
				schedule_interval: this.formData.scheduleInterval,
				schedule_week_days: this.formData.scheduleWeekDays,
				schedule_month_days: this.formData.scheduleMonthDays,
				
				patrol_item: this.formData.patrolItem.map(item => ({
					device_patrol_item_id: item.id
				}))
			};
			
			uni.showLoading({
				title: '提交中...'
			});
			
			console.log('提交巡检计划数据:', JSON.stringify(submitData, null, 2));
			
			patrolApi.createPlan(submitData)
				.then(res => {
					uni.hideLoading();
					uni.showToast({
						title: '创建成功',
						icon: 'success'
					});
					
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				})
				.catch(err => {
					uni.hideLoading();
					uni.showToast({
						title: err.errMsg || '创建失败，请重试',
						icon: 'none'
					});
				});
		},
		
		// 搜索巡检项
		handleSearch() {
			console.log('搜索关键?', this.searchKeyword);
		},
		
		// 清空搜索
		clearSearch() {
			this.searchKeyword = '';
		},
		
		// 显示热用户选择
		showHeatUnitSelector() {
			if (this.filteredHeatUnits.length === 0) {
				uni.showToast({
					title: '无可用热用户',
					icon: 'none'
				});
				return;
			}
			this.isPopupOpen = true;
			this.$refs.heatUnitSelector.open();
		},
		
		// 隐藏热用户选择
		hideHeatUnitSelector() {
			this.isPopupOpen = false;
			this.$refs.heatUnitSelector.close();
			// 用户点击关闭按钮时，提示未保存选择
			// uni.showModal({
			// 	title: '提示',
			// 	content: '您尚未确认选择，关闭后已选项将不会保存，是否确认关闭？',
			// 	success: (res) => {
			// 		if (res.confirm) {
						
			// 		}
			// 	}
			// });
		},
		
		// 选择热用户
		selectHeatUnit(heatUnit) {
			this.selectedHeatUnit = heatUnit;
			console.log('选择热用户 ', heatUnit.name, 'ID:', heatUnit.id);
		},
		
		// 确认热用户选择
		confirmHeatUnitSelection() {
			if (this.selectedHeatUnit) {
				this.formData.heatUnitId = this.selectedHeatUnit.id;
				console.log('确认选择热用户', this.selectedHeatUnit.name, 'ID:', this.selectedHeatUnit.id);
				
				// 立即加载该热用户下的设备列表
				this.loadDevicesByHeatUnit(this.selectedHeatUnit.id);
				
				this.isPopupOpen = false;
				this.$refs.heatUnitSelector.close();
			} else {
				uni.showToast({
					title: '请先选择热用户',
					icon: 'none'
				});
				return;
			}
		},
		
		// 搜索热用户
		handleHeatUnitSearch() {
			if (!this.heatUnitSearchKeyword || this.heatUnitSearchKeyword.trim() === '') {
				this.filteredHeatUnits = [...this.heatUnitOptions];
				return;
			}
			
			const keyword = this.heatUnitSearchKeyword.toLowerCase().trim();
			this.filteredHeatUnits = this.heatUnitOptions.filter(unit => 
				unit.name.toLowerCase().includes(keyword)
			);
		},
		
		// 清空热用户搜索
		clearHeatUnitSearch() {
			this.heatUnitSearchKeyword = '';
			// 恢复到原始过滤后的热用户列表，而不是所有热用户
			this.filteredHeatUnits = [...this.heatUnitOptions];
		},
		
		// 获取设备名称
		getDeviceName(deviceId) {
			const device = this.selectedDevices.find(d => d.id === deviceId);
			return device ? device.name : '未知设备';
		},
		
		// 处理设备筛选
		handleDeviceFilterChange(e) {
			this.deviceFilterIndex = parseInt(e.detail.value);
			
			if (this.deviceFilterIndex === 0) {
				// 选择"全部设备"
				this.currentFilterDeviceId = null;
			} else {
				// 选择具体设备，索引需要减1（因为添加了"全部设备"选项）
				const selectedDevice = this.selectedDevices[this.deviceFilterIndex - 1];
				this.currentFilterDeviceId = selectedDevice ? selectedDevice.id : null;
			}
		},
		
		// 获取重要性标签
		getImportanceLabel(importance) {
			switch (importance) {
				case 'normal':
					return '普通';
				case 'important':
					return '重要';
				case 'critical':
					return '关键';
				default:
					return '普通';
			}
		},
		
		// 获取重要性类
		getImportanceClass(importance) {
			switch (importance) {
				case 'normal':
					return 'normal-importance';
				case 'important':
					return 'important-importance';
				case 'critical':
					return 'critical-importance';
				default:
					return 'normal-importance';
			}
		},
		
		// 显示设备选择器
		showDeviceSelector() {
			if (!this.selectedHeatUnit) {
				uni.showToast({
					title: '请先选择热用户',
					icon: 'none'
				});
				return;
			}
			
			// 初始化临时选中设备列表
			this.tempSelectedDevices = [...this.selectedDevices];
			this.isPopupOpen = true;
			this.$refs.deviceSelector.open();
		},
		
		// 隐藏设备选择器
		hideDeviceSelector() {
			uni.showModal({
				title: '提示',
				content: '您尚未确认选择，关闭后已选项将不会保存，是否确认关闭？',
				success: (res) => {
					if (res.confirm) {
						this.isPopupOpen = false;
						this.$refs.deviceSelector.close();
						// 恢复原来的选择
						this.tempSelectedDevices = [...this.selectedDevices];
					}
				}
			});
		},
		
		// 切换设备选择状态
		toggleDeviceSelection(device) {
			const index = this.tempSelectedDevices.findIndex(d => d.id === device.id);
			if (index === -1) {
				this.tempSelectedDevices.push(device);
			} else {
				this.tempSelectedDevices.splice(index, 1);
			}
		},
		
		// 确认设备选择
		confirmDeviceSelection() {
			if (this.tempSelectedDevices.length === 0) {
				uni.showToast({
					title: '请至少选择一个设备',
					icon: 'none'
				});
				return;
			}
			
			// 更新选中的设备列表
			this.selectedDevices = [...this.tempSelectedDevices];
			this.formData.deviceIds = this.selectedDevices.map(device => device.id);
			
			// 清空已选中的巡检项
			this.formData.patrolItem = [];
			
			// 重新加载所有选中设备的巡检项
			this.selectedDevices.forEach(device => {
				this.loadPatrolItemsByDevice(device.id);
			});
			
			this.isPopupOpen = false;
			this.$refs.deviceSelector.close();
			
			uni.showToast({
				title: `已选择${this.selectedDevices.length}个设备`,
				icon: 'success'
			});
		},
		
		// 检查设备是否被选中
		isDeviceSelected(deviceId) {
			return this.tempSelectedDevices.some(device => device.id === deviceId);
		},
		
		// 显示人员选择
		showStaffSelector() {
			// 初始化临时选中人员列表
			this.tempSelectedExecutors = [...this.selectedExecutors];
			this.isPopupOpen = true;
			this.$refs.staffSelector.open();
		},
		
		// 隐藏人员选择
		hideStaffSelector() {
			// 用户点击关闭按钮时，提示未保存选择
			uni.showModal({
				title: '提示',
				content: '您尚未确认选择，关闭后已选项将不会保存，是否确认关闭？',
				success: (res) => {
					if (res.confirm) {
						this.isPopupOpen = false;
						this.$refs.staffSelector.close();
						// 恢复原来的选择
						this.tempSelectedExecutors = [...this.selectedExecutors];
					}
				}
			});
		},
		
		// 切换人员选择状态
		toggleStaffSelection(staff) {
			const index = this.tempSelectedExecutors.findIndex(s => s.id === staff.id);
			if (index === -1) {
				this.tempSelectedExecutors.push(staff);
			} else {
				this.tempSelectedExecutors.splice(index, 1);
			}
		},
		
		// 检查人员是否被选中
		isStaffSelected(staffId) {
			return this.tempSelectedExecutors.some(staff => staff.id === staffId);
		},
		
		// 确认人员选择
		confirmStaffSelection() {
			if (this.tempSelectedExecutors.length === 0) {
				uni.showToast({
					title: '请至少选择一个人员',
					icon: 'none'
				});
				return;
			}
			
			// 更新选中的人员列表
			this.selectedExecutors = [...this.tempSelectedExecutors];
			this.formData.executorIds = this.selectedExecutors.map(staff => staff.id);
			
			this.isPopupOpen = false;
			this.$refs.staffSelector.close();
			
			uni.showToast({
				title: `已选择${this.selectedExecutors.length}个人员`,
				icon: 'success'
			});
		},
		
		// 加载巡检类型选项
		loadPatrolTypeOptions() {
			dictApi.getDictDataByDictId(11)
				.then(res => {
					if (res.code === 200 && res.data) {
						this.patrolTypeOptions = res.data.map(item => ({
							label: item.name,
							value: item.name
						}));

						// 如果没有数据，使用默认选项
						if (this.patrolTypeOptions.length === 0) {
							this.patrolTypeOptions = [
								{ label: "日常巡检", value: "日常巡检" },
								{ label: "设备巡检", value: "设备巡检" },
								{ label: "管道巡检", value: "管道巡检" },
								{ label: "阀门巡检", value: "阀门巡检" },
								{ label: "换热站巡检", value: "换热站巡检" }
							];
						}
					}
				})
				.catch(err => {
					console.error('获取巡检类型选项失败:', err);
				});
		},

		// 设备全选功能
		toggleDeviceSelectAll() {
			if (this.isAllDevicesSelected) {
				// 当前是全选状态，执行取消全选
				this.tempSelectedDevices = [];
			} else {
				// 当前不是全选状态，执行全选
				this.tempSelectedDevices = [...this.deviceOptions];
			}
		},

		// 巡检项全选功能
		toggleTaskSelectAll() {
			const shouldSelectAll = !this.isAllTasksSelected;

			// 遍历当前筛选的模板，设置选中状态
			this.filteredTemplates.forEach(template => {
				const deviceItems = this.devicePatrolItems[template.deviceId];
				if (deviceItems) {
					const itemIndex = deviceItems.findIndex(item => item.id === template.id);
					if (itemIndex !== -1) {
						this.$set(deviceItems[itemIndex], 'selected', shouldSelectAll);
					}
				}
			});

			// 显示操作提示
			const message = shouldSelectAll ?
				`已全选 ${this.filteredTemplates.length} 个巡检项` :
				`已取消全选 ${this.filteredTemplates.length} 个巡检项`;

			uni.showToast({
				title: message,
				icon: 'none',
				duration: 1500
			});
		},
	}
}

</script>

<style lang="scss" scoped> 

// 新增和调整的样式
$uni-bg-color-grey: #f8f8f8; // 背景色
$uni-text-color: #333; // 主要文字颜色
$uni-text-color-placeholder: #999; // 占位文字颜色
$uni-text-color-grey: #666; // 次要文字颜色
$uni-border-color: #e5e5e5; // 边框颜色
$uni-color-primary: #007aff; // 主题色

.patrol-create-container {
	background-color: $uni-bg-color-grey;
	min-height: 100vh;
	padding-bottom: 140rpx; // 为底部提交按钮留出空间

	.form-card {
		background-color: #fff;
		margin: 20rpx;
		padding: 0 30rpx 30rpx;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

		.card-title {
			font-size: 32rpx;
			font-weight: bold;
			color: $uni-text-color;
			padding: 30rpx 0;
			border-bottom: 1rpx solid $uni-border-color;
			margin-bottom: 20rpx;
		}
		.card-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 30rpx 0;
			border-bottom: 1rpx solid $uni-border-color;
			margin-bottom: 20rpx;
			.card-title {
				padding: 0;
				border-bottom: none;
				margin-bottom: 0;
			}
			.add-button {
				display: flex;
				align-items: center;
				color: $uni-color-primary;
				font-size: 28rpx;
				&.disabled {
					color: $uni-text-color-placeholder;
					pointer-events: none;
				}
				.iconfont {
					font-size: 36rpx;
					margin-right: 8rpx;
				}
			}
		}
	}

	.form-item {
		display: flex;
		flex-direction: column;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f0f0f0;

		&:last-child {
			border-bottom: none;
		}

		.form-label {
			font-size: 28rpx;
			color: $uni-text-color-grey;
			margin-bottom: 16rpx;
			&.required::before {
				content: '*';
				color: red;
				margin-right: 4rpx;
			}
		}
		
		.form-input, .form-picker, .form-textarea, .location-input {
			font-size: 30rpx;
			color: $uni-text-color;
			padding: 20rpx 0; // 调整padding使其上下对称
			min-height: 50rpx; // 确保即使内容为空也有一定高度
			display: flex;
			align-items: center;
			width: 100%;
			box-sizing: border-box;

			&::placeholder {
				color: $uni-text-color-placeholder;
			}
		}

		input.form-input {
			padding: 20rpx 0;
		}
		
		.plan-name-input {
			width: 100%; // Ensure it takes full width
			text-align: left; // Align text to the left
			display: block; // Ensure it behaves like a block element
			line-height: normal; // Reset line height if necessary
			height: auto; // Allow height to adjust to content
			min-height: 50rpx; // Maintain a minimum height
			white-space: normal; // Allow text to wrap if it's very long (though input usually doesn't wrap)
			word-break: break-all; // Break long words if necessary
		}
		
		textarea.form-textarea {
			padding: 20rpx 0;
			min-height: 150rpx; // 文本域最小高度
			line-height: 1.5;
		}

		.form-picker {
			.picker-value {
				display: flex;
				justify-content: space-between;
				align-items: center;
				width: 100%;
				&.placeholder-style {
					color: $uni-text-color-placeholder;
					padding-left: 0 !important; // 移除内联样式带来的影响
				}
			}
		}
		.location-input {
			display: flex;
			justify-content: space-between;
			align-items: center;
			width: 100%;
		}
		
		.arrow-down {
			font-size: 24rpx;
			color: $uni-text-color-grey;
			margin-left: 10rpx;
		}
		
		.picker-flex {
			display: flex;
			justify-content: space-between;
			align-items: center;
			width: 100%;
			color: $uni-text-color-placeholder; // 默认灰色
			& > text:first-child { // "选择人员" 等文字靠左
				flex: 1;
				color: $uni-text-color-placeholder; // 确保占位符颜色
			}
			&.has-value > text:first-child {
				color: $uni-text-color; // 有值时变黑
			}
		}

		.device-selector {
			.location-input {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 20rpx;
				background-color: #fff;
				border-radius: 8rpx;
				
				text {
					color: $uni-text-color;
					&:last-child {
						color: $uni-text-color-grey;
					}
				}
			}
		}
		.heatunit-selector{
			.location-input {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 20rpx;
				background-color: #fff;
				border-radius: 8rpx;
				
				text {
					color: $uni-text-color;
					&:last-child {
						color: $uni-text-color-grey;
					}
				}
			}
		}
		.staff-selector {
			.location-input {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 20rpx;
				background-color: #fff;
				// border: 1rpx solid $uni-border-color;
				border-radius: 8rpx;
				
				text {
					color: $uni-text-color;
					&:last-child {
						color: $uni-text-color-grey;
					}
				}
			}
		}
	}

	.selected-items {
		display: flex;
		flex-wrap: wrap;
		gap: 16rpx;
		margin-top: 16rpx;

		.selected-item {
			background-color: #e6f7ff;
			color: $uni-color-primary;
			padding: 8rpx 16rpx;
			border-radius: 8rpx;
			font-size: 26rpx;
			display: flex;
			align-items: center;

			.item-text {
				margin-right: 8rpx;
			}

			.remove-icon {
				font-size: 28rpx;
				font-weight: bold;
				color: $uni-color-primary;
				cursor: pointer;
			}
		}
	}

	.tag-group {
		display: flex;
		flex-wrap: wrap;
		gap: 16rpx;
		margin-top: 10rpx;
		.tag {
			padding: 10rpx 24rpx;
			border: 1rpx solid $uni-border-color;
			border-radius: 8rpx;
			font-size: 26rpx;
			color: $uni-text-color-grey;
			background-color: #fff;
			&.active {
				background-color: $uni-color-primary;
				color: #fff;
				border-color: $uni-color-primary;
			}
		}
	}
	
	.task-list {
		.task-item {
			background-color: #fff;
			border: 1rpx solid $uni-border-color;
			border-radius: 12rpx;
			padding: 24rpx;
			margin-bottom: 20rpx;
			box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.03);
			display: flex;
			justify-content: space-between;
			align-items: flex-start;

			.task-content {
				flex: 1;
				.task-title {
					font-size: 30rpx;
					font-weight: bold;
					color: $uni-text-color;
					margin-bottom: 12rpx;
				}
				.task-info-row {
					display: flex;
					align-items: center;
					justify-content: space-between;
					margin-bottom: 6rpx;
				}
				.task-info {
					font-size: 24rpx;
					color: $uni-text-color-grey;
					display: block;
					line-height: 1.5;
					margin-bottom: 6rpx;
					&:last-child {
						margin-bottom: 0;
					}
				}
			}
			.task-actions {
				.delete-icon {
					font-size: 36rpx;
					color: #ff5252; // 醒目的删除颜色
					padding-left: 20rpx; // 增加点击区域
				}
			}
		}
	}
	
	.empty-tip {
		text-align: center;
		padding: 40rpx 0;
		color: $uni-text-color-placeholder;
		font-size: 28rpx;
	}

	.submit-button {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: $uni-color-primary;
		color: #fff;
		font-size: 32rpx;
		font-weight: bold;
		height: 98rpx;
		line-height: 98rpx;
		text-align: center;
		border-radius: 0; // uni-app 按钮默认有圆角，如果需要贴合底部则去掉
		margin: 0; // 确保按钮填满底部，uni-app 可能需要用important
		// 对于 H5 和App，可能需要处理安全区
		padding-bottom: constant(safe-area-inset-bottom);  
		padding-bottom: env(safe-area-inset-bottom);
		box-sizing: border-box; // 确保 padding 不会增加总高度
		z-index: 1; // 确保不会覆盖弹窗
	}
}

// 弹窗统一样式 (uni-popup content)
.selector { // 用于 <uni-popup><view class="selector">...</view></uni-popup>
	background-color: #fff;
	border-radius: 20rpx 20rpx 0 0;
	display: flex;
	flex-direction: column;
	max-height: 80vh; // 限制最大高度
	overflow: hidden; // 防止内容溢出圆角

	.selector-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid $uni-border-color;

		.selector-title {
			font-size: 32rpx;
			font-weight: bold;
			color: $uni-text-color;
		}
		.header-actions {
			display: flex;
			align-items: center;
		}
		.close-button {
			font-size: 28rpx;
			color: $uni-text-color-grey;
		}
		.confirm-button-header {
			font-size: 28rpx;
			color: $uni-color-primary;
			font-weight: 500;
			margin-right: 20rpx;
		}
	}

	.selector-content {
		padding: 0 30rpx 30rpx;
		flex: 1; // 使内容区域可滚动
		overflow-y: auto;

		.template-search { // 也可用于热用户搜索
			display: flex;
			align-items: center;
			padding: 20rpx 0;
			.search-input {
				flex: 1;
				height: 72rpx;
				background-color: $uni-bg-color-grey;
				border-radius: 36rpx;
				padding: 0 30rpx;
				font-size: 28rpx;
			}
			.icon-clear {
				// 自行实现或使用图标
			}
			.search-btn {
				font-size: 28rpx;
				color: $uni-color-primary;
			}
		}
		
		.selector-content-footer {
			padding: 15rpx 0;
			border-top: 1rpx solid #e5e5e5;
			margin-top: 20rpx;
			background-color: #f8f8f8;
			box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.1);
			position: fixed;
			bottom: 0;
			left: 0;
			right: 0;
			z-index: 100;
			
			.select-count-container {
				font-size: 28rpx;
				color: #333;
				font-weight: bold;
				text-align: center;
				padding: 15rpx 0;
				display: flex;
				align-items: center;
				justify-content: center;
				
				.select-count-badge {
					width: 48rpx;
					height: 48rpx;
					background-color: $uni-color-primary;
					border-radius: 50%;
					margin-right: 16rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					
					.count-number {
						color: #fff;
						font-size: 28rpx;
						font-weight: bold;
					}
				}
				
				.count-text {
					color: #333;
					font-size: 30rpx;
				}
			}
		}

		.device-filter { // 用于巡检项弹窗中的设备筛选
			margin-bottom: 20rpx;
			
			.filter-title {
				font-size: 28rpx;
				color: $uni-text-color-grey;
				margin-bottom: 10rpx;
			}
			
			.form-picker {
				border: 1rpx solid $uni-border-color;
				border-radius: 8rpx;
				padding: 15rpx 20rpx;
				.picker-value {
					font-size: 28rpx;
				}
			}
		}
		
		// 全选功能样式 - 重新设计，模仿巡检项卡片样式
		.task-select-all-wrapper {
			margin-bottom: 20rpx;

			.task-select-all-card {
				background-color: #fff;
				border: 2rpx solid $uni-color-primary;
				border-radius: 12rpx;
				padding: 24rpx;
				box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.1);
				position: relative;

				// 添加渐变背景效果
				background: linear-gradient(135deg, #f0f8ff 0%, #ffffff 100%);

				.task-select-all-content {
					display: flex;
					align-items: flex-start;

					.task-select-checkbox {
						width: 40rpx;
						height: 40rpx;
						border: 2rpx solid $uni-color-primary;
						border-radius: 8rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						margin-right: 20rpx;
						margin-top: 5rpx;
						flex-shrink: 0;
						background-color: #fff;

						&.checked {
							background-color: $uni-color-primary;
							border-color: $uni-color-primary;

							.iconfont {
								color: #fff;
								font-size: 24rpx;
								font-weight: bold;
							}
						}
					}

					.task-select-info {
						flex: 1;

						.task-select-title {
							display: flex;
							justify-content: space-between;
							align-items: center;
							margin-bottom: 12rpx;

							.select-title-text {
								font-size: 32rpx;
								font-weight: bold;
								color: $uni-color-primary;
							}

							.select-status-badge {
								background-color: #e6f7ff;
								color: $uni-color-primary;
								font-size: 24rpx;
								padding: 6rpx 16rpx;
								border-radius: 20rpx;
								font-weight: 600;
								border: 1rpx solid #91d5ff;

								&.active {
									background-color: $uni-color-primary;
									color: #fff;
									border-color: $uni-color-primary;
								}

								.badge-text {
									font-size: 24rpx;
								}
							}
						}

						.task-select-desc {
							font-size: 24rpx;
							color: $uni-text-color-grey;
							line-height: 1.4;
						}
					}
				}

				// 添加点击效果
				&:active {
					transform: scale(0.98);
					transition: transform 0.1s ease;
				}
			}
		}

		// 设备全选功能样式
		.device-select-all-wrapper {
			margin-bottom: 20rpx;

			.device-select-all-card {
				background-color: #fff;
				border: 2rpx solid $uni-color-primary;
				border-radius: 12rpx;
				padding: 24rpx;
				box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.1);
				position: relative;

				// 添加渐变背景效果
				background: linear-gradient(135deg, #f0f8ff 0%, #ffffff 100%);

				.device-select-all-content {
					display: flex;
					align-items: flex-start;

					.device-select-checkbox {
						width: 40rpx;
						height: 40rpx;
						border: 2rpx solid $uni-color-primary;
						border-radius: 8rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						margin-right: 20rpx;
						margin-top: 5rpx;
						flex-shrink: 0;
						background-color: #fff;

						&.checked {
							background-color: $uni-color-primary;
							border-color: $uni-color-primary;

							.iconfont {
								color: #fff;
								font-size: 24rpx;
								font-weight: bold;
							}
						}
					}

					.device-select-info {
						flex: 1;

						.device-select-title {
							display: flex;
							justify-content: space-between;
							align-items: center;
							margin-bottom: 12rpx;

							.select-title-text {
								font-size: 32rpx;
								font-weight: bold;
								color: $uni-color-primary;
							}

							.select-status-badge {
								background-color: #e6f7ff;
								color: $uni-color-primary;
								font-size: 24rpx;
								padding: 6rpx 16rpx;
								border-radius: 20rpx;
								font-weight: 600;
								border: 1rpx solid #91d5ff;

								&.active {
									background-color: $uni-color-primary;
									color: #fff;
									border-color: $uni-color-primary;
								}

								.badge-text {
									font-size: 24rpx;
								}
							}
						}

						.device-select-desc {
							font-size: 24rpx;
							color: $uni-text-color-grey;
							line-height: 1.4;
						}
					}
				}

				// 添加点击效果
				&:active {
					transform: scale(0.98);
					transition: transform 0.1s ease;
				}
			}
		}

		// 设备列表样式
		.device-list {
			.device-item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 25rpx 0;
				border-bottom: 1rpx solid #f0f0f0;
				&:last-child {
					border-bottom: none;
				}
				.device-name {
					font-size: 28rpx;
					color: $uni-text-color;
				}
				.checkbox {
					width: 40rpx;
					height: 40rpx;
					border: 1rpx solid $uni-border-color;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					&.checked {
						background-color: $uni-color-primary;
						border-color: $uni-color-primary;
						.iconfont {
							color: #fff;
							font-size: 24rpx;
						}
					}
				}
			}
		}

		// 热用户列表项
		.heatunit-list {
			.heatunit-item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 25rpx 0;
				border-bottom: 1rpx solid #f0f0f0;
				&:last-child {
					border-bottom: none;
				}
				.heatunit-name {
					font-size: 28rpx;
					color: $uni-text-color;
				}
				.checkbox {
					width: 40rpx;
					height: 40rpx;
					border: 1rpx solid $uni-border-color;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					&.checked {
						background-color: $uni-color-primary;
						border-color: $uni-color-primary;
						.iconfont {
							color: #fff;
							font-size: 24rpx;
						}
					}
				}
			}
		}

		// 巡检项模板列表
		.task-templates {
			.template-item {
				display: flex;
				align-items: flex-start;
				padding: 20rpx 0;
				border-bottom: 1rpx solid #f0f0f0;

				&:last-child {
					border-bottom: none;
				}

				&.active {
					background-color: #f0f8ff;
				}

				.checkbox {
					width: 40rpx;
					height: 40rpx;
					border: 1rpx solid $uni-border-color;
					border-radius: 8rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					margin-right: 20rpx;
					margin-top: 5rpx;
					flex-shrink: 0;

					&.checked {
						background-color: $uni-color-primary;
						border-color: $uni-color-primary;
						.iconfont {
							color: #fff;
							font-size: 24rpx;
						}
					}
				}

				.template-left {
					flex: 1;

					.template-header {
						display: flex;
						justify-content: space-between;
						align-items: center;
						margin-bottom: 12rpx;

						.template-title {
							font-size: 30rpx;
							font-weight: bold;
							color: $uni-text-color;
							flex: 1;
						}
					}

					.template-info {
						font-size: 24rpx;
						color: $uni-text-color-grey;
						line-height: 1.5;
						margin-bottom: 6rpx;

						&:last-child {
							margin-bottom: 0;
						}

						&.template-description {
							color: $uni-text-color;
							font-weight: 500;
						}
					}
				}
			}
		}
	}

	.selector-footer {
		padding: 25rpx 30rpx 40rpx;
		background-color: #fff;
		border-top: 1rpx solid #e5e5e5;
		box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.08);
		display: flex;
		justify-content: space-between;
		align-items: center;
		// 处理 iPhone X 等底部安全区
		padding-bottom: calc(40rpx + constant(safe-area-inset-bottom));  
		padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
		background-color: #fff; /* 确保背景色不是透明的 */
		position: relative; /* 确保显示在内容之上 */
		z-index: 1;

		.select-count {
			font-size: 28rpx;
			color: $uni-text-color;
		}
		.confirm-button {
			background-color: $uni-color-primary;
			color: #fff;
			padding: 15rpx 40rpx;
			border-radius: 30rpx;
			font-size: 28rpx;
		}
	}
	
	// 特别为热用户选择器添加的样式
	.heat-unit-selector {
		z-index: 100; // 确保覆盖底部按钮
		
		// 优化热用户选择器的整体样式
		background-color: #fff;
		border-radius: 20rpx 20rpx 0 0;
		max-height: 80vh;
		height: 80vh;
		display: flex;
		flex-direction: column;

		.selector-content {
			flex: 1;
			overflow-y: auto;
			-webkit-overflow-scrolling: touch;
		}
	}
	
	// 巡检项选择器样式
	/* 巡检项选择器样式已移至task-selector中 */
}

// 确保 .placeholder-style 生效并覆盖内联样式
.placeholder-style {
	color: $uni-text-color-placeholder !important; 
	padding-left: 0 !important; // 覆盖内联样式
}
 

.iconfont {
	// font-family: "iconfont" !important;
	font-size: 16px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

// 使用通用字符作为图标
.icon-add:before {
	content: "+"; // 使用加号替代添加图标
}
.icon-check:before {
	content: "✓"; // 使用对勾符号替代勾选图标
}
.icon-clear:before {
	content: "×"; // 使用乘号替代清除图标
}

.importance-tag {
	padding: 4rpx 8rpx;
	border-radius: 4rpx;
	font-size: 22rpx;
	&.normal-importance {
		background-color: #e6f7ff;
		color: #1890ff;
		border: 1rpx solid #91d5ff;
	}
	&.important-importance {
		background-color: #fff7e6;
		color: #fa8c16;
		border: 1rpx solid #ffd591;
	}
	&.critical-importance {
		background-color: #fff1f0;
		color: #f5222d;
		border: 1rpx solid #ffa39e;
	}
}

.task-info-row {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 6rpx;
}

.template-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12rpx;
	
	.template-title {
		font-size: 30rpx;
		font-weight: bold;
		color: $uni-text-color;
		word-break: break-all;
		margin-right: 15rpx;
		line-height: 1.4;
	}
	
	.importance-tag {
		padding: 4rpx 8rpx;
		border-radius: 4rpx;
		font-size: 22rpx;
		&.normal-importance {
			background-color: #e6f7ff;
			color: #1890ff;
			border: 1rpx solid #91d5ff;
		}
		&.important-importance {
			background-color: #fff7e6;
			color: #fa8c16;
			border: 1rpx solid #ffd591;
		}
		&.critical-importance {
			background-color: #fff1f0;
			color: #f5222d;
			border: 1rpx solid #ffa39e;
		}
	}
}

.task-selector {
	background-color: #fff;
	border-radius: 20rpx 20rpx 0 0;
	display: flex;
	flex-direction: column;
	max-height: 80vh;
	height: 80vh;
	overflow: hidden;
	z-index: 100; 

	.selector-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid $uni-border-color;
		
		.selector-title {
			font-size: 32rpx;
			font-weight: bold;
			color: $uni-text-color;
		}
		
		.header-actions {
			display: flex;
			align-items: center;
		}
		
		.close-button {
			font-size: 28rpx;
			color: $uni-text-color-grey;
		}
		
		.confirm-button-header {
			font-size: 28rpx;
			color: $uni-color-primary;
			font-weight: 500;
			margin-right: 20rpx;
		}
	}

	.selector-content {
		flex: 1;
		overflow-y: auto;
		padding: 0 20rpx;
		-webkit-overflow-scrolling: touch;
		
		.device-filter {
			display: flex;
			align-items: center;
			padding: 0 10rpx 20rpx;
			margin-bottom: 0;
			position: relative;
			
			.filter-picker {
				flex: 1;
				height: 80rpx;
				
				.picker-value {
					background: #f5f5f5;
					border-radius: 40rpx;
					padding: 0 30rpx;
					height: 80rpx;
					font-size: 28rpx;
					color: $uni-text-color;
					display: flex;
					justify-content: space-between;
					align-items: center;
					
					.arrow-down {
						color: #999;
						font-size: 24rpx;
					}
				}
			}
		}
		
		.template-search {
			display: flex;
			align-items: center;
			padding: 0 10rpx 20rpx;
			border-bottom: 1rpx solid #f0f0f0;
			margin-bottom: 20rpx;
			position: relative;
			
			.search-input {
				flex: 1;
				height: 80rpx;
				border-radius: 40rpx;
				background: #f5f5f5;
				font-size: 28rpx;
				padding: 0 80rpx 0 30rpx;
				color: $uni-text-color;
			}
			
			.search-clear {
				position: absolute;
				right: 130rpx;
				top: 50%;
				transform: translateY(-50%);
				width: 40rpx;
				height: 40rpx;
				line-height: 40rpx;
				text-align: center;
				border-radius: 50%;
				background: #ccc;
				color: #fff;
				font-size: 24rpx;
			}
			
			.search-btn {
				padding: 0 30rpx;
				font-size: 28rpx;
				color: $uni-color-primary;
				height: 80rpx;
				line-height: 80rpx;
			}
		}
		
		.task-templates {
			padding-bottom: 20rpx;
			
			.template-item {
				display: flex;
				justify-content: space-between;
				align-items: flex-start;
				background-color: #fff;
				border: 1rpx solid $uni-border-color;
				border-radius: 12rpx;
				padding: 24rpx;
				margin-bottom: 20rpx;
				box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.03);
				transition: all 0.2s ease-in-out;

				&.active {
					background-color: #e9f5ff;
					border-color: $uni-color-primary;
					box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.1);
				}

				.checkbox {
					width: 40rpx;
					height: 40rpx;
					min-width: 40rpx;
					border: 1rpx solid $uni-border-color;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					margin-right: 15rpx;
					align-self: flex-start;
					margin-top: 4rpx;
					
					&.checked {
						background-color: $uni-color-primary;
						border-color: $uni-color-primary;
						.iconfont {
							color: #fff;
							font-size: 24rpx;
						}
					}
				}

				.template-left {
					flex: 1;
					overflow: hidden;

					.template-header {
						display: flex;
						align-items: center;
						justify-content: space-between;
						margin-bottom: 12rpx;

						.template-title {
							font-size: 30rpx;
							font-weight: bold;
							color: $uni-text-color;
							word-break: break-all;
							margin-right: 15rpx;
							line-height: 1.4;
						}
					}

					.template-info {
						font-size: 24rpx;
						color: $uni-text-color-grey;
						display: block;
						line-height: 1.5;
						margin-bottom: 6rpx;
						word-break: break-all;
						
						&:last-child {
							margin-bottom: 0;
						}
					}
				}
			}
		}
		
		
		.empty-tip {
			text-align: center;
			padding: 40rpx 0;
			color: $uni-text-color-placeholder;
			font-size: 26rpx;
		}
	}
}

.device-selector-popup {
    z-index: 100;
    background-color: #fff;
    border-radius: 20rpx 20rpx 0 0;
    max-height: 60vh;
    height: 60vh;
    display: flex;
    flex-direction: column;

    .selector-content {
        flex: 1;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
        padding: 0 30rpx;

        .device-list {
            .device-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 25rpx 0;
                border-bottom: 1rpx solid #f0f0f0;
                
                &:last-child {
                    border-bottom: none;
                }
                
                .device-name {
                    font-size: 28rpx;
                    color: $uni-text-color;
                }
                
                .checkbox {
                    width: 40rpx;
                    height: 40rpx;
                    border: 1rpx solid $uni-border-color;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    
                    &.checked {
                        background-color: $uni-color-primary;
                        border-color: $uni-color-primary;
                        
                        .iconfont {
                            color: #fff;
                            font-size: 24rpx;
                        }
                    }
                }
            }
        }
    }
}

.staff-selector-popup {
	z-index: 100;
	background-color: #fff;
	border-radius: 20rpx 20rpx 0 0;
	max-height: 80vh;
	height: 80vh;
	display: flex;
	flex-direction: column;

	.selector-content {
		flex: 1;
		overflow-y: auto;
		-webkit-overflow-scrolling: touch;
		padding: 0 30rpx;

		.staff-list {
			.staff-item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 25rpx 0;
				border-bottom: 1rpx solid #f0f0f0;
				
				&:last-child {
					border-bottom: none;
				}
				
				.staff-name {
					font-size: 28rpx;
					color: $uni-text-color;
				}
				
				.checkbox {
					width: 40rpx;
					height: 40rpx;
					border: 1rpx solid $uni-border-color;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					
					&.checked {
						background-color: $uni-color-primary;
						border-color: $uni-color-primary;
						
						.iconfont {
							color: #fff;
							font-size: 24rpx;
						}
					}
				}
			}
		}
	}
}

</style>