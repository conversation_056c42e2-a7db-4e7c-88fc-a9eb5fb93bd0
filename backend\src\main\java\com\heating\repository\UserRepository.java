
package com.heating.repository;

import com.heating.entity.order.TWorkOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import java.util.List;
import java.util.Optional;
import org.springframework.stereotype.Repository;

import com.heating.entity.user.TUser; 

@Repository
public interface UserRepository extends JpaRepository<TUser, Long> {

    boolean existsByUsername(String username); 
    boolean existsByEmail(String email);
    
    // 检查手机号是否存在
    @Query("SELECT COUNT(u) > 0 FROM TUser u WHERE u.phone = :phone")
    boolean existsByPhone(String phone);

    @Query("SELECT p.permissionCode FROM TSysRolePermission p WHERE p.roleCode = :roleCode")
    List<String> findPermissionsByRoleCode(String roleCode);

    // 根据用户名查询用户
    @Query("SELECT u FROM TUser u WHERE u.username = :username")
    Optional<TUser> findByUsername(String username);
    
    // 根据手机号查询用户
    @Query("SELECT u FROM TUser u WHERE u.phone = :phone")
    Optional<TUser> findByPhone(String phone);

    // 根据用户id查询用户
    @Query("SELECT u FROM TUser u WHERE u.id = :id")
    Optional<TUser> findById(Long id);

}
