
package com.heating.controller;

import com.heating.exception.GlobalExceptionHandler;
import com.heating.dto.fault.FaultReportRequest;
import com.heating.dto.fault.FaultSetStatusRequest;
import com.heating.dto.fault.FaultWeeklyCountResponse;
import com.heating.dto.fault.FaultMessageResponse;
import com.heating.service.FaultService;
import com.heating.util.ApiResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.query.Param;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.sql.Date;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/faults")
@Validated
public class FaultController {

    private static final Logger logger = LoggerFactory.getLogger(FaultController.class);
    
    @Autowired
    private FaultService faultService; // Service for handling fault-related operations

    /**
     * 上报故障接口
     * @param request 故障报告请求体
     * @return 返回故障报告的结果
     */
    @PostMapping("/report")
    public ResponseEntity<ApiResponse<?>> reportFault(@RequestBody FaultReportRequest request) {
        logger.info("上报故障: {}", request); // Log the fault report request
        try {
            faultService.reportFault(request); 
            return ResponseEntity.ok(
                    ApiResponse.success("故障上报成功", null)    // Return success response
            );
        } catch (RuntimeException e) {
            // 直接返回原始错误信息，特别是对于住户信息相关的错误
            logger.error("上报故障时出错: {}", e.getMessage()); // Log error
            return ResponseEntity.ok(
                ApiResponse.error("上报故障错误: " + e.getMessage())
            );
        } catch (Exception e) {
            logger.error("上报故障时出错: {}", e.getMessage(), e); // Log error
            return GlobalExceptionHandler.errorResponseEntity(
                "上报故障错误: " + e.getMessage(),
                HttpStatus.INTERNAL_SERVER_ERROR // Return error response
            );
        }
    }

    /**
     * 故障状态设定接口
     * @param request 确认故障请求体，包含故障ID和操作信息
     * @return 返回确认故障的结果
     */
    @PostMapping("/status")
    public ResponseEntity<ApiResponse<?>> faultStatus(@RequestBody FaultSetStatusRequest request) {
        try {
            faultService.setFaultStatus(request);
            return ResponseEntity.ok(
                ApiResponse.success("故障状态已更新为："+request.getFaultStatus(), null) // Return success response
            );
        } catch (Exception e) {
            logger.error("更新故障状态时出错: {}", e.getMessage()); // Log error
            return GlobalExceptionHandler.errorResponseEntity(
                "更新故障状态错误: " + e.getMessage(),
                HttpStatus.INTERNAL_SERVER_ERROR // Return error response
            );
        }
    }

    /**
     * 获取故障列表接口
     * @param status 故障状态（可选）
     * @param date 故障发生日期（可选）
     * @param heatUnitId 热用户ID（可选，用于按项目权限筛选故障）
     * @param page 页码，默认1
     * @param pageSize 每页数量，默认10
     * @return 返回故障列表
     */
    @GetMapping("/list")
    public ResponseEntity<ApiResponse<?>> getFaultList(
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String date,
            @RequestParam(required = false) String heatUnitId,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        try {
            Date sqlDate = null;
            if (date != null && !date.trim().isEmpty()) {
                try {
                    LocalDate localDate = LocalDate.parse(date);
                    sqlDate = Date.valueOf(localDate);
                } catch (Exception e) {
                    throw new IllegalArgumentException("无效的日期格式，请使用 yyyy-MM-dd 格式");
                }
            }

            logger.info("获取故障列表: status={}, date={}, heatUnitId={}, page={}, pageSize={}", 
                       status, sqlDate, heatUnitId, page, pageSize);
                       
            Map<String, Object> result = faultService.getFaultList(status, sqlDate, heatUnitId, page, pageSize);
            return ResponseEntity.ok(
                ApiResponse.success("故障列表获取成功", result)
            );
        } catch (Exception e) {
            logger.error("获取故障列表时出错: {}", e.getMessage());
            return GlobalExceptionHandler.errorResponseEntity(
                "获取故障列表错误: " + e.getMessage(),
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * 获取故障详情接口
     * @param id 故障ID
     * @return 返回指定故障的详细信息
     */
    @GetMapping("/detail/{id}")
    public ResponseEntity<ApiResponse<?>> getFaultDetail(@PathVariable("id") Long id) {
        try {
            logger.info("获取故障详情: id={}", id);
            Map<String, Object> data = faultService.getFaultDetail(id);
            
            // 添加调试日志
            logger.info("故障详情数据: {}", data.keySet());
            
            return ResponseEntity.ok(
                ApiResponse.success("故障详情获取成功", data)
            );
        } catch (Exception e) {
            logger.error("获取故障详情失败: {}", e.getMessage(), e);
            return GlobalExceptionHandler.errorResponseEntity(
                "获取故障详情失败: " + e.getMessage(),
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * 获取本周故障告警数量
     */
    @GetMapping("/weekly-count")
    public ResponseEntity<ApiResponse<FaultWeeklyCountResponse>> getWeeklyFaultCount() {
        try {
            FaultWeeklyCountResponse count = faultService.getWeeklyFaultCount();
            return ResponseEntity.ok(ApiResponse.success("本周故障告警数量获取成功", count));
        } catch (Exception e) {
            return ResponseEntity.ok(ApiResponse.error("本周故障告警数量获取失败: " + e.getMessage()));
        }
    }
    
    /**
     * 根据管理员ID获取故障列表
     */
    @GetMapping("/manager/{managerId}")
    public ResponseEntity<ApiResponse<List<Map<String, Object>>>> getFaultsByManager(@PathVariable Long managerId) {
        try {
            List<Map<String, Object>> faults = faultService.getFaultsByManagerId(managerId);
            return ResponseEntity.ok(ApiResponse.success("获取管理员负责的故障列表成功", faults));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.ok(ApiResponse.error("获取管理员负责的故障列表失败: " + e.getMessage()));
        }
    }

    /**
     * 获取故障消息
     * 从故障信息表中获取状态为 '待确认' 的记录列表
     * @param uid 用户ID
     * @param role 用户角色
     * @param heatUnitId 用户项目权限，逗号分隔的项目ID字符串
     * @return 返回待确认的故障消息列表
     */
    @GetMapping("/messages")
    public ResponseEntity<?> getFaultMessages(
            @RequestParam(required = false) Long uid,
            @RequestParam(required = false) String role,
            @RequestParam(required = false) String heatUnitId) {
        logger.info("获取故障消息: uid={}, role={}, heatUnitId={}", uid, role, heatUnitId);
        try {
            List<FaultMessageResponse> messages = faultService.getFaultMessages(uid, role, heatUnitId);
            return ResponseEntity.ok(ApiResponse.success("故障消息获取成功", messages));
        } catch (Exception e) {
            logger.error("获取故障消息失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
}
