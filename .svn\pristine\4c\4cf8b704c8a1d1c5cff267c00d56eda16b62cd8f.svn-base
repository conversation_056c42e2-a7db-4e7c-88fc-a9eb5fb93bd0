package com.heating.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.heating.entity.order.TOperationLog;

import java.util.List;

@Repository
public interface OperationLogRepository extends JpaRepository<TOperationLog, Long> {

    @Query("SELECT ol.operationType as operationType, ol.operationDesc as operationDesc, ol.operatorId as operatorId, ol.createdAt as createdTime, u.name as operatorName FROM TOperationLog ol LEFT JOIN TUser u ON ol.operatorId = u.id WHERE ol.workOrderId = :workOrderId")
    List<TOperationLog> findByWorkOrderId(@Param("workOrderId") long workOrderId);

    @Query("SELECT ol.operationType as operationType, ol.operationDesc as operationDesc, ol.operatorId as operatorId, ol.createdAt as createdTime, u.name as operatorName FROM TOperationLog ol LEFT JOIN TUser u ON ol.operatorId = u.id WHERE ol.operatorId = :operatorId")
    List<TOperationLog> findByOperatorId(@Param("operatorId") long operatorId); 
}