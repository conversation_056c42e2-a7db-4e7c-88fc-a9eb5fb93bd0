package com.heating.service.impl;

import com.heating.dto.system.AppSystemParamResponse;
import com.heating.entity.system.TAppSystemParam;
import com.heating.repository.system.AppSystemParamRepository;
import com.heating.service.AppSystemParamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 系统参数Service实现类
 */
@Service
public class AppSystemParamServiceImpl implements AppSystemParamService {

    @Autowired
    private AppSystemParamRepository appSystemParamRepository;

    /**
     * 获取系统参数
     * @param id 系统参数ID，默认为1
     * @return 系统参数DTO
     */
    @Override
    public AppSystemParamResponse getSystemParam(Integer id) {
        // 如果id为null，则默认获取id=1的系统参数
        if (id == null) {
            id = 1;
        }
        
        // 查询系统参数
        TAppSystemParam param = appSystemParamRepository.findById(id)
                .orElse(null);
        
        // 如果未找到系统参数，返回默认参数
        if (param == null) {
            return getDefaultSystemParam();
        }
        
        // 转换为DTO
        AppSystemParamResponse response = new AppSystemParamResponse();
        response.setSystemName(param.getSystemName());
        response.setSystemLogo(param.getSystemLogo());
        response.setSystemVersions(param.getSystemVersions());
        response.setCopyright(param.getCopyright());
        response.setCompany(param.getCompany());
        response.setLinkman(param.getLinkman());
        response.setMobile(param.getMobile());
        response.setInternetAddr(param.getInternetAddr());
        response.setCompanyAddr(param.getCompanyAddr());
        response.setIntro(param.getIntro());
        response.setEmail(param.getEmail());
        response.setReleaseDate(param.getReleaseDate());
        
        // 构建号：版本号+日期
        String buildNumber = "Build " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        response.setBuildNumber(buildNumber);
        
        return response;
    }
    
    /**
     * 获取默认系统参数
     * @return 默认系统参数DTO
     */
    private AppSystemParamResponse getDefaultSystemParam() {
        AppSystemParamResponse response = new AppSystemParamResponse();
        response.setSystemName("热力维护管理系统");
        response.setSystemLogo("/static/logo.png");
        response.setSystemVersions("V1.0.0");
        response.setCopyright("Copyright © 2024 陕西杰明新能源有限公司");
        response.setCompany("陕西杰明新能源有限公司");
        response.setLinkman("客服");
        response.setMobile("029-88888888");
        response.setInternetAddr("www.example.com");
        response.setCompanyAddr("陕西省西安市");
        response.setIntro("热力维护管理系统是一款专业的热力设备维护管理软件，提供工单管理、巡检管理、设备管理等功能。");
        
        // 构建号：版本号+日期
        String buildNumber = "Build " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        response.setBuildNumber(buildNumber);
        
        return response;
    }
} 