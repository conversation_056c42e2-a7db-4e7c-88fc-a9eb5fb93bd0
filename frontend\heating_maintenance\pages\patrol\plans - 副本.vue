<template>
  <view class="patrol-plans-container">
    <!-- 搜索栏 -->
    <view class="search-filter-area">
      <view class="search-bar">
        <view class="search-input-wrapper">
<!--          <text class="iconfont icon-search"></text> -->
          <input
            type="text"
            placeholder="搜索巡检计划"
            v-model="searchKeyword"
            confirm-type="search"
            @confirm="handleSearch"
          />
          <text
            class="iconfont icon-clear"
            v-if="searchKeyword"
            @click="clearSearch"
          ></text>
        </view>
      </view>

      <!-- 筛选栏 -->
      <view class="filter-bar">
        <view class="filter-item" @click="showStatusFilter">
          <text>状态: {{ statusFilterText }}</text>
		      <text>▼</text>
     <!--     <text class="iconfont icon-arrow-down"></text> -->
        </view>
        <view class="filter-item" @click="showTimeFilter">
          <text>时间: {{ timeFilterText }}</text>
        <!--  <text class="iconfont icon-arrow-down"></text> -->
		    <text>▼</text>
        </view>
        <view class="filter-item" @click="showTypeFilter">
          <text>类型: {{ typeFilterText }}</text>
         <!-- <text class="iconfont icon-arrow-down"></text> -->
		     <text>▼</text>
        </view>
      </view>
    </view>

    <!-- 巡检计划列表 -->
    <view class="plan-list" v-if="patrolPlans.length > 0">
      <view
        class="plan-item"
        v-for="(plan, index) in patrolPlans"
        :key="index"
        @click="navigateToPlanDetail(plan.id)"
      >
        <view class="plan-header">
          <text class="plan-title">{{ plan.title }}</text>
          <view class="plan-status" :class="plan.status">{{
            getStatusText(plan.status)
          }}</view>
        </view>

        <view class="plan-info">
          <view class="info-row">
            <view class="info-item">
              <text class="item-label">计划编号:</text>
              <text class="item-value">{{ plan.code }}</text>
            </view>
            <view class="info-item">
              <text class="item-label">巡检类型:</text>
              <text class="item-value">{{ plan.type }}</text>
            </view>
          </view>

          <view class="info-row">
            <view class="info-item">
              <text class="item-label">开始时间:</text>
              <text class="item-value">{{ formatDate(plan.startTime) }}</text>
            </view>
            <view class="info-item">
              <text class="item-label">结束时间:</text>
              <text class="item-value">{{ formatDate(plan.endTime) }}</text>
            </view>
          </view>

          <view class="info-row">
            <view class="info-item">
              <text class="item-label">巡检地点:</text>
              <text class="item-value">{{ plan.location }}</text>
            </view>
            <view class="info-item">
              <text class="item-label">负责人:</text>
              <text class="item-value">{{ plan.manager }}</text>
            </view>
          </view>
        </view>

        <view class="plan-footer">
          <view class="plan-actions">
            <PermissionCheck permission="patrol:plans:execute">
              <view
                v-if="plan.isPlay"
                class="action-button start"
                @click.stop="startPatrolPlan(plan.id)"
              >
                <text class="iconfont icon-play"></text>
                <text>开始巡检</text>
              </view>

              <view
                v-if="plan.status === 'in_progress'"
                class="action-button continue"
                @click.stop="continuePatrolPlan(plan.id)"
              >
                <text class="iconfont icon-continue"></text>
                <text>继续巡检</text>
              </view>
            </PermissionCheck>

            <view
              class="action-button view"
              @click.stop="navigateToPlanDetail(plan.id)"
            >
               <!-- <text class="iconfont icon-detail"></text> -->
                <text>查看详情</text>
              </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载中 -->
    <view class="loading-container" v-if="isLoading">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 加载失败 -->
    <view class="error-container" v-if="loadError && !isLoading">
      <text class="error-text">加载失败，请重试</text>
      <button class="retry-btn" @click="loadPatrolPlans">重新加载</button>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" v-if="!isLoading && patrolPlans.length === 0">
      <image class="empty-image" src="/static/images/empty.png" mode="aspectFit"></image>
      <text class="empty-text">暂无巡检计划</text>
    </view>

    <!-- 状态筛选弹窗 -->
    <uni-popup ref="statusFilterPopup" type="bottom">
      <view class="filter-popup">
        <view class="popup-header">
          <text class="popup-title">状态筛选</text>
          <text class="popup-close" @click="$refs.statusFilterPopup.close()">关闭</text>
        </view>
        <view class="popup-content">
          <view
            class="filter-option"
            v-for="(option, index) in statusOptions"
            :key="index"
            :class="{ active: statusFilter === option.value }"
            @click="selectStatusFilter(option.value)"
          >
            {{ option.label }}
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 时间筛选弹窗 -->
    <uni-popup ref="timeFilterPopup" type="bottom">
      <view class="filter-popup">
        <view class="popup-header">
          <text class="popup-title">时间筛选</text>
          <text class="popup-close" @click="$refs.timeFilterPopup.close()">关闭</text>
        </view>
        <view class="popup-content">
          <view
            class="filter-option"
            v-for="(option, index) in timeOptions"
            :key="index"
            :class="{ active: timeFilter === option.value }"
            @click="selectTimeFilter(option.value)"
          >
            {{ option.label }}
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 类型筛选弹窗 -->
    <uni-popup ref="typeFilterPopup" type="bottom">
      <view class="filter-popup">
        <view class="popup-header">
          <text class="popup-title">类型筛选</text>
          <text class="popup-close" @click="$refs.typeFilterPopup.close()">关闭</text>
        </view>
        <view class="popup-content">
          <view
            class="filter-option"
            v-for="(option, index) in typeOptions"
            :key="index"
            :class="{ active: typeFilter === option.value }"
            @click="selectTypeFilter(option.value)"
          >
            {{ option.label }}
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 底部新建按钮 -->
    <PermissionCheck permission="patrol:plans:add">
      <view class="floating-button" @click="navigateToCreatePlan">
        <text class="plus-icon">+</text>
      </view>
    </PermissionCheck>
  </view>
</template>

<script>
import { patrolApi } from "@/utils/api.js";
import PermissionCheck from "@/components/PermissionCheck.vue";

export default {
  components: {
    PermissionCheck, // 本地注册组件
  },
  data() {
    return {
      searchKeyword: "",
      patrolPlans: [],
      currentPage: 1,
      pageSize: 3,
      hasMore: true,
      isLoading: false,
      loadError: false,

      // 筛选相关
      statusFilter: "all",
      timeFilter: "all",
      typeFilter: "all",

      // 筛选选项
      statusOptions: [
        { label: "全部", value: "all" },
        { label: "待执行", value: "pending" },
        { label: "进行中", value: "processing" },
        { label: "已完成", value: "completed" },
        { label: "已超时", value: "overdue" },
        { label: "已取消", value: "canceled" },
      ],
      timeOptions: [
        { label: "全部时间", value: "all" },
        { label: "今天", value: "today" },
        { label: "本周", value: "this_week" },
        { label: "本月", value: "this_month" },
        { label: "上个月", value: "last_month" },
      ],
      typeOptions: [
        { label: "全部类型", value: "all" },
        { label: "日常巡检", value: "日常巡检" },
        { label: "设备巡检", value: "设备巡检" },
        { label: "管道巡检", value: "管道巡检" },
        { label: "阀门巡检", value: "阀门巡检" },
        { label: "换热站巡检", value: "换热站巡检" },
      ],
    };
  },
  computed: {
    statusFilterText() {
      const option = this.statusOptions.find((item) => item.value === this.statusFilter);
      return option ? option.label : "全部";
    },
    timeFilterText() {
      const option = this.timeOptions.find((item) => item.value === this.timeFilter);
      return option ? option.label : "全部时间";
    },
    typeFilterText() {
      const option = this.typeOptions.find((item) => item.value === this.typeFilter);
      return option ? option.label : "全部类型";
    },
  },
  onPullDownRefresh() {
    this.currentPage = 1;
    this.patrolPlans = [];
    this.hasMore = true;
    this.loadPatrolPlans().then(() => {
      uni.stopPullDownRefresh();
    });
  },
    // 上拉加载更多
  onReachBottom() {
	  console.log("执行上拉加载！")
    if (this.hasMore && !this.isLoading) {
      this.currentPage++;
      this.loadPatrolPlans(true);
    }
  },
  onLoad() {
    this.loadPatrolPlans();
  },
  methods: {
    // 加载巡检计划列表
    async loadPatrolPlans(isLoadMore = false) {
      if (this.isLoading) return;
      this.isLoading = true;
      
      if (!isLoadMore) {
        this.loadError = false;
      }

      try {
        // 准备请求参数
        const params = {
          page: this.currentPage,
          pageSize: this.pageSize,
          name: this.searchKeyword,
          status: this.statusFilter !== "all" ? this.statusFilter : "",
          type: this.typeFilter !== "all" ? this.typeFilter : "",
          timeRange: this.timeFilter !== "all" ? this.timeFilter : "",
          locations: ""
        };
        console.log("请求参数:", params);
        // 调用API获取巡检计划列表
        const res = await patrolApi.getPlanList(params);
        console.log("API响应:", res);

        if (res.code === 200) {
          // 兼容不同的数据结构
          let planList = [];
          let totalPages = 1;
          
          if (Array.isArray(res.data)) {
            // 如果直接返回数组
            planList = res.data;
            totalPages = Math.ceil(planList.length / this.pageSize);
          } else if (res.data && res.data.list) {
            // 如果返回对象中包含list字段
            planList = res.data.list;
            totalPages = res.data.totalPages || Math.ceil(res.data.total / this.pageSize) || 1;
          } else if (res.data) {
            // 其他情况，尝试使用res.data
            planList = Array.isArray(res.data) ? res.data : [res.data];
            totalPages = 1;
          }
          
          // 转换为页面需要的格式
          const formattedPlans = planList.map((item) => ({
            id: item.id,
            title: item.name,
            code: item.planNo || item.plan_no,
            type: item.patrolType || item.patrol_type,
            startTime: item.startDate || item.start_date,
            endTime: item.endDate || item.end_date,
            location: item.locations,
            manager: item.executors?.join("、") || "未分配",
            status: item.status,
            isPlay: item.isPlay
          }));

          if (isLoadMore) {
            // 加载更多模式：追加数据
            this.patrolPlans = [...this.patrolPlans, ...formattedPlans];
          } else {
            // 刷新模式：替换数据
            this.patrolPlans = formattedPlans;
          }

          // 判断是否还有更多数据
          this.hasMore = this.currentPage < totalPages;
        } else {
          this.loadError = true;
          uni.showToast({
            title: res.message || "获取巡检计划失败",
            icon: "none"
          });
        }
      } catch (error) {
        console.error("获取巡检计划列表失败:", error);
        this.loadError = true;
        uni.showToast({
          title: "网络异常，请稍后重试",
          icon: "none"
        });
      } finally {
        this.isLoading = false;
      }
    },

    // 搜索处理
    handleSearch() {
      this.currentPage = 1;
      this.patrolPlans = [];
      this.hasMore = true;
      this.loadPatrolPlans();
    },

    // 清除搜索关键词
    clearSearch() {
      this.searchKeyword = "";
      this.currentPage = 1;
      this.patrolPlans = [];
      this.hasMore = true;
      this.loadPatrolPlans();
    },

    // 显示状态筛选弹窗
    showStatusFilter() {
      this.$refs.statusFilterPopup.open();
    },

    // 显示时间筛选弹窗
    showTimeFilter() {
      this.$refs.timeFilterPopup.open();
    },

    // 显示类型筛选弹窗
    showTypeFilter() {
      this.$refs.typeFilterPopup.open();
    },

    // 选择状态筛选
    selectStatusFilter(value) {
      this.statusFilter = value;
      this.currentPage = 1;
      this.patrolPlans = [];
      this.hasMore = true;
      this.loadPatrolPlans();
      this.$refs.statusFilterPopup.close();
    },

    // 选择时间筛选
    selectTimeFilter(value) {
      this.timeFilter = value;
      this.currentPage = 1;
      this.patrolPlans = [];
      this.hasMore = true;
      this.loadPatrolPlans();
      this.$refs.timeFilterPopup.close();
    },

    // 选择类型筛选
    selectTypeFilter(value) {
      this.typeFilter = value;
      this.currentPage = 1;
      this.patrolPlans = [];
      this.hasMore = true;
      this.loadPatrolPlans();
      this.$refs.typeFilterPopup.close();
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        pending: "待执行",
        in_progress: "进行中",
        completed: "已完成",
        overdue: "已超时",
        canceled: "已取消",
      };
      return statusMap[status] || "未知";
    },

    // 导航到巡检计划详情
    navigateToPlanDetail(id) {
      uni.navigateTo({
        url: `/pages/patrol/detail?id=${id}`,
      });
    },

    // 导航到创建巡检计划
    navigateToCreatePlan() {
      uni.navigateTo({
        url: "/pages/patrol/create",
      });
    },

    // 开始巡检
    startPatrolPlan(id) {
      uni.navigateTo({
        url: `/pages/patrol/execute?id=${id}`,
      });
    },

    // 继续巡检
    continuePatrolPlan(id) {
      uni.navigateTo({
        url: `/pages/patrol/execute?id=${id}`,
      });
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return "";
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return dateStr;

      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");

      return `${year}-${month}-${day}`;
    },
  },
};
</script>

<style lang="scss">
.patrol-plans-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
  padding-top: 0;
  padding-bottom: 120rpx; // 为底部按钮留出空间
}

/* 页面标题 */
.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 90rpx;
  background-color: #0088ff;
  color: #ffffff;
  font-size: 34rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 999;
}

/* 搜索和筛选区域样式 */
.search-filter-area {
  background-color: #fff;
  border-bottom: 1rpx solid #eeeeee;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 100;
}

/* 搜索栏样式 */
.search-bar {
  padding: 20rpx 24rpx 16rpx;

  .search-input-wrapper {
    display: flex;
    align-items: center;
    background-color: #f5f5f5;
    border-radius: 36rpx;
    padding: 14rpx 24rpx;

    .iconfont {
      font-size: 28rpx;
      color: #999;
    }

    input {
      flex: 1;
      height: 40rpx;
      font-size: 28rpx;
      margin: 0 16rpx;
      color: #333;
    }
  }
}

/* 筛选栏样式 */
.filter-bar {
  display: flex;
  padding: 0;
  margin: 0 12rpx 16rpx;
  height: 70rpx;

  .filter-item {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 26rpx;
    color: #333;
    height: 100%;
    position: relative;

    &:after {
      content: "";
      position: absolute;
      right: 0;
      top: 20%;
      height: 60%;
      width: 1rpx;
      background-color: #eee;
    }

    &:last-child:after {
      display: none;
    }

    text {
      margin-right: 6rpx;
    }

    .iconfont {
      font-size: 22rpx;
      color: #999;
      margin-left: 4rpx;
    }
  }
}

.plan-list {
  padding: 20rpx;

  .plan-item {
    background-color: #fff;
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    padding: 24rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    width: 100%;
    box-sizing: border-box;
    border: 1rpx solid #eeeeee;

    .plan-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16rpx;

      .plan-title {
        font-size: 30rpx;
        font-weight: bold;
        color: $uni-text-color;
        max-width: 65%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .plan-status {
        padding: 4rpx 16rpx;
        border-radius: 24rpx;
        font-size: 24rpx;
        font-weight: normal;

        &.pending {
          background-color: rgba(24, 144, 255, 0.08);
          color: #1890ff;
        }

        &.in_progress {
          background-color: rgba(82, 196, 26, 0.08);
          color: #52c41a;
        }

        &.completed {
          background-color: rgba(82, 196, 26, 0.08);
          color: #52c41a;
        }

        &.overdue {
          background-color: rgba(245, 34, 45, 0.08);
          color: #f5222d;
        }

        &.canceled {
          background-color: rgba(102, 102, 102, 0.08);
          color: #999999;
        }
      }
    }

    .plan-info {
      padding: 16rpx 0;
      border-top: 1rpx solid #f2f2f2;
      border-bottom: 1rpx solid #f2f2f2;
      margin-bottom: 16rpx;

      .info-row {
        display: flex;
        margin-bottom: 16rpx;
        flex-wrap: wrap;

        &:last-child {
          margin-bottom: 0;
        }

        .info-item {
          flex: 0 0 50%;
          display: flex;
          align-items: flex-start;
          min-width: 0;
          box-sizing: border-box;
          padding-right: 15rpx;
          margin-bottom: 10rpx;
          overflow: hidden;

          .item-label {
            width: 130rpx;
            flex-shrink: 0;
            font-size: 26rpx;
            color: #888888;
          }

          .item-value {
            flex: 1;
            font-size: 26rpx;
            color: #333333;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: calc(100% - 130rpx);
          }
        }
      }
    }

    .plan-footer {
      .plan-actions {
        display: flex;
        justify-content: flex-end;
        flex-wrap: wrap;

        .action-button {
          display: flex;
          align-items: center;
          padding: 8rpx 20rpx;
          border-radius: 28rpx;
          margin-left: 16rpx;
          margin-bottom: 8rpx;
          transition: all 0.2s;

          &:active {
            opacity: 0.8;
          }

          .iconfont {
            font-size: 24rpx;
            margin-right: 8rpx;
          }

          text {
            font-size: 26rpx;
          }

          &.start {
            background-color: rgba(24, 144, 255, 0.1);
            color: #1890ff;
          }

          &.continue {
            background-color: rgba(82, 196, 26, 0.1);
            color: #52c41a;
          }

          &.view {
            background-color: #f5f5f5;
            color: #666666;
            margin-right: 0;
            padding: 8rpx 20rpx;
          }
        }
      }
    }
  }
}

// 加载状态
.loading-container {
  display: flex;
  justify-content: center;
  padding: 30rpx 0;

  .loading-text {
    font-size: 28rpx;
    color: #999;
  }
}

// 错误状态
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 50rpx 0;

  .error-text {
    font-size: 28rpx;
    color: #f5222d;
    margin-bottom: 20rpx;
  }

  .retry-btn {
    font-size: 28rpx;
    color: #fff;
    background-color: #1890ff;
    padding: 8rpx 30rpx;
    border-radius: 30rpx;
  }
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .empty-image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 30rpx;
  }

  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}

.filter-popup {
  background-color: #fff;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #eee;

    .popup-title {
      font-size: 32rpx;
      font-weight: bold;
    }

    .popup-close {
      font-size: 28rpx;
      color: $uni-color-primary;
    }
  }

  .popup-content {
    padding: 20rpx 30rpx;
    max-height: 600rpx;
    overflow-y: auto;

    .filter-option {
      padding: 20rpx 0;
      font-size: 30rpx;
      color: $uni-text-color;
      border-bottom: 1rpx solid #eee;

      &.active {
        color: $uni-color-primary;
        font-weight: bold;
      }

      &:last-child {
        border-bottom: none;
      }
    }
  }
}

.floating-button {
  position: fixed;
  right: 30rpx;
  bottom: 30rpx;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: #0088ff;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 136, 255, 0.4);
  z-index: 10;

  .plus-icon {
    font-size: 60rpx;
    color: #fff;
    font-weight: normal;
    line-height: 60rpx;
    margin-top: -3rpx;
  }
}
</style>
