package com.heating.dto.user;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import javax.validation.constraints.Email;


public record RegisterRequest(
    @NotEmpty(message = "用户名不能为空")
    @Size(min = 4, max = 50, message = "用户名长度必须在4-50个字符之间")
    String username,
    
    @NotEmpty(message = "密码不能为空")
    @Size(min = 6, message = "密码长度不能少于6个字符")
    String password,
    
    @NotEmpty(message = "姓名不能为空")
    @Size(max = 50, message = "姓名长度不能超过50个字符")
    String name,
    
    @NotEmpty(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    String email,
    
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    String phone
) {
    public RegisterRequest {
        if (username != null) username = username.trim();
        if (password != null) password = password.trim();
        if (name != null) name = name.trim();
        if (email != null) email = email.trim();
        if (phone != null) phone = phone.trim();
    }
} 