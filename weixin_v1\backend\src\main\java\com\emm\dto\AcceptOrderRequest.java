package com.emm.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public class AcceptOrderRequest {
    @JsonProperty("order_id")
    private String orderId;

    @JsonProperty("action")
    private String action;  // accept/transfer

    @JsonProperty("repair_user_id")
    private Long repairUserId;

    @JsonProperty("operator_id")
    private Long operatorId;

    // Getters and Setters
    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public Long getRepairUserId() {
        return repairUserId;
    }

    public void setRepairUserId(Long repairUserId) {
        this.repairUserId = repairUserId;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }
} 