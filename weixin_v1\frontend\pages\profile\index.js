Page({
  data: {
    userInfo: {
      avatar: '',
      name: '张三',
      role: '维修人员'
    }
  },

  onLoad() {
    this.loadUserInfo();
    this.getImages();
  },

  getImages: function() {
    var that = this;
    wx.request({
      url: 'http://localhost:5000/api/images', 
      method: 'GET',
      success: function(res) {
        that.setData({
          imageList: res.data
        });
      },
      fail: function(error) {
        console.error(error);
        wx.showToast({
          title: '获取图片失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  chooseImage: function() {
    var that = this;
    wx.chooseImage({
      count: 1,
      sizeType: ['original', 'compressed'],
      sourceType: ['album', 'camera'],
      success: function(res) {
        that.uploadImage(res.tempFilePaths[0]);
      }
    })
  },

  uploadImage: function(filePath) {
    var that = this;
    wx.uploadFile({
      url: 'http://your_backend_url/upload',  // 替换为你的后端上传接口
      filePath: filePath,
      name: 'image',
      success: function(res) {
        var data = JSON.parse(res.data);
        wx.showToast({
          title: '上传成功',
          icon: 'success',
          duration: 2000
        });
        that.getImages();  // 重新获取图片列表
      },
      fail: function(error) {
        console.error(error);
        wx.showToast({
          title: '上传失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  }, 

  loadUserInfo() {
    // TODO: 调用接口获取用户信息
    // 这里使用模拟数据
    // this.setData({
    //   userInfo: {
    //     avatar: '/images/empty.png',
    //     name: '张三',
    //     role: '维修人员'
    //   }
    // });

    const userInfo = wx.getStorageSync('userInfo');

    console.log(userInfo);


    if (userInfo) {
      this.setData({
        userInfo: userInfo
      });
    }
  },

  goToMyFaults() {
    wx.navigateTo({
      url: '/pages/fault/list/index?type=my'
    });
  },

  goToMyWorkorders() {
    wx.navigateTo({
      url: '/pages/workorder/list/index?type=my'
    });
  },

  goToMyTemps() {
    wx.navigateTo({
      url: '/pages/temperature/list/index?type=my'
    });
  },

  goToSettings() {
    wx.navigateTo({
      url: '/pages/settings/index'
    });
  },

  goToAbout() {
    wx.navigateTo({
      url: '/pages/about/index'
    });
  },

  handleLogout() {
    wx.showModal({
      title: '提示',
      content: '确认退出登录？',
      success: (res) => {
        if (res.confirm) {
          // TODO: 调用退出登录接口
          wx.showToast({
            title: '已退出登录',
            icon: 'success',
            duration: 2000,
            success: () => {
              // 清除本地存储的用户信息
              wx.clearStorageSync();
              // 重置用户信息
              this.setData({
                userInfo: {
                  avatar: '',
                  name: '',
                  role: ''
                }
              });
            }
          });
        }
      }
    });
  }
}); 