<template>
  <view class="login-container">
    <!-- 背景圆形 -->
    <view class="bg-circle bg-circle-1"></view>
    <view class="bg-circle bg-circle-2"></view>
    <view class="bg-circle bg-circle-3"></view>

    <!-- 顶部小方块 LOGO -->
    <view class="logo-box" v-if="!showRegister">
      <view class="logo-text"></view>
    </view>
    <!-- 主标题 -->
    <view class="title-box" v-if="!showRegister">
      <text class="main-title">洁明智慧供热</text>
      <text class="sub-title">AI连接未来</text>
    </view>

    <!-- 登录表单 -->
    <view class="login-form" v-if="!showRegister">
      <!-- 用户名/手机号输入框 -->
      <view class="input-item">
        <input
          type="text"
          v-model="loginAccount"
          placeholder="请输入用户名或手机号"
          maxlength="20"
          placeholder-class="placeholder"
        />
      </view>

      <!-- 密码输入框 -->
      <view class="input-item">
        <input
          type="password"
          v-model="password"
          placeholder="请输入密码"
          maxlength="20"
          password
          placeholder-class="placeholder"
        />
      </view>

      <!-- 登录按钮 -->
      <view class="login-btn" @click="handleLogin">
        <text>登 录</text>
      </view>

      <!-- 其他登录方式 -->
      <!-- <view class="other-login">
        <text class="other-title">其他登录方式</text>
        <view class="login-icons">
          <view class="login-icon wechat-icon" @click="loginWithWechat"></view>
          <view class="login-icon phone-icon" @click="loginWithPhone"></view>
        </view>
      </view> -->
    </view>

    <!-- 注册表单 -->
    <view class="register-form" v-if="showRegister">
      <!-- 用户名输入框 -->
      <view class="input-item">
        <input
          type="text"
          v-model="registerForm.username"
          placeholder="请输入用户名"
          placeholder-class="placeholder"
        />
      </view>
      <!-- 用户名输入框 -->
      <view class="input-item">
        <input
          type="text"
          v-model="registerForm.name"
          placeholder="请输入姓名"
          placeholder-class="placeholder"
        />
      </view>
      <!-- 手机号输入框 -->
      <view class="input-item">
        <input
          type="number"
          v-model="registerForm.phone"
          placeholder="请输入手机号"
          maxlength="11"
          placeholder-class="placeholder"
        />
      </view>

      <!-- 验证码输入框 -->
     <!-- <view class="input-item verification-code">
        <input
          type="number"
          v-model="registerForm.verificationCode"
          placeholder="请输入验证码"
          maxlength="6"
          placeholder-class="placeholder"
        />
        <view
          class="get-code-btn"
          :class="{ disabled: countDown > 0 }"
          @click="getVerificationCode"
        >
          <text>{{ countDown > 0 ? `${countDown}秒后重新获取` : "获取验证码" }}</text>
        </view>
      </view> -->
 

      <!-- 密码输入框 -->
      <view class="input-item">
        <input
          type="password"
          v-model="registerForm.password"
          placeholder="请设置密码"
          maxlength="20"
          password
          placeholder-class="placeholder"
        />
      </view>

      <!-- 确认密码输入框 -->
      <view class="input-item">
        <input
          type="password"
          v-model="registerForm.confirmPassword"
          placeholder="请确认密码"
          maxlength="20"
          password
          placeholder-class="placeholder"
        />
      </view>

      <!-- 注册按钮 -->
      <view class="login-btn register-btn" @click="handleRegister">
        <text>注 册</text>
      </view>

      <!-- 返回登录 -->
      <view class="switch-action" @click="toggleForm">
        <text>已有账号？返回登录</text>
      </view>
    </view>

    <!-- 底部用户协议 -->
    <view class="agreement">
      <text class="agreement-text" v-if="!showRegister">登录即表示同意</text>
      <text class="agreement-text" v-else>注册即表示同意</text>
      <text class="agreement-link" @click="openAgreement">《用户协议》</text>
      <text class="register-link" v-if="!showRegister" @click="toggleForm"
        >新用户注册</text>
    </view>
  </view>
</template>

<script>
import { userApi, testApiConnection } from "../../utils/api";

export default {
  data() {
    return {
      loginAccount: "", // 用户名或手机号
      password: "", // 密码
      apiConnected: true, // API连接状态
      showRegister: false, // 是否显示注册表单
      countDown: 0, // 验证码倒计时
      registerForm: {
        username: "",
        password: "",
        confirmPassword: "",
        name: "",
        phone: "",
        verificationCode: "",
      },
    };
  },
  onLoad() {
    // 检测API连接
    this.checkApiConnection();
  },
  methods: {
    // 检测API连接
    async checkApiConnection() {
      try {
        // 检查API服务是否可连接
        console.log("开始检测API连接...");
        this.apiConnected = await testApiConnection();
        console.log("API连接检测结果:", this.apiConnected);

        if (!this.apiConnected) {
          uni.showToast({
            title: "无法连接到服务器，请确认后端服务是否已启动",
            icon: "none",
            duration: 3000,
          });
        }
      } catch (err) {
        console.error("API连接检测出错:", err);
        this.apiConnected = false;
      }
    },

    // 处理登录
    handleLogin() {
      // 表单验证
      if (!this.loginAccount.trim()) {
        uni.showToast({
          title: "请输入用户名或手机号",
          icon: "none",
          position: 'center'
        });
        return;
      }

      if (!this.password.trim()) {
        uni.showToast({
          title: "请输入密码",
          icon: "none",
          position: 'center'
        });
        return;
      }

      // 显示加载
      uni.showLoading({
        title: "登录中...",
      });

      console.log("准备提交登录请求:", this.loginAccount);

      // 判断输入的是手机号还是用户名
      const isPhone = /^1\d{10}$/.test(this.loginAccount);
      
      // 使用原生请求，避免跨域问题
      uni.request({
        url: `${this.$baseUrl}/api/auth/login`,
        method: "POST",
        data: {
          username: this.loginAccount,
          password: this.password,
          loginType: isPhone ? 'phone' : 'username' // 添加登录类型标识
        },
        header: {
          "Content-Type": "application/json",
        },
        withCredentials: false,
        success: (res) => {
          uni.hideLoading();
          console.log("登录响应:", res);

          // 检查返回状态
          if (res.statusCode === 200 && res.data.code === 200) {
            // 登录成功
            const { token, userId, role, permissions, isPositioning, heatUnitId } = res.data.data;
            console.log("userId=======", userId);
            console.log("heatUnitId=======", heatUnitId);
            // 存储登录信息
            uni.setStorageSync("token", token);
            uni.setStorageSync("userId", userId);
            uni.setStorageSync("userRole", role);
            uni.setStorageSync("userPermissions", permissions);
            uni.setStorageSync("heatUnitId", heatUnitId || ""); // 存储热力单位ID
        
            // 存储定位设置参数
            uni.setStorageSync("isPositioning", isPositioning || 0);
            console.log("是否开启定位上传: ", isPositioning || 0);
            console.log("用户热力单位ID: ", heatUnitId || "无");

            // 获取用户详细信息
            this.getUserInfo();

            // 提示登录成功
            uni.showToast({
              title: "登录成功",
              icon: "success",
            });

            // 跳转到首页
            setTimeout(() => {
              uni.reLaunch({
                url: "/pages/home/<USER>",
              });
            }, 1000);
          } else {
           // 登录失败
           uni.showToast({
             title: res.data?.message || "登录失败，请检查用户名和密码",
             icon: "none",
           });
          }
        },
        fail: (err) => {
          uni.hideLoading();

          // 增强的错误处理
          console.error("登录请求失败详情:", err);

          // 处理跨域错误
          if (err.errMsg.includes("OPTIONS")) {
            uni.showToast({
              title: "跨域请求失败，请检查后端CORS配置",
              icon: "none",
              duration: 3000,
            });
            return;
          }

          // 根据错误类型提供不同的错误提示
          let errorMsg = "网络异常，请稍后重试";

          if (err.errMsg.includes("timeout")) {
            errorMsg = "请求超时，请检查网络连接";
          } else if (err.errMsg.includes("fail")) {
            errorMsg = "无法连接到服务器，请检查网络或后端服务状态";
          }

          // 显示错误提示
          uni.showToast({
            title: errorMsg,
            icon: "none",
            duration: 3000,
          });
        },
      });
    },

    // 获取用户详细信息
    getUserInfo() {
      userApi
        .getUserInfo()
        .then((res) => {
          if (res.code === 200) {
            // 存储用户信息
            uni.setStorageSync("userInfo", res.data);
          } else {
            console.error("获取用户信息失败:", res);
          }
        })
        .catch((err) => {
          console.error("获取用户信息请求失败:", err);
        });
    },

    // 打开用户协议
    openAgreement() {
      uni.navigateTo({
        url: "/pages/user/agreement",
      });
    },

    // 切换登录/注册表单
    toggleForm() {
      this.showRegister = !this.showRegister;
    },

    // 获取验证码
    getVerificationCode() {
      // 倒计时中不允许再次获取
      if (this.countDown > 0) {
        return;
      }

      // 验证手机号
      if (!this.registerForm.phone.trim()) {
        uni.showToast({
          title: "请输入手机号",
          icon: "none",
        });
        return;
      }

      if (!/^1\d{10}$/.test(this.registerForm.phone)) {
        uni.showToast({
          title: "请输入正确的手机号",
          icon: "none",
        });
        return;
      }

      // 显示加载
      uni.showLoading({
        title: "发送中...",
      });

      // 假设这里调用发送验证码的API
      // 真实环境应当调用后端接口发送验证码
      setTimeout(() => {
        uni.hideLoading();

        // 开始倒计时
        this.countDown = 60;
        const timer = setInterval(() => {
          this.countDown--;
          if (this.countDown <= 0) {
            clearInterval(timer);
          }
        }, 1000);

        uni.showToast({
          title: "验证码已发送",
          icon: "success",
        });
      }, 1500);
    },

    // 处理注册
    handleRegister() {
      // 表单验证
      if (!this.registerForm.phone.trim()) {
        uni.showToast({
          title: "请输入手机号",
          icon: "none",
        });
        return;
      }

      if (!/^1\d{10}$/.test(this.registerForm.phone)) {
        uni.showToast({
          title: "请输入正确的手机号",
          icon: "none",
        });
        return;
      }

      if (!this.registerForm.username.trim()) {
        uni.showToast({
          title: "请输入用户名",
          icon: "none",
        });
        return;
      }

      if (!this.registerForm.password.trim()) {
        uni.showToast({
          title: "请输入密码",
          icon: "none",
        });
        return;
      }

      if (this.registerForm.password !== this.registerForm.confirmPassword) {
        uni.showToast({
          title: "两次输入的密码不一致",
          icon: "none",
        });
        return;
      }

      if (!this.registerForm.name.trim()) {
        uni.showToast({
          title: "请输入姓名",
          icon: "none",
        });
        return;
      }

      // 显示加载
      uni.showLoading({
        title: "注册中...",
      });

      // 发送注册请求
      uni.request({
        url: `${this.$baseUrl}/api/auth/register`,
        method: "POST",
        data: {
          username: this.registerForm.username,
          password: this.registerForm.password,
          name: this.registerForm.name,
          phone: this.registerForm.phone,
        },
        header: {
          "Content-Type": "application/json",
        },
        success: (res) => {
          uni.hideLoading();
          console.log("注册响应:", res);

          // 检查返回状态
          if (res.statusCode === 200 && res.data.code === 200) {
            // 注册成功
            uni.hideLoading();
			uni.showToast({
			  title: "该账号暂未审核,请等待管理员审核",
			   duration: 3000,
			  icon: "none",
			});
            // 清空表单并切换到登录页
            setTimeout(() => {
              this.loginAccount = this.registerForm.username;
              this.password = this.registerForm.password;
              this.registerForm = {
                username: "",
                password: "",
                confirmPassword: "",
                name: "",
                phone: "",
                verificationCode: "",
              };
              this.showRegister = false;
            }, 2500);
          } else {
            // 注册失败
            uni.showToast({
              title: res.data?.message || "注册失败，请稍后重试",
              icon: "none",
              duration: 2000
            });
          }
        },
        fail: (err) => {
          uni.hideLoading();
          console.error("注册请求失败:", err);

          uni.showToast({
            title: "网络异常，请稍后重试",
            icon: "none",
          });
        },
      });
    },
  },
};
</script>

<style lang="scss">
/* Add page-level styles to prevent scrolling */
page {
  height: 100%;
  overflow: hidden; /* 改回hidden防止滚动 */
}

.login-container {
  display: flex;
  flex-direction: column;
  height: 100vh; /* 使用固定高度而不是min-height */
  background-color: #0a0e17;
  position: relative;
  overflow: hidden; /* 改回hidden防止滚动 */
  padding: 0 40rpx;
  box-sizing: border-box;
  padding-top: 120rpx;
  padding-bottom: 100rpx;
}

/* 背景圆形 */
.bg-circle {
  position: absolute;
  border-radius: 50%;
  z-index: 0;

  &.bg-circle-1 {
    width: 500rpx;
    height: 500rpx;
    background: linear-gradient(135deg, rgba(28, 40, 86, 0.6), rgba(41, 26, 87, 0.6));
    top: -100rpx;
    right: -150rpx;
  }

  &.bg-circle-2 {
    width: 700rpx;
    height: 700rpx;
    background: linear-gradient(135deg, rgba(28, 40, 86, 0.4), rgba(41, 26, 87, 0.4));
    bottom: -300rpx;
    left: -300rpx;
  }

  &.bg-circle-3 {
    width: 300rpx;
    height: 300rpx;
    background: linear-gradient(135deg, rgba(41, 26, 87, 0.3), rgba(28, 40, 86, 0.3));
    top: 40%;
    right: -100rpx;
  }
}

/* LOGO 方块 */
.logo-box {
  width: 100rpx;
  height: 100rpx;
  background-color: transparent;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 80rpx; /* Reduced from 150rpx */
  position: relative;
  z-index: 1;

  .logo-text {
    width: 100%;
    height: 100%;
    background-image: url("@/static/images/logo4.png");
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
  }
}

/* 标题 */
.title-box {
  margin-top: 30rpx; /* Reduced from 60rpx */
  margin-bottom: 40rpx; /* Reduced from 80rpx */
  position: relative;
  z-index: 1;

  .main-title {
    font-size: 48rpx; /* Reduced from 56rpx */
    font-weight: bold;
    color: #ffffff;
    display: block;
    margin-bottom: 10rpx; /* Reduced from 16rpx */
  }

  .sub-title {
    font-size: 28rpx; /* Reduced from 32rpx */
    color: rgba(255, 255, 255, 0.6);
    display: block;
  }
}

/* 登录表单 */
.login-form {
  position: relative;
  z-index: 1;
 margin-top: 60rpx;
  .input-item {
    height: 90rpx; /* Reduced from 100rpx */
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 45rpx;
    margin-bottom: 30rpx; /* Reduced from 40rpx */
    padding: 0 40rpx;
    display: flex;
    align-items: center;

    input {
      flex: 1;
      height: 100%;
      color: #ffffff;
      font-size: 28rpx; /* Reduced from 30rpx */
    }

    .placeholder {
      color: rgba(255, 255, 255, 0.4);
    }
  }

  .login-btn {
    height: 90rpx; /* Reduced from 100rpx */
    background: linear-gradient(90deg, #0ab9fd, #2b79fb);
    border-radius: 45rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 40rpx; /* Reduced from 60rpx */
    margin-bottom: 40rpx; /* Reduced from 80rpx */
    color:#2b79fb;
    text {
      color: #ffffff;
      font-size: 32rpx;
      font-weight: 500;
    }
  }
}

/* 其他登录方式 */
.other-login {
  display: flex;
  flex-direction: column;
  align-items: center;

  .other-title {
    color: rgba(255, 255, 255, 0.5);
    font-size: 28rpx;
    margin-bottom: 40rpx;
  }

  .login-icons {
    display: flex;
    justify-content: center;

    .login-icon {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      margin: 0 40rpx;

      &.wechat-icon {
        background-color: #33b347;
        position: relative;

        &::before {
          content: "";
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 50rpx;
          height: 50rpx;
          background-color: #ffffff;
          mask-size: cover;
          -webkit-mask-size: cover;
          mask-repeat: no-repeat;
          -webkit-mask-repeat: no-repeat;
          mask-position: center;
          -webkit-mask-position: center;
          mask-image: url("data:image/svg+xml,%3Csvg t='1682517223093' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='2368' width='200' height='200'%3E%3Cpath d='M664.250054 368.541681c10.015098 0 19.892049 0.732687 29.67281 1.795606-26.647917-122.810047-159.358451-214.077703-310.826188-214.077703-169.353083 0-308.085774 114.232694-308.085774 259.274068 0 83.708494 46.165436 152.460344 123.281791 205.78483l-30.80868 91.730191 107.688651-53.455469c38.558178 7.53665 69.459978 15.308661 107.924012 15.308661 9.66308 0 19.230993-0.470721 28.752858-1.225921-6.025227-20.36584-9.521864-41.723264-9.521864-63.862493C402.328693 476.632491 517.908058 368.541681 664.250054 368.541681zM498.62897 285.87389c23.200398 0 38.557154 15.120372 38.557154 38.061874 0 22.846334-15.356756 38.156018-38.557154 38.156018-23.107277 0-46.260603-15.309684-46.260603-38.156018C452.368366 300.994262 475.522716 285.87389 498.62897 285.87389zM283.016307 362.090758c-23.107277 0-46.402843-15.309684-46.402843-38.156018 0-22.941502 23.295566-38.061874 46.402843-38.061874 23.081695 0 38.46301 15.120372 38.46301 38.061874C321.479317 346.782098 306.098002 362.090758 283.016307 362.090758zM945.448458 606.151333c0-121.888048-123.258255-221.236753-261.683954-221.236753-146.57838 0-262.015505 99.348706-262.015505 221.236753 0 122.06508 115.437126 221.200938 262.015505 221.200938 30.66644 0 61.617359-7.609305 92.423993-15.262612l84.513836 45.786813-23.178909-76.17082C899.379213 735.776599 945.448458 674.90216 945.448458 606.151333zM598.803483 567.994292c-15.332197 0-30.807656-15.096836-30.807656-30.501688 0-15.190981 15.47546-30.477129 30.807656-30.477129 23.295566 0 38.558178 15.286148 38.558178 30.477129C637.361661 552.897456 622.099049 567.994292 598.803483 567.994292zM768.25071 567.994292c-15.213493 0-30.594809-15.096836-30.594809-30.501688 0-15.190981 15.381315-30.477129 30.594809-30.477129 23.107277 0 38.558178 15.286148 38.558178 30.477129C806.808888 552.897456 791.357987 567.994292 768.25071 567.994292z' p-id='2369'%3E%3C/path%3E%3C/svg%3E");
          -webkit-mask-image: url("data:image/svg+xml,%3Csvg t='1682517223093' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='2368' width='200' height='200'%3E%3Cpath d='M664.250054 368.541681c10.015098 0 19.892049 0.732687 29.67281 1.795606-26.647917-122.810047-159.358451-214.077703-310.826188-214.077703-169.353083 0-308.085774 114.232694-308.085774 259.274068 0 83.708494 46.165436 152.460344 123.281791 205.78483l-30.80868 91.730191 107.688651-53.455469c38.558178 7.53665 69.459978 15.308661 107.924012 15.308661 9.66308 0 19.230993-0.470721 28.752858-1.225921-6.025227-20.36584-9.521864-41.723264-9.521864-63.862493C402.328693 476.632491 517.908058 368.541681 664.250054 368.541681zM498.62897 285.87389c23.200398 0 38.557154 15.120372 38.557154 38.061874 0 22.846334-15.356756 38.156018-38.557154 38.156018-23.107277 0-46.260603-15.309684-46.260603-38.156018C452.368366 300.994262 475.522716 285.87389 498.62897 285.87389zM283.016307 362.090758c-23.107277 0-46.402843-15.309684-46.402843-38.156018 0-22.941502 23.295566-38.061874 46.402843-38.061874 23.081695 0 38.46301 15.120372 38.46301 38.061874C321.479317 346.782098 306.098002 362.090758 283.016307 362.090758zM945.448458 606.151333c0-121.888048-123.258255-221.236753-261.683954-221.236753-146.57838 0-262.015505 99.348706-262.015505 221.236753 0 122.06508 115.437126 221.200938 262.015505 221.200938 30.66644 0 61.617359-7.609305 92.423993-15.262612l84.513836 45.786813-23.178909-76.17082C899.379213 735.776599 945.448458 674.90216 945.448458 606.151333zM598.803483 567.994292c-15.332197 0-30.807656-15.096836-30.807656-30.501688 0-15.190981 15.47546-30.477129 30.807656-30.477129 23.295566 0 38.558178 15.286148 38.558178 30.477129C637.361661 552.897456 622.099049 567.994292 598.803483 567.994292zM768.25071 567.994292c-15.213493 0-30.594809-15.096836-30.594809-30.501688 0-15.190981 15.381315-30.477129 30.594809-30.477129 23.107277 0 38.558178 15.286148 38.558178 30.477129C806.808888 552.897456 791.357987 567.994292 768.25071 567.994292z' p-id='2369'%3E%3C/path%3E%3C/svg%3E");
        }
      }

      &.phone-icon {
        background-color: #ca5147;
        position: relative;

        &::before {
          content: "";
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 50rpx;
          height: 50rpx;
          background-color: #ffffff;
          mask-size: cover;
          -webkit-mask-size: cover;
          mask-repeat: no-repeat;
          -webkit-mask-repeat: no-repeat;
          mask-position: center;
          -webkit-mask-position: center;
          mask-image: url("data:image/svg+xml,%3Csvg t='1682517275274' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='3196' width='200' height='200'%3E%3Cpath d='M736.277333 927.808c-0.362667 0-0.704 0.021333-1.066667 0.021333-61.557333 0-150.997333-33.28-250.346666-93.44-73.792-44.736-148.437333-102.912-210.112-164.586666-61.674667-61.674667-119.850667-136.32-164.586667-210.112-60.16-99.349333-93.461333-188.789333-93.461333-250.368 0-36.202667 8.661333-62.698667 27.477333-81.493334 13.653333-13.674667 34.752-29.12 78.805334-29.12 4.117333 0 8.384 0.170667 12.821333 0.512 29.589333 2.24 53.909333 25.28 78.72 74.176 13.909333 27.477333 27.477333 59.136 40.448 94.101334 8.832 23.722667 13.269333 47.466667 13.269333 70.805333 0 24.234667-5.12 44.437333-15.317333 60.202667-7.232 11.242667-16.192 22.314667-26.773333 33.024-3.989333 4.053333-4.373333 9.173333-3.626667 12.629333 15.957333 72.746667 58.026667 137.173333 125.162667 191.637333l1.066666 0.874667c30.613333 24.682667 62.250667 44.8 94.122667 59.904 7.616 3.626667 15.338667 3.221333 20.458667-1.066667a756.992 756.992 0 0 0 52.586666-44.373333c11.498667-11.157333 26.538667-17.301333 44.693334-17.301333 18.496 0 38.890667 6.421333 60.693333 19.072 29.546667 17.216 57.130667 37.162667 79.402667 57.472 44.672 40.746667 58.922667 70.122667 58.922666 122.346666 0 26.88-5.973333 47.957333-19.114666 67.413334-13.226667 19.562667-32.426667 38.826667-57.130667 57.216-18.816 13.973333-39.850667 20.949333-62.570667 20.949333z' p-id='3197'%3E%3C/path%3E%3C/svg%3E");
          -webkit-mask-image: url("data:image/svg+xml,%3Csvg t='1682517275274' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='3196' width='200' height='200'%3E%3Cpath d='M736.277333 927.808c-0.362667 0-0.704 0.021333-1.066667 0.021333-61.557333 0-150.997333-33.28-250.346666-93.44-73.792-44.736-148.437333-102.912-210.112-164.586666-61.674667-61.674667-119.850667-136.32-164.586667-210.112-60.16-99.349333-93.461333-188.789333-93.461333-250.368 0-36.202667 8.661333-62.698667 27.477333-81.493334 13.653333-13.674667 34.752-29.12 78.805334-29.12 4.117333 0 8.384 0.170667 12.821333 0.512 29.589333 2.24 53.909333 25.28 78.72 74.176 13.909333 27.477333 27.477333 59.136 40.448 94.101334 8.832 23.722667 13.269333 47.466667 13.269333 70.805333 0 24.234667-5.12 44.437333-15.317333 60.202667-7.232 11.242667-16.192 22.314667-26.773333 33.024-3.989333 4.053333-4.373333 9.173333-3.626667 12.629333 15.957333 72.746667 58.026667 137.173333 125.162667 191.637333l1.066666 0.874667c30.613333 24.682667 62.250667 44.8 94.122667 59.904 7.616 3.626667 15.338667 3.221333 20.458667-1.066667a756.992 756.992 0 0 0 52.586666-44.373333c11.498667-11.157333 26.538667-17.301333 44.693334-17.301333 18.496 0 38.890667 6.421333 60.693333 19.072 29.546667 17.216 57.130667 37.162667 79.402667 57.472 44.672 40.746667 58.922667 70.122667 58.922666 122.346666 0 26.88-5.973333 47.957333-19.114666 67.413334-13.226667 19.562667-32.426667 38.826667-57.130667 57.216-18.816 13.973333-39.850667 20.949333-62.570667 20.949333z' p-id='3197'%3E%3C/path%3E%3C/svg%3E");
        }
      }
    }
  }
}

/* 注册表单样式 */
.register-form {
  position: relative;
  z-index: 1;
  padding-top: 250rpx; /* 增加顶部padding，使表单位置下移 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  
  .input-item {
    height: 80rpx; /* 减小高度 */
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 45rpx;
    margin-bottom: 15rpx; /* 减少间距 */
    padding: 0 40rpx;
    display: flex;
    align-items: center;

    input {
      flex: 1;
      height: 100%;
      color: #ffffff;
      font-size: 28rpx; /* Reduced from 30rpx */
    }

    .placeholder {
      color: rgba(255, 255, 255, 0.4);
    }

    &.verification-code {
      padding-right: 0;

      .get-code-btn {
        height: 60rpx;
        min-width: 180rpx;
        background: linear-gradient(90deg, #0ab9fd, #2b79fb);
        border-radius: 30rpx;
        margin-right: 20rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        text {
          color: #ffffff;
          font-size: 24rpx;
          white-space: nowrap;
        }

        &.disabled {
          background: rgba(255, 255, 255, 0.2);
        }
      }
    }
  }

  .register-btn {
    margin-top: 20rpx; /* 减少上边距 */
    margin-bottom: 20rpx; /* 减少下边距 */
    height: 90rpx; /* Reduced from 100rpx */
    background: linear-gradient(90deg, #0ab9fd, #2b79fb);
    border-radius: 45rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    color:#2b79fb;
    text {
      color: #ffffff;
      font-size: 32rpx;
      font-weight: 500;
    }
  }

  .switch-action {
    text-align: center;
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 20rpx;
  }
}

/* 底部注册链接 */
.register-link {
  color: #0abcfd;
  margin-left: 20rpx;
}

/* 底部协议 */
.agreement {
  position: relative; /* 改为relative，不固定位置 */
  margin-top: auto; /* 自动占据剩余空间 */
  padding: 30rpx 0; /* 添加上下padding */
  left: 0;
  right: 0;
  text-align: center;
  font-size: 24rpx;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  color: rgba(255, 255, 255, 0.6);

  .agreement-text,
  .agreement-link,
  .register-link {
    margin: 0 4rpx;
  }
}
</style>
