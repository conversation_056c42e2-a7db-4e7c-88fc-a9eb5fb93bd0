package com.heating.dto.user;

import lombok.Data;
import java.util.List;

import javax.validation.constraints.Email;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

import com.heating.entity.user.TUser;

@Data
public class UserInfoUpdateRequest {
    @Size(max = 50, message = "姓名长度不能超过50个字符")
    private String name;
    private String avatar;
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;
    private String department;
    private List<String> skills;
    private List<Certification> certifications;
    private WorkStats workStats;
    private Integer monthlyOrders;
    private Double averageRating;
    private Double attendanceRate;
    @Email(message = "邮箱格式不正确")
    private String email;

    // 添加构造函数
    public UserInfoUpdateRequest() {
    }

    // 添加带参数的构造函数
    public UserInfoUpdateRequest(String name, String avatar, String phone, String department, 
                               List<String> skills, List<Certification> certifications, WorkStats workStats, Integer monthlyOrders, Double averageRating, Double attendanceRate, String email) {
        this.name = name != null ? name.trim() : null;
        this.avatar = avatar;
        this.phone = phone != null ? phone.trim() : null;
        this.department = department;
        this.skills = skills;
        this.certifications = certifications;
        this.workStats = workStats;
        this.monthlyOrders = monthlyOrders;
        this.averageRating = averageRating;
        this.attendanceRate = attendanceRate;
        this.email = email != null ? email.trim() : null;
    }
} 