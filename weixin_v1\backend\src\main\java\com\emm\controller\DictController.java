package com.emm.controller;

import com.emm.model.DictData;
import com.emm.service.DictService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/dict")
public class DictController {

    private static final Logger logger = LoggerFactory.getLogger(DictController.class);

    @Autowired
    private DictService dictService;

    @GetMapping("/data")
    public ResponseEntity<?> getDictData(@RequestParam(value = "dict_id", required = false) String dictId) {
        logger.info("Accessing GET /api/dict/data with dictId: {}", dictId);
        if (dictId == null || dictId.isEmpty()) {
            logger.warn("Missing dict_id parameter");
            return createErrorResponse("缺少参数dict_id");
        }

        try {
            Long dictIdLong = Long.parseLong(dictId);
            logger.info("Parsed dictId: {}", dictIdLong);

            List<DictData> items;
            try {
                items = dictService.getDictData(dictIdLong);
                logger.info("Retrieved {} items from DictService", items.size());
            } catch (DataAccessException e) {
                logger.error("Database access error when retrieving dict data for dictId: {}", dictIdLong, e);
                return createErrorResponse("数据库访问错误: " + e.getMessage());
            } catch (Exception e) {
                logger.error("Unexpected error when retrieving dict data for dictId: {}", dictIdLong, e);
                return createErrorResponse("获取字典数据时发生未知错误: " + e.getMessage());
            }

            if (items.isEmpty()) {
                logger.warn("No items found for dictId: {}", dictIdLong);
                return createErrorResponse("未找到相关字典数据");
            }

            // Log the first few items for debugging
            for (int i = 0; i < Math.min(items.size(), 3); i++) {
                DictData item = items.get(i);
                logger.info("Item {}: id={}, name={}, code={}, status={}, dictId={}, mark={}",
                    i, item.getId(), item.getName(), item.getCode(), item.getStatus(), item.getDictId(), item.getMark());
            }

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", items);
            return ResponseEntity.ok(response);
        } catch (NumberFormatException e) {
            logger.error("Invalid dict_id format: {}", dictId, e);
            return createErrorResponse("无效的dict_id格式");
        } catch (Exception e) {
            logger.error("Unexpected error in GET /api/dict/data: {}", e.getMessage(), e);
            return createErrorResponse("服务器内部错误: " + e.getMessage());
        }
    }

    private ResponseEntity<?> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", message);
        return ResponseEntity.badRequest().body(response);
    }
}