/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.custom-tabbar[data-v-6def6a3b] {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3.4375rem;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding-bottom: env(safe-area-inset-bottom);
  box-shadow: 0 -0.125rem 0.625rem rgba(0, 0, 0, 0.06);
  z-index: 999;
}
.custom-tabbar .tab-item[data-v-6def6a3b] {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.375rem 0;
  position: relative;
  transition: all 0.3s;
}
.custom-tabbar .tab-item.active[data-v-6def6a3b] {
  transform: translateY(-0.1875rem);
}
.custom-tabbar .tab-item.active .tab-text[data-v-6def6a3b] {
  color: #1890ff;
  font-weight: 500;
}
.custom-tabbar .tab-item .icon-container[data-v-6def6a3b] {
  width: 1.875rem;
  height: 1.875rem;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 0.1875rem;
}
.custom-tabbar .tab-item .icon-container .tab-icon[data-v-6def6a3b] {
  width: 1.5rem;
  height: 1.5rem;
  transition: all 0.3s;
}
.custom-tabbar .tab-item .tab-text[data-v-6def6a3b] {
  font-size: 0.75rem;
  color: #999;
  line-height: 1;
  transition: all 0.3s;
}
.custom-tabbar .tab-item .active-indicator[data-v-6def6a3b] {
  position: absolute;
  bottom: -0.09375rem;
  left: 50%;
  transform: translateX(-50%);
  width: 0.5rem;
  height: 0.1875rem;
  background: #1890ff;
  border-radius: 0.1875rem;
}
body[data-v-6def6a3b] {
  padding-bottom: calc(3.4375rem + env(safe-area-inset-bottom));
}

.no-permission-item[data-v-9e1a7520] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0.5;
}
.no-permission-text[data-v-9e1a7520] {
  font-size: 0.75rem;
  color: #999;
  text-align: center;
}

/* 确保permission-check组件继承其父容器的布局特性 */
[data-v-9e1a7520] .permission-check {
  display: inherit;
  width: inherit;
  height: inherit;
  flex: inherit;
  position: inherit;
}

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.message-center-container {
  padding: 0 0.625rem 0.625rem 0.625rem;
  box-sizing: border-box;
}
.message-tabs {
  display: flex;
  background-color: #fff;
  margin-bottom: 0.625rem;
  overflow-x: auto;
  white-space: nowrap;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 999;
}
.message-tabs .message-tab {
  flex: 1;
  min-width: 3.75rem;
  text-align: center;
  padding: 0.625rem 0;
  font-size: 0.875rem;
  position: relative;
}
.message-tabs .message-tab .badge {
  position: absolute;
  top: 0.3125rem;
  right: 0.3125rem;
  background-color: #f5222d;
  color: #fff;
  font-size: 0.625rem;
  border-radius: 0.625rem;
  padding: 0.0625rem 0.3125rem;
  min-width: 0.625rem;
  height: 0.625rem;
  line-height: 0.625rem;
  text-align: center;
}
.message-tabs .message-tab.active {
  color: #1890ff;
  font-weight: bold;
}
.message-tabs .message-tab.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 1.25rem;
  height: 0.125rem;
  background-color: #1890ff;
}
.action-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.625rem;
  background-color: #fff;
  border-radius: 0.25rem;
  padding: 0.5rem 0.625rem;
}
.action-toolbar .action-btn {
  font-size: 0.8125rem;
  color: #1890ff;
  padding: 0.1875rem 0.5rem;
}
.action-toolbar .action-filter {
  display: flex;
  align-items: center;
  font-size: 0.8125rem;
  color: #333;
}
.action-toolbar .action-filter .action-arrow {
  margin-left: 0.3125rem;
  font-size: 0.75rem;
}
.filter-panel {
  background-color: #fff;
  border-radius: 0.25rem;
  padding: 0.625rem;
  margin-bottom: 0.625rem;
}
.filter-panel .filter-item {
  padding: 0.5rem 0;
  font-size: 0.875rem;
  border-bottom: 0.03125rem solid rgba(0, 0, 0, 0.05);
}
.filter-panel .filter-item:last-child {
  border-bottom: none;
}
.filter-panel .filter-item.active {
  color: #1890ff;
}
.message-list .message-card {
  background-color: #fff;
  border-radius: 0.25rem;
  padding: 0.75rem;
  margin-bottom: 0.625rem;
  border-left: 0.1875rem solid transparent;
}
.message-list .message-card.unread {
  background-color: rgba(24, 144, 255, 0.05);
}
.message-list .message-card.urgent {
  border-left-color: #f5222d;
}
.message-list .message-card.important {
  border-left-color: #faad14;
}
.message-list .message-card.normal {
  border-left-color: #1890ff;
}
.message-list .message-card .message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}
.message-list .message-card .message-header .message-title {
  font-size: 1rem;
  font-weight: bold;
  display: flex;
  align-items: center;
}
.message-list .message-card .message-header .message-title .priority-dot {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  margin-right: 0.375rem;
}
.message-list .message-card .message-header .message-title .priority-dot.urgent {
  background-color: #f5222d;
}
.message-list .message-card .message-header .message-title .priority-dot.important {
  background-color: #faad14;
}
.message-list .message-card .message-header .message-title .priority-dot.normal {
  background-color: #1890ff;
}
.message-list .message-card .message-header .message-time {
  font-size: 0.75rem;
  color: #666;
}
.message-list .message-card .message-content {
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 0.5rem;
}
.message-list .message-card .message-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.message-list .message-card .message-footer .message-type {
  font-size: 0.75rem;
  color: #666;
  background-color: #f5f5f5;
  padding: 0.125rem 0.5rem;
  border-radius: 0.125rem;
}
.message-list .message-card .message-footer .message-actions {
  display: flex;
}
.message-list .message-card .message-footer .message-actions .action-item {
  font-size: 0.75rem;
  color: #1890ff;
  margin-left: 0.625rem;
}
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3.125rem 0;
}
.empty-state .empty-icon {
  width: 6.25rem;
  height: 6.25rem;
  margin-bottom: 0.9375rem;
}
.empty-state .empty-text {
  font-size: 0.875rem;
  color: #666;
}
.load-more,
.no-more {
  text-align: center;
  padding: 0.625rem 0;
  font-size: 0.8125rem;
  color: #666;
}
.message-actions .action-item.ignore-btn {
  color: #faad14;
}
.message-actions .action-item.delete-btn {
  color: #f5222d;
}