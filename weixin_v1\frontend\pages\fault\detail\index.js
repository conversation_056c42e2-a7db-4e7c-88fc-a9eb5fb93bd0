Page({
  data: {
    fault: {},
    images: [],
    videos: [],
    workOrders: [],
    statusClass: '',
    showRepairUserSelect: false,
    repairUsers: [],
    selectedUserId: null,
    selectedUserName: '', 
  },

  onLoad(options) { 
    // 打印日志
    console.log('wx.getStorageSync(isAdmin)=', wx.getStorageSync('isAdmin'));
    const faultId = options.id;
    this.loadFaultDetail(faultId);
  },

  loadFaultDetail(faultId) {
    wx.request({
      url: `http://localhost:5000/api/fault/${faultId}`,
      method: 'GET',
      success: (res) => {
        if (res.data.success) {
          const { fault, attachments, workOrders } = res.data.data;
          
          // 处理状态样式类名
          let statusClass = '';
          switch (fault.fault_status) {
            case '待确认':
              statusClass = 'pending';
              break;
            case '已确认':
              statusClass = 'confirmed';
              break;
            case '已退回':
              statusClass = 'returned';
              break;
          }

          this.setData({
            fault,
            images: attachments.images || [],
            videos: attachments.videos || [],
            workOrders,
            statusClass
          });
        } else {
          wx.showToast({
            title: res.data.message || '加载失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  previewImage(e) {
    const current = e.currentTarget.dataset.url;
    const urls = this.data.images.map(item => item.file_path);
    wx.previewImage({
      current,
      urls
    });
  },

  goToWorkOrder(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `http://localhost:5000/api/workorder/detail/index?id=${orderId}`
    });
  },

  handleConfirm() {
    console.log('当前选择:', this.data.selectedUserId, this.data.selectedUserName);
    
    if (!this.data.selectedUserId) {
      wx.showToast({
        title: '请选择维修人员',
        icon: 'none'
      });
      return;
    }
     
    const userInfo = wx.getStorageSync('userInfo'); 
    wx.request({
      url: 'http://localhost:5000/api/fault/confirm',
      method: 'POST',
      data: {
        action: 'confirm',
        fault_id: this.data.fault.fault_id, 
        repair_user_id: this.data.selectedUserId, // 维修人员ID
        operator_id: userInfo.id
      },
      success: (res) => {
        if (res.data.success) {
          wx.showToast({
            title: '确认成功',
            icon: 'success',
            success: () => {
              // 4. 返回首页
              setTimeout(() => {
                wx.navigateBack({
                  delta: 1
                });
              }, 1500);
            }
          });
        } else {
          wx.showToast({
            title: res.data.message || '确认失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  handleReject() {
    wx.showModal({
      title: '退回故障',
      content: '确认退回该故障？',
      success: (res) => {
        if (res.confirm) {
          // TODO: 调用退回接口
        }
      }
    });
  },

  showRepairUserSelect() {
    console.log('获取维修人员列表');
    wx.request({
      url: 'http://localhost:5000/api/users/repair',
      method: 'GET',
      success: (res) => {
        if (res.data.success) {
          this.setData({
            repairUsers: res.data.data,
            showRepairUserSelect: true
          });
        } else {
          wx.showToast({
            title: '获取维修人员失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  hideRepairUserSelect() {
    this.setData({
      showRepairUserSelect: false,
      selectedUserId: null,
      selectedUserName: ''
    });
  },

  selectRepairUser(e) {
    const selectedId = e.detail.value;
    const selectedUser = this.data.repairUsers.find(user => user.id.toString() === selectedId);
    
    console.log('选择维修人员:', selectedId, selectedUser);
    
    this.setData({
      selectedUserId: selectedId,
      selectedUserName: selectedUser ? selectedUser.name : ''
    });
  }
}); 