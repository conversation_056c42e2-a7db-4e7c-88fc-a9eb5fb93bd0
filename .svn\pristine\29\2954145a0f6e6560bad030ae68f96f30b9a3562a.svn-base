<template>
	<view class="video-player-container">
		<view class="video-wrapper">
			<video 
				:src="videoUrl" 
				controls 
				autoplay
				object-fit="contain"
				@error="onVideoError"
				@fullscreenchange="onFullscreenChange"
				show-center-play-btn="true"
				show-loading="true"
				enable-play-gesture="true"
				enable-progress-gesture="true"
				codec="hardware"
				vslide-gesture="true"
				vslide-gesture-in-fullscreen="true"
				play-btn-position="center"
				direction="0"
				id="videoPlayer"
			></video>
		</view>
		
		<!-- <view class="controls">
			<button class="btn-back" @click="goBack">返回</button>
			<button class="btn-open-browser" @click="openInBrowser">在浏览器中打开</button>
		</view>
		 -->
		<view class="error-message" v-if="loadError">
			<text>视频加载失败，请尝试在浏览器中打开</text>
		</view>
	</view>
</template>

<script>
	/**
	 * 通用视频播放器页面
	 * 支持从URL参数接收视频地址并播放
	 */
	export default {
		data() {
			return {
				videoUrl: '',
				loadError: false,
			}
		},
		onLoad(options) {
			// 设置状态栏为透明
			// #ifdef APP-PLUS
			plus.navigator.setStatusBarStyle('light');
			plus.navigator.setStatusBarBackground('#000000');
			// #endif
			
			// 从URL参数获取视频地址
			if (options.url || options.src) {
				// 解码URL
				this.videoUrl = decodeURIComponent(options.url || options.src);
				console.log('视频播放器: 加载视频URL:', this.videoUrl);
				
				// 设置页面标题
				if (options.title) {
					uni.setNavigationBarTitle({
						title: options.title
					});
				}
				
				// 设置导航栏背景为黑色
				uni.setNavigationBarColor({
					frontColor: '#ffffff',
					backgroundColor: '#000000'
				});
				
				// 预加载视频
				this.preloadVideo(this.videoUrl);
			} else {
				uni.showToast({
					title: '未提供视频地址',
					icon: 'none'
				});
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			}
		},
		onReady() {
			// 获取视频上下文
			this.videoContext = uni.createVideoContext('videoPlayer', this);
			
			// 设置页面背景为黑色
			// #ifdef H5
			document.body.style.backgroundColor = '#000000';
			// #endif
		},
		onShow() {
			// 页面显示时尝试播放视频
			setTimeout(() => {
				if (this.videoContext) {
					this.videoContext.play();
				}
			}, 300);
		},
		onHide() {
			// 页面隐藏时暂停视频
			if (this.videoContext) {
				this.videoContext.pause();
			}
		},
		onUnload() {
			// 页面卸载时停止视频
			if (this.videoContext) {
				this.videoContext.stop();
				this.videoContext = null;
			}
			
			// 恢复页面背景色
			// #ifdef H5
			document.body.style.backgroundColor = '';
			// #endif
			
			// 恢复状态栏样式
			// #ifdef APP-PLUS
			plus.navigator.setStatusBarStyle('dark');
			plus.navigator.setStatusBarBackground('#ffffff');
			// #endif
		},
		methods: {
			// 视频加载错误处理
			onVideoError(e) {
				console.error('视频播放器: 视频加载失败:', e.detail);
				this.loadError = true;
				
				uni.showToast({
					title: '视频加载失败，请尝试其他方式查看',
					icon: 'none',
					duration: 2000
				});
			},
			
			// 处理全屏状态变化
			onFullscreenChange(e) {
				console.log('视频全屏状态变化:', e.detail.fullScreen);
				
				// 在退出全屏时，可能需要进行一些调整
				if (!e.detail.fullScreen) {
					// 一些设备上可能需要手动调整布局
					setTimeout(() => {
						// 刷新视频组件布局
						if (this.videoContext) {
							this.videoContext.play();
							setTimeout(() => this.videoContext.pause(), 10);
						}
					}, 100);
				}
			},
			
			// 返回上一页
			goBack() {
				uni.navigateBack();
			},
			
			// 预加载视频
			preloadVideo(url) {
				// 创建Image对象预加载视频首帧（对于一些平台可能有效）
				// #ifdef H5
				const img = new Image();
				img.src = url;
				// #endif
				
				// 在APP中预热视频播放引擎
				// #ifdef APP-PLUS
				setTimeout(() => {
					if (this.videoContext) {
						this.videoContext.seek(0);
						this.videoContext.pause();
					}
				}, 100);
				// #endif
			},
			
			// 在浏览器中打开
			openInBrowser() {
				if (!this.videoUrl) {
					uni.showToast({
						title: '视频URL无效',
						icon: 'none'
					});
					return;
				}
				
				console.log('在浏览器中打开视频:', this.videoUrl);
				
				// 使用系统浏览器打开
				// #ifdef APP-PLUS
				plus.runtime.openURL(this.videoUrl, (err) => {
					if (err) {
						console.error('打开浏览器失败:', err);
						uni.showToast({
							title: '打开浏览器失败',
							icon: 'none'
						});
					}
				});
				// #endif
				
				// #ifdef H5
				window.open(this.videoUrl, '_blank');
				// #endif
				
				// #ifdef MP
				uni.setClipboardData({
					data: this.videoUrl,
					success: () => {
						uni.showToast({
							title: 'URL已复制，请在浏览器中打开',
							icon: 'none'
						});
					}
				});
				// #endif
			}
		}
	}
</script>

<style lang="scss">
	/* 修改页面样式以消除顶部的白色间隙 */
	page {
		background-color: #000000;
		height: 100%;
		overflow: hidden;
		margin: 0;
		padding: 0;
	}

	/* 隐藏默认导航栏的可能空间 */
	.uni-page-head {
		display: none;
	}
	
	.uni-page-wrapper,
	.uni-page-body {
		height: 100%;
		background-color: #000000 !important;
		overflow: hidden;
		position: relative;
		z-index: 1;
	}

	.video-player-container {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		display: flex;
		flex-direction: column;
		width: 100%;
		height: 100vh;
		background-color: #000;
		overflow: hidden;
		z-index: 10;
		
		.video-wrapper {
			flex: 1;
			display: flex;
			justify-content: center;
			align-items: center;
			width: 100%;
			height: 100%;
			
			video {
				width: 100%;
				height: 100%;
				max-height: 100%;
				object-fit: contain;
				z-index: 1;
			}
		}
		
		.controls {
			display: flex;
			padding: 30rpx;
			background-color: #222;
			gap: 30rpx;
			
			button {
				flex: 1;
				margin: 0;
				padding: 20rpx 0;
				border-radius: 8rpx;
				font-size: 30rpx;
				
				&.btn-back {
					background-color: #444;
					color: #fff;
				}
				
				&.btn-open-browser {
					background-color: $uni-color-primary;
					color: #fff;
				}
			}
		}
		
		.error-message {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			background-color: rgba(0, 0, 0, 0.7);
			padding: 20rpx 40rpx;
			border-radius: 8rpx;
			text-align: center;
			z-index: 2;
			
			text {
				color: #fff;
				font-size: 28rpx;
			}
		}
	}
</style> 