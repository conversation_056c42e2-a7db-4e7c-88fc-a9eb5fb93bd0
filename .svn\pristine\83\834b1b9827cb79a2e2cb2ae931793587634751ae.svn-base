package com.heating.dto.user;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
 
import java.time.LocalDateTime;
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WorkStats {
    private Integer completedRepairs;
    private Integer ongoingRepairs;
    private Double averageRating;
    private LocalDateTime lastWorkDate;
    private Integer attendanceRate;
} 