// utils/messageService.js

// 引入 uni-app 的请求模块
import { request } from './api.js'; // 修改：使用相对路径导入同目录的 api.js
// 或者使用 uni-app 自带的请求
// const request = uni.request; 

// 定时器ID
let intervalId = null;
// 服务运行状态标志
let isRunning = false;
// 轮询周期（毫秒）
const POLLING_INTERVAL = 10000; // 10秒
// 轮询执行锁标志
let isPolling = false; 

// 调用后端消息接口的通用函数
async function fetchMessages(apiPath, messageType) {
  console.log(`消息服务：正在调用 ${messageType} 消息接口 (${apiPath})...`);
  try {
    // 注意：根据你的 request 封装调整参数
    // 如果直接使用 uni.request，结构类似
    const res = await request({
      url: apiPath,  
      method: 'GET' 
    }); 
    // 假设你的 request 封装返回了完整的响应对象
    // 如果直接用 uni.request, res 是 [error, responseData]
    // 需要根据你的封装或 uni.request 的返回格式处理 res
    if (res && res.code === 200 && res.data) {
      console.log(`消息服务：成功获取 ${messageType} 消息`, res.data);
      // 在这里可以处理获取到的消息，例如触发事件、存入状态管理等
      if (res.data.length > 0) {
          console.warn(`消息服务：收到 ${res.data.length} 条新的 ${messageType} 消息！`);
          // 可选：触发全局事件或通知
          uni.$emit('newMessageReceived', { type: messageType, data: res.data });
      }
    } else {
      console.error(`消息服务：获取 ${messageType} 消息失败`, res);
    }
  } catch (error) {
    // 只记录错误，不中断轮询
    console.error(`消息服务：调用 ${messageType} 接口 (${apiPath}) 时发生网络错误或异常`, error);
  }
}

// 轮询执行的任务
async function pollTasks() {
  // 检查服务是否仍在运行且当前没有其他轮询在执行
  if (!isRunning || isPolling) { 
    if(isPolling) console.log("消息服务：上一次轮询仍在进行中，跳过本次执行。");
    return; 
  }

  console.log("消息服务：开始执行轮询任务...");
  isPolling = true; // 设置轮询锁

  try {
    // 使用 Promise.allSettled 来并发调用接口，并等待所有接口完成（无论成功或失败）
    await Promise.allSettled([
      fetchMessages('/api/patrols/plans/messages', '巡检'), // 3.14 巡检消息
      fetchMessages('/api/alarm/messages', '告警'),        // 4.5 告警消息
      fetchMessages('/api/faults/messages', '故障'),       // 4.6 故障消息 
      fetchMessages('/api/WorkOrders/messages', '工单')    // 5.6 工单消息
    ]);
    console.log("消息服务：轮询任务执行完毕。");
  } catch (error) {
    // Promise.allSettled 不会走到 catch, 但保留以防万一
    console.error("消息服务：轮询任务中发生未预料的错误", error);
  } finally {
    isPolling = false; // 释放轮询锁
  }
}

// 启动消息服务
function startService() {
  // 强制清除可能存在的旧定时器
  if (intervalId) {
    console.log("消息服务：检测到可能残留的定时器，正在清除...");
    clearInterval(intervalId);
    intervalId = null;
  }
  // 如果服务已启动，则退出
  if (isRunning) {
    console.log("消息服务：服务已经在运行中。");
    return;
  }
  console.log("消息服务：正在启动...");
  isRunning = true;
  isPolling = false; // 重置轮询锁状态
  // 立即执行一次
  pollTasks(); 
  // 设置定时器
  intervalId = setInterval(pollTasks, POLLING_INTERVAL);
  console.log("消息服务：已启动，轮询周期", POLLING_INTERVAL / 1000, "秒");
}

// 停止消息服务
function stopService() {
  if (!isRunning) {
    console.log("消息服务：服务尚未运行。");
    return;
  }
  console.log("消息服务：正在停止...");
  clearInterval(intervalId);
  intervalId = null;
  isRunning = false;
  isPolling = false; // 停止时也重置轮询锁
  console.log("消息服务：已停止。");
}

// 获取服务运行状态
function getServiceStatus() {
  return isRunning;
}

// 导出服务控制函数
export const messageService = {
  start: startService,
  stop: stopService,
  getStatus: getServiceStatus
}; 