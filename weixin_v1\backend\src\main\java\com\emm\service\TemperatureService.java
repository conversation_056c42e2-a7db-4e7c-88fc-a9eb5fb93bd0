package com.emm.service;

import com.emm.model.RoomTemperature;
import com.emm.repository.OutTemperatureRepository;
import com.emm.repository.RoomTemperatureRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Date;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Service
public class TemperatureService {

    private static final Logger logger = LoggerFactory.getLogger(TemperatureService.class);

    @Autowired
    private OutTemperatureRepository outTemperatureRepository;

    @Autowired
    private RoomTemperatureRepository roomTemperatureRepository;

    public Double getOutdoorTemperature() {
        return outTemperatureRepository.findCurrentOutdoorTemperature();
    }

    @Transactional
    public RoomTemperature saveRoomTemperature(RoomTemperature temperature) {
        return roomTemperatureRepository.save(temperature);
    } 

    public List<RoomTemperature> getTemperatures(String communityName, Date date) { 
        logger.info("Fetching temperatures for community: {}, date: {}", communityName, date);
        
        if (communityName == "" && date == null) {
            logger.debug("Fetching all temperatures");
            return roomTemperatureRepository.findAll();
        } else if (communityName == "" && date != null) {
            logger.debug("Fetching temperatures for date: {}", date);
            return roomTemperatureRepository.findByDate(date);
        } else if (communityName != "" && date == null) {
            logger.debug("Fetching temperatures for community: {}", communityName);
            return roomTemperatureRepository.findByCommunityName(communityName);
        } else {
            logger.debug("Fetching temperatures for community: {} and date: {}", communityName, date);
            return roomTemperatureRepository.findByCommunityNameAndDate(communityName, date);
        }
    }

    public RoomTemperature getTemperatureById(Long id) {
        Optional<RoomTemperature> optionalTemperature = roomTemperatureRepository.findById(id);
        return optionalTemperature.orElse(null);
    }   
} 