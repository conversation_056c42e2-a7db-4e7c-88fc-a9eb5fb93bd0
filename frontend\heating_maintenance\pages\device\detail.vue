<template>
	<view class="device-detail-container">
		<!-- 设备基本信息卡片 -->
		<view class="detail-card">
			<view class="card-header">
				<text class="card-title">基本信息</text>
				<view class="device-status" :class="deviceInfo.status">{{ getStatusText(deviceInfo.status) }}</view>
			</view>
			
			<view class="info-item">
				<text class="info-label">设备名称</text>
				<text class="info-value">{{ deviceInfo.name }}</text>
			</view>
			<view class="info-item">
				<text class="info-label">设备类型</text>
				<text class="info-value">{{ deviceInfo.type }}</text>
			</view>
			<view class="info-item">
				<text class="info-label">设备型号</text>
				<text class="info-value">{{ deviceInfo.model }}</text>
			</view>
			<view class="info-item">
				<text class="info-label">设备编号</text>
				<text class="info-value">{{ deviceInfo.deviceId }}</text>
			</view>
			<view class="info-item">
				<text class="info-label">安装位置</text>
				<text class="info-value">{{ getLocation() }}</text>
			</view>
			<view class="info-item">
				<text class="info-label">生产厂商</text>
				<text class="info-value">{{ deviceInfo.manufacturer || '未知' }}</text>
			</view>
			<view class="info-item">
				<text class="info-label">安装日期</text>
				<text class="info-value">{{ formatDate(deviceInfo.installTime) }}</text>
			</view>
		</view>
		
		<!-- 运行状态卡片 -->
		<view class="detail-card">
			<view class="card-header">
				<text class="card-title">运行状态</text>
				<text class="refresh-btn" @click="refreshData">刷新</text>
			</view>
			
			<view class="status-panel">
				<view class="status-item" v-for="(metric, index) in deviceMetrics" :key="index">
					<view class="metric-value" :class="{ alert: metric.isAlert }">{{ metric.value }}</view>
					<view class="metric-label">{{ metric.label }}</view>
				</view>
			</view>
			
			<view class="chart-container" v-if="deviceInfo.type === 'pump' || deviceInfo.type === 'sensor'">
				<!-- 图表区域，实际项目中集成echart或其他图表库 -->
				<view class="chart-placeholder">
					<text>实时数据趋势图</text>
				</view>
			</view>
		</view>
		
		<!-- 告警信息卡片 -->
		<view class="detail-card" v-if="alarmList.length > 0">
			<view class="card-header">
				<text class="card-title">告警信息</text>
				<text class="view-all" @click="navigateToAlarmList">查看全部</text>
			</view>
			
			<view class="alarm-list">
				<view class="alarm-item" v-for="(alarm, index) in alarmList" :key="index">
					<view class="alarm-icon" :class="alarm.level"></view>
					<view class="alarm-content">
						<view class="alarm-title">{{ alarm.title }}</view>
						<view class="alarm-desc">{{ alarm.description }}</view>
						<view class="alarm-time">{{ alarm.time }}</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 维护记录卡片 -->
		<view class="detail-card">
			<view class="card-header">
				<text class="card-title">维护记录</text>
				<text class="view-all" @click="navigateToMaintenanceList">查看全部</text>
			</view>
			
			<view class="maintenance-list">
				<view class="maintenance-item" v-for="(record, index) in maintenanceRecords" :key="index">
					<view class="maintenance-time">
						<text class="date">{{ formatMaintenanceDate(record.time) }}</text>
						<text class="time">{{ formatMaintenanceTime(record.time) }}</text>
					</view>
					<view class="maintenance-content">
						<view class="maintenance-title">{{ record.title }}</view>
						<view class="maintenance-desc">{{ record.description }}</view>
						<view class="maintenance-operator">操作人：{{ record.operator }}</view>
					</view>
				</view>
				
				<view class="empty-list" v-if="maintenanceRecords.length === 0">
					<text>暂无维护记录</text>
				</view>
			</view>
		</view>
		
		<!-- 操作按钮 -->
		<view class="action-buttons">
			<view class="action-btn warning" @click="reportFault">
				<text class="iconfont icon-warning"></text>
				<text>上报故障</text>
			</view>
			<view class="action-btn primary" @click="createMaintenance">
				<text class="iconfont icon-maintain"></text>
				<text>添加维护</text>
			</view>
		</view>
	</view>
</template>

<script>
	// 引入API模块
	import { deviceApi } from '../../utils/api';

	export default {
		data() {
			return {
				deviceId: '',
				deviceInfo: {
					deviceId: '',
					name: '',
					type: '',
					model: '',
					status: 'offline',
					location: {
						building: '',
						floor: '',
						room: '',
						coordinates: {
							lat: 0,
							lng: 0
						}
					},
					manufacturer: '',
					installTime: '',
					lastMaintenance: '',
					nextMaintenance: ''
				},
				deviceMetrics: [],
				alarmList: [],
				maintenanceRecords: [],
				isLoading: false
			}
		},
		onLoad(options) {
			// 获取路由参数中的设备ID
			this.deviceId = options.id;
			// 加载设备详情
			this.loadDeviceDetails();
			// 加载设备运行指标
			this.loadDeviceMetrics();
			// 加载设备告警信息
			this.loadDeviceAlarms();
			// 加载设备维护记录
			this.loadMaintenanceRecords();
		},
		methods: {
			// 加载设备详情
			loadDeviceDetails() {
				this.isLoading = true;
				
				// 调用API获取设备详情
				deviceApi.getDetail(this.deviceId)
					.then(res => {
						uni.hideLoading();
						if (res.code === 200) {
							this.deviceInfo = res.data;
						} else {
							this.showError(res.message || '加载失败');
						}
					})
					.catch(err => {
						uni.hideLoading();
						this.showError('网络异常，请稍后重试');
						console.error('获取设备详情失败:', err);
					});
				
				// 如果后端API还未就绪，可以使用模拟数据
				/*
				setTimeout(() => {
					this.deviceInfo = this.getMockDeviceDetail();
					uni.hideLoading();
				}, 500);
				*/
			},
			
			// 加载设备运行指标
			loadDeviceMetrics() {
				// 模拟API调用
				setTimeout(() => {
					// 实际项目中应该调用API
					// uni.request({
					//     url: `/api/devices/${this.deviceId}/metrics`,
					//     method: 'GET',
					//     success: (res) => {
					//         this.deviceMetrics = res.data;
					//     }
					// });
					
					// 模拟数据
					switch(this.deviceInfo.type) {
						case 'pump':
							this.deviceMetrics = [
								{ label: '流量', value: '200 m³/h', isAlert: false },
								{ label: '温度', value: '65 °C', isAlert: false },
								{ label: '压力', value: '0.4 MPa', isAlert: false },
								{ label: '功率', value: '5.5 kW', isAlert: false }
							];
							break;
						case 'valve':
							this.deviceMetrics = [
								{ label: '开度', value: '80%', isAlert: false },
								{ label: '压差', value: '0.2 MPa', isAlert: false }
							];
							break;
						case 'sensor':
							this.deviceMetrics = [
								{ label: '温度', value: '72 °C', isAlert: true },
								{ label: '信号强度', value: '95%', isAlert: false }
							];
							break;
						case 'controller':
							this.deviceMetrics = [
								{ label: 'CPU使用率', value: '45%', isAlert: false },
								{ label: '内存使用率', value: '60%', isAlert: false },
								{ label: '网络状态', value: '正常', isAlert: false }
							];
							break;
						default:
							this.deviceMetrics = [];
					}
				}, 600);
			},
			
			// 加载设备告警信息
			loadDeviceAlarms() {
				// 模拟API调用
				setTimeout(() => {
					// 实际项目中应该调用API
					// uni.request({
					//     url: `/api/devices/${this.deviceId}/alarms`,
					//     method: 'GET',
					//     success: (res) => {
					//         this.alarmList = res.data;
					//     }
					// });
					
					// 模拟数据
					this.alarmList = [
						{
							id: 'al001',
							level: 'warning',
							title: '温度过高警告',
							description: '设备温度达到75°C，接近阈值（80°C）',
							time: '2023-12-05 14:30:25'
						},
						{
							id: 'al002',
							level: 'error',
							title: '压力异常',
							description: '压力低于正常运行范围，可能存在泄漏',
							time: '2023-12-04 08:15:10'
						}
					];
				}, 700);
			},
			
			// 加载维护记录
			loadMaintenanceRecords() {
				// 模拟API调用
				setTimeout(() => {
					// 实际项目中应该调用API
					// uni.request({
					//     url: `/api/devices/${this.deviceId}/maintenance`,
					//     method: 'GET',
					//     success: (res) => {
					//         this.maintenanceRecords = res.data;
					//     }
					// });
					
					// 模拟数据
					this.maintenanceRecords = [
						{
							id: 'mr001',
							title: '例行检查',
							description: '检查设备运行状态，更换润滑油，无异常',
							time: '2023-12-01 10:15:00',
							operator: '张工'
						},
						{
							id: 'mr002',
							title: '故障维修',
							description: '更换损坏的轴承，恢复设备运行',
							time: '2023-11-15 14:30:00',
							operator: '李工'
						}
					];
				}, 800);
			},
			
			// 刷新数据
			refreshData() {
				uni.showLoading({
					title: '刷新中...'
				});
				
				this.loadDeviceMetrics();
				this.loadDeviceAlarms();
				
				setTimeout(() => {
					uni.hideLoading();
					uni.showToast({
						title: '数据已更新',
						icon: 'success'
					});
				}, 800);
			},
			
			// 上报故障
			reportFault() {
				uni.navigateTo({
					url: `/pages/fault/report?deviceId=${this.deviceId}`
				});
			},
			
			// 添加维护记录
			createMaintenance() {
				uni.navigateTo({
					url: `/pages/maintenance/add?deviceId=${this.deviceId}`
				});
			},
			
			// 跳转到告警列表
			navigateToAlarmList() {
				uni.navigateTo({
					url: `/pages/alarm/list?deviceId=${this.deviceId}`
				});
			},
			
			// 跳转到维护记录列表
			navigateToMaintenanceList() {
				uni.navigateTo({
					url: `/pages/maintenance/list?deviceId=${this.deviceId}`
				});
			},
			
			// 获取状态文本
			getStatusText(status) {
				switch(status) {
					case 'online':
						return '在线';
					case 'offline':
						return '离线';
					case 'fault':
						return '故障';
					default:
						return '未知';
				}
			},
			
			// 获取位置文本
			getLocation() {
				const loc = this.deviceInfo.location;
				if (!loc) return '未知位置';
				
				return `${loc.building || ''} ${loc.floor || ''} ${loc.room || ''}`.trim();
			},
			
			// 格式化日期
			formatDate(dateString) {
				if (!dateString) return '未知';
				
				const date = new Date(dateString);
				return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
			},
			
			// 格式化维护日期
			formatMaintenanceDate(dateString) {
				if (!dateString) return '';
				
				const date = new Date(dateString);
				return `${date.getMonth() + 1}月${date.getDate()}日`;
			},
			
			// 格式化维护时间
			formatMaintenanceTime(dateString) {
				if (!dateString) return '';
				
				const date = new Date(dateString);
				return `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
			}
		}
	}
</script>

<style lang="scss">
	.device-detail-container {
		padding: 30rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
	}
	
	.detail-card {
		background-color: #fff;
		border-radius: 12rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
		
		.card-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 24rpx;
			
			.card-title {
				font-size: 32rpx;
				font-weight: bold;
				color: $uni-text-color;
				position: relative;
				padding-left: 20rpx;
				
				&::before {
					content: '';
					position: absolute;
					left: 0;
					top: 8rpx;
					width: 6rpx;
					height: 28rpx;
					background-color: $uni-color-primary;
					border-radius: 3rpx;
				}
			}
			
			.device-status {
				padding: 4rpx 16rpx;
				font-size: 24rpx;
				border-radius: 20rpx;
				color: #fff;
				
				&.online {
					background-color: $uni-color-success;
				}
				
				&.offline {
					background-color: $uni-text-color-grey;
				}
				
				&.fault {
					background-color: $uni-color-error;
				}
			}
			
			.refresh-btn, .view-all {
				font-size: 26rpx;
				color: $uni-color-primary;
			}
		}
	}
	
	.info-item {
		display: flex;
		margin-bottom: 20rpx;
		
		.info-label {
			width: 180rpx;
			font-size: 28rpx;
			color: $uni-text-color-grey;
		}
		
		.info-value {
			flex: 1;
			font-size: 28rpx;
			color: $uni-text-color;
		}
	}
	
	.status-panel {
		display: flex;
		flex-wrap: wrap;
		margin-bottom: 30rpx;
		
		.status-item {
			width: 33.33%;
			text-align: center;
			margin-bottom: 30rpx;
			
			.metric-value {
				font-size: 36rpx;
				font-weight: bold;
				color: $uni-text-color;
				margin-bottom: 8rpx;
				
				&.alert {
					color: $uni-color-error;
				}
			}
			
			.metric-label {
				font-size: 26rpx;
				color: $uni-text-color-grey;
			}
		}
	}
	
	.chart-container {
		margin-top: 20rpx;
		
		.chart-placeholder {
			height: 300rpx;
			background-color: #f5f5f5;
			border-radius: 8rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			
			text {
				font-size: 28rpx;
				color: $uni-text-color-grey;
			}
		}
	}
	
	.alarm-list {
		.alarm-item {
			display: flex;
			margin-bottom: 20rpx;
			
			.alarm-icon {
				width: 16rpx;
				height: 16rpx;
				border-radius: 50%;
				margin-top: 12rpx;
				margin-right: 16rpx;
				
				&.warning {
					background-color: $uni-color-warning;
				}
				
				&.error {
					background-color: $uni-color-error;
				}
			}
			
			.alarm-content {
				flex: 1;
				
				.alarm-title {
					font-size: 28rpx;
					color: $uni-text-color;
					margin-bottom: 8rpx;
				}
				
				.alarm-desc {
					font-size: 26rpx;
					color: $uni-text-color-grey;
					margin-bottom: 8rpx;
				}
				
				.alarm-time {
					font-size: 24rpx;
					color: $uni-text-color-grey;
				}
			}
		}
	}
	
	.maintenance-list {
		.maintenance-item {
			display: flex;
			margin-bottom: 30rpx;
			
			.maintenance-time {
				width: 120rpx;
				margin-right: 20rpx;
				
				.date {
					display: block;
					font-size: 26rpx;
					color: $uni-text-color-grey;
					margin-bottom: 4rpx;
				}
				
				.time {
					display: block;
					font-size: 24rpx;
					color: $uni-text-color-grey;
				}
			}
			
			.maintenance-content {
				flex: 1;
				position: relative;
				padding-left: 30rpx;
				
				&::before {
					content: '';
					position: absolute;
					left: 0;
					top: 0;
					bottom: 0;
					width: 2rpx;
					background-color: #ddd;
				}
				
				&::after {
					content: '';
					position: absolute;
					left: -4rpx;
					top: 10rpx;
					width: 10rpx;
					height: 10rpx;
					border-radius: 50%;
					background-color: $uni-color-primary;
				}
				
				.maintenance-title {
					font-size: 28rpx;
					color: $uni-text-color;
					margin-bottom: 8rpx;
				}
				
				.maintenance-desc {
					font-size: 26rpx;
					color: $uni-text-color-grey;
					margin-bottom: 8rpx;
				}
				
				.maintenance-operator {
					font-size: 24rpx;
					color: $uni-text-color-grey;
				}
			}
		}
		
		.empty-list {
			text-align: center;
			padding: 40rpx 0;
			
			text {
				font-size: 28rpx;
				color: $uni-text-color-grey;
			}
		}
	}
	
	.action-buttons {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		padding: 20rpx 30rpx;
		background-color: #fff;
		box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);
		display: flex;
		
		.action-btn {
			flex: 1;
			height: 80rpx;
			border-radius: 40rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 28rpx;
			margin: 0 10rpx;
			
			.iconfont {
				margin-right: 8rpx;
			}
			
			&.primary {
				background-color: $uni-color-primary;
				color: #fff;
			}
			
			&.warning {
				background-color: #fff;
				color: $uni-color-warning;
				border: 1px solid $uni-color-warning;
			}
		}
	}
</style> 