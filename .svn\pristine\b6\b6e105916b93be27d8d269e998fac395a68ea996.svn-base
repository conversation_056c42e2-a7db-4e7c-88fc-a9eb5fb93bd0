<<<<<<< .mine
package com.heating.service;

import com.heating.dto.patrol.PatrolPlanCreateRequest;
import com.heating.dto.patrol.PatrolPlanCreateResponse;
import com.heating.dto.patrol.PatrolRecordSubmitRequest;
import com.heating.dto.patrol.PatrolRecordUpdateRequest;
import com.heating.dto.patrol.PatrolResultUpdateRequest;
import com.heating.dto.patrol.PatrolStatsResponse;  
import com.heating.dto.patrol.PatrolPlanListRequest;
import com.heating.dto.patrol.PatrolPlanListResponse;
import com.heating.dto.patrol.PatrolPlanDetailResponse;
import com.heating.dto.patrol.PatrolItemResponse;
import com.heating.dto.patrol.PatrolItemDictionaryResponse;
import com.heating.dto.patrol.PatrolCategoryResponse;
import com.heating.dto.patrol.PatrolRecordListRequest;
import com.heating.dto.patrol.PatrolRecordListResponse;
import com.heating.dto.patrol.PatrolResultDetailResponse;
import com.heating.dto.patrol.PatrolExecutorResponse;
import com.heating.dto.patrol.PatrolMessageResponse;
import com.heating.dto.patrol.PatrolResultDetailFullResponse;
import java.util.List;
import java.util.Map;

public interface PatrolService {
    /**
     * 创建巡检计划
     * @param request 巡检计划创建请求
     * @return 创建结果
     */
    PatrolPlanCreateResponse createPatrolPlan(PatrolPlanCreateRequest request);

    /**
     * 提交巡检记录
     * @param request 巡检记录提交请求
     * @return 提交结果
     */
    boolean submitPatrolRecord(PatrolRecordSubmitRequest request);

    /**
     * 更新巡检记录
     * @param recordId 巡检记录ID
     * @param request 更新请求
     * @return 更新是否成功
     */
    boolean updatePatrolRecord(Long recordId, PatrolRecordUpdateRequest request);

    /**
     * 更新巡检结果
     * @param resultId 巡检结果ID
     * @param request 更新请求
     * @return 更新是否成功
     */
    boolean updatePatrolResult(Long resultId, PatrolResultUpdateRequest request);

    /**
     * 获取巡检项目列表
     * @param planId 巡检计划ID
     * @return 巡检项目列表
     */
    List<PatrolItemResponse> getPatrolItems(Long planId);

    /**
     * 获取巡检记录统计数据
     * @return 巡检统计信息
     */
    PatrolStatsResponse getPatrolStats();
    
    /**
     * 获取巡检计划列表
     * @param request 巡检计划查询条件
     * @return 巡检计划列表
     */
     Map<String, Object> getPatrolPlanList(PatrolPlanListRequest request);
    
    /**
     * 获取巡检计划详情
     * @param planId 巡检计划ID
     * @return 巡检计划详情
     */
    PatrolPlanDetailResponse getPatrolPlanDetail(Long planId);
     
    
    /**
     * 获取巡检项目字典列表
     * @param categoryId 可选的类别ID参数，为null时获取全部
     * @return 巡检项目字典列表
     */
    List<PatrolItemDictionaryResponse> getPatrolItemDictionary(Long categoryId);
    
    /**
     * 获取巡检项目类别列表
     * @return 巡检项目类别列表
     */
    List<PatrolCategoryResponse> getPatrolCategories();
    
    /**
     * 获取巡检记录列表
     * @param request 查询条件
     * @return 巡检记录列表
     */
    Map<String, Object> getPatrolRecordList(PatrolRecordListRequest request);
    
    /**
     * 获取巡检结果详情
     * @param recordId 巡检记录ID
     * @return 巡检结果详情列表
     */
    List<PatrolResultDetailResponse> getPatrolResultDetail(Long recordId);
    
    /**
     * 获取巡检计划执行人列表
     * @param planId 巡检计划ID
     * @return 执行人列表
     */
    List<PatrolExecutorResponse> getPatrolExecutors(Long planId);
    
    /**
     * 获取巡检消息
     * 根据巡检计划的周期配置，返回当天需要执行的巡检计划列表
     * 
     * @param userId 用户ID
     * @param role 用户角色，如果是'admin'或'manage'，则获取所有记录，否则根据userId过滤
     * @return 巡检消息列表
     */
    List<PatrolMessageResponse> getPatrolMessages(Long userId, String role);

    /**
     * 获取巡检结果详情的完整数据（含记录信息、结果列表、统计和计划信息）
     * 优化性能的新方法
     * 
     * @param recordId 巡检记录ID
     * @return 完整的巡检结果详情响应
     */
    PatrolResultDetailFullResponse getPatrolResultDetailFull(Long recordId);

    /**
     * 获取有限数量的巡检工单信息
     * 根据用户ID和角色进行权限控制，返回指定数量的巡检工单
     * 
     * @param userId 用户ID
     * @param role 用户角色，如果是'admin'或'manage'，则获取所有记录，否则根据userId过滤
     * @param limit 限制返回的记录数量
     * @return 巡检工单列表
     */
    List<PatrolRecordListResponse> getLimitedPatrolRecords(Long userId, String role, Integer limit);
} ||||||| .r0
=======
package com.heating.service;

import com.heating.dto.patrol.PatrolPlanCreateRequest;
import com.heating.dto.patrol.PatrolPlanCreateResponse;
import com.heating.dto.patrol.PatrolRecordSubmitRequest;
import com.heating.dto.patrol.PatrolRecordUpdateRequest;
import com.heating.dto.patrol.PatrolResultUpdateRequest;
import com.heating.dto.patrol.PatrolStatsResponse;  
import com.heating.dto.patrol.PatrolPlanListRequest;
import com.heating.dto.patrol.PatrolPlanListResponse;
import com.heating.dto.patrol.PatrolPlanDetailResponse;
import com.heating.dto.patrol.PatrolItemResponse;
import com.heating.dto.patrol.PatrolItemDictionaryResponse;
import com.heating.dto.patrol.PatrolCategoryResponse;
import com.heating.dto.patrol.PatrolRecordListRequest;
import com.heating.dto.patrol.PatrolRecordListResponse;
import com.heating.dto.patrol.PatrolResultDetailResponse;
import com.heating.dto.patrol.PatrolExecutorResponse;
import com.heating.dto.patrol.PatrolMessageResponse;
import com.heating.dto.patrol.PatrolResultDetailFullResponse;
import java.util.List;
import java.util.Map;

public interface PatrolService {
    /**
     * 创建巡检计划
     * @param request 巡检计划创建请求
     * @return 创建结果
     */
    PatrolPlanCreateResponse createPatrolPlan(PatrolPlanCreateRequest request);

    /**
     * 提交巡检记录
     * @param request 巡检记录提交请求
     * @return 提交结果
     */
    boolean submitPatrolRecord(PatrolRecordSubmitRequest request);

    /**
     * 更新巡检记录
     * @param recordId 巡检记录ID
     * @param request 更新请求
     * @return 更新是否成功
     */
    boolean updatePatrolRecord(Long recordId, PatrolRecordUpdateRequest request);

    /**
     * 更新巡检结果
     * @param resultId 巡检结果ID
     * @param request 更新请求
     * @return 更新是否成功
     */
    boolean updatePatrolResult(Long resultId, PatrolResultUpdateRequest request);

    /**
     * 获取巡检项目列表
     * @param planId 巡检计划ID
     * @return 巡检项目列表
     */
    List<PatrolItemResponse> getPatrolItems(Long planId);

    /**
     * 获取巡检记录统计数据
     * @return 巡检统计信息
     */
    PatrolStatsResponse getPatrolStats();
    
    /**
     * 获取巡检计划列表
     * @param request 巡检计划查询条件
     * @return 巡检计划列表
     */
     Map<String, Object> getPatrolPlanList(PatrolPlanListRequest request);
    
    /**
     * 获取巡检计划详情
     * @param planId 巡检计划ID
     * @return 巡检计划详情
     */
    PatrolPlanDetailResponse getPatrolPlanDetail(Long planId);
     
    
    /**
     * 获取巡检项目字典列表
     * @param categoryId 可选的类别ID参数，为null时获取全部
     * @return 巡检项目字典列表
     */
    List<PatrolItemDictionaryResponse> getPatrolItemDictionary(Long categoryId);
    
    /**
     * 获取巡检项目类别列表
     * @return 巡检项目类别列表
     */
    List<PatrolCategoryResponse> getPatrolCategories();
    
    /**
     * 获取巡检记录列表
     * @param request 查询条件
     * @return 巡检记录列表
     */
    Map<String, Object> getPatrolRecordList(PatrolRecordListRequest request);
    
    /**
     * 获取巡检结果详情
     * @param recordId 巡检记录ID
     * @return 巡检结果详情列表
     */
    List<PatrolResultDetailResponse> getPatrolResultDetail(Long recordId);
    
    /**
     * 获取巡检计划执行人列表
     * @param planId 巡检计划ID
     * @return 执行人列表
     */
    List<PatrolExecutorResponse> getPatrolExecutors(Long planId);
    
    /**
     * 获取巡检消息
     * 根据巡检计划的周期配置，返回当天需要执行的巡检计划列表
     * 
     * @param userId 用户ID
     * @param role 用户角色，如果是'admin'或'manage'，则获取所有记录，否则根据userId过滤
     * @return 巡检消息列表
     */
    List<PatrolMessageResponse> getPatrolMessages(Long userId, String role);

    /**
     * 获取巡检结果详情的完整数据（含记录信息、结果列表、统计和计划信息）
     * 优化性能的新方法
     * 
     * @param recordId 巡检记录ID
     * @return 完整的巡检结果详情响应
     */
    PatrolResultDetailFullResponse getPatrolResultDetailFull(Long recordId);
} >>>>>>> .r5108
