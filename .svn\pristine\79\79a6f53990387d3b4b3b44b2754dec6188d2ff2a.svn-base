<template>
  <!-- 有权限时渲染内容 -->
  <template v-if="hasRequiredPermission">
    <slot></slot>
  </template>

  <!-- 无权限且需要显示提示时 -->
  <template v-else-if="showTip">
    <view class="no-permission-item">
      <text class="no-permission-text">无权限</text>
    </view>
  </template>

  <!-- 无权限且不需要提示时什么都不渲染 -->
</template>

<script>
import { hasPermission } from "@/utils/auth.js";

export default {
  name: "PermissionCheck",
  // 移除函数式组件标记，使用普通组件以确保正确渲染
  // functional: true,
  props: {
    // 必需的权限编码，可以是单个权限编码字符串或权限编码数组（满足其中一个即可）
    permission: {
      type: [String, Array],
      required: true,
    },
    // 是否显示无权限提示，默认不显示
    showTip: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      // 在这里可以添加组件状态
    };
  },
  computed: {
    hasRequiredPermission() {
      const result = Array.isArray(this.permission)
        ? this.permission.some((code) => hasPermission(code))
        : hasPermission(this.permission);
     // console.log("检查结果:", result ? "有权限" : "无权限");
      return result;
    },
  },
  mounted() {
   // console.log("PermissionCheck组件已挂载:", this.permission);
  },
};
</script>

<style>
.no-permission-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0.5;
}

.no-permission-text {
  font-size: 24rpx;
  color: #999;
  text-align: center;
}

/* 确保permission-check组件继承其父容器的布局特性 */
:deep(.permission-check) {
  display: inherit;
  width: inherit;
  height: inherit;
  flex: inherit;
  position: inherit;
}
</style>
