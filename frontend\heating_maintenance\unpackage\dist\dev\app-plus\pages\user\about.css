/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.about-container {
  background-color: #f5f7fa;
  min-height: 100vh;
  padding-bottom: 0.9375rem;
}
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}
.loading-container .loading-spinner {
  width: 2.5rem;
  height: 2.5rem;
  border: 0.1875rem solid rgba(24, 144, 255, 0.2);
  border-top: 0.1875rem solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 0.625rem;
}
.loading-container .loading-text {
  font-size: 0.875rem;
  color: #666;
}
@keyframes spin {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
.logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.875rem 0;
  background-color: #fff;
}
.logo-section .logo {
  width: 5.625rem;
  height: 5.625rem;
  margin-bottom: 0.9375rem;
  border-radius: 0.9375rem;
  box-shadow: 0 0.3125rem 0.9375rem rgba(24, 144, 255, 0.2);
}
.logo-section .app-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.logo-section .app-info .app-name {
  font-size: 1.125rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.3125rem;
}
.logo-section .app-info .app-version {
  font-size: 0.8125rem;
  color: #999;
}
.info-section {
  padding: 0.625rem 0;
}
.info-section .info-group {
  margin: 0.625rem 0.9375rem;
  background-color: #fff;
  border-radius: 0.375rem;
  padding: 0.625rem;
  box-shadow: 0 0.125rem 0.375rem rgba(0, 0, 0, 0.05);
}
.info-section .info-group .group-title {
  font-size: 0.9375rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.625rem;
  padding-left: 0.625rem;
  border-left: 0.1875rem solid #1890ff;
}
.info-section .info-group .info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.625rem 0.3125rem;
  border-bottom: 0.03125rem solid rgba(0, 0, 0, 0.05);
}
.info-section .info-group .info-item:last-child {
  border-bottom: none;
}
.info-section .info-group .info-item .item-label {
  font-size: 0.875rem;
  color: #666;
}
.info-section .info-group .info-item .item-value {
  font-size: 0.875rem;
  color: #333;
}
.info-section .info-group .info-item .item-value.link {
  color: #1890ff;
  text-decoration: underline;
}
.info-section .info-group .info-item .item-value.arrow {
  color: #999;
  display: flex;
  align-items: center;
}
.info-section .info-group .info-item .item-value.arrow .iconfont {
  font-size: 0.75rem;
  margin-left: 0.3125rem;
}
.info-section .info-group .info-item .check-update {
  color: #1890ff;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
}
.info-section .info-group .info-item .check-update .iconfont {
  font-size: 0.75rem;
  margin-left: 0.3125rem;
}
.copyright {
  margin-top: 1.25rem;
  padding: 0.9375rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.copyright .copyright-text {
  font-size: 0.75rem;
  color: #999;
  line-height: 1.6;
}