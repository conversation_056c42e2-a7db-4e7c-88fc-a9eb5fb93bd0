package com.heating.service;

import java.util.List;
import java.util.Map;
import com.heating.dto.hes.HesRequest;
import com.heating.dto.hes.HesDetailRequest;
import com.heating.dto.hes.HesControlRequest;
import com.heating.dto.hes.HesHistoryRequest;   
import com.heating.dto.hes.HesChartRequest;
import com.heating.dto.hes.HesAlarmsListRequest;
import com.heating.dto.hes.HesAlarmsDetailRequest;
import com.heating.dto.hes.HesAlarmsStatsRequest;
import com.heating.dto.hes.HesOnlineRateResponse;

public interface HesService {
    /**
     * 获取换热站列表
     * @param request 包含查询条件的请求
     * @return 换热站列表
     */ 
    List<Map<String, Object>> getHesList(HesRequest request); 
    
    /**
     * 获取换热站详细数据
     * @param request 包含换热站ID的请求
     * @return 换热站详细数据
     */
    Map<String, Object> getHesDetail(HesDetailRequest request);

    /**
     * 控制换热站
     * @param request 包含控制命令的请求
     * @return 控制结果
     */ 
    Map<String, Object> controlHes(HesControlRequest request);

    /**
     * 获取换热站历史数据
     * @param request 包含查询条件的请求
     * @return 换热站历史数据
     */
    List<Map<String, Object>> getHesHistory(HesHistoryRequest request);
    
    /**
     * 获取换热站数据曲线
     * @param request 包含查询条件的请求
     * @return 换热站数据曲线
     */
    Map<String, Object> getHesChart(HesChartRequest request);
    
    /**
     * 获取换热站告警列表
     * @param request 包含查询条件的请求
     * @return 换热站告警列表
     */
    Map<String, Object> getHesAlarmsList(HesAlarmsListRequest request);
    
    /**
     * 获取换热站告警详情
     * @param request 包含告警ID的请求
     * @return 换热站告警详情
     */
    Map<String, Object> getHesAlarmsDetail(HesAlarmsDetailRequest request);
    
    /**
     * 获取换热站告警统计
     * @param request 包含查询条件的请求
     * @return 换热站告警统计
     */
    Map<String, Object> getHesAlarmsStats(HesAlarmsStatsRequest request);
    
    /**
     * 获取换热站在线率
     * @return 换热站在线率
     */
    HesOnlineRateResponse getOnlineRate();
}