.container {
  padding: 30rpx;
  background: #f5f5f5;
}

.form-group {
  background: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
}

.form-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.form-item:last-child {
  border-bottom: none;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.picker {
  height: 80rpx;
  line-height: 80rpx;
  background: #f8f8f8;
  padding: 0 20rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #666;
}

.textarea {
  width: 100%;
  height: 200rpx;
  background: #f8f8f8;
  padding: 20rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.upload-box {
  margin-bottom: 20rpx;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 16rpx;
}

.image-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
}

.image-item image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.delete-btn {
  position: absolute;
  top: -16rpx;
  right: -16rpx;
  background: rgba(0, 0, 0, 0.5);
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.delete-btn .wx-icon {
  font-size: 24rpx;
  color: #fff;
}

.upload-actions {
  display: flex;
  gap: 20rpx;
}

.upload-btn {
  width: 160rpx;
  height: 160rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-btn .wx-icon {
  font-size: 48rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.upload-text {
  font-size: 24rpx;
  color: #999;
}

.tips {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.submit-btn {
  margin-top: 40rpx;
  padding: 0 30rpx;
}

button[type="primary"] {
  background: #4CAF50;
} 