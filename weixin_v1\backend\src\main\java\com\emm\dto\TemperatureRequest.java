package com.emm.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public class TemperatureRequest {

    @JsonProperty("community_name")
    private String communityName;

    @JsonProperty("building_no")
    private String buildingNo;

    @JsonProperty("unit_no")
    private String unitNo;

    @JsonProperty("room_no")
    private String roomNo;

    @JsonProperty("indoor_temp")
    private Double indoorTemp;

    @JsonProperty("outdoor_temp")
    private Double outdoorTemp;

    @JsonProperty("latitude")
    private String latitude;

    @JsonProperty("longitude")
    private String longitude;

    @JsonProperty("image_url")
    private String imageUrl;

    @JsonProperty("video_url")
    private String videoUrl;

    // Getters
    public String getCommunityName() {
        return communityName;
    }

    public String getBuildingNo() {
        return buildingNo;
    }

    public String getUnitNo() {
        return unitNo;
    }

    public String getRoomNo() {
        return roomNo;
    }

    public Double getIndoorTemp() {
        return indoorTemp;
    }

    public Double getOutdoorTemp() {
        return outdoorTemp;
    }

    public String getLatitude() {
        return latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    // Setters
    public void setCommunityName(String communityName) {
        this.communityName = communityName;
    }

    public void setBuildingNo(String buildingNo) {
        this.buildingNo = buildingNo;
    }

    public void setUnitNo(String unitNo) {
        this.unitNo = unitNo;
    }

    public void setRoomNo(String roomNo) {
        this.roomNo = roomNo;
    }

    public void setIndoorTemp(Double indoorTemp) {
        this.indoorTemp = indoorTemp;
    }

    public void setOutdoorTemp(Double outdoorTemp) {
        this.outdoorTemp = outdoorTemp;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    // equals 方法
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        TemperatureRequest that = (TemperatureRequest) o;
        
        if (communityName != null ? !communityName.equals(that.communityName) : that.communityName != null) return false;
        if (buildingNo != null ? !buildingNo.equals(that.buildingNo) : that.buildingNo != null) return false;
        if (unitNo != null ? !unitNo.equals(that.unitNo) : that.unitNo != null) return false;
        if (roomNo != null ? !roomNo.equals(that.roomNo) : that.roomNo != null) return false;
        if (indoorTemp != null ? !indoorTemp.equals(that.indoorTemp) : that.indoorTemp != null) return false;
        if (outdoorTemp != null ? !outdoorTemp.equals(that.outdoorTemp) : that.outdoorTemp != null) return false;
        if (latitude != null ? !latitude.equals(that.latitude) : that.latitude != null) return false;
        if (longitude != null ? !longitude.equals(that.longitude) : that.longitude != null) return false;
        if (imageUrl != null ? !imageUrl.equals(that.imageUrl) : that.imageUrl != null) return false;
        return videoUrl != null ? videoUrl.equals(that.videoUrl) : that.videoUrl == null;
    }

    // hashCode 方法
    @Override
    public int hashCode() {
        int result = communityName != null ? communityName.hashCode() : 0;
        result = 31 * result + (buildingNo != null ? buildingNo.hashCode() : 0);
        result = 31 * result + (unitNo != null ? unitNo.hashCode() : 0);
        result = 31 * result + (roomNo != null ? roomNo.hashCode() : 0);
        result = 31 * result + (indoorTemp != null ? indoorTemp.hashCode() : 0);
        result = 31 * result + (outdoorTemp != null ? outdoorTemp.hashCode() : 0);
        result = 31 * result + (latitude != null ? latitude.hashCode() : 0);
        result = 31 * result + (longitude != null ? longitude.hashCode() : 0);
        result = 31 * result + (imageUrl != null ? imageUrl.hashCode() : 0);
        result = 31 * result + (videoUrl != null ? videoUrl.hashCode() : 0);
        return result;
    }

    // toString 方法
    @Override
    public String toString() {
        return "TemperatureRequest{" +
                "communityName='" + communityName + '\'' +
                ", buildingNo='" + buildingNo + '\'' +
                ", unitNo='" + unitNo + '\'' +
                ", roomNo='" + roomNo + '\'' +
                ", indoorTemp=" + indoorTemp +
                ", outdoorTemp=" + outdoorTemp +
                ", latitude='" + latitude + '\'' +
                ", longitude='" + longitude + '\'' +
                ", imageUrl='" + imageUrl + '\'' +
                ", videoUrl='" + videoUrl + '\'' +
                '}';
    }
}