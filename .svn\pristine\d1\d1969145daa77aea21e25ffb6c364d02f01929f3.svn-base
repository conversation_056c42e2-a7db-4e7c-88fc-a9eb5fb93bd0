package com.heating.dto.fault;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 故障消息响应DTO
 * 用于返回故障记录列表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FaultMessageResponse {
    
    /**
     * 故障记录ID
     */
    private Long id;

    /** 
     * 热用户ID
     */
    private Long heatUnitId;

    /**
     * 热用户名称
     */ 
    private String heatUnitName;

    /**
     * 项目管理员ID列表
     */
    private List<Long> managerIds;
    
    /**
     * 故障描述
     */
    private String faultDesc;
    
    /**
     * 故障时间
     */
    private String occurTime;
} 