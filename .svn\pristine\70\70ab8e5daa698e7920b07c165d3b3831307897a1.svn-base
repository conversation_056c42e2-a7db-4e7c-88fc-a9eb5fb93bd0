package com.heating.converter;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import com.fasterxml.jackson.module.paramnames.ParameterNamesModule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.IOException;
import java.util.List;
import java.util.Map;


@Converter  
public class JsonConverter implements AttributeConverter<Object, String> {

    // 添加日志输出
    private static final Logger logger = LoggerFactory.getLogger(JsonConverter.class);

    private static final ObjectMapper objectMapper = new ObjectMapper()
        .registerModule(new JavaTimeModule())
        .registerModule(new ParameterNamesModule());

    @Override
    public String convertToDatabaseColumn(Object attribute) {
        if (attribute == null) {
            return null;
        }

        try {
            String json = objectMapper.writeValueAsString(attribute);
            logger.debug("Serialized JSON: {}", json);
            return json;
        } catch (JsonProcessingException e) {
            logger.error("Error converting to JSON: {}", e.getMessage());
            throw new IllegalArgumentException("Error converting to JSON", e);
        }
    }

    @Override
    public Object convertToEntityAttribute(String dbData) {
        if (dbData == null || dbData.isEmpty()) {
            return null;
        }

        try {
            // 首先将字符串解析为JsonNode
            JsonNode jsonNode = objectMapper.readTree(dbData);
            
            // 根据JSON结构类型返回相应的Java对象
            if (jsonNode.isArray()) {
                return objectMapper.readValue(dbData, new TypeReference<List<Object>>() {});
            } else if (jsonNode.isObject()) {
                return objectMapper.readValue(dbData, new TypeReference<Map<String, Object>>() {});
            } else {
                // 简单类型
                return objectMapper.readValue(dbData, Object.class);
            }
        } catch (IOException e) {
            logger.error("Error converting from JSON: {}", e.getMessage(), e);
            logger.error("Problematic JSON string: {}", dbData);
            throw new IllegalArgumentException("Error converting from JSON: " + e.getMessage(), e);
        }
    }
} 