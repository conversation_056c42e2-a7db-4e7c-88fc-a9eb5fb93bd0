package com.heating.entity.device;

import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "t_device_maintenance")
public class TDeviceMaintenance {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "device_id", nullable = false)
    private Long deviceId;

    @Column(name = "start_time", nullable = false)
    private LocalDateTime startTime;

    @Column(name = "end_time")
    private LocalDateTime endTime;

    @Column(name = "maintenance_type", nullable = false)
    private String maintenanceType;

    @Column(name = "executor_id", nullable = false)
    private String executorId;

    @Column(name = "executor_name", nullable = false)
    private String executorName;

    @Column(name = "description", length = 500)
    private String description;

    @Column(name = "images", columnDefinition = "json")
    private String images;

    @Column(name = "status", nullable = false)
    @Enumerated(EnumType.STRING)
    private MaintenanceStatus status = MaintenanceStatus.pending;

    @Column(name = "create_time", nullable = false, updatable = false)
    private LocalDateTime createTime;

    @Column(name = "update_time", nullable = false)
    private LocalDateTime updateTime;

    public enum MaintenanceStatus {
        pending,    // 待执行
        processing, // 执行中
        completed,   // 已完成
        cancelled    // 已取消
    }

    @PrePersist
    protected void onCreate() {
        createTime = LocalDateTime.now();
        updateTime = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updateTime = LocalDateTime.now();
    }
} 