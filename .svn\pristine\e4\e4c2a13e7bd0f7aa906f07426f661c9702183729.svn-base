package com.heating.controller;

import com.heating.dto.ApiResponse;
import com.heating.dto.attendance.*;
import com.heating.service.AttendanceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/attendance")
public class AttendanceController {

    private static final Logger logger = LoggerFactory.getLogger(AttendanceController.class);

    @Autowired
    private AttendanceService attendanceService;

    /**
     * 7.1 打卡（分上下班打卡，上班打卡开启定时推送位置功能，下班打卡关闭推送位置功能）
     * @param request 打卡请求
     * @return 打卡结果
     */
    @PostMapping("/clock")
    public ResponseEntity<ApiResponse<AttendanceClockResponse>> clock(@RequestBody AttendanceClockRequest request) {
        try {
            logger.info("用户打卡请求: userId={}, clockType={}", request.getUserId(), request.getClockType());
            
            // 如果请求中没有提供用户ID，从认证上下文中获取
            if (request.getUserId() == null) {
                request.setUserId(getCurrentUserId());
            }
            
            AttendanceClockResponse response = attendanceService.clockIn(request);
            return ResponseEntity.ok(ApiResponse.success("打卡成功", response));
        } catch (Exception e) {
            logger.error("打卡失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error("打卡失败: " + e.getMessage()));
        }
    }

    /**
     * 7.2 考勤记录
     * 根据用户id查询考勤记录，如果用户id为空则查询所有用户考勤记录，其他参数也可以为空
     * 
     * @param user_id 用户ID（可选）
     * @param year 年份（可选）
     * @param month 月份（可选）
     * @param day 日期（可选）
     * @param status 状态（可选）
     * @param startDate 开始日期（可选，格式：yyyy-MM-dd）
     * @param endDate 结束日期（可选，格式：yyyy-MM-dd）
     * @param keyword 搜索关键词（可选）
     * @return 考勤记录
     */
    @GetMapping("/records")
    public ResponseEntity<ApiResponse<AttendanceRecordResponse>> getAttendanceRecords(
            @RequestParam(value = "user_id", required = false) Long user_id,
            @RequestParam(required = false) Integer year,
            @RequestParam(required = false) Integer month,
            @RequestParam(required = false) Integer day,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) String keyword) {
        try {
            logger.info("查询考勤记录: userId={}, year={}, month={}, day={}, status={}, startDate={}, endDate={}, keyword={}", 
                    user_id, year, month, day, status, startDate, endDate, keyword);
            
            // 如果未提供用户ID且用户不是管理员，则使用当前登录用户的ID
            if (user_id == null && !isAdmin()) {
                user_id = getCurrentUserId();
            }
            
            // 如果是普通用户但尝试查询其他用户的记录，拒绝请求
            if (!isAdmin() && user_id != null && !user_id.equals(getCurrentUserId())) {
                return ResponseEntity.badRequest().body(ApiResponse.error("无权查看其他用户的考勤记录"));
            }
            
            // 调用服务层方法查询考勤记录
            AttendanceRecordResponse response;
            
            // 如果提供了日期范围，优先使用日期范围
            if (startDate != null && endDate != null) {
                response = attendanceService.getAttendanceRecordsByDateRange(user_id, startDate, endDate, status);
            } else {
                response = attendanceService.getAttendanceRecords(user_id, year, month, day, status);
            }
            
            // 如果有搜索关键词，在应用层过滤结果
            if (keyword != null && !keyword.isEmpty() && response.getRecords() != null) {
                List<AttendanceRecordResponse.AttendanceRecordDetail> filteredRecords = response.getRecords().stream()
                    .filter(record -> {
                        if (record.getUser() != null) {
                            String userName = record.getUser().getName();
                            String department = record.getUser().getDepartment();
                            return (userName != null && userName.contains(keyword)) || 
                                   (department != null && department.contains(keyword));
                        }
                        return false;
                    })
                    .collect(Collectors.toList());
                
                response.setRecords(filteredRecords);
                
                // 更新统计数据
                if (response.getSummary() != null) {
                    response.getSummary().put("total", filteredRecords.size());
                }
            }
            
            return ResponseEntity.ok(ApiResponse.success("查询考勤记录成功", response));
        } catch (Exception e) {
            logger.error("查询考勤记录失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error("查询考勤记录失败: " + e.getMessage()));
        }
    }

    /**
     * 7.3 考勤统计
     * 根据用户id查询考勤统计，如果用户id为空则查询所有用户考勤统计，其他参数也可以为空
     * 
     * @param user_id 用户ID（可选）
     * @param year 年份（可选）
     * @param month 月份（可选）
     * @param day 天（可选）
     * @return 考勤统计
     */
    @GetMapping("/stats")
    public ResponseEntity<ApiResponse<AttendanceStatsResponse>> getAttendanceStats(
            @RequestParam(value = "user_id", required = false) Long user_id,
            @RequestParam(required = false) Integer year,
            @RequestParam(required = false) Integer month,
            @RequestParam(required = false) Integer day
            ) {
        try {
            logger.info("查询考勤统计: userId={}, year={}, month={}, day={}", user_id, year, month, day);
            
            // 如果未提供用户ID且用户不是管理员，则使用当前登录用户的ID
            if (user_id == null && !isAdmin()) {
                user_id = getCurrentUserId();
            }
            
            // 如果是普通用户但尝试查询其他用户的统计，拒绝请求
            if (!isAdmin() && user_id != null && !user_id.equals(getCurrentUserId())) {
                return ResponseEntity.badRequest().body(ApiResponse.error("无权查看其他用户的考勤统计"));
            }
            
            AttendanceStatsResponse response = attendanceService.getAttendanceStats(user_id, year, month, day);
            return ResponseEntity.ok(ApiResponse.success("查询考勤统计成功", response));
        } catch (Exception e) {
            logger.error("查询考勤统计失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error("查询考勤统计失败: " + e.getMessage()));
        }
    }

    /**
     * 7.4 获取今日打卡记录
     * @return 今日打卡记录
     */
    @GetMapping("/today")
    public ResponseEntity<ApiResponse<AttendanceTodayResponse>> getTodayAttendance(@RequestParam(value = "user_id", required = false) Long user_id) {
        try {
            AttendanceTodayResponse todayAttendance = attendanceService.getTodayAttendance(user_id);
            return ResponseEntity.ok(ApiResponse.success("获取今日打卡记录成功", todayAttendance));
        } catch (Exception e) {
            logger.error("获取今日打卡记录失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error("获取今日打卡记录失败: " + e.getMessage()));
        }
    }

    /**
     * 7.5 获取最近打卡记录
     * @param  user_id 请求参数
     * @return 最近打卡记录
     */
    @GetMapping("/recent")
    public ResponseEntity<ApiResponse<AttendanceRecentResponse>> getRecentAttendance(
            @RequestParam(value = "user_id", required = false) Long user_id,
            @RequestParam(required = false, defaultValue = "7") Integer days) {
        try {
            AttendanceRecentResponse recentAttendance = attendanceService.getRecentAttendance(user_id, days);
            return ResponseEntity.ok(ApiResponse.success("获取最近打卡记录成功", recentAttendance));
        } catch (Exception e) {
            logger.error("获取最近打卡记录失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error("获取最近打卡记录失败: " + e.getMessage()));
        }
    }

    /**
     * 7.6 获取打卡规则
     * @return 打卡规则
     */
    @GetMapping("/rules")
    public ResponseEntity<ApiResponse<AttendanceRulesResponse>> getAttendanceRules() {
        try {
            AttendanceRulesResponse rules = attendanceService.getAttendanceRules();
            return ResponseEntity.ok(ApiResponse.success("获取打卡规则成功", rules));
        } catch (Exception e) {
            logger.error("获取打卡规则失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error("获取打卡规则失败: " + e.getMessage()));
        }
    }

    /**
     * 7.7 检查考勤范围
     * @param request 检查考勤范围请求
     * @return 考勤范围检查结果
     */
    @PostMapping("/check-area")
    public ResponseEntity<ApiResponse<AttendanceCheckAreaResponse>> checkAttendanceArea(
            @RequestBody AttendanceCheckAreaRequest request) {
        try {
            AttendanceCheckAreaResponse response = attendanceService.checkAttendanceArea(request);
            return ResponseEntity.ok(ApiResponse.success("考勤范围检查成功", response));
        } catch (Exception e) {
            logger.error("考勤范围检查失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error("考勤范围检查失败: " + e.getMessage()));
        }
    }

    /**
     * 7.8 提交补卡申请
     * @param request 补卡申请请求
     * @return 补卡申请结果
     */
    @PostMapping("/supplement")
    public ResponseEntity<ApiResponse<AttendanceSupplementResponse>> submitSupplement(
            @RequestBody AttendanceSupplementRequest request) {
        try {
            // 从认证上下文中获取用户ID，如果请求中没有提供
            if (request.getUserId() == null) {
                request.setUserId(getCurrentUserId());
            }
            
            AttendanceSupplementResponse response = attendanceService.submitSupplement(request);
            return ResponseEntity.ok(ApiResponse.success("补卡申请提交成功", response));
        } catch (Exception e) {
            logger.error("补卡申请提交失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error("补卡申请提交失败: " + e.getMessage()));
        }
    }

    /**
     * 7.9 获取员工考勤列表(管理员用)
     * @param query 查询条件
     * @return 员工考勤列表
     */
    @GetMapping("/staff")
    public ResponseEntity<ApiResponse<AttendanceStaffResponse>> getStaffAttendance(
            @RequestParam(required = false) Long departmentId,
            @RequestParam(required = false) String month,
            @RequestParam(required = false) String status) {
        try {
            AttendanceStaffQuery query = new AttendanceStaffQuery();
            query.setDepartmentId(departmentId);
            query.setMonth(month);
            query.setStatus(status);
            
            AttendanceStaffResponse response = attendanceService.getStaffAttendance(query);
            return ResponseEntity.ok(ApiResponse.success("获取员工考勤列表成功", response));
        } catch (Exception e) {
            logger.error("获取员工考勤列表失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error("获取员工考勤列表失败: " + e.getMessage()));
        }
    }

    /**
     * 7.10 获取部门考勤统计(管理员用)
     * @param year 年份
     * @param month 月份
     * @return 部门考勤统计
     */
    @GetMapping("/department-stats")
    public ResponseEntity<ApiResponse<AttendanceDepartmentStatsResponse>> getDepartmentStats(
            @RequestParam(required = false) Integer year,
            @RequestParam(required = false) Integer month) {
        try {
            // 检查当前用户是否有管理员权限
            checkAdminPermission();
            
            AttendanceDepartmentStatsQuery query = new AttendanceDepartmentStatsQuery();
            query.setYear(year);
            query.setMonth(month);
            
            AttendanceDepartmentStatsResponse response = attendanceService.getDepartmentStats(query);
            return ResponseEntity.ok(ApiResponse.success("获取部门考勤统计成功", response));
        } catch (Exception e) {
            logger.error("获取部门考勤统计失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error("获取部门考勤统计失败: " + e.getMessage()));
        }
    }

    /**
     * 7.11 审批补卡申请(管理员用)
     * @param request 审批补卡申请请求
     * @return 审批结果
     */
    @PostMapping("/approve-supplement")
    public ResponseEntity<ApiResponse<AttendanceSupplementApproveResponse>> approveSupplement(
            @RequestBody AttendanceSupplementApproveRequest request) {
        try {
            // 检查当前用户是否有管理员权限
            checkAdminPermission();
            
            AttendanceSupplementApproveResponse response = attendanceService.approveSupplement(request);
            return ResponseEntity.ok(ApiResponse.success("审批成功", response));
        } catch (Exception e) {
            logger.error("审批失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error("审批失败: " + e.getMessage()));
        }
    }

    /**
     * 7.12 获取所有员工列表
     * @return 员工列表
     */
    @GetMapping("/all-staff")
    public ResponseEntity<ApiResponse<AttendanceStaffListResponse>> getAllStaff() {
        try {
            // 不再检查管理员权限，允许所有用户获取员工列表
            // checkAdminPermission();
            
            AttendanceStaffListResponse response = attendanceService.getAllStaff();
            return ResponseEntity.ok(ApiResponse.success("获取员工列表成功", response));
        } catch (Exception e) {
            logger.error("获取员工列表失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error("获取员工列表失败: " + e.getMessage()));
        }
    }

    /**
     * 7.13 更新打卡规则(管理员用)
     * @param request 更新打卡规则请求
     * @return 更新后的打卡规则
     */
    @PostMapping("/updaterules")
    public ResponseEntity<ApiResponse<AttendanceRulesResponse>> updateAttendanceRules(
            @RequestBody UpdateAttendanceRulesRequest request) {
        try { 
            AttendanceRulesResponse response = attendanceService.updateAttendanceRules(
                    request.getClockInTime(), request.getClockOutTime());
            return ResponseEntity.ok(ApiResponse.success("更新打卡规则成功", response));
        } catch (Exception e) {
            logger.error("更新打卡规则失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error("更新打卡规则失败: " + e.getMessage()));
        }
    }

    /**
     * 从认证上下文中获取当前用户ID
     * @return 当前用户ID
     */
    private Long getCurrentUserId() {
        // TODO: 从认证上下文中获取当前用户ID
        // 这里应该从Spring Security的SecurityContextHolder中获取
        // 临时返回一个假的用户ID用于测试
        return 1L;
    }

    /**
     * 检查当前用户是否有管理员权限
     * @throws RuntimeException 如果当前用户没有管理员权限
     */
    private void checkAdminPermission() {
        // TODO: 检查当前用户是否有管理员权限
        // 这里应该从Spring Security的SecurityContextHolder中获取用户权限
        // 如果没有管理员权限，则抛出异常
        // 临时不做任何检查，假设用户有管理员权限
    }

    /**
     * 检查当前用户是否是管理员
     * @return 是否是管理员
     */
    private boolean isAdmin() {
        // TODO: 检查当前用户是否是管理员
        // 这里应该从Spring Security的SecurityContextHolder中获取用户权限
        // 临时返回一个假的管理员状态用于测试
        return true;
    }
} 