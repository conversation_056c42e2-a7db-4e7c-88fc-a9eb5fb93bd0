package com.heating.dto.patrol;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class PatrolRecordSubmitRequest {

    @JsonProperty("id")
    private Long id;


    @JsonProperty("patrol_plan_id")
    private int patrolPlanId;
    
    @JsonProperty("executor_id")
    private Long executorId;
    
    @JsonProperty("start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    
    @JsonProperty("end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
    
    @JsonProperty("status")
    private String status;
    
    @JsonProperty("remark")
    private String remark;
    
    @JsonProperty("patrol_results")
    private List<PatrolItemResult> patrolResults;
    
    @Data
    public static class PatrolItemResult {
        @JsonProperty("patrol_item_id")
        private Long patrolItemId;
        
        @JsonProperty("check_result")
        private String checkResult;
        
        @JsonProperty("param_value")
        private String paramValue;
        
        @JsonProperty("description")
        private String description;
        
        @JsonProperty("images")
        private List<String> images;
        
        @JsonProperty("latitude")
        private Double latitude;
        
        @JsonProperty("longitude")
        private Double longitude;
    }
} 