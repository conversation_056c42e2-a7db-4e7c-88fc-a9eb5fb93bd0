.container {
  padding: 30rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.info-card,
.location-card,
.photo-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 30rpx;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.time {
  font-size: 26rpx;
  color: #999;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 28rpx;
}

.label {
  color: #666;
}

.value {
  color: #333;
}

.temp {
  font-weight: bold;
}

.temp.normal { color: #4CAF50; }
.temp.high { color: #F56C6C; }
.temp.low { color: #409EFF; }

.map {
  width: 100%;
  height: 400rpx;
  border-radius: 8rpx;
}

.photo-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.photo {
  width: 100%;
  height: 200rpx;
  border-radius: 8rpx;
} 