/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.change-password-container {
  background-color: #f5f7fa;
  min-height: 100vh;
  padding-bottom: 1.25rem;
}
.page-header {
  background-color: #fff;
  padding: 0.9375rem;
  border-bottom: 0.03125rem solid #eee;
}
.page-header .page-title {
  font-size: 1.125rem;
  font-weight: bold;
  color: #333;
}
.form-card {
  margin: 0.9375rem;
  background-color: #fff;
  border-radius: 0.375rem;
  padding: 0.625rem;
  box-shadow: 0 0.1875rem 0.625rem rgba(0, 0, 0, 0.05);
}
.form-card .form-item {
  padding: 0.75rem 0.625rem;
  border-bottom: 0.03125rem solid rgba(0, 0, 0, 0.05);
}
.form-card .form-item:last-child {
  border-bottom: none;
}
.form-card .form-item .form-label {
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 0.5rem;
  display: block;
}
.form-card .form-item .form-input uni-input {
  height: 2.5rem;
  font-size: 1rem;
  color: #333;
  width: 100%;
}
.form-card .form-item .password-rules {
  display: flex;
  flex-wrap: wrap;
  margin-top: 0.5rem;
}
.form-card .form-item .password-rules .rule-item {
  font-size: 0.75rem;
  color: #999;
  margin-right: 0.9375rem;
  margin-bottom: 0.3125rem;
  display: flex;
  align-items: center;
}
.form-card .form-item .password-rules .rule-item::before {
  content: "";
  display: inline-block;
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background-color: #e8e8e8;
  margin-right: 0.25rem;
}
.form-card .form-item .password-rules .rule-item.rule-passed {
  color: #52c41a;
}
.form-card .form-item .password-rules .rule-item.rule-passed::before {
  background-color: #52c41a;
}
.form-card .form-item .confirm-tip {
  margin-top: 0.5rem;
}
.form-card .form-item .confirm-tip .tip-error {
  font-size: 0.75rem;
  color: #f5222d;
}
.submit-btn {
  margin: 1.875rem 0.9375rem;
  height: 2.8125rem;
  line-height: 2.8125rem;
  background-color: #1890ff;
  color: #fff;
  text-align: center;
  border-radius: 0.375rem;
  font-size: 1rem;
  box-shadow: 0 0.1875rem 0.625rem rgba(24, 144, 255, 0.3);
  font-weight: bold;
}
.submit-btn:active {
  opacity: 0.9;
  transform: translateY(0.0625rem);
}
.submit-btn.btn-disabled {
  background-color: #ccc;
  box-shadow: none;
}
.help-tip {
  padding: 0 1.25rem;
  margin-top: 1.25rem;
}
.help-tip .tip-title {
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 0.5rem;
  display: block;
}
.help-tip .tip-content {
  font-size: 0.8125rem;
  color: #999;
  margin-bottom: 0.3125rem;
  display: block;
  line-height: 1.6;
}