package com.heating.entity.device;

import jakarta.persistence.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;
 
@Data
@Entity
@Table(name = "t_device_alarm")
public class TDeviceAlarm {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "device_id", nullable = false, length = 50)
    private String deviceId;

    @Column(name = "alarm_type", nullable = false, length = 50)
    private String alarmType;

    @Column(name = "alarm_level", nullable = false, length = 20)
    private String alarmLevel;

    @Column(name = "alarm_content", nullable = false)
    private String alarmContent;

    @Column(name = "param_name", length = 50)
    private String paramName;

    @Column(name = "param_value", columnDefinition = "DECIMAL(10,2)")
    private BigDecimal paramValue;

    @Column(name = "status", nullable = false, length = 20)
    private String status;

    @Column(name = "start_time", nullable = false)
    private LocalDateTime startTime;

    @Column(name = "end_time")
    private LocalDateTime endTime;

    @Column(name = "auto_plan", columnDefinition = "json")
    private String autoPlan;

    @Column(name = "plan_execution_log")
    private String planExecutionLog;

    @Column(name = "create_time", nullable = false)
    private LocalDateTime createTime;
}
