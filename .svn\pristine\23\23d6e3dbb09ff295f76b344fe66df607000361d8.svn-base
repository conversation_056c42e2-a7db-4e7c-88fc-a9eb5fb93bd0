/**
 * 文件上传工具类
 */
import { getToken } from './auth.js';

// 直接使用后端API地址，不通过Vite代理
const BASE_URL = 'http://43.139.65.175:8889';
//const BASE_URL = 'http://192.168.0.155:8889';
// 修改上传URL，添加/api前缀与后端控制器路径匹配
const UPLOAD_IMAGE_URL = BASE_URL + '/api/upload/uploadImage/app';
const UPLOAD_VIDEO_URL = BASE_URL + '/api/upload/uploadVideo/app';

export default {
  /**
   * 上传图片到服务器
   * @param {String} filePath - 本地图片路径
   * @returns {Promise} - 返回上传结果，包含服务器文件路径
   */
  uploadImage(filePath) {
    return new Promise((resolve, reject) => {
      console.log('开始上传图片:', filePath);
      console.log('上传URL:', UPLOAD_IMAGE_URL);
      
      // 获取token并添加Bearer前缀
      let token = getToken();
      if (token && !token.startsWith('Bearer ')) {
        token = 'Bearer ' + token;
      }
      
      uni.uploadFile({
        url: UPLOAD_IMAGE_URL,
        filePath: filePath,
        name: 'file',
        header: {
          'Authorization': token
        },
        success: (res) => {
          console.log('图片上传响应:', res);
          try {
            // 服务器返回的数据是字符串，需要转换为对象
            const data = JSON.parse(res.data);
            if (data.code === 200) {
              // 返回服务器文件路径
              //console.log('图片上传成功, 服务器路径:', data.data);
              resolve(data.data);
            } else {
              //console.error('图片上传失败, 错误信息:', data.message);
              reject(new Error(data.message || '上传图片失败'));
            }
          } catch (e) {
            //console.error('解析上传响应失败:', e, '原始响应:', res.data);
            reject(new Error('解析上传响应失败'));
          }
        },
        fail: (err) => {
          console.error('图片上传请求失败:', err);
          reject(new Error('上传图片失败: ' + JSON.stringify(err)));
        }
      });
    });
  },

  /**
   * 上传视频到服务器
   * @param {String} filePath - 本地视频路径
   * @returns {Promise} - 返回上传结果，包含服务器文件路径
   */
  uploadVideo(filePath) {
    return new Promise((resolve, reject) => {
      console.log('开始上传视频:', filePath);
      console.log('上传URL:', UPLOAD_VIDEO_URL);
      
      // 获取token并添加Bearer前缀
      let token = getToken();
      if (token && !token.startsWith('Bearer ')) {
        token = 'Bearer ' + token;
      }
      
      uni.uploadFile({
        url: UPLOAD_VIDEO_URL,
        filePath: filePath,
        name: 'file',
        header: {
          'Authorization': token
        },
        success: (res) => {
          console.log('视频上传响应:', res);
          try {
            const data = JSON.parse(res.data);
            if (data.code === 200) {
              // 返回服务器文件路径
              console.log('视频上传成功, 服务器路径:', data.data);
              resolve(data.data);
            } else {
              console.error('视频上传失败, 错误信息:', data.message);
              reject(new Error(data.message || '上传视频失败'));
            }
          } catch (e) {
            console.error('解析上传响应失败:', e, '原始响应:', res.data);
            reject(new Error('解析上传响应失败'));
          }
        },
        fail: (err) => {
          console.error('视频上传请求失败:', err);
          reject(new Error('上传视频失败: ' + JSON.stringify(err)));
        }
      });
    });
  },

  /**
   * 获取完整的文件访问URL
   * @param {String} filePath - 服务器返回的文件路径
   * @returns {String} - 完整的文件访问URL
   */
  getFileUrl(filePath) {
    if (!filePath) return '';
    filePath='/uploads'+filePath;
    console.log('原始文件路径:', filePath);
    
    // 处理blob URL（本地预览URL）
    if (filePath.startsWith('blob:')) {
     // console.log('Blob URL，直接返回:', filePath);
      return filePath;
    }
    
    // 如果已经是完整URL，直接返回
    if (filePath.startsWith('http')) {
    //  console.log('已是完整URL，直接返回:', filePath);
      return filePath;
    }
    
    // 视频路径特殊处理 - 修复可能的中文编码问题
    if (filePath.includes('/uploads/videos/') || filePath.endsWith('.mp4')) {
      // 确保路径使用正确的编码
      let pathParts = filePath.split('/');
      let encodedParts = pathParts.map(part => {
        // 只对文件名进行URL编码，避免对路径分隔符编码
        if (part.includes('.mp4')) {
          return encodeURIComponent(part);
        }
        return part;
      });
      let encodedPath = encodedParts.join('/');
      
      // 构建完整URL
      let fullUrl;
      if (encodedPath.startsWith('/')) {
        fullUrl = BASE_URL + encodedPath;
      } else {
        fullUrl = BASE_URL + '/' + encodedPath;
      }
      
     // console.log('视频完整URL(带编码):', fullUrl);
      return fullUrl;
    }
    
    // 处理相对路径，构建完整URL
    // 注意：后端返回的路径可能是 /uploads/xxx 或 uploads/xxx
    let fullUrl;
    if (filePath.startsWith('/')) {
      // 以/开头的路径，直接拼接
      fullUrl = BASE_URL + filePath;
    } else {
      // 不以/开头的路径，添加/后拼接
      fullUrl = BASE_URL + '/' + filePath;
    }
    
   // console.log('生成的完整URL:', fullUrl);
    return fullUrl;
  }
} 