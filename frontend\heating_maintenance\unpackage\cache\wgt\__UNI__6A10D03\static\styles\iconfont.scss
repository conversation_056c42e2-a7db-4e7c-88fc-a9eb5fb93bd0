@font-face {
	font-family: "iconfont";
	src: url('../fonts/iconfont.ttf') format('truetype');
}

.iconfont {
	font-family: "iconfont" !important;
	font-size: 16px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.icon-search:before {
	content: "\e7b7";
}

.icon-clear:before {
	content: "\e62b";
}

.icon-filter:before {
	content: "\e650";
}

.icon-alarm:before {
	content: "\e6c4";
}

.icon-control:before {
	content: "\e8d9";
}

.icon-detail:before {
	content: "\e633";
}

.icon-camera:before {
	content: "\e626";
}

.icon-task:before {
	content: "\e64d";
}

.icon-patrol:before {
	content: "\e624";
}

.icon-calendar:before {
	content: "\e621";
}

.icon-report:before {
	content: "\e6a5";
}

.icon-user:before {
	content: "\e630";
}

.icon-certificate:before {
	content: "\e649";
}

.icon-notification:before {
	content: "\e60b";
}

.icon-lock:before {
	content: "\e60e";
}

.icon-phone:before {
	content: "\e634";
}

.icon-log:before {
	content: "\e623";
}

.icon-help:before {
	content: "\e691";
}

.icon-feedback:before {
	content: "\e61b";
}

.icon-info:before {
	content: "\e622";
}

.icon-arrow-right:before {
	content: "\e61c";
}

.icon-arrow-left:before {
	content: "\e60c";
}

.icon-arrow-down:before {
	content: "\e60d";
}

.icon-add:before {
	content: "\e712";
}

.icon-temperature:before {
	content: "\e61a";
}

.icon-location:before {
	content: "\e619";
}

.icon-right:before {
	content: "\e61c";
}

.icon-check:before {
	content: "\e645";
} 