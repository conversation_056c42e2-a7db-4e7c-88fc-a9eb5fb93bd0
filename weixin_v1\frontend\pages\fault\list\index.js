Page({
  data: {
    selectedDate: '',
    currentStatus: '',
    faults: [],
    originalFaults: []
  },

  onLoad() {
    this.loadFaultList();
  },

  loadFaultList(params = {}) {
    wx.showLoading({ title: '加载中' });
    
    const queryString = Object.keys(params)
      .map(key => `${key}=${params[key]}`)
      .join('&');
    const url = `http://localhost:5000/api/fault/list${queryString ? '?' + queryString : ''}`;

    wx.request({
      url,
      method: 'GET',
      success: (res) => {
        if (res.data.success) {
          this.setData({
            faults: res.data.data,
            originalFaults: res.data.data
          });
        } else {
          wx.showToast({
            title: res.data.message || '加载失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      },
      complete: () => {
        wx.hideLoading();
        wx.stopPullDownRefresh();
      }
    });
  },

  onDateChange(e) {
    const selectedDate = e.detail.value;
    this.setData({ selectedDate });
    
    this.loadFaultList({ date: selectedDate });
  },

  filterByStatus(e) {
    const status = e.currentTarget.dataset.status;
    this.setData({ currentStatus: status });

    const params = {};
    if (status) params.status = status;
    if (this.data.selectedDate) params.date = this.data.selectedDate;

    this.loadFaultList(params);
  },

  onPullDownRefresh() {
    const params = {};
    if (this.data.currentStatus) params.status = this.data.currentStatus;
    if (this.data.selectedDate) params.date = this.data.selectedDate;
    
    this.loadFaultList(params);
  },

  goToDetail(e) {
    const faultId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/fault/detail/index?id=${faultId}`
    });
  }
}); 