import { guardPageAccess } from './auth.js';

/**
 * 页面权限混入
 * 在页面组件中使用：
 * 1. 导入混入：import permissionMixin from '@/utils/permission-mixin.js';
 * 2. 在组件配置中混入：mixins: [permissionMixin]
 * 3. 在组件 data 中设置权限代码：permissionCode: 'your:permission:code'
 * 
 * 如果用户无权访问，会自动重定向并阻止页面加载
 */
export default {
  data() {
    return {
      // 默认允许访问
      permissionCode: '', 
      // 控制内容是否显示，用于避免权限检查过程中内容闪烁
      canShowContent: false
    };
  },
  
  onLoad() {
    // 检查是否设置了权限编码
    if (this.permissionCode) {
      // 检查权限
      if (!guardPageAccess(this.permissionCode)) {
        return; // 无权限访问，已重定向，停止加载
      }
    }
    
    // 有权限或无需权限，设置内容可见
    this.canShowContent = true;
    
    // 如果存在原始onLoad方法，调用它
    if (this.$options.originalOnLoad) {
      this.$options.originalOnLoad.call(this);
    }
  },
  
  // 创建前保存原始onLoad方法
  beforeCreate() {
    // 保存原始的onLoad方法，以便在权限检查后调用
    if (this.$options.onLoad && this.$options.onLoad !== this.$options.mixins[0].onLoad) {
      this.$options.originalOnLoad = this.$options.onLoad;
    }
  }
}; 