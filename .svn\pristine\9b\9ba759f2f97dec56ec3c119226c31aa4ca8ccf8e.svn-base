import { hasPermission } from './auth.js';

/**
 * 权限指令 - 用于控制元素的显示/隐藏
 * 使用方法：
 * 1. 在 main.js 中引入并注册：
 *    import permissionDirective from './utils/permission-directive';
 *    Vue.use(permissionDirective);
 * 
 * 2. 在模板中使用：
 *    <button v-permission="'user:add'">添加用户</button>
 *    <div v-permission="['user:edit', 'user:delete']">需要多个权限之一</div>
 */
export default {
  install(Vue) {
    Vue.directive('permission', {
      inserted(el, binding) {
        const { value } = binding;
        let hasAuth = false;

        if (typeof value === 'string') {
          // 单个权限
          hasAuth = hasPermission(value);
        } else if (Array.isArray(value)) {
          // 多个权限（满足其中一个即可）
          hasAuth = value.some(permission => hasPermission(permission));
        }

        if (!hasAuth) {
          // 如果没有权限，从DOM中移除元素
          if (el.parentNode) {
            el.parentNode.removeChild(el);
          }
        }
      }
    });

    // 添加全局方法
    Vue.prototype.$hasPermission = hasPermission;
  }
}; 