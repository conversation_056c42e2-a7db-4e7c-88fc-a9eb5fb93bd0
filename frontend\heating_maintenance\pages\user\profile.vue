<template>
	<view class="profile-container">
		<view class="form-card">
			<view class="form-item">
				<text class="form-label">姓名</text>
				<view class="form-input">
					<input type="text" v-model="userInfo.name" placeholder="请输入姓名" />
				</view>
			</view>
			<!-- <view class="form-item">
				<text class="form-label">工号</text>
				<view class="form-input">
					<text class="input-readonly">{{ userInfo.id }}</text>
				</view>
			</view> -->
			<!-- <view class="form-item">
				<text class="form-label">账号</text>
				<view class="form-input">
					<text class="input-readonly">{{userInfo.username}}</text>
				</view>
			</view> -->
			
			<view class="form-item">
				<text class="form-label">手机号码</text>
				<view class="form-input">
					<input type="number" v-model="userInfo.phone" placeholder="请输入手机号码" maxlength="11" />
				</view>
			</view>
			
			<view class="form-item">
				<text class="form-label">邮箱</text>
				<view class="form-input">
					<input type="text" v-model="userInfo.email" placeholder="请输入邮箱" />
				</view>
			</view>
		
			
			<view class="form-item">
				<text class="form-label">部门</text>
				<view class="form-input">
					<text class="input-readonly">{{ userInfo.department }}</text>
				</view>
			</view>
		</view>
		
		<view class="submit-btn" @click="saveProfile">保存修改</view>
	</view>
</template>

<script>
import { userApi } from '../../utils/api';

export default {
	data() {
		return {
			userInfo: {
				name: '',
				id: '',
				phone: '',
				email: '',
				role: '',
				department: ''
			}
		}
	},
	onLoad() {
		// 初始加载用户信息
		this.loadUserInfo();
	},
	onShow() {
		// 每次显示页面时都重新加载用户信息，确保数据最新
		this.loadUserInfo();
	},
	methods: {
		// 加载用户信息
		loadUserInfo() {
			// 从本地存储获取用户信息
			const userInfo = uni.getStorageSync('userInfo');
			const userId = uni.getStorageSync('userId');
			const userRole = uni.getStorageSync('userRole');
			
			if (userInfo) {
				console.log('本地存储的用户信息:', userInfo);
				
				// 更新数据 - 检查是否有user字段
				const userData = userInfo.user;
				this.userInfo = {
					name: userData.name,
					phone: userData.phone || '',
					email: userData.email || '',
					role: this.getRoleName(userRole) || '普通用户',
					department: userData.department || '未分配'
				};
			} else {
				// 如果本地没有用户信息，从服务器获取
				this.fetchUserInfoFromServer();
			}
		},
		
		// 从服务器获取用户信息
		fetchUserInfoFromServer() {
			uni.showLoading({
				title: '加载中...'
			});
			
			userApi.getUserInfo()
				.then(res => {
					uni.hideLoading();
					if (res.code === 200) {
						// 存储用户信息
						uni.setStorageSync('userInfo', res.data);
						
						// 更新数据 - 检查是否有user字段
						const userData = res.data.user || res.data;
						
						this.userInfo = {
							name: userData.name || userData.username || '用户',
							id: userData.id || res.data.userId || 'USER-' + Date.now(),
							phone: userData.phone || '',
							email: userData.email || '',
							role: this.getRoleName(userData.role || res.data.role) || '普通用户',
							department: userData.department || '未分配'
						};
					} else {
						this.showError('获取用户信息失败');
					}
				})
				.catch(err => {
					uni.hideLoading();
					console.error('获取用户信息失败:', err);
					this.showError('网络错误，请稍后重试');
				});
		},
		
		// 角色名称转换
		getRoleName(role) {
			const roleMap = {
				'admin': '系统管理员',
				'manager': '主管',
				'engineer': '维修工程师',
				'operator': '操作员',
				'user': '普通用户'
			};
			
			return roleMap[role] || role || '普通用户';
		},
		
		// 保存用户资料
		saveProfile() {
			uni.showLoading({
				title: '保存中...'
			});
			
			// 验证输入
			if (!this.userInfo.name || this.userInfo.name.trim() === '') {
				uni.hideLoading();
				this.showError('姓名不能为空');
				return;
			}
			
			if (this.userInfo.phone && !/^1\d{10}$/.test(this.userInfo.phone)) {
				uni.hideLoading();
				this.showError('请输入正确的手机号码');
				return;
			}
			
			if (this.userInfo.email && !/^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/.test(this.userInfo.email)) {
				uni.hideLoading();
				this.showError('请输入正确的邮箱地址');
				return;
			}
			
			// 准备更新的数据
			const updateData = {
				name: this.userInfo.name,
				phone: this.userInfo.phone,
				email: this.userInfo.email
			};
			
			// 调用API保存用户信息
			userApi.updateUserInfo(updateData)
				.then(res => {
					uni.hideLoading();
					
					if (res.code === 200) {
						// 更新本地存储
						const userInfo = uni.getStorageSync('userInfo') || {};
						// 检查是否有user字段
						if (userInfo.user) {
							userInfo.user.name = this.userInfo.name;
							userInfo.user.phone = this.userInfo.phone;
							userInfo.user.email = this.userInfo.email;
						} else {
							userInfo.name = this.userInfo.name;
							userInfo.phone = this.userInfo.phone;
							userInfo.email = this.userInfo.email;
						}
						uni.setStorageSync('userInfo', userInfo);
						
						uni.showToast({
							title: '保存成功',
							icon: 'success'
						});
						
						// 返回上一页
						setTimeout(() => {
							uni.navigateBack();
						}, 1500);
					} else {
						this.showError(res.message || '保存失败');
					}
				})
				.catch(err => {
					uni.hideLoading();
					console.error('保存用户资料失败:', err);
					this.showError('网络错误，请稍后重试');
				});
		},
		
		// 显示错误提示
		showError(message) {
			uni.showToast({
				title: message,
				icon: 'none'
			});
		}
	}
}
</script>

<style lang="scss">
.profile-container {
	background-color: #f5f7fa;
	min-height: 100vh;
	padding-bottom: 40rpx;
}

.form-card {
	margin: 30rpx;
	background-color: #fff;
	border-radius: 12rpx;
	padding: 20rpx;
	box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.05);
	
	.form-item {
		padding: 24rpx 20rpx;
		border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
		
		&:last-child {
			border-bottom: none;
		}
		
		.form-label {
			font-size: 28rpx;
			color: #666;
			margin-bottom: 16rpx;
			display: block;
		}
		
		.form-input {
			input {
				height: 80rpx;
				font-size: 32rpx;
				color: #333;
				width: 100%;
			}
			
			.input-readonly {
				height: 80rpx;
				line-height: 80rpx;
				font-size: 32rpx;
				color: #999;
				display: block;
			}
		}
	}
}

.submit-btn {
	margin: 60rpx 30rpx;
	height: 90rpx;
	line-height: 90rpx;
	background-color: $uni-color-primary;
	color: #fff;
	text-align: center;
	border-radius: 12rpx;
	font-size: 32rpx;
	box-shadow: 0 6rpx 20rpx rgba(24, 144, 255, 0.3);
	font-weight: bold;
	
	&:active {
		opacity: 0.9;
		transform: translateY(2rpx);
	}
}
</style> 