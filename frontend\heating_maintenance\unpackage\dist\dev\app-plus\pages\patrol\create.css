/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-popup[data-v-7db519c7] {
  position: fixed;
  z-index: 99;
}
.uni-popup.top[data-v-7db519c7], .uni-popup.left[data-v-7db519c7], .uni-popup.right[data-v-7db519c7] {
  top: 0;
}
.uni-popup .uni-popup__wrapper[data-v-7db519c7] {
  display: block;
  position: relative;
  /* iphonex 等安全区设置，底部安全区适配 */
}
.uni-popup .uni-popup__wrapper.left[data-v-7db519c7], .uni-popup .uni-popup__wrapper.right[data-v-7db519c7] {
  padding-top: 0;
  flex: 1;
}
.fixforpc-z-index[data-v-7db519c7] {
  z-index: 999;
}
.fixforpc-top[data-v-7db519c7] {
  top: 0;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.patrol-create-container[data-v-778b6e1a] {
  background-color: #f8f8f8;
  min-height: 100vh;
  padding-bottom: 4.375rem;
}
.patrol-create-container .form-card[data-v-778b6e1a] {
  background-color: #fff;
  margin: 0.625rem;
  padding: 0 0.9375rem 0.9375rem;
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.375rem rgba(0, 0, 0, 0.05);
}
.patrol-create-container .form-card .card-title[data-v-778b6e1a] {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
  padding: 0.9375rem 0;
  border-bottom: 0.03125rem solid #e5e5e5;
  margin-bottom: 0.625rem;
}
.patrol-create-container .form-card .card-header[data-v-778b6e1a] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.9375rem 0;
  border-bottom: 0.03125rem solid #e5e5e5;
  margin-bottom: 0.625rem;
}
.patrol-create-container .form-card .card-header .card-title[data-v-778b6e1a] {
  padding: 0;
  border-bottom: none;
  margin-bottom: 0;
}
.patrol-create-container .form-card .card-header .add-button[data-v-778b6e1a] {
  display: flex;
  align-items: center;
  color: #007aff;
  font-size: 0.875rem;
}
.patrol-create-container .form-card .card-header .add-button.disabled[data-v-778b6e1a] {
  color: #999;
  pointer-events: none;
}
.patrol-create-container .form-card .card-header .add-button .iconfont[data-v-778b6e1a] {
  font-size: 1.125rem;
  margin-right: 0.25rem;
}
.patrol-create-container .form-item[data-v-778b6e1a] {
  display: flex;
  flex-direction: column;
  padding: 0.625rem 0;
  border-bottom: 0.03125rem solid #f0f0f0;
}
.patrol-create-container .form-item[data-v-778b6e1a]:last-child {
  border-bottom: none;
}
.patrol-create-container .form-item .form-label[data-v-778b6e1a] {
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 0.5rem;
}
.patrol-create-container .form-item .form-label.required[data-v-778b6e1a]::before {
  content: "*";
  color: red;
  margin-right: 0.125rem;
}
.patrol-create-container .form-item .form-input[data-v-778b6e1a], .patrol-create-container .form-item .form-picker[data-v-778b6e1a], .patrol-create-container .form-item .form-textarea[data-v-778b6e1a], .patrol-create-container .form-item .location-input[data-v-778b6e1a] {
  font-size: 0.9375rem;
  color: #333;
  padding: 0.625rem 0;
  min-height: 1.5625rem;
  display: flex;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}
.patrol-create-container .form-item .form-input[data-v-778b6e1a]::-webkit-input-placeholder, .patrol-create-container .form-item .form-picker[data-v-778b6e1a]::-webkit-input-placeholder, .patrol-create-container .form-item .form-textarea[data-v-778b6e1a]::-webkit-input-placeholder, .patrol-create-container .form-item .location-input[data-v-778b6e1a]::-webkit-input-placeholder {
  color: #999;
}
.patrol-create-container .form-item .form-input[data-v-778b6e1a]::placeholder, .patrol-create-container .form-item .form-picker[data-v-778b6e1a]::placeholder, .patrol-create-container .form-item .form-textarea[data-v-778b6e1a]::placeholder, .patrol-create-container .form-item .location-input[data-v-778b6e1a]::placeholder {
  color: #999;
}
.patrol-create-container .form-item uni-input.form-input[data-v-778b6e1a] {
  padding: 0.625rem 0;
}
.patrol-create-container .form-item .plan-name-input[data-v-778b6e1a] {
  width: 100%;
  text-align: left;
  display: block;
  line-height: normal;
  height: auto;
  min-height: 1.5625rem;
  white-space: normal;
  word-break: break-all;
}
.patrol-create-container .form-item uni-textarea.form-textarea[data-v-778b6e1a] {
  padding: 0.625rem 0;
  min-height: 4.6875rem;
  line-height: 1.5;
}
.patrol-create-container .form-item .form-picker .picker-value[data-v-778b6e1a] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.patrol-create-container .form-item .form-picker .picker-value.placeholder-style[data-v-778b6e1a] {
  color: #999;
  padding-left: 0 !important;
}
.patrol-create-container .form-item .location-input[data-v-778b6e1a] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.patrol-create-container .form-item .arrow-down[data-v-778b6e1a] {
  font-size: 0.75rem;
  color: #666;
  margin-left: 0.3125rem;
}
.patrol-create-container .form-item .picker-flex[data-v-778b6e1a] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  color: #999;
}
.patrol-create-container .form-item .picker-flex > uni-text[data-v-778b6e1a]:first-child {
  flex: 1;
  color: #999;
}
.patrol-create-container .form-item .picker-flex.has-value > uni-text[data-v-778b6e1a]:first-child {
  color: #333;
}
.patrol-create-container .form-item .device-selector .location-input[data-v-778b6e1a] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.625rem;
  background-color: #fff;
  border-radius: 0.25rem;
}
.patrol-create-container .form-item .device-selector .location-input uni-text[data-v-778b6e1a] {
  color: #333;
}
.patrol-create-container .form-item .device-selector .location-input uni-text[data-v-778b6e1a]:last-child {
  color: #666;
}
.patrol-create-container .form-item .heatunit-selector .location-input[data-v-778b6e1a] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.625rem;
  background-color: #fff;
  border-radius: 0.25rem;
}
.patrol-create-container .form-item .heatunit-selector .location-input uni-text[data-v-778b6e1a] {
  color: #333;
}
.patrol-create-container .form-item .heatunit-selector .location-input uni-text[data-v-778b6e1a]:last-child {
  color: #666;
}
.patrol-create-container .form-item .staff-selector .location-input[data-v-778b6e1a] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.625rem;
  background-color: #fff;
  border-radius: 0.25rem;
}
.patrol-create-container .form-item .staff-selector .location-input uni-text[data-v-778b6e1a] {
  color: #333;
}
.patrol-create-container .form-item .staff-selector .location-input uni-text[data-v-778b6e1a]:last-child {
  color: #666;
}
.patrol-create-container .selected-items[data-v-778b6e1a] {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}
.patrol-create-container .selected-items .selected-item[data-v-778b6e1a] {
  background-color: #e6f7ff;
  color: #007aff;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.8125rem;
  display: flex;
  align-items: center;
}
.patrol-create-container .selected-items .selected-item .item-text[data-v-778b6e1a] {
  margin-right: 0.25rem;
}
.patrol-create-container .selected-items .selected-item .remove-icon[data-v-778b6e1a] {
  font-size: 0.875rem;
  font-weight: bold;
  color: #007aff;
  cursor: pointer;
}
.patrol-create-container .tag-group[data-v-778b6e1a] {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.3125rem;
}
.patrol-create-container .tag-group .tag[data-v-778b6e1a] {
  padding: 0.3125rem 0.75rem;
  border: 0.03125rem solid #e5e5e5;
  border-radius: 0.25rem;
  font-size: 0.8125rem;
  color: #666;
  background-color: #fff;
}
.patrol-create-container .tag-group .tag.active[data-v-778b6e1a] {
  background-color: #007aff;
  color: #fff;
  border-color: #007aff;
}
.patrol-create-container .task-list .task-item[data-v-778b6e1a] {
  background-color: #fff;
  border: 0.03125rem solid #e5e5e5;
  border-radius: 0.375rem;
  padding: 0.75rem;
  margin-bottom: 0.625rem;
  box-shadow: 0 0.0625rem 0.1875rem rgba(0, 0, 0, 0.03);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.patrol-create-container .task-list .task-item .task-content[data-v-778b6e1a] {
  flex: 1;
}
.patrol-create-container .task-list .task-item .task-content .task-title[data-v-778b6e1a] {
  font-size: 0.9375rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.375rem;
}
.patrol-create-container .task-list .task-item .task-content .task-info-row[data-v-778b6e1a] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.1875rem;
}
.patrol-create-container .task-list .task-item .task-content .task-info[data-v-778b6e1a] {
  font-size: 0.75rem;
  color: #666;
  display: block;
  line-height: 1.5;
  margin-bottom: 0.1875rem;
}
.patrol-create-container .task-list .task-item .task-content .task-info[data-v-778b6e1a]:last-child {
  margin-bottom: 0;
}
.patrol-create-container .task-list .task-item .task-actions .delete-icon[data-v-778b6e1a] {
  font-size: 1.125rem;
  color: #ff5252;
  padding-left: 0.625rem;
}
.patrol-create-container .empty-tip[data-v-778b6e1a] {
  text-align: center;
  padding: 1.25rem 0;
  color: #999;
  font-size: 0.875rem;
}
.patrol-create-container .submit-button[data-v-778b6e1a] {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #007aff;
  color: #fff;
  font-size: 1rem;
  font-weight: bold;
  height: 3.0625rem;
  line-height: 3.0625rem;
  text-align: center;
  border-radius: 0;
  margin: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  box-sizing: border-box;
  z-index: 1;
}
.selector[data-v-778b6e1a] {
  background-color: #fff;
  border-radius: 0.625rem 0.625rem 0 0;
  display: flex;
  flex-direction: column;
  max-height: 80vh;
  overflow: hidden;
  /* 巡检项选择器样式已移至task-selector中 */
}
.selector .selector-header[data-v-778b6e1a] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.9375rem;
  border-bottom: 0.03125rem solid #e5e5e5;
}
.selector .selector-header .selector-title[data-v-778b6e1a] {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
}
.selector .selector-header .header-actions[data-v-778b6e1a] {
  display: flex;
  align-items: center;
}
.selector .selector-header .close-button[data-v-778b6e1a] {
  font-size: 0.875rem;
  color: #666;
}
.selector .selector-header .confirm-button-header[data-v-778b6e1a] {
  font-size: 0.875rem;
  color: #007aff;
  font-weight: 500;
  margin-right: 0.625rem;
}
.selector .selector-content[data-v-778b6e1a] {
  padding: 0 0.9375rem 0.9375rem;
  flex: 1;
  overflow-y: auto;
  /* 巡检项模板列表样式已移至task-selector中 */
}
.selector .selector-content .template-search[data-v-778b6e1a] {
  display: flex;
  align-items: center;
  padding: 0.625rem 0;
}
.selector .selector-content .template-search .search-input[data-v-778b6e1a] {
  flex: 1;
  height: 2.25rem;
  background-color: #f8f8f8;
  border-radius: 1.125rem;
  padding: 0 0.9375rem;
  font-size: 0.875rem;
}
.selector .selector-content .template-search .search-btn[data-v-778b6e1a] {
  font-size: 0.875rem;
  color: #007aff;
}
.selector .selector-content .selector-content-footer[data-v-778b6e1a] {
  padding: 0.46875rem 0;
  border-top: 0.03125rem solid #e5e5e5;
  margin-top: 0.625rem;
  background-color: #f8f8f8;
  box-shadow: 0 -0.125rem 0.3125rem rgba(0, 0, 0, 0.1);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
}
.selector .selector-content .selector-content-footer .select-count-container[data-v-778b6e1a] {
  font-size: 0.875rem;
  color: #333;
  font-weight: bold;
  text-align: center;
  padding: 0.46875rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.selector .selector-content .selector-content-footer .select-count-container .select-count-badge[data-v-778b6e1a] {
  width: 1.5rem;
  height: 1.5rem;
  background-color: #007aff;
  border-radius: 50%;
  margin-right: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.selector .selector-content .selector-content-footer .select-count-container .select-count-badge .count-number[data-v-778b6e1a] {
  color: #fff;
  font-size: 0.875rem;
  font-weight: bold;
}
.selector .selector-content .selector-content-footer .select-count-container .count-text[data-v-778b6e1a] {
  color: #333;
  font-size: 0.9375rem;
}
.selector .selector-content .device-filter[data-v-778b6e1a] {
  margin-bottom: 0.625rem;
}
.selector .selector-content .device-filter .filter-title[data-v-778b6e1a] {
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 0.3125rem;
}
.selector .selector-content .device-filter .form-picker[data-v-778b6e1a] {
  border: 0.03125rem solid #e5e5e5;
  border-radius: 0.25rem;
  padding: 0.46875rem 0.625rem;
}
.selector .selector-content .device-filter .form-picker .picker-value[data-v-778b6e1a] {
  font-size: 0.875rem;
}
.selector .selector-content .heatunit-list .heatunit-item[data-v-778b6e1a] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.78125rem 0;
  border-bottom: 0.03125rem solid #f0f0f0;
}
.selector .selector-content .heatunit-list .heatunit-item[data-v-778b6e1a]:last-child {
  border-bottom: none;
}
.selector .selector-content .heatunit-list .heatunit-item .heatunit-name[data-v-778b6e1a] {
  font-size: 0.875rem;
  color: #333;
}
.selector .selector-content .heatunit-list .heatunit-item .checkbox[data-v-778b6e1a] {
  width: 1.25rem;
  height: 1.25rem;
  border: 0.03125rem solid #e5e5e5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.selector .selector-content .heatunit-list .heatunit-item .checkbox.checked[data-v-778b6e1a] {
  background-color: #007aff;
  border-color: #007aff;
}
.selector .selector-content .heatunit-list .heatunit-item .checkbox.checked .iconfont[data-v-778b6e1a] {
  color: #fff;
  font-size: 0.75rem;
}
.selector .selector-footer[data-v-778b6e1a] {
  padding: 0.78125rem 0.9375rem 1.25rem;
  background-color: #fff;
  border-top: 0.03125rem solid #e5e5e5;
  box-shadow: 0 -0.125rem 0.5rem rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: calc(1.25rem + constant(safe-area-inset-bottom));
  padding-bottom: calc(1.25rem + env(safe-area-inset-bottom));
  background-color: #fff;
  /* 确保背景色不是透明的 */
  position: relative;
  /* 确保显示在内容之上 */
  z-index: 1;
}
.selector .selector-footer .select-count[data-v-778b6e1a] {
  font-size: 0.875rem;
  color: #333;
}
.selector .selector-footer .confirm-button[data-v-778b6e1a] {
  background-color: #007aff;
  color: #fff;
  padding: 0.46875rem 1.25rem;
  border-radius: 0.9375rem;
  font-size: 0.875rem;
}
.selector .heat-unit-selector[data-v-778b6e1a] {
  z-index: 100;
  background-color: #fff;
  border-radius: 0.625rem 0.625rem 0 0;
  max-height: 80vh;
  height: 80vh;
  display: flex;
  flex-direction: column;
}
.selector .heat-unit-selector .selector-content[data-v-778b6e1a] {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}
.placeholder-style[data-v-778b6e1a] {
  color: #999 !important;
  padding-left: 0 !important;
}
.iconfont[data-v-778b6e1a] {
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.icon-add[data-v-778b6e1a]:before {
  content: "+";
}
.icon-check[data-v-778b6e1a]:before {
  content: "✓";
}
.icon-clear[data-v-778b6e1a]:before {
  content: "×";
}
.importance-tag[data-v-778b6e1a] {
  padding: 0.125rem 0.25rem;
  border-radius: 0.125rem;
  font-size: 0.6875rem;
}
.importance-tag.normal-importance[data-v-778b6e1a] {
  background-color: #e6f7ff;
  color: #1890ff;
  border: 0.03125rem solid #91d5ff;
}
.importance-tag.important-importance[data-v-778b6e1a] {
  background-color: #fff7e6;
  color: #fa8c16;
  border: 0.03125rem solid #ffd591;
}
.importance-tag.critical-importance[data-v-778b6e1a] {
  background-color: #fff1f0;
  color: #f5222d;
  border: 0.03125rem solid #ffa39e;
}
.task-info-row[data-v-778b6e1a] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.1875rem;
}
.template-header[data-v-778b6e1a] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.375rem;
}
.template-header .template-title[data-v-778b6e1a] {
  font-size: 0.9375rem;
  font-weight: bold;
  color: #333;
  word-break: break-all;
  margin-right: 0.46875rem;
  line-height: 1.4;
}
.template-header .importance-tag[data-v-778b6e1a] {
  padding: 0.125rem 0.25rem;
  border-radius: 0.125rem;
  font-size: 0.6875rem;
}
.template-header .importance-tag.normal-importance[data-v-778b6e1a] {
  background-color: #e6f7ff;
  color: #1890ff;
  border: 0.03125rem solid #91d5ff;
}
.template-header .importance-tag.important-importance[data-v-778b6e1a] {
  background-color: #fff7e6;
  color: #fa8c16;
  border: 0.03125rem solid #ffd591;
}
.template-header .importance-tag.critical-importance[data-v-778b6e1a] {
  background-color: #fff1f0;
  color: #f5222d;
  border: 0.03125rem solid #ffa39e;
}
.task-selector[data-v-778b6e1a] {
  background-color: #fff;
  border-radius: 0.625rem 0.625rem 0 0;
  display: flex;
  flex-direction: column;
  max-height: 80vh;
  height: 80vh;
  overflow: hidden;
  z-index: 100;
}
.task-selector .selector-header[data-v-778b6e1a] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.9375rem;
  border-bottom: 0.03125rem solid #e5e5e5;
}
.task-selector .selector-header .selector-title[data-v-778b6e1a] {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
}
.task-selector .selector-header .header-actions[data-v-778b6e1a] {
  display: flex;
  align-items: center;
}
.task-selector .selector-header .close-button[data-v-778b6e1a] {
  font-size: 0.875rem;
  color: #666;
}
.task-selector .selector-header .confirm-button-header[data-v-778b6e1a] {
  font-size: 0.875rem;
  color: #007aff;
  font-weight: 500;
  margin-right: 0.625rem;
}
.task-selector .selector-content[data-v-778b6e1a] {
  flex: 1;
  overflow-y: auto;
  padding: 0 0.625rem;
  -webkit-overflow-scrolling: touch;
}
.task-selector .selector-content .device-filter[data-v-778b6e1a] {
  display: flex;
  align-items: center;
  padding: 0 0.3125rem 0.625rem;
  margin-bottom: 0;
  position: relative;
}
.task-selector .selector-content .device-filter .filter-picker[data-v-778b6e1a] {
  flex: 1;
  height: 2.5rem;
}
.task-selector .selector-content .device-filter .filter-picker .picker-value[data-v-778b6e1a] {
  background: #f5f5f5;
  border-radius: 1.25rem;
  padding: 0 0.9375rem;
  height: 2.5rem;
  font-size: 0.875rem;
  color: #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.task-selector .selector-content .device-filter .filter-picker .picker-value .arrow-down[data-v-778b6e1a] {
  color: #999;
  font-size: 0.75rem;
}
.task-selector .selector-content .template-search[data-v-778b6e1a] {
  display: flex;
  align-items: center;
  padding: 0 0.3125rem 0.625rem;
  border-bottom: 0.03125rem solid #f0f0f0;
  margin-bottom: 0.625rem;
  position: relative;
}
.task-selector .selector-content .template-search .search-input[data-v-778b6e1a] {
  flex: 1;
  height: 2.5rem;
  border-radius: 1.25rem;
  background: #f5f5f5;
  font-size: 0.875rem;
  padding: 0 2.5rem 0 0.9375rem;
  color: #333;
}
.task-selector .selector-content .template-search .search-clear[data-v-778b6e1a] {
  position: absolute;
  right: 4.0625rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1.25rem;
  height: 1.25rem;
  line-height: 1.25rem;
  text-align: center;
  border-radius: 50%;
  background: #ccc;
  color: #fff;
  font-size: 0.75rem;
}
.task-selector .selector-content .template-search .search-btn[data-v-778b6e1a] {
  padding: 0 0.9375rem;
  font-size: 0.875rem;
  color: #007aff;
  height: 2.5rem;
  line-height: 2.5rem;
}
.task-selector .selector-content .task-templates[data-v-778b6e1a] {
  padding-bottom: 0.625rem;
}
.task-selector .selector-content .task-templates .template-item[data-v-778b6e1a] {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background-color: #fff;
  border: 0.03125rem solid #e5e5e5;
  border-radius: 0.375rem;
  padding: 0.75rem;
  margin-bottom: 0.625rem;
  box-shadow: 0 0.0625rem 0.1875rem rgba(0, 0, 0, 0.03);
  transition: all 0.2s ease-in-out;
}
.task-selector .selector-content .task-templates .template-item.active[data-v-778b6e1a] {
  background-color: #e9f5ff;
  border-color: #007aff;
  box-shadow: 0 0.125rem 0.375rem rgba(0, 122, 255, 0.1);
}
.task-selector .selector-content .task-templates .template-item .checkbox[data-v-778b6e1a] {
  width: 1.25rem;
  height: 1.25rem;
  min-width: 1.25rem;
  border: 0.03125rem solid #e5e5e5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.46875rem;
  align-self: flex-start;
  margin-top: 0.125rem;
}
.task-selector .selector-content .task-templates .template-item .checkbox.checked[data-v-778b6e1a] {
  background-color: #007aff;
  border-color: #007aff;
}
.task-selector .selector-content .task-templates .template-item .checkbox.checked .iconfont[data-v-778b6e1a] {
  color: #fff;
  font-size: 0.75rem;
}
.task-selector .selector-content .task-templates .template-item .template-left[data-v-778b6e1a] {
  flex: 1;
  overflow: hidden;
}
.task-selector .selector-content .task-templates .template-item .template-left .template-header[data-v-778b6e1a] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.375rem;
}
.task-selector .selector-content .task-templates .template-item .template-left .template-header .template-title[data-v-778b6e1a] {
  font-size: 0.9375rem;
  font-weight: bold;
  color: #333;
  word-break: break-all;
  margin-right: 0.46875rem;
  line-height: 1.4;
}
.task-selector .selector-content .task-templates .template-item .template-left .template-info[data-v-778b6e1a] {
  font-size: 0.75rem;
  color: #666;
  display: block;
  line-height: 1.5;
  margin-bottom: 0.1875rem;
  word-break: break-all;
}
.task-selector .selector-content .task-templates .template-item .template-left .template-info[data-v-778b6e1a]:last-child {
  margin-bottom: 0;
}
.task-selector .selector-content .empty-tip[data-v-778b6e1a] {
  text-align: center;
  padding: 1.25rem 0;
  color: #999;
  font-size: 0.8125rem;
}
.device-selector-popup[data-v-778b6e1a] {
  z-index: 100;
  background-color: #fff;
  border-radius: 0.625rem 0.625rem 0 0;
  max-height: 60vh;
  height: 60vh;
  display: flex;
  flex-direction: column;
}
.device-selector-popup .selector-content[data-v-778b6e1a] {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 0 0.9375rem;
}
.device-selector-popup .selector-content .device-list .device-item[data-v-778b6e1a] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.78125rem 0;
  border-bottom: 0.03125rem solid #f0f0f0;
}
.device-selector-popup .selector-content .device-list .device-item[data-v-778b6e1a]:last-child {
  border-bottom: none;
}
.device-selector-popup .selector-content .device-list .device-item .device-name[data-v-778b6e1a] {
  font-size: 0.875rem;
  color: #333;
}
.device-selector-popup .selector-content .device-list .device-item .checkbox[data-v-778b6e1a] {
  width: 1.25rem;
  height: 1.25rem;
  border: 0.03125rem solid #e5e5e5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.device-selector-popup .selector-content .device-list .device-item .checkbox.checked[data-v-778b6e1a] {
  background-color: #007aff;
  border-color: #007aff;
}
.device-selector-popup .selector-content .device-list .device-item .checkbox.checked .iconfont[data-v-778b6e1a] {
  color: #fff;
  font-size: 0.75rem;
}
.staff-selector-popup[data-v-778b6e1a] {
  z-index: 100;
  background-color: #fff;
  border-radius: 0.625rem 0.625rem 0 0;
  max-height: 80vh;
  height: 80vh;
  display: flex;
  flex-direction: column;
}
.staff-selector-popup .selector-content[data-v-778b6e1a] {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 0 0.9375rem;
}
.staff-selector-popup .selector-content .staff-list .staff-item[data-v-778b6e1a] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.78125rem 0;
  border-bottom: 0.03125rem solid #f0f0f0;
}
.staff-selector-popup .selector-content .staff-list .staff-item[data-v-778b6e1a]:last-child {
  border-bottom: none;
}
.staff-selector-popup .selector-content .staff-list .staff-item .staff-name[data-v-778b6e1a] {
  font-size: 0.875rem;
  color: #333;
}
.staff-selector-popup .selector-content .staff-list .staff-item .checkbox[data-v-778b6e1a] {
  width: 1.25rem;
  height: 1.25rem;
  border: 0.03125rem solid #e5e5e5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.staff-selector-popup .selector-content .staff-list .staff-item .checkbox.checked[data-v-778b6e1a] {
  background-color: #007aff;
  border-color: #007aff;
}
.staff-selector-popup .selector-content .staff-list .staff-item .checkbox.checked .iconfont[data-v-778b6e1a] {
  color: #fff;
  font-size: 0.75rem;
}