/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-popup[data-v-7db519c7] {
  position: fixed;
  z-index: 99;
}
.uni-popup.top[data-v-7db519c7], .uni-popup.left[data-v-7db519c7], .uni-popup.right[data-v-7db519c7] {
  top: 0;
}
.uni-popup .uni-popup__wrapper[data-v-7db519c7] {
  display: block;
  position: relative;
  /* iphonex 等安全区设置，底部安全区适配 */
}
.uni-popup .uni-popup__wrapper.left[data-v-7db519c7], .uni-popup .uni-popup__wrapper.right[data-v-7db519c7] {
  padding-top: 0;
  flex: 1;
}
.fixforpc-z-index[data-v-7db519c7] {
  z-index: 999;
}
.fixforpc-top[data-v-7db519c7] {
  top: 0;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.complete-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 0.625rem;
}
.form-card {
  background-color: #fff;
  border-radius: 0.375rem;
  margin-bottom: 0.625rem;
  padding: 0.9375rem;
}
.form-card .form-title {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.9375rem;
  padding-bottom: 0.625rem;
  border-bottom: 0.03125rem solid #eee;
}
.form-card .form-item {
  margin-bottom: 0.9375rem;
}
.form-card .form-item .form-label {
  font-size: 0.875rem;
  color: #333;
  margin-bottom: 0.5rem;
}
.form-card .form-item .form-label.required::before {
  content: "*";
  color: #f5222d;
  margin-right: 0.1875rem;
}
.form-card .form-item .form-textarea {
  width: 100%;
  height: 6.25rem;
  padding: 0.625rem;
  background-color: #f5f5f5;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  color: #333;
  box-sizing: border-box;
}
.form-card .form-item .picker-view {
  width: 100%;
  height: 2.5rem;
  padding: 0 0.625rem;
  background-color: #f5f5f5;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  color: #333;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.3125rem;
}
.form-card .form-item .picker-view .iconfont {
  font-size: 1rem;
  color: #999;
}
.form-card .form-item .upload-area .form-header {
  margin-bottom: 0.625rem;
}
.form-card .form-item .upload-area .form-header .form-subtitle {
  font-size: 0.75rem;
  color: #666;
}
.form-card .form-item .upload-area .image-grid {
  display: flex;
  flex-wrap: wrap;
}
.form-card .form-item .upload-area .image-grid .image-item, .form-card .form-item .upload-area .image-grid .upload-item {
  width: 6.25rem;
  height: 6.25rem;
  margin-right: 0.625rem;
  margin-bottom: 0.625rem;
  position: relative;
}
.form-card .form-item .upload-area .image-grid .image-item uni-image {
  width: 100%;
  height: 100%;
  border-radius: 0.25rem;
}
.form-card .form-item .upload-area .image-grid .image-item .delete-icon {
  position: absolute;
  top: -0.5rem;
  right: -0.5rem;
  width: 1.25rem;
  height: 1.25rem;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1rem;
}
.form-card .form-item .upload-area .image-grid .upload-item {
  border: 0.0625rem dashed #ddd;
  border-radius: 0.25rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.form-card .form-item .upload-area .image-grid .upload-item .iconfont {
  font-size: 1.875rem;
  color: #666;
  margin-bottom: 0.5rem;
}
.form-card .form-item .upload-area .image-grid .upload-item uni-text {
  font-size: 0.75rem;
  color: #666;
}
.form-card .form-item .upload-area .video-container {
  width: 100%;
  height: 12.5rem;
  position: relative;
}
.form-card .form-item .upload-area .video-container uni-video {
  width: 100%;
  height: 100%;
  border-radius: 0.25rem;
}
.form-card .form-item .upload-area .video-container .delete-icon {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  width: 1.25rem;
  height: 1.25rem;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1rem;
}
.action-buttons {
  display: flex;
  padding: 0.625rem 0;
}
.action-buttons uni-button {
  flex: 1;
  height: 2.5rem;
  line-height: 2.5rem;
  text-align: center;
  border-radius: 1.25rem;
  font-size: 0.875rem;
  margin: 0 0.3125rem;
}
.action-buttons uni-button::after {
  border: none;
}
.action-buttons .btn-cancel {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}
.action-buttons .btn-submit {
  background-color: #1890ff;
  color: #fff;
}
.materials-container .material-item {
  margin-bottom: 0.46875rem;
}
.materials-container .material-item .material-row {
  display: flex;
  align-items: center;
}
.materials-container .material-item .material-row .material-selector {
  flex: 2;
  margin-right: 0.46875rem;
}
.materials-container .material-item .material-row .material-selector .material-input {
  height: 2.1875rem;
  background-color: #f5f5f5;
  border-radius: 0.25rem;
  padding: 0 0.625rem;
  font-size: 0.875rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.materials-container .material-item .material-row .material-selector .material-input .arrow-down {
  font-size: 0.75rem;
  color: #999;
}
.materials-container .material-item .material-row .quantity-input {
  flex: 1;
  height: 2.1875rem;
  background-color: #f5f5f5;
  border-radius: 0.25rem;
  padding: 0 0.625rem;
  font-size: 0.875rem;
}
.materials-container .material-item .material-row .delete-btn {
  width: 1.875rem;
  height: 1.875rem;
  line-height: 1.875rem;
  text-align: center;
  font-size: 1.25rem;
  color: #999;
}
.materials-container .add-material-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 2.1875rem;
  background-color: #f5f5f5;
  border-radius: 0.25rem;
  color: #1890ff;
  font-size: 0.875rem;
  border: 1px dashed #ccc;
}
.materials-container .add-material-btn .iconfont {
  margin-right: 0.3125rem;
}
.material-selector-popup {
  background-color: #fff;
  border-radius: 0.625rem 0.625rem 0 0;
  max-height: 80vh;
  height: 60vh;
  display: flex;
  flex-direction: column;
  z-index: 100;
}
.material-selector-popup .selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.9375rem;
  border-bottom: 0.03125rem solid #e5e5e5;
}
.material-selector-popup .selector-header .selector-title {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
}
.material-selector-popup .selector-header .header-actions {
  display: flex;
  align-items: center;
}
.material-selector-popup .selector-header .close-button {
  font-size: 0.875rem;
  color: #666;
}
.material-selector-popup .selector-header .confirm-button-header {
  font-size: 0.875rem;
  color: #1890ff;
  font-weight: 500;
  margin-right: 0.625rem;
}
.material-selector-popup .selector-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 0.9375rem;
  -webkit-overflow-scrolling: touch;
}
.material-selector-popup .selector-content .template-search {
  display: flex;
  align-items: center;
  padding: 0.625rem 0;
  border-bottom: 0.03125rem solid #f0f0f0;
  margin-bottom: 0.625rem;
  position: relative;
}
.material-selector-popup .selector-content .template-search .search-input {
  flex: 1;
  height: 2.1875rem;
  border-radius: 1.09375rem;
  background: #f5f5f5;
  font-size: 0.875rem;
  padding: 0 2.1875rem 0 0.9375rem;
  color: #333;
}
.material-selector-popup .selector-content .template-search .search-clear {
  position: absolute;
  right: 4.0625rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1.25rem;
  height: 1.25rem;
  line-height: 1.25rem;
  text-align: center;
  border-radius: 50%;
  background: #ccc;
  color: #fff;
  font-size: 0.75rem;
}
.material-selector-popup .selector-content .template-search .search-btn {
  padding: 0 0.9375rem;
  font-size: 0.875rem;
  color: #1890ff;
  height: 2.1875rem;
  line-height: 2.1875rem;
}
.material-selector-popup .selector-content .material-list .material-item-popup {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.78125rem 0;
  border-bottom: 0.03125rem solid #f0f0f0;
}
.material-selector-popup .selector-content .material-list .material-item-popup:last-child {
  border-bottom: none;
}
.material-selector-popup .selector-content .material-list .material-item-popup .material-info {
  flex: 1;
}
.material-selector-popup .selector-content .material-list .material-item-popup .material-info .material-name {
  font-size: 0.875rem;
  color: #333;
  margin-bottom: 0.1875rem;
  display: block;
}
.material-selector-popup .selector-content .material-list .material-item-popup .material-info .material-type {
  font-size: 0.75rem;
  color: #666;
}
.material-selector-popup .selector-content .material-list .material-item-popup .checkbox {
  width: 1.25rem;
  height: 1.25rem;
  border: 0.03125rem solid #e5e5e5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.material-selector-popup .selector-content .material-list .material-item-popup .checkbox.checked {
  background-color: #1890ff;
  border-color: #1890ff;
}
.material-selector-popup .selector-content .material-list .material-item-popup .checkbox.checked .iconfont {
  color: #fff;
  font-size: 0.75rem;
}
.material-selector-popup .selector-content .empty-tip {
  text-align: center;
  padding: 1.25rem 0;
  color: #666;
  font-size: 0.875rem;
}
.iconfont {
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.icon-add:before {
  content: "+";
}
.icon-check:before {
  content: "✓";
}