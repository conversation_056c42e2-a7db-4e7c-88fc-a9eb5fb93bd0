<template>
	<view class="payment-stats-container">
		<!-- 页面标题 -->
		<view class="page-header">
			<text class="page-title">缴费统计</text>
		</view>
		
		<!-- 查询条件 -->
		<view class="query-form">
			<view class="form-item">
				<text class="form-label">热力单元</text>
				<picker class="form-picker" @change="handleHeatUnitChange" :value="heatUnitIndex" :range="heatUnitOptions">
					<view class="picker-text">{{ heatUnitOptions[heatUnitIndex] }}</view>
				</picker>
			</view>
			
			<view class="form-item">
				<text class="form-label">时间范围</text>
				<view class="date-range">
					<picker class="date-picker" mode="date" :value="startDate" @change="handleStartDateChange">
						<view class="picker-text">{{ startDate }}</view>
					</picker>
					<text class="date-separator">至</text>
					<picker class="date-picker" mode="date" :value="endDate" @change="handleEndDateChange">
						<view class="picker-text">{{ endDate }}</view>
					</picker>
				</view>
			</view>
			
			<view class="form-item">
				<text class="form-label">缴费状态</text>
				<picker class="form-picker" @change="handleStatusChange" :value="statusIndex" :range="statusOptions">
					<view class="picker-text">{{ statusOptions[statusIndex] }}</view>
				</picker>
			</view>
			
			<button class="query-btn" @click="queryStats">查询</button>
		</view>
		
		<!-- 统计卡片 -->
		<view class="stats-cards" v-if="!loading">
			<view class="stats-card">
				<text class="stats-value">¥{{ statsData.total.toLocaleString() }}</text>
				<text class="stats-label">总金额</text>
			</view>
			<view class="stats-card">
				<text class="stats-value">¥{{ statsData.paid.toLocaleString() }}</text>
				<text class="stats-label">已缴费</text>
			</view>
			<view class="stats-card">
				<text class="stats-value">¥{{ statsData.unpaid.toLocaleString() }}</text>
				<text class="stats-label">未缴费</text>
			</view>
			<view class="stats-card">
				<text class="stats-value">{{ (statsData.paid / statsData.total * 100).toFixed(1) }}%</text>
				<text class="stats-label">缴费率</text>
			</view>
		</view>
		
		<!-- 加载提示 -->
		<view class="loading-container" v-if="loading">
			<uni-load-more status="loading" :content-text="loadingText"></uni-load-more>
		</view>
		
		<!-- 图表区域 -->
		<view class="chart-container" v-if="!loading">
			<view class="chart-header">
				<text class="chart-title">缴费趋势分析</text>
				<view class="chart-tabs">
					<text class="chart-tab" :class="{ active: activeChart === 'trend' }" @click="activeChart = 'trend'">趋势图</text>
					<text class="chart-tab" :class="{ active: activeChart === 'proportion' }" @click="activeChart = 'proportion'">占比图</text>
				</view>
			</view>
			
			<!-- 趋势图 -->
			<view class="chart-content" v-if="activeChart === 'trend'">
				<view class="chart-placeholder">
					<text>趋势图展示区域</text>
					<!-- 实际项目中，这里应该使用支持的图表组件 -->
					<!-- 例如: <qiun-data-charts type="line" :chartData="trendChartData"></qiun-data-charts> -->
					<view class="trend-chart-mock">
						<view class="chart-legend">
							<view class="legend-item">
								<view class="legend-color" style="background-color: #1890ff;"></view>
								<text class="legend-text">已缴费</text>
							</view>
							<view class="legend-item">
								<view class="legend-color" style="background-color: #faad14;"></view>
								<text class="legend-text">未缴费</text>
							</view>
						</view>
						<view class="chart-bars">
							<view class="chart-bar" v-for="(item, index) in mockTrendData" :key="index">
								<view class="bar-label">{{ item.date }}</view>
								<view class="bar-wrapper">
									<view class="bar-value paid" :style="{ height: item.paid + '%' }"></view>
									<view class="bar-value unpaid" :style="{ height: item.unpaid + '%' }"></view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 占比图 -->
			<view class="chart-content" v-if="activeChart === 'proportion'">
				<view class="chart-placeholder">
					<text>占比图展示区域</text>
					<!-- 实际项目中，这里应该使用支持的图表组件 -->
					<!-- 例如: <qiun-data-charts type="pie" :chartData="pieChartData"></qiun-data-charts> -->
					<view class="pie-chart-mock">
						<view class="pie-chart">
							<view class="pie-slice paid" :style="{ transform: 'rotate(0deg)', 'clip-path': 'polygon(50% 50%, 50% 0%, ' + (50 + 50 * Math.cos(Math.PI * 2 * statsData.paid / statsData.total)) + '% ' + (50 - 50 * Math.sin(Math.PI * 2 * statsData.paid / statsData.total)) + '%)' }"></view>
							<view class="pie-slice unpaid" :style="{ transform: 'rotate(' + (360 * statsData.paid / statsData.total) + 'deg)', 'clip-path': 'polygon(50% 50%, 50% 0%, ' + (50 + 50 * Math.cos(Math.PI * 2 * statsData.unpaid / statsData.total)) + '% ' + (50 - 50 * Math.sin(Math.PI * 2 * statsData.unpaid / statsData.total)) + '%)' }"></view>
							<view class="pie-center">
								<text class="pie-percent">{{ (statsData.paid / statsData.total * 100).toFixed(0) }}%</text>
								<text class="pie-label">缴费率</text>
							</view>
						</view>
						<view class="pie-legend">
							<view class="legend-item">
								<view class="legend-color" style="background-color: #1890ff;"></view>
								<view class="legend-info">
									<text class="legend-text">已缴费</text>
									<text class="legend-value">¥{{ statsData.paid.toLocaleString() }}</text>
								</view>
							</view>
							<view class="legend-item">
								<view class="legend-color" style="background-color: #faad14;"></view>
								<view class="legend-info">
									<text class="legend-text">未缴费</text>
									<text class="legend-value">¥{{ statsData.unpaid.toLocaleString() }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 数据为空提示 -->
		<view class="empty-container" v-if="!loading && isEmpty">
			<image class="empty-icon" src="/static/icons/empty.png" mode="aspectFit"></image>
			<text class="empty-text">暂无缴费数据</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			// 获取当前日期
			const now = new Date();
			const year = now.getFullYear();
			const month = now.getMonth() + 1;
			const day = now.getDate();
			
			// 格式化日期为 YYYY-MM-DD
			const formatDate = (y, m, d) => {
				return `${y}-${m < 10 ? '0' + m : m}-${d < 10 ? '0' + d : d}`;
			};
			
			// 默认开始日期为当前月份第一天
			const startDate = formatDate(year, month, 1);
			// 默认结束日期为当前日期
			const endDate = formatDate(year, month, day);
			
			return {
				// 查询条件
				heatUnitOptions: ['全部', '金色家园', '翠湖花园', '阳光小区', '幸福家园', '和平广场'],
				heatUnitIndex: 0,
				statusOptions: ['全部', '已缴费', '未缴费'],
				statusIndex: 0,
				startDate: startDate,
				endDate: endDate,
				
				// 加载状态
				loading: false,
				loadingText: {
					contentdown: '正在加载...',
					contentrefresh: '加载中...',
					contentnomore: '没有更多数据了'
				},
				
				// 统计数据
				statsData: {
					total: 1000000,
					paid: 800000,
					unpaid: 200000
				},
				
				// 图表控制
				activeChart: 'trend',
				
				// 模拟趋势数据
				mockTrendData: [
					{ date: '1月', paid: 65, unpaid: 35 },
					{ date: '2月', paid: 70, unpaid: 30 },
					{ date: '3月', paid: 75, unpaid: 25 },
					{ date: '4月', paid: 80, unpaid: 20 },
					{ date: '5月', paid: 85, unpaid: 15 },
					{ date: '6月', paid: 90, unpaid: 10 }
				],
				
				isEmpty: false
			}
		},
		onLoad() {
			// 页面加载时获取缴费统计数据
			this.getPaymentStats();
		},
		methods: {
			// 选择热力单元变化
			handleHeatUnitChange(e) {
				this.heatUnitIndex = e.detail.value;
			},
			
			// 开始日期变化
			handleStartDateChange(e) {
				this.startDate = e.detail.value;
			},
			
			// 结束日期变化
			handleEndDateChange(e) {
				this.endDate = e.detail.value;
			},
			
			// 缴费状态变化
			handleStatusChange(e) {
				this.statusIndex = e.detail.value;
			},
			
			// 查询按钮点击
			queryStats() {
				this.getPaymentStats();
			},
			
			// 获取缴费统计数据
			getPaymentStats() {
				this.loading = true;
				
				// 构建请求参数
				const params = {
					heat_unit_name: this.heatUnitIndex === 0 ? '' : this.heatUnitOptions[this.heatUnitIndex],
					start_time: this.startDate + ' 00:00:00',
					end_time: this.endDate + ' 23:59:59',
					status: this.statusIndex === 0 ? '' : (this.statusIndex === 1 ? 'paid' : 'unpaid')
				};
				
				console.log('查询参数:', params);
				
				// 实际应用中这里会调用API获取统计数据
				// 模拟API调用
				setTimeout(() => {
					// 模拟响应数据
					const mockResponse = {
						code: 200,
						message: "缴费统计获取成功",
						data: {
							total: Math.floor(Math.random() * 1000000) + 500000,
							paid: Math.floor(Math.random() * 800000) + 200000,
							unpaid: 0
						}
					};
					
					// 计算未缴费金额
					mockResponse.data.unpaid = mockResponse.data.total - mockResponse.data.paid;
					
					// 更新统计数据
					this.statsData = mockResponse.data;
					
					// 更新图表数据
					this.updateChartData();
					
					// 判断是否为空数据
					this.isEmpty = mockResponse.data.total === 0;
					
					this.loading = false;
				}, 1000);
				
				// 实际API调用示例
				/*
				uni.request({
					url: '/api/payments/stats',
					method: 'POST',
					data: params,
					success: (res) => {
						if (res.data.code === 200) {
							this.statsData = res.data.data;
							this.updateChartData();
							this.isEmpty = this.statsData.total === 0;
						} else {
							uni.showToast({
								title: res.data.message || '获取统计数据失败',
								icon: 'none'
							});
						}
					},
					fail: (err) => {
						uni.showToast({
							title: '网络请求失败',
							icon: 'none'
						});
						console.error('获取缴费统计失败:', err);
					},
					complete: () => {
						this.loading = false;
					}
				});
				*/
			},
			
			// 更新图表数据
			updateChartData() {
				// 实际项目中，这里应该根据API返回的数据更新图表数据
				// 这里仅作示例，使用随机数据
				const mockDates = ['1月', '2月', '3月', '4月', '5月', '6月'];
				this.mockTrendData = mockDates.map(date => {
					const paidPercent = Math.floor(Math.random() * 30) + 60; // 60%-90%
					return {
						date: date,
						paid: paidPercent,
						unpaid: 100 - paidPercent
					};
				});
			}
		}
	}
</script>

<style lang="scss">
	.payment-stats-container {
		padding: 30rpx;
		background-color: #f8f8f8;
		min-height: 100vh;
	}
	
	.page-header {
		margin-bottom: 30rpx;
		
		.page-title {
			font-size: 36rpx;
			font-weight: bold;
			color: #333;
		}
	}
	
	.query-form {
		background-color: #fff;
		border-radius: 12rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		
		.form-item {
			margin-bottom: 20rpx;
			
			.form-label {
				display: block;
				font-size: 28rpx;
				color: #666;
				margin-bottom: 10rpx;
			}
			
			.form-picker {
				height: 80rpx;
				border: 1px solid #e8e8e8;
				border-radius: 8rpx;
				padding: 0 20rpx;
				display: flex;
				align-items: center;
				background-color: #f8f8f8;
				
				.picker-text {
					font-size: 28rpx;
					color: #333;
				}
			}
			
			.date-range {
				display: flex;
				align-items: center;
				
				.date-picker {
					flex: 1;
					height: 80rpx;
					border: 1px solid #e8e8e8;
					border-radius: 8rpx;
					padding: 0 20rpx;
					display: flex;
					align-items: center;
					background-color: #f8f8f8;
					
					.picker-text {
						font-size: 28rpx;
						color: #333;
					}
				}
				
				.date-separator {
					margin: 0 20rpx;
					color: #999;
				}
			}
		}
		
		.query-btn {
			background-color: #1890ff;
			color: #fff;
			border-radius: 8rpx;
			height: 80rpx;
			line-height: 80rpx;
			font-size: 28rpx;
			margin-top: 20rpx;
			
			&:active {
				background-color: #0e80eb;
			}
		}
	}
	
	.stats-cards {
		display: flex;
		flex-wrap: wrap;
		margin: 0 -10rpx 30rpx;
		
		.stats-card {
			width: calc(50% - 20rpx);
			margin: 0 10rpx 20rpx;
			background-color: #fff;
			border-radius: 12rpx;
			padding: 30rpx;
			box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			
			.stats-value {
				font-size: 36rpx;
				font-weight: bold;
				color: #1890ff;
				margin-bottom: 10rpx;
			}
			
			.stats-label {
				font-size: 24rpx;
				color: #666;
			}
			
			&:nth-child(3) {
				.stats-value {
					color: #faad14;
				}
			}
			
			&:nth-child(4) {
				.stats-value {
					color: #52c41a;
				}
			}
		}
	}
	
	.loading-container {
		padding: 40rpx 0;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	
	.chart-container {
		background-color: #fff;
		border-radius: 12rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		
		.chart-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 30rpx;
			
			.chart-title {
				font-size: 32rpx;
				font-weight: bold;
				color: #333;
			}
			
			.chart-tabs {
				display: flex;
				
				.chart-tab {
					font-size: 28rpx;
					color: #666;
					margin-left: 30rpx;
					padding-bottom: 10rpx;
					position: relative;
					
					&.active {
						color: #1890ff;
						
						&:after {
							content: "";
							position: absolute;
							bottom: 0;
							left: 0;
							width: 100%;
							height: 4rpx;
							background-color: #1890ff;
							border-radius: 2rpx;
						}
					}
				}
			}
		}
		
		.chart-content {
			min-height: 500rpx;
			
			.chart-placeholder {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				height: 100%;
				
				.trend-chart-mock {
					width: 100%;
					height: 500rpx;
					padding: 20rpx 0;
					
					.chart-legend {
						display: flex;
						justify-content: center;
						margin-bottom: 20rpx;
						
						.legend-item {
							display: flex;
							align-items: center;
							margin: 0 20rpx;
							
							.legend-color {
								width: 24rpx;
								height: 24rpx;
								border-radius: 4rpx;
								margin-right: 10rpx;
							}
							
							.legend-text {
								font-size: 24rpx;
								color: #666;
							}
						}
					}
					
					.chart-bars {
						flex: 1;
						display: flex;
						justify-content: space-around;
						align-items: flex-end;
						height: 400rpx;
						
						.chart-bar {
							display: flex;
							flex-direction: column;
							align-items: center;
							width: calc(100% / 6);
							
							.bar-label {
								font-size: 24rpx;
								color: #999;
								margin-top: 10rpx;
							}
							
							.bar-wrapper {
								width: 60rpx;
								height: 300rpx;
								display: flex;
								flex-direction: column-reverse;
								position: relative;
								
								.bar-value {
									position: absolute;
									bottom: 0;
									width: 100%;
									transition: height 0.5s ease;
									
									&.paid {
										background-color: #1890ff;
										border-radius: 8rpx 8rpx 0 0;
										z-index: 2;
									}
									
									&.unpaid {
										background-color: #faad14;
										border-radius: 0 0 8rpx 8rpx;
										z-index: 1;
									}
								}
							}
						}
					}
				}
				
				.pie-chart-mock {
					width: 100%;
					height: 500rpx;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					
					.pie-chart {
						width: 300rpx;
						height: 300rpx;
						position: relative;
						border-radius: 50%;
						overflow: hidden;
						margin-bottom: 30rpx;
						
						.pie-slice {
							position: absolute;
							width: 100%;
							height: 100%;
							
							&.paid {
								background-color: #1890ff;
							}
							
							&.unpaid {
								background-color: #faad14;
							}
						}
						
						.pie-center {
							position: absolute;
							top: 50%;
							left: 50%;
							transform: translate(-50%, -50%);
							width: 200rpx;
							height: 200rpx;
							background-color: white;
							border-radius: 50%;
							display: flex;
							flex-direction: column;
							align-items: center;
							justify-content: center;
							box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.05);
							
							.pie-percent {
								font-size: 40rpx;
								font-weight: bold;
								color: #1890ff;
							}
							
							.pie-label {
								font-size: 24rpx;
								color: #666;
							}
						}
					}
					
					.pie-legend {
						display: flex;
						justify-content: center;
						
						.legend-item {
							display: flex;
							align-items: center;
							margin: 0 30rpx;
							
							.legend-color {
								width: 24rpx;
								height: 24rpx;
								border-radius: 4rpx;
								margin-right: 10rpx;
							}
							
							.legend-info {
								display: flex;
								flex-direction: column;
								
								.legend-text {
									font-size: 24rpx;
									color: #666;
								}
								
								.legend-value {
									font-size: 22rpx;
									color: #999;
								}
							}
						}
					}
				}
			}
		}
	}
	
	.empty-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 100rpx 0;
		
		.empty-icon {
			width: 200rpx;
			height: 200rpx;
			margin-bottom: 20rpx;
		}
		
		.empty-text {
			font-size: 28rpx;
			color: #999;
		}
	}
</style> 