package com.heating.controller;

import com.heating.dto.temperature.RoomTemperatureRequest;
import com.heating.service.TemperatureService;
import com.heating.util.ApiResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.sql.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.heating.dto.temperature.RoomTemperatureResponse;
import com.heating.entity.temperature.TRoomTemperature;

@RestController
@RequestMapping("/api/temperatures")
public class TemperatureController {

    private static final Logger logger = LoggerFactory.getLogger(TemperatureController.class);
    
    @Autowired
    private TemperatureService temperatureService;

    /**
     * 获取户外温度
     * @return 返回户外温度和记录时间
     */
    @GetMapping("/outdoor")
    public ResponseEntity<?> getOutdoorTemperature() {
        logger.info("Accessing GET /api/temperature/outdoor");
        try {
            Double temperature = temperatureService.getCurrentOutdoorTemperature();
            Map<String, Object> data = new HashMap<>();
            String dateStr = LocalDate.now().toString(); 
            // 处理 null 或 NaN 的情况
            if (temperature == null || temperature.isNaN()) {
                temperature = 100.0;  // 或者其他默认值
                dateStr = "0000-00-00 00:00:00";
            } 
            data.put("temperature", temperature);
            data.put("record_time", dateStr); 
            return ResponseEntity.ok(ApiResponse.success("获取户外温度成功", data));
            
        } catch (Exception e) {
            logger.error("Error in GET /api/temperature/outdoor: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * 获取室温列表
     * @param heat_unit_name 小区名称
     * @param date 查询日期
     * @return 返回室温记录列表和总数
     */
    @GetMapping("/list")
    public ResponseEntity<?> getRoomTemperatureList(
            @RequestParam(required = false) String heat_unit_name,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
        try {  
            List<RoomTemperatureResponse>  resultList = temperatureService.getTemperatures(heat_unit_name, date);  
            return ResponseEntity.ok(ApiResponse.success("获取室温列表成功", resultList)); 
        } catch (Exception e) {
            logger.error("Error in GET /api/temperature/list: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 根据室温记录ID获取室温信息
     * @param id 室温记录ID
     * @return 返回指定ID的室温记录
     */
    @GetMapping("/{id}")
    public ResponseEntity<?> getRoomTemperatureById(@PathVariable Long id) {
        logger.info("Accessing GET /api/temperature/{}", id);
        try {
                TRoomTemperature temperature = temperatureService.getTemperatureById(id); 
                if (temperature == null) {
                    return ResponseEntity.ok(ApiResponse.error("记录不存在") );
                } 
                // 转换为Map处理返回数据
                Map<String, Object> detail = new HashMap<>();
                detail.put("id", temperature.getId());
                detail.put("heat_unit_name", temperature.getHeatUnitName());
                detail.put("building_no", temperature.getBuildingNo());
                detail.put("unit_no", temperature.getUnitNo());
                detail.put("room_no", temperature.getRoomNo());
                detail.put("indoor_temp", temperature.getIndoorTemp());
                detail.put("outdoor_temp", temperature.getOutdoorTemp());
                detail.put("latitude", temperature.getLatitude());
                detail.put("longitude", temperature.getLongitude()); 
                detail.put("images", temperature.getImages());
                detail.put("videos", temperature.getVideos());
                detail.put("remark", temperature.getRemark());
                detail.put("report_time", 
                    temperature.getReportTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")));

                // 处理温度状态
                double temp = temperature.getIndoorTemp() != null ? temperature.getIndoorTemp() : 0;
                detail.put("status", temp < 18 ? "温度低" : temp > 24 ? "温度高" : "温度正常"); 

                return ResponseEntity.ok(ApiResponse.success("获取室温记录成功", detail));

        } catch (Exception e) {
            logger.error("Error in GET /api/temperature/{}: {}", id, e.getMessage(), e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

   
    /**
     * 上报室温记录
     * @param request 室温请求体
     * @return 返回上报结果
     */
    @PostMapping("/report")
    public ResponseEntity<?> reportRoomTemperature(@RequestBody RoomTemperatureRequest request) {
        logger.info("-----Accessing POST /api/temperature/report with request: {}", request);
        try {
            // 参数验证
            if (request.getBuildingNo() == null || request.getBuildingNo().trim().isEmpty()) {
                throw new IllegalArgumentException("楼号不能为空");
            }
            if (request.getUnitNo() == null || request.getUnitNo().trim().isEmpty()) {
                throw new IllegalArgumentException("单元号不能为空");
            }
            if (request.getRoomNo() == null || request.getRoomNo().trim().isEmpty()) {
                throw new IllegalArgumentException("房间号不能为空");
            }
            if (request.getHeatUnitName() == null || request.getHeatUnitName().trim().isEmpty()) {
                throw new IllegalArgumentException("小区名不能为空");
            }

            // 创建新的温度记录实体
            TRoomTemperature temperature = new TRoomTemperature();
            temperature.setHeatUnitName(request.getHeatUnitName().trim());
            temperature.setBuildingNo(request.getBuildingNo().trim());
            temperature.setUnitNo(request.getUnitNo().trim());
            temperature.setRoomNo(request.getRoomNo().trim());
            temperature.setIndoorTemp(request.getIndoorTemp());
            temperature.setOutdoorTemp(request.getOutdoorTemp());
            temperature.setLatitude(request.getLatitude());
            temperature.setLongitude(request.getLongitude());
            temperature.setReportTime(LocalDateTime.now());
            temperature.setImages(request.getImages());
            temperature.setVideos(request.getVideos());
            temperature.setRemark(request.getRemark());
            temperature.setReportUserId(request.getReportUserId()); 
            temperature.setCreatedAt(LocalDateTime.now());
            temperature.setUpdatedAt(LocalDateTime.now()); 
            // 保存温度记录
            temperatureService.saveRoomTemperature(temperature);
 
            return ResponseEntity.ok(ApiResponse.success("温度上报成功", null));

        } catch (IllegalArgumentException e) {
            logger.warn("Invalid request in POST /api/temperature/report: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            logger.error("Error in POST /api/temperature/report: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body(ApiResponse.error("温度上报失败：" + e.getMessage()));
        }
    }
}
 