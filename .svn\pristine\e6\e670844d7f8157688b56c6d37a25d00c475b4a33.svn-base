package com.heating.entity.order;

import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;

@Entity
@Table(name = "t_work_order")
@Data
public class TWorkOrder {

    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    private long id;

    @Column(name = "order_no")
    private String orderNo;

    @Column(name = "fault_id")
    private long faultId; 

    /*故障类型 */
    @Column(name = "fault_type")
    private String faultType;
    
    /*维修人员ID */
    @Column(name = "repair_user_id")
    private long repairUserId;

    /*转派人 ID*/
    @Column(name = "transfer_user_id")
    private Integer transferUserId;

    /*转派原因 */
    @Column(name = "transfer_reason")
    private String transferReason;

    /*转派时间 */
    @Column(name = "transfer_time")
    private LocalDateTime transferTime;

    @Column(name = "repair_content")
    private String repairContent;

    @Column(name = "repair_result")
    private String repairResult;

    @Column(name = "repair_time")
    private LocalDateTime repairTime;

    @Column(name = "repair_materials_quantity", columnDefinition = "json")
    private String repairMaterialsQuantity;

    @Column(name = "order_status")
    private String orderStatus;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
}