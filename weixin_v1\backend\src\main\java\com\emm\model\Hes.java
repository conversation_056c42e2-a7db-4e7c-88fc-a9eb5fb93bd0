package com.emm.model;

import javax.persistence.*;

@Entity
@Table(name = "t_hes")
public class Hes {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "Name")
    private String name;

    @Column(name = "UseHeatUnit_name")
    private String useHeatUnitName;

    // Getters and setters

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUseHeatUnitName() {
        return useHeatUnitName;
    }

    public void setUseHeatUnitName(String useHeatUnitName) {
        this.useHeatUnitName = useHeatUnitName;
    }
}