package com.heating.service.impl;

import com.heating.repository.OutTemperatureRepository;
import com.heating.repository.RoomTemperatureRepository;
import com.heating.service.TemperatureService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Date;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.time.format.DateTimeFormatter;
import com.heating.dto.temperature.RoomTemperatureResponse;
import com.heating.entity.temperature.TRoomTemperature;

@Service
public class TemperatureServiceImpl implements TemperatureService {

    private static final Logger logger = LoggerFactory.getLogger(TemperatureServiceImpl.class);

    @Autowired
    private OutTemperatureRepository outTemperatureRepository;

    @Autowired
    private RoomTemperatureRepository roomTemperatureRepository;

    @Override
    public Double getCurrentOutdoorTemperature() {
        return outTemperatureRepository.findCurrentOutdoorTemperature();
    }

    @Override
    @Transactional
    public TRoomTemperature saveRoomTemperature(TRoomTemperature temperature) {
        return roomTemperatureRepository.save(temperature);
    } 

    @Override
    public List<RoomTemperatureResponse> getTemperatures(String heat_unit_name, Date date) { 
        try {   
                List<TRoomTemperature> temperatures = roomTemperatureRepository.findByHeatUnitNameAndDate(heat_unit_name, date);
                List<RoomTemperatureResponse> resultList = new ArrayList<>();
                for (TRoomTemperature temp : temperatures) {
                    RoomTemperatureResponse item = new RoomTemperatureResponse();
                    item.setHeat_unit_name(temp.getHeatUnitName());
                    item.setBuilding_no(temp.getBuildingNo());
                    item.setUnit_no(temp.getUnitNo());
                    item.setRoom_no(temp.getRoomNo());
                    item.setIndoor_temp(temp.getIndoorTemp());
                    item.setReport_time(temp.getReportTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")));
                    
                    // 添加状态
                    double temperature = temp.getIndoorTemp();
                    String status = temperature < 18 ? "温度低" : temperature > 24 ? "温度高" : "温度正常";
                    item.setStatus(status);
                    
                    // 添加房间信息
                    item.setRoom_info(temp.getBuildingNo() + "-" + temp.getUnitNo() + "-" + temp.getRoomNo()); 
                    resultList.add(item);
                }  
                return resultList; 
        } catch (Exception e) {
            throw new IllegalArgumentException("无效的日期格式，请使用 yyyy-MM-dd 格式");
        } 
    }

    @Override
    public TRoomTemperature getTemperatureById(Long id) {
        Optional<TRoomTemperature> optionalTemperature = roomTemperatureRepository.findById(id);
        return optionalTemperature.orElse(null);
    }
 
} 