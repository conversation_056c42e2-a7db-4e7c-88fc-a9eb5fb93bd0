
.no-permission-item[data-v-9e1a7520] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0.5;
}
.no-permission-text[data-v-9e1a7520] {
  font-size: 0.75rem;
  color: #999;
  text-align: center;
}

/* 确保permission-check组件继承其父容器的布局特性 */
[data-v-9e1a7520] .permission-check {
  display: inherit;
  width: inherit;
  height: inherit;
  flex: inherit;
  position: inherit;
}

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.order-detail-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
  overflow: hidden;
}
.order-info-card {
  background-color: #fff;
  margin: 0.625rem;
  border-radius: 0.375rem;
  padding: 0.9375rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
}
.order-info-card .order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.625rem;
}
.order-info-card .order-header .order-number {
  font-size: 0.875rem;
  color: #333;
  font-weight: bold;
}
.order-info-card .order-header .order-status {
  padding: 0.125rem 0.625rem;
  border-radius: 0.625rem;
  font-size: 0.75rem;
  color: #fff;
}
.order-info-card .order-header .order-status.status-pending {
  background-color: #faad14;
}
.order-info-card .order-header .order-status.status-processing {
  background-color: #1890ff;
}
.order-info-card .order-header .order-status.status-completed {
  background-color: #52c41a;
}
.order-info-card .order-header .order-status.status-transferred {
  background-color: #6777ef;
}
.order-info-card .order-location {
  display: flex;
  align-items: center;
  margin-bottom: 0.625rem;
}
.order-info-card .order-location .location-icon {
  margin-right: 0.3125rem;
}
.order-info-card .order-location .location-icon .iconfont {
  font-size: 1.125rem;
  color: #1890ff;
}
.order-info-card .order-location .location-info .location-name {
  font-size: 1rem;
  color: #333;
  font-weight: bold;
}
.order-info-card .fault-info {
  display: flex;
  margin-top: 0.9375rem;
}
.order-info-card .fault-info .fault-type,
.order-info-card .fault-info .fault-level {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.order-info-card .fault-info .fault-type .fault-label,
.order-info-card .fault-info .fault-level .fault-label {
  font-size: 0.75rem;
  color: #666;
  margin-bottom: 0.25rem;
}
.order-info-card .fault-info .fault-type .fault-value,
.order-info-card .fault-info .fault-level .fault-value {
  font-size: 0.875rem;
  color: #333;
  font-weight: bold;
}
.order-info-card .fault-info .fault-type .fault-value.level-urgent,
.order-info-card .fault-info .fault-level .fault-value.level-urgent {
  color: #f5222d;
}
.order-info-card .fault-info .fault-type .fault-value.level-warning,
.order-info-card .fault-info .fault-level .fault-value.level-warning {
  color: #faad14;
}
.order-info-card .fault-info .fault-type .fault-value.level-notice,
.order-info-card .fault-info .fault-level .fault-value.level-notice {
  color: #1890ff;
}
.detail-tabs {
  display: flex;
  background-color: #fff;
  border-bottom: 0.03125rem solid #eee;
  flex-shrink: 0;
}
.detail-tabs .tab-item {
  flex: 1;
  text-align: center;
  height: 2.5rem;
  line-height: 2.5rem;
  font-size: 0.875rem;
  color: #333;
  position: relative;
}
.detail-tabs .tab-item.active {
  color: #1890ff;
  font-weight: bold;
}
.detail-tabs .tab-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40%;
  height: 0.125rem;
  background-color: #1890ff;
  border-radius: 0.0625rem;
}
.detail-swiper {
  flex: 1;
  height: 0;
  overflow: visible;
}
.detail-swiper .tab-scroll-view {
  height: calc(100vh - 10.625rem);
}
.detail-swiper .tab-content {
  padding: 0.625rem;
  padding-bottom: 3.75rem;
}
.detail-swiper .tab-content .info-section {
  background-color: #fff;
  border-radius: 0.375rem;
  padding: 0.625rem;
  margin-bottom: 0.625rem;
}
.detail-swiper .tab-content .info-section .section-title {
  font-size: 0.875rem;
  color: #333;
  margin-bottom: 0.625rem;
  padding-left: 0.625rem;
}
.detail-swiper .tab-content .info-section .section-content {
  font-size: 0.875rem;
  color: #333;
  line-height: 1.6;
}
.detail-swiper .tab-content .info-section .section-content .info-item {
  margin-bottom: 0.625rem;
  display: flex;
}
.detail-swiper .tab-content .info-section .section-content .info-item .info-label {
  width: 5rem;
  font-size: 0.8125rem;
  color: #666;
}
.detail-swiper .tab-content .info-section .section-content .info-item .info-value {
  flex: 1;
  font-size: 0.875rem;
  color: #333;
  word-break: break-all;
}
.detail-swiper .tab-content .info-section .section-content .info-item .materials-list {
  width: 100%;
}
.detail-swiper .tab-content .info-section .section-content .info-item .materials-list .material-quantity-item {
  display: flex;
  padding: 0.3125rem 0.46875rem;
  background-color: #f8f8f8;
  border-radius: 0.1875rem;
  margin-bottom: 0.3125rem;
}
.detail-swiper .tab-content .info-section .section-content .info-item .materials-list .material-quantity-item:last-child {
  margin-bottom: 0;
}
.detail-swiper .tab-content .info-section .section-content .info-item .materials-list .material-quantity-item .material-name {
  flex: 2;
  font-size: 0.8125rem;
  color: #333;
}
.detail-swiper .tab-content .info-section .section-content .info-item .materials-list .material-quantity-item .material-value {
  flex: 1;
  font-size: 0.8125rem;
  color: #1890ff;
  font-weight: bold;
  text-align: right;
}
.detail-swiper .tab-content .info-section .section-content .info-item-materials {
  margin-bottom: 0.625rem;
}
.detail-swiper .tab-content .info-section .section-content .info-item-materials .info-label {
  font-size: 0.8125rem;
  color: #666;
  margin-bottom: 0.25rem;
  display: block;
}
.detail-swiper .tab-content .info-section .section-content .info-item-materials .materials-list {
  margin-top: 0.3125rem;
}
.detail-swiper .tab-content .info-section .section-content .info-item-materials .materials-list .material-quantity-item {
  display: flex;
  padding: 0.3125rem 0.46875rem;
  background-color: #f8f8f8;
  border-radius: 0.1875rem;
  margin-bottom: 0.3125rem;
}
.detail-swiper .tab-content .info-section .section-content .info-item-materials .materials-list .material-quantity-item:last-child {
  margin-bottom: 0;
}
.detail-swiper .tab-content .info-section .section-content .info-item-materials .materials-list .material-quantity-item .material-name {
  flex: 2;
  font-size: 0.8125rem;
  color: #333;
}
.detail-swiper .tab-content .info-section .section-content .info-item-materials .materials-list .material-quantity-item .material-value {
  flex: 1;
  font-size: 0.8125rem;
  color: #1890ff;
  font-weight: bold;
  text-align: right;
}
.detail-swiper .tab-content .attachment-section .attachment-item {
  background-color: #fff;
  border-radius: 0.375rem;
  padding: 0.625rem;
  margin-bottom: 0.625rem;
}
.detail-swiper .tab-content .attachment-section .attachment-item .attachment-title {
  font-size: 0.875rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.625rem;
}
.detail-swiper .tab-content .attachment-section .attachment-item .attachment-preview {
  width: 100%;
  border-radius: 0.25rem;
  overflow: hidden;
}
.detail-swiper .tab-content .attachment-section .attachment-item .attachment-preview uni-image {
  width: 100%;
  height: 12.5rem;
  background-color: #f5f5f5;
  object-fit: cover;
}
.detail-swiper .tab-content .attachment-section .attachment-item .attachment-preview uni-video {
  width: 100%;
  height: 12.5rem;
  background-color: #000;
  z-index: 1;
  position: relative;
  /* 解决Android设备上的黑屏问题 */
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  /* 解决iOS设备上的闪烁问题 */
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}
.detail-swiper .tab-content .attachment-section .attachment-item .attachment-preview .file-preview {
  height: 6.25rem;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.detail-swiper .tab-content .attachment-section .attachment-item .attachment-preview .file-preview .iconfont {
  font-size: 2.5rem;
  color: #1890ff;
  margin-bottom: 0.625rem;
}
.detail-swiper .tab-content .attachment-section .attachment-item .attachment-preview .file-preview uni-text {
  font-size: 0.875rem;
  color: #666;
}
.detail-swiper .tab-content .log-timeline {
  padding: 0.625rem 0;
}
.detail-swiper .tab-content .log-timeline .log-item {
  position: relative;
  padding-left: 1.25rem;
  margin-bottom: 0.9375rem;
}
.detail-swiper .tab-content .log-timeline .log-item .log-time {
  font-size: 0.75rem;
  color: #666;
  margin-bottom: 0.3125rem;
}
.detail-swiper .tab-content .log-timeline .log-item .log-content {
  display: flex;
}
.detail-swiper .tab-content .log-timeline .log-item .log-content .log-dot {
  position: absolute;
  left: 0;
  top: 0.9375rem;
  width: 0.625rem;
  height: 0.625rem;
  background-color: #1890ff;
  border-radius: 50%;
}
.detail-swiper .tab-content .log-timeline .log-item .log-content .log-dot::before {
  content: "";
  position: absolute;
  left: 0.28125rem;
  top: 0.625rem;
  width: 0.0625rem;
  height: calc(100% + 0.625rem);
  background-color: #e8e8e8;
}
.detail-swiper .tab-content .log-timeline .log-item .log-content .log-info {
  flex: 1;
  background-color: #fff;
  border-radius: 0.375rem;
  padding: 0.625rem;
}
.detail-swiper .tab-content .log-timeline .log-item .log-content .log-info .log-type {
  font-size: 0.875rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.3125rem;
  display: block;
}
.detail-swiper .tab-content .log-timeline .log-item .log-content .log-info .log-desc {
  font-size: 0.8125rem;
  color: #333;
  margin-bottom: 0.5rem;
  display: block;
}
.detail-swiper .tab-content .log-timeline .log-item .log-content .log-info .log-operator {
  font-size: 0.75rem;
  color: #666;
  display: block;
}
.detail-swiper .tab-content .log-timeline .log-item:last-child .log-content .log-dot::before {
  display: none;
}
.detail-swiper .tab-content .empty-attachment,
.detail-swiper .tab-content .empty-logs {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 3.125rem;
}
.detail-swiper .tab-content .empty-attachment uni-image,
.detail-swiper .tab-content .empty-logs uni-image {
  width: 6.25rem;
  height: 6.25rem;
  margin-bottom: 0.625rem;
}
.detail-swiper .tab-content .empty-attachment uni-text,
.detail-swiper .tab-content .empty-logs uni-text {
  font-size: 0.875rem;
  color: #666;
}
.order-actions {
  display: flex;
  padding: 0.625rem;
  background-color: #fff;
  border-top: 0.03125rem solid #eee;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
}
.order-actions .action-btn {
  flex: 1;
  height: 2.5rem;
  line-height: 2.5rem;
  text-align: center;
  border-radius: 1.25rem;
  font-size: 0.875rem;
  margin: 0 0.3125rem;
}
.order-actions .action-btn::after {
  border: none;
}
.order-actions .accept-btn {
  background-color: #1890ff;
  color: #fff;
}
.order-actions .complete-btn {
  background-color: #52c41a;
  color: #fff;
}
.order-actions .transfer-btn {
  background-color: #f5f5f5;
  color: #333;
}
.video-container {
  width: 100%;
  height: 12.5rem;
  background-color: #000;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 0.25rem;
  overflow: hidden;
}
.video-container .video-bg {
  width: 100%;
  height: 100%;
  background-color: #222;
  position: absolute;
  top: 0;
  left: 0;
}
.video-container .video-play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}
.video-container .video-play-icon .iconfont {
  color: #fff;
  font-size: 1.875rem;
}
.video-container .video-play-icon .play-circle {
  width: 3.125rem;
  height: 3.125rem;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.video-container .video-play-icon .play-circle .play-triangle {
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0.625rem 0 0.625rem 0.9375rem;
  border-color: transparent transparent transparent #ffffff;
  margin-left: 0.25rem;
}
.file-preview {
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}