package com.emm.repository;


import com.emm.model.OutTemperature;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Repository
public interface OutTemperatureRepository extends JpaRepository<OutTemperature, Long> {

    @Query(value = "SELECT temperature FROM outdoor_temperature " +
           "WHERE DATE(record_time) = CURRENT_DATE() " +
           "ORDER BY record_time DESC LIMIT 1", 
           nativeQuery = true)
    Double findCurrentOutdoorTemperature();
}