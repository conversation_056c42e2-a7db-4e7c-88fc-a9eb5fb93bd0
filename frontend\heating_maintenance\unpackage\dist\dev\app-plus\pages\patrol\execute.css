/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-popup[data-v-7db519c7] {
  position: fixed;
  z-index: 99;
}
.uni-popup.top[data-v-7db519c7], .uni-popup.left[data-v-7db519c7], .uni-popup.right[data-v-7db519c7] {
  top: 0;
}
.uni-popup .uni-popup__wrapper[data-v-7db519c7] {
  display: block;
  position: relative;
  /* iphonex 等安全区设置，底部安全区适配 */
}
.uni-popup .uni-popup__wrapper.left[data-v-7db519c7], .uni-popup .uni-popup__wrapper.right[data-v-7db519c7] {
  padding-top: 0;
  flex: 1;
}
.fixforpc-z-index[data-v-7db519c7] {
  z-index: 999;
}
.fixforpc-top[data-v-7db519c7] {
  top: 0;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.patrol-execute-container {
  padding: 0.625rem;
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 3.75rem;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f5f5;
}
.loading-spinner {
  width: 2.5rem;
  height: 2.5rem;
  border: 0.1875rem solid rgba(0, 122, 255, 0.1);
  border-top-color: #007aff;
  border-radius: 50%;
  animation: spin 1s infinite linear;
  margin-bottom: 0.9375rem;
}
.loading-text {
  font-size: 0.9375rem;
  color: #666;
}
@keyframes spin {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
.progress-bar {
  height: 0.3125rem;
  background-color: #eee;
  border-radius: 0.15625rem;
  overflow: hidden;
  margin-bottom: 0.3125rem;
}
.progress-bar .progress-inner {
  height: 100%;
  background-color: #1890ff;
}
.progress-info {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: #666;
  margin-bottom: 0.625rem;
}
.task-card {
  background-color: #fff;
  border-radius: 0.375rem;
  padding: 0.9375rem;
  margin-bottom: 0.625rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.task-card .task-header {
  margin-bottom: 0.625rem;
}
.task-card .task-header .task-title {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
}
.task-card .task-details .detail-row {
  display: flex;
  margin-bottom: 0.5rem;
}
.task-card .task-details .detail-row:last-child {
  margin-bottom: 0;
}
.task-card .task-details .detail-row .detail-label {
  width: 3.75rem;
  font-size: 0.875rem;
  color: #666;
}
.task-card .task-details .detail-row .detail-value {
  flex: 1;
  font-size: 0.875rem;
  color: #333;
}
.task-card .task-details .detail-row .importance-tag {
  display: inline-block;
  padding: 0.125rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  max-width: -webkit-fit-content;
  max-width: fit-content;
}
.task-card .task-details .detail-row .importance-tag.normal-importance {
  background-color: #e6f7ff;
  color: #1890ff;
  border: 0.03125rem solid #91d5ff;
}
.task-card .task-details .detail-row .importance-tag.important-importance {
  background-color: #fff7e6;
  color: #fa8c16;
  border: 0.03125rem solid #ffd591;
}
.task-card .task-details .detail-row .importance-tag.critical-importance {
  background-color: #fff1f0;
  color: #f5222d;
  border: 0.03125rem solid #ffa39e;
}
.input-card {
  background-color: #fff;
  border-radius: 0.375rem;
  padding: 0.9375rem;
  margin-bottom: 0.625rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.input-card .card-header {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.625rem;
}
.input-card .card-content .form-input {
  width: 100%;
  height: 2.5rem;
  padding: 0 0.625rem;
  border: 0.03125rem solid #ddd;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  box-sizing: border-box;
}
.input-card .card-content .picker-value {
  width: 100%;
  height: 2.5rem;
  line-height: 2.5rem;
  text-align: center;
}
.input-card .card-content .radio-group {
  display: flex;
}
.input-card .card-content .radio-group .radio-item {
  display: flex;
  align-items: center;
  margin-right: 1.875rem;
}
.input-card .card-content .radio-group .radio-item .radio-dot {
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  border: 0.03125rem solid #ddd;
  margin-right: 0.3125rem;
  display: flex;
  justify-content: center;
  align-items: center;
}
.input-card .card-content .radio-group .radio-item .radio-dot::after {
  content: "";
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background-color: #1890ff;
  opacity: 0;
}
.input-card .card-content .radio-group .radio-item uni-text {
  font-size: 0.875rem;
  color: #333;
}
.input-card .card-content .radio-group .radio-item.radio-selected .radio-dot {
  border-color: #1890ff;
}
.input-card .card-content .radio-group .radio-item.radio-selected .radio-dot::after {
  opacity: 1;
}
.input-card .card-content .radio-group .radio-item.radio-selected uni-text {
  color: #1890ff;
}
.input-card .card-content .form-textarea {
  width: 100%;
  height: 5rem;
  padding: 0.625rem;
  border: 0.03125rem solid #ddd;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  box-sizing: border-box;
}
.input-card .card-content .image-list {
  display: flex;
  flex-wrap: wrap;
}
.input-card .card-content .image-list .image-item {
  width: 5rem;
  height: 5rem;
  margin-right: 0.625rem;
  margin-bottom: 0.625rem;
  position: relative;
  border-radius: 0.25rem;
  overflow: hidden;
}
.input-card .card-content .image-list .image-item uni-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.input-card .card-content .image-list .image-item .delete-icon {
  position: absolute;
  top: 0;
  right: 0;
  width: 1.25rem;
  height: 1.25rem;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  font-size: 0.875rem;
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom-left-radius: 0.25rem;
}
.input-card .card-content .image-list .image-upload {
  width: 5rem;
  height: 5rem;
  border: 0.03125rem dashed #ddd;
  border-radius: 0.25rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-bottom: 0.625rem;
}
.input-card .card-content .image-list .image-upload .upload-icon {
  font-size: 1.5rem;
  color: #999;
  margin-bottom: 0.25rem;
}
.input-card .card-content .image-list .image-upload .upload-text {
  font-size: 0.75rem;
  color: #999;
}
.action-buttons {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 0.625rem;
  background-color: #fff;
  display: flex;
  box-shadow: 0 -0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.action-buttons .action-btn {
  flex: 1;
  height: 2.5rem;
  line-height: 2.5rem;
  text-align: center;
  border-radius: 0.25rem;
  font-size: 0.9375rem;
  margin: 0 0.3125rem;
}
.action-buttons .action-btn.prev {
  background-color: #f5f5f5;
  color: #333;
}
.action-buttons .action-btn.skip {
  background-color: #f5f5f5;
  color: #faad14;
}
.action-buttons .action-btn.submit {
  background-color: #1890ff;
  color: #fff;
}
.popup-content {
  width: 17.5rem;
  background-color: #fff;
  border-radius: 0.375rem;
  overflow: hidden;
}
.popup-content .popup-title {
  text-align: center;
  font-size: 1rem;
  font-weight: bold;
  padding: 0.9375rem 0;
}
.popup-content .popup-message {
  padding: 0 0.9375rem 0.9375rem;
  text-align: center;
  font-size: 0.875rem;
  color: #333;
}
.popup-content .popup-buttons {
  display: flex;
  border-top: 0.03125rem solid #eee;
}
.popup-content .popup-buttons .popup-button {
  flex: 1;
  text-align: center;
  height: 2.8125rem;
  line-height: 2.8125rem;
  font-size: 0.9375rem;
}
.popup-content .popup-buttons .popup-button.cancel {
  color: #333;
  border-right: 0.03125rem solid #eee;
}
.popup-content .popup-buttons .popup-button.confirm {
  color: #1890ff;
}
.content-description {
  word-break: break-word;
  white-space: normal;
}
uni-textarea {
  width: 100%;
  height: 5rem;
  padding: 0.625rem;
  border: 0.03125rem solid #ddd;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  box-sizing: border-box;
}
.unit-text {
  font-size: 0.75rem;
  color: #999;
  margin-left: 0.3125rem;
}