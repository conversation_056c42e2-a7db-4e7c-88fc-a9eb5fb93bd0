package com.heating.entity.attendance;

import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalTime;

@Data
@Entity
@Table(name = "t_other_param")
public class TOtherParam {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "clock_in_time", nullable = false)
    private LocalTime clockInTime; // 上班打卡时间

    @Column(name = "clock_out_time", nullable = false)
    private LocalTime clockOutTime; // 下班打卡时间

  }